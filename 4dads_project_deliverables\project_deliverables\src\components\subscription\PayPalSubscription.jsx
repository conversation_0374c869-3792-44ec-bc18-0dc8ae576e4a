// src/components/subscription/PayPalSubscription.jsx
// [!] هذا المكون يتعامل مع عملية الدفع باستخدام PayPal
// [!] يجب تعديل متغيرات PayPal حسب إعدادات حساب PayPal للمطورين الخاص بك

import React, { useState, useEffect } from 'react';

// [!] متغيرات يجب تعديلها: PAYPAL_CLIENT_ID
const PAYPAL_CLIENT_ID = 'YOUR_PAYPAL_CLIENT_ID'; // يجب استبدالها بمعرف العميل الخاص بك من حساب PayPal للمطورين

const PayPalSubscription = ({ plan, onSuccess, onError, onCancel }) => {
  const [scriptLoaded, setScriptLoaded] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [message, setMessage] = useState({ type: '', text: '' });

  // تحميل سكريبت PayPal
  useEffect(() => {
    // التحقق من وجود السكريبت بالفعل
    if (document.querySelector(`script[src*="paypal"]`)) {
      setScriptLoaded(true);
      return;
    }

    // إنشاء عنصر السكريبت
    const script = document.createElement('script');
    script.src = `https://www.paypal.com/sdk/js?client-id=${PAYPAL_CLIENT_ID}&vault=true&intent=subscription`;
    script.async = true;
    script.onload = () => setScriptLoaded(true);
    script.onerror = () => {
      console.error('PayPal script failed to load');
      setMessage({
        type: 'error',
        text: 'فشل تحميل سكريبت PayPal. يرجى تحديث الصفحة والمحاولة مرة أخرى.'
      });
    };
    
    document.body.appendChild(script);
    
    // تنظيف عند إزالة المكون
    return () => {
      if (document.body.contains(script)) {
        document.body.removeChild(script);
      }
    };
  }, []);

  // تهيئة زر PayPal
  useEffect(() => {
    if (scriptLoaded && window.paypal && plan) {
      // إزالة أي أزرار سابقة
      const container = document.getElementById('paypal-button-container');
      if (container) {
        container.innerHTML = '';
      }

      // إنشاء زر PayPal
      window.paypal.Buttons({
        style: {
          layout: 'vertical',
          color: 'blue',
          shape: 'rect',
          label: 'subscribe'
        },
        createSubscription: async (data, actions) => {
          setProcessing(true);
          setMessage({ type: '', text: '' });
          
          try {
            // [!] في بيئة الإنتاج، يجب استخدام معرف خطة الاشتراك الحقيقي من PayPal
            // يمكن إنشاء خطط الاشتراك من لوحة تحكم PayPal للمطورين
            
            // للاختبار، نستخدم معرف وهمي
            const planId = 'P-FAKE_PLAN_ID';
            
            return actions.subscription.create({
              plan_id: planId,
              application_context: {
                shipping_preference: 'NO_SHIPPING'
              }
            });
          } catch (error) {
            console.error('Error creating subscription:', error);
            setMessage({
              type: 'error',
              text: 'حدث خطأ أثناء إنشاء الاشتراك. يرجى المحاولة مرة أخرى.'
            });
            setProcessing(false);
            throw error;
          }
        },
        onApprove: async (data, actions) => {
          try {
            // الحصول على تفاصيل الاشتراك
            const subscriptionDetails = await actions.subscription.get();
            
            // إرسال بيانات الاشتراك إلى الخادم
            const response = await fetch('/api/subscription/confirm', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                subscriptionId: data.subscriptionID,
                planId: plan.id,
                details: subscriptionDetails
              }),
            });
            
            const result = await response.json();
            
            if (response.ok && result.success) {
              setMessage({
                type: 'success',
                text: 'تم إنشاء الاشتراك بنجاح!'
              });
              
              if (onSuccess) {
                onSuccess({
                  subscriptionId: data.subscriptionID,
                  details: subscriptionDetails
                });
              }
            } else {
              throw new Error(result.message || 'فشل تأكيد الاشتراك');
            }
          } catch (error) {
            console.error('Error processing subscription:', error);
            setMessage({
              type: 'error',
              text: 'حدث خطأ أثناء معالجة الاشتراك. يرجى الاتصال بالدعم الفني.'
            });
            
            if (onError) {
              onError(error.message);
            }
          } finally {
            setProcessing(false);
          }
        },
        onCancel: () => {
          setMessage({
            type: 'info',
            text: 'تم إلغاء عملية الاشتراك.'
          });
          setProcessing(false);
          
          if (onCancel) {
            onCancel();
          }
        },
        onError: (err) => {
          console.error('PayPal error:', err);
          setMessage({
            type: 'error',
            text: 'حدث خطأ في PayPal. يرجى المحاولة مرة أخرى لاحقًا.'
          });
          setProcessing(false);
          
          if (onError) {
            onError(err.message);
          }
        }
      }).render('#paypal-button-container');
    }
  }, [scriptLoaded, plan, onSuccess, onError, onCancel]);

  return (
    <div className="mt-8 max-w-md mx-auto">
      {plan && (
        <div className="bg-white p-6 rounded-lg shadow-md mb-6">
          <h3 className="text-xl font-bold text-gray-900 mb-4">تأكيد الاشتراك</h3>
          
          <div className="mb-4 pb-4 border-b border-gray-200">
            <p className="text-gray-700 mb-1">الخطة: <span className="font-semibold">{plan.name}</span></p>
            <p className="text-gray-700 mb-1">السعر: <span className="font-semibold">${plan.price}</span></p>
            <p className="text-gray-700">المدة: <span className="font-semibold">
              {plan.duration_days === 7 && 'أسبوع'}
              {plan.duration_days === 30 && 'شهر'}
              {plan.duration_days === 365 && 'سنة'}
            </span></p>
          </div>
          
          {message.text && (
            <div className={`p-4 mb-4 rounded ${
              message.type === 'success' ? 'bg-green-100 text-green-700' : 
              message.type === 'error' ? 'bg-red-100 text-red-700' : 
              'bg-blue-100 text-blue-700'
            }`}>
              {message.text}
            </div>
          )}
          
          {processing ? (
            <div className="flex justify-center items-center p-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mr-2"></div>
              <span>جاري معالجة الاشتراك...</span>
            </div>
          ) : (
            <div id="paypal-button-container" className="mt-4"></div>
          )}
        </div>
      )}
    </div>
  );
};

export default PayPalSubscription;
