/* الأنماط الرئيسية */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    direction: rtl;
    overflow: hidden;
    height: 100vh;
    width: 100vw;
    background-color: #f0f0f0;
}

/* رأس الصفحة */
.app-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    background-color: #3a3a8c;
    color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 1000;
}

.app-header h1 {
    font-size: 1.5rem;
    margin: 0;
}

.header-controls {
    display: flex;
    align-items: center;
}

.header-controls .canvas-size-selector {
    background-color: transparent;
    border: none;
    padding: 0;
    margin-right: 15px;
    display: flex;
    align-items: center;
}

.header-controls .canvas-size-selector label {
    color: white;
    margin-left: 8px;
}

.header-controls .canvas-size-selector select {
    padding: 6px 10px;
    border-radius: 4px;
    border: 1px solid #5a5aac;
    background-color: #4a4a9c;
    color: white;
    width: 200px;
}

/* حاوية التطبيق الرئيسية */
.container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 60px); /* طرح ارتفاع رأس الصفحة */
    width: 100vw;
    position: relative;
}

/* أقسام التطبيق */
.section {
    transition: all 0.3s ease;
}

.three-section {
    flex: 1;
    background-color: #222;
    position: relative;
}

.fabric-section {
    height: 50%;
    background-color: #fff;
    border-top: 1px solid #ddd;
    display: flex;
    flex-direction: column;
    position: relative;
}

/* مؤشر التحميل */
.loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    color: white;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 10px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* إخفاء لوحة Fabric.js افتراضيًا */
.hidden {
    display: none;
}

/* شريط الأدوات */
.toolbar-container {
    width: 100%;
    background-color: #f8f8f8;
    border-bottom: 1px solid #ddd;
}

.horizontal-scrollable {
    display: flex;
    overflow-x: auto;
    white-space: nowrap;
    padding: 8px;
    scrollbar-width: thin;
}

.horizontal-scrollable::-webkit-scrollbar {
    height: 6px;
}

.horizontal-scrollable::-webkit-scrollbar-thumb {
    background-color: #ccc;
    border-radius: 3px;
}

.toolbar button, .subtoolbar button {
    margin: 0 4px;
    padding: 8px 12px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    white-space: nowrap;
    transition: all 0.2s ease;
}

.toolbar button:hover, .subtoolbar button:hover {
    background-color: #f0f0f0;
}

.toolbar button.active, .subtoolbar button.active {
    background-color: #e0e0ff;
    border-color: #9090ff;
}

/* حاوية لوحة Fabric.js */
.canvas-wrapper {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: auto;
    padding: 20px;
}

/* لوحة Fabric.js */
#fabric-canvas {
    margin: auto;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

/* أزرار التحكم */
.controls {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    z-index: 1000;
}

.control-btn {
    padding: 10px 20px;
    background-color: #4a4a9c;
    color: white;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}

.control-btn:hover {
    background-color: #3a3a8c;
    transform: translateY(-2px);
}

.control-btn i {
    margin-left: 5px;
}

/* التصميم المستجيب */
@media (min-width: 768px) {
    .container {
        flex-direction: row;
    }
    
    .three-section {
        width: 50%;
    }
    
    .fabric-section {
        width: 50%;
        height: 100%;
        border-top: none;
        border-right: 1px solid #ddd;
    }
    
    .container.fabric-left .three-section {
        order: 2;
    }
    
    .container.fabric-left .fabric-section {
        order: 1;
        border-right: none;
        border-left: 1px solid #ddd;
    }
}

/* تصميم مستجيب للهواتف */
@media (max-width: 767px) {
    .app-header {
        flex-direction: column;
        align-items: flex-start;
        padding: 10px;
    }
    
    .header-controls {
        margin-top: 10px;
        width: 100%;
    }
    
    .header-controls .canvas-size-selector {
        width: 100%;
        margin-right: 0;
    }
    
    .header-controls .canvas-size-selector select {
        width: 100%;
    }
    
    .container {
        flex-direction: column;
        height: calc(100vh - 100px); /* طرح ارتفاع رأس الصفحة للهواتف */
    }
    
    .three-section {
        height: 50%;
    }
    
    .fabric-section {
        height: 50%;
    }
    
    .container.fabric-top .three-section {
        order: 2;
    }
    
    .container.fabric-top .fabric-section {
        order: 1;
        border-top: none;
        border-bottom: 1px solid #ddd;
    }
    
    .controls {
        bottom: 10px;
    }
    
    .control-btn {
        padding: 8px 15px;
        font-size: 0.9rem;
    }
}
