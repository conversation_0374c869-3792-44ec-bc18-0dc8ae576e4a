<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أداة قص الصور المحسنة</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .app-content {
            padding: 40px;
        }

        .image-section {
            text-align: center;
            margin-bottom: 30px;
        }

        .preview-text {
            font-size: 1.5em;
            color: #666;
            margin-bottom: 30px;
            padding: 40px;
            border: 3px dashed #ddd;
            border-radius: 15px;
        }

        .image-container {
            position: relative;
            display: inline-block;
            max-width: 100%;
            margin-bottom: 20px;
        }

        #imagePreview, #croppedResult {
            max-width: 100%;
            max-height: 600px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            display: none;
        }

        #croppedResult {
            border: 3px solid #4facfe;
        }

        /* Crop overlay and rectangle */
        .crop-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: none;
            pointer-events: none;
        }

        .crop-rect {
            position: absolute;
            border: 2px solid #4facfe;
            background: transparent;
            cursor: move;
            display: none;
            min-width: 50px;
            min-height: 50px;
        }

        .crop-handle {
            position: absolute;
            width: 12px;
            height: 12px;
            background: #4facfe;
            border: 2px solid white;
            border-radius: 50%;
            z-index: 10;
        }

        .crop-handle.nw { top: -6px; left: -6px; cursor: nw-resize; }
        .crop-handle.ne { top: -6px; right: -6px; cursor: ne-resize; }
        .crop-handle.sw { bottom: -6px; left: -6px; cursor: sw-resize; }
        .crop-handle.se { bottom: -6px; right: -6px; cursor: se-resize; }

        /* Edge handles for resizing from sides */
        .crop-handle.n { top: -6px; left: 50%; margin-left: -6px; cursor: n-resize; }
        .crop-handle.s { bottom: -6px; left: 50%; margin-left: -6px; cursor: s-resize; }
        .crop-handle.e { top: 50%; right: -6px; margin-top: -6px; cursor: e-resize; }
        .crop-handle.w { top: 50%; left: -6px; margin-top: -6px; cursor: w-resize; }

        /* File input styling */
        .file-input-container {
            margin: 30px 0;
        }

        .file-input-label {
            display: inline-block;
            padding: 20px 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 50px;
            cursor: pointer;
            font-size: 1.2em;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .file-input-label:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        #imageLoader {
            display: none;
        }

        /* Crop button above image */
        .crop-toggle-btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.3);
            transition: all 0.3s ease;
            display: none;
        }

        .crop-toggle-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(79, 172, 254, 0.4);
        }

        .crop-toggle-btn.active {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        }

        /* Horizontal scrollable crop controls */
        .crop-controls {
            display: none;
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .crop-controls.active {
            display: block;
        }

        .controls-header {
            text-align: center;
            margin-bottom: 20px;
            color: #333;
            font-size: 1.3em;
            font-weight: bold;
        }

        .current-ratio {
            text-align: center;
            margin-bottom: 15px;
            color: #666;
            font-size: 1.1em;
        }

        .ratio-indicator {
            color: #4facfe;
            font-weight: bold;
        }

        .controls-scroll {
            overflow-x: auto;
            white-space: nowrap;
            padding: 10px 0;
            margin-bottom: 20px;
        }

        .controls-scroll::-webkit-scrollbar {
            height: 6px;
        }

        .controls-scroll::-webkit-scrollbar-track {
            background: #e9ecef;
            border-radius: 3px;
        }

        .controls-scroll::-webkit-scrollbar-thumb {
            background: #4facfe;
            border-radius: 3px;
        }

        .controls-row {
            display: inline-flex;
            gap: 15px;
            padding: 0 10px;
        }

        .crop-btn {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 15px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            min-width: 120px;
            flex-shrink: 0;
        }

        .crop-btn:hover {
            border-color: #4facfe;
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.2);
            transform: translateY(-2px);
        }

        .crop-btn.active {
            background: #4facfe;
            color: white;
            border-color: #4facfe;
        }

        .crop-btn i {
            display: block;
            font-size: 1.5em;
            margin-bottom: 8px;
            color: #4facfe;
        }

        .crop-btn.active i {
            color: white;
        }

        .crop-btn .ratio-indicator {
            display: block;
            font-size: 0.9em;
            font-weight: bold;
            margin-top: 5px;
        }

        /* Action buttons */
        .action-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .action-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .action-btn.primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .action-btn.success {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        }

        /* Result actions */
        .result-actions {
            margin-top: 30px;
            text-align: center;
        }

        .result-btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            margin: 0 10px;
            cursor: pointer;
            font-size: 1.1em;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.3);
        }

        .result-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(79, 172, 254, 0.4);
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .app-content {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .controls-row {
                gap: 10px;
            }
            
            .crop-btn {
                min-width: 100px;
                padding: 12px 15px;
            }
            
            .action-controls {
                flex-direction: column;
                align-items: center;
            }
            
            .action-btn {
                width: 100%;
                max-width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-crop-alt"></i> أداة قص الصور المتقدمة</h1>
            <p>قم بتحميل صورة وقصها بالمقاسات المطلوبة بسهولة</p>
        </div>

        <div class="app-content">
            <div class="image-section">
                <div class="preview-text" id="previewText">قم بتحميل صورة لبدء استخدام أداة القص</div>
                
                <button class="crop-toggle-btn" id="cropToggleBtn">
                    <i class="fas fa-crop-alt"></i> تفعيل أداة القص
                </button>
                
                <div class="image-container">
                    <img id="imagePreview" alt="الصورة المحددة">
                    <img id="croppedResult" alt="الصورة بعد القص">
                    <div class="crop-overlay" id="cropOverlay"></div>
                    <div class="crop-rect" id="cropRect">
                        <div class="crop-handle nw"></div>
                        <div class="crop-handle ne"></div>
                        <div class="crop-handle sw"></div>
                        <div class="crop-handle se"></div>
                        <div class="crop-handle n"></div>
                        <div class="crop-handle s"></div>
                        <div class="crop-handle e"></div>
                        <div class="crop-handle w"></div>
                    </div>
                </div>
                
                <div class="file-input-container" id="fileInputContainer">
                    <label for="imageLoader" class="file-input-label">
                        <i class="fas fa-cloud-upload-alt"></i> تحميل صورة
                    </label>
                    <input type="file" id="imageLoader" accept="image/*">
                </div>
                
                <!-- Crop Controls (appears below image when crop is active) -->
                <div class="crop-controls" id="cropControls">
                    <div class="controls-header">
                        <i class="fas fa-crop-alt"></i> نسب القص المتاحة
                    </div>
                    
                    <div class="current-ratio" id="currentRatio">
                        النسبة الحالية: <span class="ratio-indicator">-</span>
                    </div>
                    
                    <div class="controls-scroll">
                        <div class="controls-row">
                            <button class="crop-btn active" data-ratio="custom">
                                <i class="fas fa-ruler-combined"></i>
                                مخصص
                                <span class="ratio-indicator">-</span>
                            </button>
                            <button class="crop-btn" data-ratio="1:1">
                                <i class="fas fa-square"></i>
                                مربع
                                <span class="ratio-indicator">1:1</span>
                            </button>
                            <button class="crop-btn" data-ratio="4:3">
                                <i class="fas fa-desktop"></i>
                                شاشة
                                <span class="ratio-indicator">4:3</span>
                            </button>
                            <button class="crop-btn" data-ratio="16:9">
                                <i class="fas fa-tv"></i>
                                عريض
                                <span class="ratio-indicator">16:9</span>
                            </button>
                            <button class="crop-btn" data-ratio="3:2">
                                <i class="fas fa-camera"></i>
                                كاميرا
                                <span class="ratio-indicator">3:2</span>
                            </button>
                            <button class="crop-btn" data-ratio="5:4">
                                <i class="fas fa-image"></i>
                                تقليدي
                                <span class="ratio-indicator">5:4</span>
                            </button>
                            <button class="crop-btn" data-ratio="9:16">
                                <i class="fas fa-mobile-alt"></i>
                                عمودي
                                <span class="ratio-indicator">9:16</span>
                            </button>
                            <button class="action-btn" id="rotateRatioBtn">
                                <i class="fas fa-sync-alt"></i> تدوير النسبة
                            </button>
                            <button class="action-btn" id="rotateImageBtn">
                                <i class="fas fa-redo"></i> تدوير الصورة 90°
                            </button>
                            <button class="action-btn primary" id="applyCropBtn">
                                <i class="fas fa-check"></i> تطبيق القص
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="result-actions" id="resultActions" style="display: none;">
                    <button id="saveBtn" class="result-btn">
                        <i class="fas fa-download"></i> حفظ الصورة
                    </button>
                    <button id="newCropBtn" class="result-btn">
                        <i class="fas fa-crop-alt"></i> قص صورة جديدة
                    </button>
                    <button id="backToCropBtn" class="result-btn">
                        <i class="fas fa-undo"></i> الرجوع للتعديل
                    </button>
                    <button id="resetBtn" class="result-btn">
                        <i class="fas fa-redo"></i> إعادة تعيين
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        class ImageCropper {
            constructor() {
                this.imagePreview = document.getElementById('imagePreview');
                this.croppedResult = document.getElementById('croppedResult');
                this.previewText = document.getElementById('previewText');
                this.fileInputContainer = document.getElementById('fileInputContainer');
                this.cropToggleBtn = document.getElementById('cropToggleBtn');
                this.cropControls = document.getElementById('cropControls');
                this.cropOverlay = document.getElementById('cropOverlay');
                this.cropRect = document.getElementById('cropRect');
                this.resultActions = document.getElementById('resultActions');
                this.currentRatio = document.getElementById('currentRatio');
                
                this.currentImage = null;
                this.cropMode = false;
                this.selectedRatio = 'custom';
                this.cropData = { x: 0, y: 0, width: 0, height: 0 };
                this.isDragging = false;
                this.isResizing = false;
                this.dragStart = { x: 0, y: 0 };
                this.imageRotation = 0;
                
                this.initEventListeners();
            }
            
            initEventListeners() {
                document.getElementById('imageLoader').addEventListener('change', (e) => this.handleImageLoad(e));
                this.cropToggleBtn.addEventListener('click', () => this.toggleCropMode());
                
                // Crop ratio buttons
                document.querySelectorAll('.crop-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => this.selectRatio(e.target.closest('.crop-btn').dataset.ratio));
                });
                
                // Action buttons
                document.getElementById('applyCropBtn').addEventListener('click', () => this.applyCrop());
                document.getElementById('rotateRatioBtn').addEventListener('click', () => this.rotateRatio());
                document.getElementById('rotateImageBtn').addEventListener('click', () => this.rotateImage());
                document.getElementById('saveBtn').addEventListener('click', () => this.saveImage());
                document.getElementById('newCropBtn').addEventListener('click', () => this.newCrop());
                document.getElementById('backToCropBtn').addEventListener('click', () => this.backToCrop());
                document.getElementById('resetBtn').addEventListener('click', () => this.reset());
                
                // Crop rectangle interactions
                this.cropRect.addEventListener('mousedown', (e) => this.startDrag(e));
                document.addEventListener('mousemove', (e) => this.handleMouseMove(e));
                document.addEventListener('mouseup', () => this.endDrag());
                
                // Handle crop handles
                document.querySelectorAll('.crop-handle').forEach(handle => {
                    handle.addEventListener('mousedown', (e) => this.startResize(e));
                });
            }
            
            handleImageLoad(event) {
                const file = event.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        this.currentImage = e.target.result;
                        this.displayImage();
                    };
                    reader.readAsDataURL(file);
                }
            }
            
            displayImage() {
                this.imagePreview.src = this.currentImage;
                this.imagePreview.style.display = 'block';
                this.previewText.style.display = 'none';
                this.cropToggleBtn.style.display = 'inline-block';
                this.croppedResult.style.display = 'none';
                this.resultActions.style.display = 'none';
                
                this.imagePreview.onload = () => {
                    this.initializeCropRect();
                };
            }
            
            toggleCropMode() {
                this.cropMode = !this.cropMode;
                
                if (this.cropMode) {
                    this.cropToggleBtn.innerHTML = '<i class="fas fa-times"></i> إلغاء القص';
                    this.cropToggleBtn.classList.add('active');
                    this.cropControls.classList.add('active');
                    this.showCropInterface();
                } else {
                    this.cropToggleBtn.innerHTML = '<i class="fas fa-crop-alt"></i> تفعيل أداة القص';
                    this.cropToggleBtn.classList.remove('active');
                    this.cropControls.classList.remove('active');
                    this.hideCropInterface();
                }
            }
            
            showCropInterface() {
                this.cropOverlay.style.display = 'block';
                this.cropRect.style.display = 'block';
                this.initializeCropRect();
            }
            
            hideCropInterface() {
                this.cropOverlay.style.display = 'none';
                this.cropRect.style.display = 'none';
            }
            
            initializeCropRect() {
                const imgRect = this.imagePreview.getBoundingClientRect();
                const containerRect = this.imagePreview.parentElement.getBoundingClientRect();
                
                // Set crop rectangle to center of image
                const width = Math.min(200, imgRect.width * 0.6);
                const height = Math.min(200, imgRect.height * 0.6);
                const x = (imgRect.width - width) / 2;
                const y = (imgRect.height - height) / 2;
                
                this.cropData = { x, y, width, height };
                this.updateCropRect();
                this.updateCurrentRatio();
            }
            
            updateCropRect() {
                const imgRect = this.imagePreview.getBoundingClientRect();
                const containerRect = this.imagePreview.parentElement.getBoundingClientRect();
                
                const scaleX = imgRect.width / this.imagePreview.naturalWidth;
                const scaleY = imgRect.height / this.imagePreview.naturalHeight;
                
                this.cropRect.style.left = (imgRect.left - containerRect.left + this.cropData.x * scaleX) + 'px';
                this.cropRect.style.top = (imgRect.top - containerRect.top + this.cropData.y * scaleY) + 'px';
                this.cropRect.style.width = (this.cropData.width * scaleX) + 'px';
                this.cropRect.style.height = (this.cropData.height * scaleY) + 'px';
            }
            
            selectRatio(ratio) {
                document.querySelectorAll('.crop-btn').forEach(btn => btn.classList.remove('active'));
                document.querySelector(`[data-ratio="${ratio}"]`).classList.add('active');
                
                this.selectedRatio = ratio;
                if (ratio !== 'custom') {
                    this.applyRatio();
                }
                this.updateCurrentRatio();
            }
            
            applyRatio() {
                if (this.selectedRatio === 'custom') return;
                
                const [w, h] = this.selectedRatio.split(':').map(Number);
                const aspectRatio = w / h;
                
                const imgRect = this.imagePreview.getBoundingClientRect();
                const maxWidth = this.imagePreview.naturalWidth * 0.8;
                const maxHeight = this.imagePreview.naturalHeight * 0.8;
                
                let newWidth, newHeight;
                
                if (maxWidth / maxHeight > aspectRatio) {
                    newHeight = maxHeight;
                    newWidth = newHeight * aspectRatio;
                } else {
                    newWidth = maxWidth;
                    newHeight = newWidth / aspectRatio;
                }
                
                this.cropData.width = newWidth;
                this.cropData.height = newHeight;
                this.cropData.x = (this.imagePreview.naturalWidth - newWidth) / 2;
                this.cropData.y = (this.imagePreview.naturalHeight - newHeight) / 2;
                
                this.updateCropRect();
            }
            
            updateCurrentRatio() {
                if (this.selectedRatio === 'custom') {
                    const ratio = (this.cropData.width / this.cropData.height).toFixed(2);
                    this.currentRatio.querySelector('.ratio-indicator').textContent = `${ratio}:1`;
                } else {
                    this.currentRatio.querySelector('.ratio-indicator').textContent = this.selectedRatio;
                }
            }
            
            rotateRatio() {
                if (this.selectedRatio === 'custom') return;
                
                const [w, h] = this.selectedRatio.split(':').map(Number);
                this.selectedRatio = `${h}:${w}`;
                this.applyRatio();
                this.updateCurrentRatio();
            }
            
            rotateImage() {
                this.imageRotation = (this.imageRotation + 90) % 360;
                this.imagePreview.style.transform = `rotate(${this.imageRotation}deg)`;
            }
            
            startDrag(e) {
                if (e.target.classList.contains('crop-handle')) return;
                
                this.isDragging = true;
                this.dragStart.x = e.clientX;
                this.dragStart.y = e.clientY;
                e.preventDefault();
            }
            
            startResize(e) {
                this.isResizing = true;
                this.resizeHandle = e.target.classList[1]; // nw, ne, sw, se
                this.dragStart.x = e.clientX;
                this.dragStart.y = e.clientY;
                e.preventDefault();
                e.stopPropagation();
            }
            
            handleMouseMove(e) {
                if (this.isDragging) {
                    const deltaX = e.clientX - this.dragStart.x;
                    const deltaY = e.clientY - this.dragStart.y;
                    
                    const imgRect = this.imagePreview.getBoundingClientRect();
                    const containerRect = this.imagePreview.parentElement.getBoundingClientRect();
                    const scaleX = this.imagePreview.naturalWidth / imgRect.width;
                    const scaleY = this.imagePreview.naturalHeight / imgRect.height;
                    
                    this.cropData.x += deltaX * scaleX;
                    this.cropData.y += deltaY * scaleY;
                    
                    // Keep within bounds
                    this.cropData.x = Math.max(0, Math.min(this.cropData.x, this.imagePreview.naturalWidth - this.cropData.width));
                    this.cropData.y = Math.max(0, Math.min(this.cropData.y, this.imagePreview.naturalHeight - this.cropData.height));
                    
                    this.updateCropRect();
                    this.dragStart.x = e.clientX;
                    this.dragStart.y = e.clientY;
                }
                
                if (this.isResizing) {
                    const deltaX = e.clientX - this.dragStart.x;
                    const deltaY = e.clientY - this.dragStart.y;
                    
                    const imgRect = this.imagePreview.getBoundingClientRect();
                    const scaleX = this.imagePreview.naturalWidth / imgRect.width;
                    const scaleY = this.imagePreview.naturalHeight / imgRect.height;
                    
                    const scaledDeltaX = deltaX * scaleX;
                    const scaledDeltaY = deltaY * scaleY;
                    
                    const originalData = { ...this.cropData };
                    const aspectRatio = this.selectedRatio !== 'custom' ? 
                        this.selectedRatio.split(':').map(Number).reduce((a, b) => a / b) : null;
                    
                    switch (this.resizeHandle) {
                        case 'nw':
                            this.cropData.x += scaledDeltaX;
                            this.cropData.y += scaledDeltaY;
                            this.cropData.width -= scaledDeltaX;
                            this.cropData.height -= scaledDeltaY;
                            if (aspectRatio) {
                                const newHeight = this.cropData.width / aspectRatio;
                                this.cropData.y = originalData.y + originalData.height - newHeight;
                                this.cropData.height = newHeight;
                            }
                            break;
                        case 'ne':
                            this.cropData.y += scaledDeltaY;
                            this.cropData.width += scaledDeltaX;
                            this.cropData.height -= scaledDeltaY;
                            if (aspectRatio) {
                                const newHeight = this.cropData.width / aspectRatio;
                                this.cropData.y = originalData.y + originalData.height - newHeight;
                                this.cropData.height = newHeight;
                            }
                            break;
                        case 'sw':
                            this.cropData.x += scaledDeltaX;
                            this.cropData.width -= scaledDeltaX;
                            this.cropData.height += scaledDeltaY;
                            if (aspectRatio) {
                                this.cropData.height = this.cropData.width / aspectRatio;
                            }
                            break;
                        case 'se':
                            this.cropData.width += scaledDeltaX;
                            this.cropData.height += scaledDeltaY;
                            if (aspectRatio) {
                                this.cropData.height = this.cropData.width / aspectRatio;
                            }
                            break;
                        case 'n':
                            this.cropData.y += scaledDeltaY;
                            this.cropData.height -= scaledDeltaY;
                            if (aspectRatio) {
                                const newWidth = this.cropData.height * aspectRatio;
                                this.cropData.x = originalData.x + (originalData.width - newWidth) / 2;
                                this.cropData.width = newWidth;
                            }
                            break;
                        case 's':
                            this.cropData.height += scaledDeltaY;
                            if (aspectRatio) {
                                const newWidth = this.cropData.height * aspectRatio;
                                this.cropData.x = originalData.x + (originalData.width - newWidth) / 2;
                                this.cropData.width = newWidth;
                            }
                            break;
                        case 'e':
                            this.cropData.width += scaledDeltaX;
                            if (aspectRatio) {
                                const newHeight = this.cropData.width / aspectRatio;
                                this.cropData.y = originalData.y + (originalData.height - newHeight) / 2;
                                this.cropData.height = newHeight;
                            }
                            break;
                        case 'w':
                            this.cropData.x += scaledDeltaX;
                            this.cropData.width -= scaledDeltaX;
                            if (aspectRatio) {
                                const newHeight = this.cropData.width / aspectRatio;
                                this.cropData.y = originalData.y + (originalData.height - newHeight) / 2;
                                this.cropData.height = newHeight;
                            }
                            break;
                    }
                    
                    // Ensure minimum size
                    this.cropData.width = Math.max(50, this.cropData.width);
                    this.cropData.height = Math.max(50, this.cropData.height);
                    
                    // Keep within image bounds
                    this.cropData.x = Math.max(0, Math.min(this.cropData.x, this.imagePreview.naturalWidth - this.cropData.width));
                    this.cropData.y = Math.max(0, Math.min(this.cropData.y, this.imagePreview.naturalHeight - this.cropData.height));
                    
                    // Adjust size if it goes out of bounds
                    if (this.cropData.x + this.cropData.width > this.imagePreview.naturalWidth) {
                        this.cropData.width = this.imagePreview.naturalWidth - this.cropData.x;
                    }
                    if (this.cropData.y + this.cropData.height > this.imagePreview.naturalHeight) {
                        this.cropData.height = this.imagePreview.naturalHeight - this.cropData.y;
                    }
                    
                    this.updateCropRect();
                    this.dragStart.x = e.clientX;
                    this.dragStart.y = e.clientY;
                }
            }
            
            endDrag() {
                this.isDragging = false;
                this.isResizing = false;
                this.resizeHandle = null;
                this.updateCurrentRatio();
            }
            
            applyCrop() {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                
                canvas.width = this.cropData.width;
                canvas.height = this.cropData.height;
                
                const img = new Image();
                img.onload = () => {
                    ctx.drawImage(img, 
                        this.cropData.x, this.cropData.y, this.cropData.width, this.cropData.height,
                        0, 0, this.cropData.width, this.cropData.height
                    );
                    
                    this.croppedResult.src = canvas.toDataURL('image/png');
                    this.croppedResult.style.display = 'block';
                    this.imagePreview.style.display = 'none';
                    this.hideCropInterface();
                    this.cropToggleBtn.style.display = 'none';
                    this.cropControls.classList.remove('active');
                    this.resultActions.style.display = 'block';
                };
                img.src = this.currentImage;
            }
            
            saveImage() {
                const link = document.createElement('a');
                link.download = 'cropped-image.png';
                link.href = this.croppedResult.src;
                link.click();
            }
            
            newCrop() {
                this.fileInputContainer.style.display = 'block';
                this.croppedResult.style.display = 'none';
                this.resultActions.style.display = 'none';
                this.previewText.style.display = 'block';
                this.cropToggleBtn.style.display = 'none';
                this.currentImage = null;
            }
            
            backToCrop() {
                this.imagePreview.style.display = 'block';
                this.croppedResult.style.display = 'none';
                this.cropToggleBtn.style.display = 'inline-block';
                this.resultActions.style.display = 'none';
                this.toggleCropMode();
            }
            
            reset() {
                location.reload();
            }
        }
        
        // Initialize the image cropper when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            new ImageCropper();
        });
    </script>
</body>
</html>