<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محرر متكامل للصور والرسم مع قص متقدم</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #2a4365;
            --secondary: #4299e1;
            --accent: #48bb78;
            --light: #ebf8ff;
            --dark: #1a202c;
            --crop-primary: #e53e3e;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
            color: var(--dark);
            line-height: 1.6;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        header {
            background: var(--primary);
            color: white;
            padding: 20px;
            text-align: center;
            border-bottom: 4px solid var(--secondary);
        }
        
        h1 {
            font-size: 2.2rem;
            margin-bottom: 10px;
        }
        
        .main-toolbar {
            display: flex;
            background: var(--secondary);
            padding: 10px;
            justify-content: center;
            flex-wrap: wrap;
            gap: 15px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }
        
        .main-btn {
            background: white;
            color: var(--primary);
            border: none;
            padding: 12px 24px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
        }
        
        .main-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            background: var(--light);
        }
        
        .main-btn.active {
            background: var(--accent);
            color: white;
        }
        
        .sub-toolbar-container {
            background: var(--light);
            padding: 15px;
            overflow-x: auto;
            border-bottom: 2px solid #cbd5e0;
        }
        
        .sub-toolbar {
            display: flex;
            gap: 15px;
            padding: 10px;
            min-width: max-content;
        }
        
        .sub-toolbar.hidden {
            display: none;
        }
        
        .sub-btn {
            background: white;
            color: var(--dark);
            border: 1px solid #cbd5e0;
            padding: 8px 16px;
            border-radius: 30px;
            font-size: 0.95rem;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .sub-btn:hover {
            background: var(--secondary);
            color: white;
            border-color: var(--secondary);
        }
        
        .crop-btn {
            background: white;
            color: var(--crop-primary);
            border: 1px solid var(--crop-primary);
            padding: 8px 16px;
            border-radius: 30px;
            font-size: 0.95rem;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .crop-btn:hover {
            background: var(--crop-primary);
            color: white;
        }
        
        .crop-btn.primary {
            background: var(--crop-primary);
            color: white;
            border: none;
        }
        
        .crop-btn.primary:hover {
            background: #c53030;
        }
        
        .result-btn {
            background: var(--accent);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 30px;
            font-size: 0.95rem;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .result-btn:hover {
            background: #38a169;
        }
        
        .canvas-container {
            padding: 20px;
            display: flex;
            justify-content: center;
        }
        
        #c {
            border: 2px solid #5d9eab;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            background: white;
        }
        
        .instructions {
            text-align: center;
            padding: 15px;
            background: #f7fafc;
            border-top: 1px solid #e2e8f0;
            font-size: 0.9rem;
            color: #4a5568;
        }
        
        .info-label {
            font-weight: bold;
            color: var(--primary);
            margin: 0 5px;
        }
        
        .controls-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
            background: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
        }
        
        .crop-ratio-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            background: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
        }
        
        label {
            display: flex;
            align-items: center;
            gap: 10px;
            white-space: nowrap;
        }
        
        input[type="range"], input[type="color"], select {
            cursor: pointer;
        }
        
        .ratio-indicator {
            font-weight: bold;
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .main-toolbar {
                gap: 10px;
            }
            
            .main-btn {
                padding: 10px 15px;
                font-size: 0.95rem;
            }
            
            .sub-toolbar {
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .crop-ratio-group {
                justify-content: center;
            }
        }
        
        .icon {
            font-size: 1.2rem;
        }
        
        .tooltip {
            position: relative;
            display: inline-block;
        }
        
        .tooltip .tooltiptext {
            visibility: hidden;
            width: 120px;
            background-color: #555;
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 5px 0;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -60px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 0.8rem;
        }
        
        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>محرر الصور والرسم المتكامل</h1>
            <p>أداة متقدمة لتحرير الصور والرسم باستخدام Fabric.js</p>
        </header>
        
        <div class="main-toolbar">
            <button id="add-image-btn" class="main-btn active">
                <span class="icon">🖼️</span> إضافة صورة
            </button>
            <button id="drawing-mode-btn" class="main-btn">
                <span class="icon">✏️</span> أدوات الرسم
            </button>
            <button id="crop-mode-btn" class="main-btn">
                <span class="icon">✂️</span> أدوات القص
            </button>
        </div>
        
        <div class="sub-toolbar-container">
            <div id="sub-toolbar-add-image" class="sub-toolbar">
                <button id="upload-btn" class="sub-btn">تحميل صورة</button>
                <input type="file" id="file-input" multiple style="display: none;">
            </div>
            
            <div id="sub-toolbar-drawing" class="sub-toolbar hidden">
                <button id="drawing-toggle" class="sub-btn">إيقاف وضع الرسم</button>
                <button id="clear-canvas" class="sub-btn">مسح اللوحة</button>
                
                <div class="controls-group">
                    <label for="drawing-mode-selector">
                        <span class="info-label">أداة الرسم:</span>
                        <select id="drawing-mode-selector">
                            <option>قلم رصاص</option>
                            <option>دائرة</option>
                            <option>رش</option>
                            <option>نمط</option>
                            <option>خط أفقي</option>
                            <option>خط عمودي</option>
                            <option>مربع</option>
                            <option>معين</option>
                            <option>نسيج</option>
                        </select>
                    </label>
                </div>
                
                <div class="controls-group">
                    <label for="drawing-line-width">
                        <span class="info-label">سمك الخط:</span>
                        <span id="line-width-info">30</span>
                        <input type="range" min="1" max="150" id="drawing-line-width" value="30">
                    </label>
                </div>
                
                <div class="controls-group">
                    <label for="drawing-color">
                        <span class="info-label">لون الخط:</span>
                        <input type="color" id="drawing-color" value="#76cef4">
                    </label>
                </div>
                
                <div class="controls-group">
                    <label for="drawing-shadow-color">
                        <span class="info-label">لون الظل:</span>
                        <input type="color" id="drawing-shadow-color" value="#5a7896">
                    </label>
                </div>
                
                <div class="controls-group">
                    <label for="drawing-shadow-width">
                        <span class="info-label">حجم الظل:</span>
                        <span id="shadow-width-info">0</span>
                        <input type="range" min="0" max="50" id="drawing-shadow-width" value="0">
                    </label>
                </div>
                
                <div class="controls-group">
                    <label for="drawing-shadow-offset">
                        <span class="info-label">إزاحة الظل:</span>
                        <span id="shadow-offset-info">0</span>
                        <input type="range" min="0" max="50" id="drawing-shadow-offset" value="0">
                    </label>
                </div>
            </div>
            
            <div id="sub-toolbar-crop" class="sub-toolbar hidden">
                <div class="controls-group">
                    <p><span class="info-label">تعليمات القص:</span></p>
                    <p>انقر نقراً مزدوجاً على الصورة لبدء القص</p>
                    <p>حرك أو غيّر حجم منطقة القص كما تريد</p>
                    <p>ألغِ التحديد لإنهاء القص</p>
                </div>
                
                <div class="crop-ratio-group">
                    <button class="crop-btn tooltip" data-ratio="1:1">
                        <i class="fas fa-square"></i>
                        <span class="ratio-indicator">1:1</span>
                        <span class="tooltiptext">نسبة مربعة</span>
                    </button>
                    <button class="crop-btn tooltip" data-ratio="4:3">
                        <i class="fas fa-desktop"></i>
                        <span class="ratio-indicator">4:3</span>
                        <span class="tooltiptext">شاشة كمبيوتر</span>
                    </button>
                    <button class="crop-btn tooltip" data-ratio="16:9">
                        <i class="fas fa-tv"></i>
                        <span class="ratio-indicator">16:9</span>
                        <span class="tooltiptext">شاشة عريضة</span>
                    </button>
                    <button class="crop-btn tooltip" data-ratio="3:2">
                        <i class="fas fa-camera"></i>
                        <span class="ratio-indicator">3:2</span>
                        <span class="tooltiptext">كاميرا احترافية</span>
                    </button>
                    <button class="crop-btn tooltip" data-ratio="5:4">
                        <i class="fas fa-image"></i>
                        <span class="ratio-indicator">5:4</span>
                        <span class="tooltiptext">صورة كلاسيكية</span>
                    </button>
                    <button class="crop-btn tooltip" data-ratio="9:16">
                        <i class="fas fa-mobile-alt"></i>
                        <span class="ratio-indicator">9:16</span>
                        <span class="tooltiptext">هاتف محمول</span>
                    </button>
                    <button class="crop-btn tooltip" id="rotateRatioBtn">
                        <i class="fas fa-sync-alt"></i>
                        <span class="tooltiptext">تدوير النسبة</span>
                    </button>
                    <button class="crop-btn tooltip" id="rotateImageBtn">
                        <i class="fas fa-redo"></i>
                        <span class="tooltiptext">تدوير الصورة</span>
                    </button>
                    <button class="crop-btn primary tooltip" id="applyCropBtn">
                        <i class="fas fa-check"></i>
                        <span class="tooltiptext">تطبيق القص</span>
                    </button>
                    <button id="saveBtn" class="result-btn tooltip">
                        <i class="fas fa-download"></i>
                        <span class="tooltiptext">حفظ النتيجة</span>
                    </button>
                    <button id="newCropBtn" class="crop-btn tooltip">
                        <i class="fas fa-crop-alt"></i>
                        <span class="tooltiptext">قص جديد</span>
                    </button>
                    <button id="backToCropBtn" class="crop-btn tooltip">
                        <i class="fas fa-undo"></i>
                        <span class="tooltiptext">العودة للقص</span>
                    </button>
                    <button id="resetBtn" class="crop-btn tooltip">
                        <i class="fas fa-redo"></i>
                        <span class="tooltiptext">إعادة الضبط</span>
                    </button>
                </div>
            </div>
        </div>
        
        <div class="canvas-container">
            <canvas id="c" width="800" height="500"></canvas>
        </div>
        
        <div class="instructions">
            <p>استخدم شريط الأدوات العلوي لاختيار الوظيفة المطلوبة، ثم استخدم الأدوات الفرعية للتحكم</p>
        </div>
    </div>

    <script>
        // تهيئة لوحة الرسم
        const canvas = new fabric.Canvas('c', {
            isDrawingMode: false,
            selection: true
        });
        canvas.background = '#FFFFFF';
        canvas.renderAll();
        fabric.Object.prototype.transparentCorners = false;
        
        // عناصر DOM
        const $ = id => document.getElementById(id);
        
        // الأزرار الرئيسية
        const addImageBtn = $('add-image-btn');
        const drawingModeBtn = $('drawing-mode-btn');
        const cropModeBtn = $('crop-mode-btn');
        
        // الأشرطة الفرعية
        const addImageToolbar = $('sub-toolbar-add-image');
        const drawingToolbar = $('sub-toolbar-drawing');
        const cropToolbar = $('sub-toolbar-crop');
        
        // عناصر أدوات الرسم
        const drawingToggle = $('drawing-toggle');
        const clearCanvas = $('clear-canvas');
        const drawingColorEl = $('drawing-color');
        const drawingShadowColorEl = $('drawing-shadow-color');
        const drawingLineWidthEl = $('drawing-line-width');
        const drawingShadowWidth = $('drawing-shadow-width');
        const drawingShadowOffset = $('drawing-shadow-offset');
        const selectorEl = $('drawing-mode-selector');
        
        // متغيرات قص الصور
        let currentCropRect = null;
        let currentCropImage = null;
        let currentOverlay = null;
        let currentRatio = null;
        
        // إظهار/إخفاء الأشرطة الفرعية
        function showToolbar(toolbarToShow) {
            // إخفاء جميع الأشرطة الفرعية
            addImageToolbar.classList.add('hidden');
            drawingToolbar.classList.add('hidden');
            cropToolbar.classList.add('hidden');
            
            // إزالة النشاط من جميع الأزرار الرئيسية
            addImageBtn.classList.remove('active');
            drawingModeBtn.classList.remove('active');
            cropModeBtn.classList.remove('active');
            
            // إظهار الشريط المطلوب وإضافة النشاط للزر الرئيسي
            toolbarToShow.classList.remove('hidden');
            
            if (toolbarToShow === addImageToolbar) {
                addImageBtn.classList.add('active');
            } else if (toolbarToShow === drawingToolbar) {
                drawingModeBtn.classList.add('active');
            } else if (toolbarToShow === cropToolbar) {
                cropModeBtn.classList.add('active');
            }
        }
        
        // أحداث الأزرار الرئيسية
        addImageBtn.addEventListener('click', () => {
            showToolbar(addImageToolbar);
            canvas.isDrawingMode = false;
            drawingToggle.textContent = 'تفعيل وضع الرسم';
        });
        
        drawingModeBtn.addEventListener('click', () => {
            showToolbar(drawingToolbar);
            canvas.isDrawingMode = true;
            drawingToggle.textContent = 'إيقاف وضع الرسم';
        });
        
        cropModeBtn.addEventListener('click', () => {
            showToolbar(cropToolbar);
            canvas.isDrawingMode = false;
            drawingToggle.textContent = 'تفعيل وضع الرسم';
        });
        
        // تحميل الصور
        $('upload-btn').addEventListener('click', () => {
            $('file-input').click();
        });
        
        $('file-input').addEventListener('change', function(e) {
            const files = e.target.files;
            for (let i = 0; i < files.length; i++) {
                const reader = new FileReader();
                reader.onload = function(f) {
                    const data = f.target.result;
                    fabric.Image.fromURL(data, function(img) {
                        const canvasWidth = canvas.getWidth();
                        const canvasHeight = canvas.getHeight();
                        
                        const scaleFactor = Math.min(
                            canvasWidth / img.width,
                            canvasHeight / img.height
                        );
                        
                        img.scale(scaleFactor);
                        img.set({
                            left: (canvasWidth - img.getScaledWidth()) / 2,
                            top: (canvasHeight - img.getScaledHeight()) / 2,
                            selectable: true
                        });
                        
                        canvas.add(img);
                        canvas.renderAll();
                    });
                };
                reader.readAsDataURL(files[i]);
            }
        });
        
        // وظائف قص الصور
        canvas.on('mouse:dblclick', function(obj) {
            const target = obj.target ? obj.target : null;
            if (target && target.type === 'image') {
                prepareCrop(target);
            }
        });
        
        function prepareCrop(img) {
            // إزالة أي عملية قص سابقة
            if (currentCropRect) {
                canvas.remove(currentCropRect);
                canvas.remove(currentOverlay);
            }
            
            currentCropImage = img;
            
            const cropRect = new fabric.Rect({
                id: "crop-rect",
                top: img.top,
                left: img.left,
                angle: img.angle,
                width: img.getScaledWidth(),
                height: img.getScaledHeight(),
                stroke: "rgb(42, 67, 101)",
                strokeWidth: 2,
                strokeDashArray: [5, 5],
                fill: "rgba(255, 255, 255, 0.3)",
                globalCompositeOperation: "overlay",
                lockRotation: true,
                hasControls: true,
                lockUniScaling: false
            });
            
            const overlayRect = new fabric.Rect({
                id: "overlay-rect",
                top: img.top,
                left: img.left,
                angle: img.angle,
                width: img.getScaledWidth(),
                height: img.getScaledHeight(),
                selectable: false,
                fill: "rgba(0, 0, 0, 0.5)",
                lockRotation: true,
                evented: false
            });
            
            const s = img.cropX || 0,
                o = img.cropY || 0,
                c = img.width || img._originalElement.naturalWidth,
                l = img.height || img._originalElement.naturalHeight;
            
            img.set({
                cropX: null,
                cropY: null,
                left: img.left - s * img.scaleX,
                top: img.top - o * img.scaleY,
                width: img._originalElement.naturalWidth,
                height: img._originalElement.naturalHeight,
                dirty: true,
                selectable: false
            });
            
            cropRect.set({
                left: img.left + s * img.scaleX,
                top: img.top + o * img.scaleY,
                width: c * img.scaleX,
                height: l * img.scaleY,
                dirty: true
            });
            
            overlayRect.set({
                left: img.left,
                top: img.top,
                width: img.width * img.scaleX,
                height: img.height * img.scaleY,
                dirty: true
            });
            
            canvas.add(overlayRect);
            canvas.add(cropRect);
            canvas.discardActiveObject();
            canvas.setActiveObject(cropRect);
            canvas.renderAll();
            
            cropRect.on("moving", function() {
                if (cropRect.top < img.top || cropRect.left < img.left) {
                    cropRect.left = cropRect.left < img.left ? img.left : cropRect.left;
                    cropRect.top = cropRect.top < img.top ? img.top : cropRect.top;
                }
                
                if (cropRect.top + cropRect.getScaledHeight() > img.top + img.getScaledHeight() ||
                    cropRect.left + cropRect.getScaledWidth() > img.left + img.getScaledWidth()) {
                    cropRect.top = cropRect.top + cropRect.getScaledHeight() > img.top + img.getScaledHeight() ?
                        img.top + img.getScaledHeight() - cropRect.getScaledHeight() : cropRect.top;
                    cropRect.left = cropRect.left + cropRect.getScaledWidth() > img.left + img.getScaledWidth() ?
                        img.left + img.getScaledWidth() - cropRect.getScaledWidth() : cropRect.left;
                }
            });
            
            cropRect.on("deselected", function() {
                cropImage(cropRect, img);
            });
            
            currentCropRect = cropRect;
            currentOverlay = overlayRect;
        }
        
        function cropImage(cropRect, img) {
            canvas.remove(cropRect);
            canvas.remove(currentOverlay);
            
            const s = (cropRect.left - img.left) / img.scaleX,
                o = (cropRect.top - img.top) / img.scaleY,
                c = (cropRect.width * cropRect.scaleX) / img.scaleX,
                l = (cropRect.height * cropRect.scaleY) / img.scaleY;
            
            img.set({
                cropX: s,
                cropY: o,
                width: c,
                height: l,
                top: img.top + o * img.scaleY,
                left: img.left + s * img.scaleX,
                selectable: true,
                cropped: true
            });
            
            canvas.renderAll();
            currentCropRect = null;
            currentOverlay = null;
        }
        
        // وظائف أدوات الرسم
        function updateBrush() {
            const brush = canvas.freeDrawingBrush;
            brush.color = drawingColorEl.value;
            brush.width = parseInt(drawingLineWidthEl.value, 10) || 1;
            brush.shadow = new fabric.Shadow({
                blur: parseInt(drawingShadowWidth.value, 10) || 0,
                offsetX: parseInt(drawingShadowOffset.value, 10) || 0,
                offsetY: parseInt(drawingShadowOffset.value, 10) || 0,
                color: drawingShadowColorEl.value,
            });
        }
        
        drawingToggle.onclick = () => {
            canvas.isDrawingMode = !canvas.isDrawingMode;
            drawingToggle.textContent = canvas.isDrawingMode ? 
                'إيقاف وضع الرسم' : 'تفعيل وضع الرسم';
        };
        
        clearCanvas.onclick = () => canvas.clear();
        
        drawingColorEl.onchange = updateBrush;
        drawingShadowColorEl.onchange = updateBrush;
        
        drawingLineWidthEl.oninput = () => {
            $('line-width-info').textContent = drawingLineWidthEl.value;
            updateBrush();
        };
        
        drawingShadowWidth.oninput = () => {
            $('shadow-width-info').textContent = drawingShadowWidth.value;
            updateBrush();
        };
        
        drawingShadowOffset.oninput = () => {
            $('shadow-offset-info').textContent = drawingShadowOffset.value;
            updateBrush();
        };
        
        selectorEl.onchange = () => {
            const val = selectorEl.value;
            if (val === 'خط أفقي') {
                const brush = new fabric.PatternBrush(canvas);
                brush.getPatternSrc = () => {
                    const patternCanvas = document.createElement('canvas');
                    patternCanvas.width = patternCanvas.height = 10;
                    const ctx = patternCanvas.getContext('2d');
                    ctx.strokeStyle = brush.color;
                    ctx.lineWidth = 5;
                    ctx.beginPath();
                    ctx.moveTo(0, 5);
                    ctx.lineTo(10, 5);
                    ctx.stroke();
                    return patternCanvas;
                };
                canvas.freeDrawingBrush = brush;
            } else if (val === 'خط عمودي') {
                const brush = new fabric.PatternBrush(canvas);
                brush.getPatternSrc = () => {
                    const patternCanvas = document.createElement('canvas');
                    patternCanvas.width = patternCanvas.height = 10;
                    const ctx = patternCanvas.getContext('2d');
                    ctx.strokeStyle = brush.color;
                    ctx.lineWidth = 5;
                    ctx.beginPath();
                    ctx.moveTo(5, 0);
                    ctx.lineTo(5, 10);
                    ctx.stroke();
                    return patternCanvas;
                };
                canvas.freeDrawingBrush = brush;
            } else {
                let brushClass;
                switch(val) {
                    case 'قلم رصاص': brushClass = fabric.PencilBrush; break;
                    case 'دائرة': brushClass = fabric.CircleBrush; break;
                    case 'رش': brushClass = fabric.SprayBrush; break;
                    case 'نمط': brushClass = fabric.PatternBrush; break;
                    case 'مربع': brushClass = fabric.SquareBrush; break;
                    case 'معين': brushClass = fabric.DiamondBrush; break;
                    case 'نسيج': brushClass = fabric.TextureBrush; break;
                    default: brushClass = fabric.PencilBrush;
                }
                canvas.freeDrawingBrush = new brushClass(canvas);
            }
            updateBrush();
        };
        
        // وظائف قص متقدمة
        document.querySelectorAll('.crop-btn[data-ratio]').forEach(btn => {
            btn.addEventListener('click', function() {
                if (!currentCropImage || !currentCropRect) return;
                
                const ratio = this.getAttribute('data-ratio').split(':');
                const ratioW = parseInt(ratio[0]);
                const ratioH = parseInt(ratio[1]);
                
                currentRatio = {w: ratioW, h: ratioH};
                
                const imgWidth = currentCropImage.getScaledWidth();
                const imgHeight = currentCropImage.getScaledHeight();
                
                let newWidth, newHeight;
                
                if (imgWidth / imgHeight > ratioW / ratioH) {
                    newHeight = imgHeight;
                    newWidth = (ratioW / ratioH) * newHeight;
                } else {
                    newWidth = imgWidth;
                    newHeight = (ratioH / ratioW) * newWidth;
                }
                
                if (newWidth > imgWidth) {
                    newWidth = imgWidth;
                    newHeight = (ratioH / ratioW) * newWidth;
                }
                
                if (newHeight > imgHeight) {
                    newHeight = imgHeight;
                    newWidth = (ratioW / ratioH) * newHeight;
                }
                
                currentCropRect.set({
                    width: newWidth,
                    height: newHeight,
                    left: currentCropImage.left + (imgWidth - newWidth) / 2,
                    top: currentCropImage.top + (imgHeight - newHeight) / 2
                });
                
                canvas.renderAll();
            });
        });
        
        $('rotateRatioBtn').addEventListener('click', function() {
            if (!currentRatio) return;
            
            // تدوير النسبة (تبديل الطول والعرض)
            const temp = currentRatio.w;
            currentRatio.w = currentRatio.h;
            currentRatio.h = temp;
            
            // إعادة تطبيق النسبة الجديدة
            if (currentCropImage && currentCropRect) {
                const imgWidth = currentCropImage.getScaledWidth();
                const imgHeight = currentCropImage.getScaledHeight();
                
                let newWidth, newHeight;
                
                if (imgWidth / imgHeight > currentRatio.w / currentRatio.h) {
                    newHeight = imgHeight;
                    newWidth = (currentRatio.w / currentRatio.h) * newHeight;
                } else {
                    newWidth = imgWidth;
                    newHeight = (currentRatio.h / currentRatio.w) * newWidth;
                }
                
                if (newWidth > imgWidth) {
                    newWidth = imgWidth;
                    newHeight = (currentRatio.h / currentRatio.w) * newWidth;
                }
                
                if (newHeight > imgHeight) {
                    newHeight = imgHeight;
                    newWidth = (currentRatio.w / currentRatio.h) * newHeight;
                }
                
                currentCropRect.set({
                    width: newWidth,
                    height: newHeight,
                    left: currentCropImage.left + (imgWidth - newWidth) / 2,
                    top: currentCropImage.top + (imgHeight - newHeight) / 2
                });
                
                canvas.renderAll();
            }
        });
        
        $('rotateImageBtn').addEventListener('click', function() {
            if (currentCropImage) {
                currentCropImage.rotate(currentCropImage.angle + 90);
                canvas.renderAll();
            }
        });
        
        $('applyCropBtn').addEventListener('click', function() {
            if (currentCropRect && currentCropImage) {
                cropImage(currentCropRect, currentCropImage);
            }
        });
        
        $('saveBtn').addEventListener('click', function() {
            // إنشاء رابط تنزيل للصورة
            const dataURL = canvas.toDataURL({
                format: 'png',
                quality: 1.0
            });
            
            const link = document.createElement('a');
            link.download = 'صورة-معدلة.png';
            link.href = dataURL;
            link.click();
        });
        
        $('newCropBtn').addEventListener('click', function() {
            if (currentCropImage) {
                prepareCrop(currentCropImage);
            }
        });
        
        $('backToCropBtn').addEventListener('click', function() {
            if (currentCropImage) {
                prepareCrop(currentCropImage);
            }
        });
        
        $('resetBtn').addEventListener('click', function() {
            if (currentCropImage) {
                // إعادة الصورة إلى حالتها الأصلية
                currentCropImage.set({
                    cropX: null,
                    cropY: null,
                    width: currentCropImage._originalElement.naturalWidth,
                    height: currentCropImage._originalElement.naturalHeight,
                    top: (canvas.getHeight() - currentCropImage.getScaledHeight()) / 2,
                    left: (canvas.getWidth() - currentCropImage.getScaledWidth()) / 2,
                    selectable: true,
                    cropped: false
                });
                
                if (currentCropRect) {
                    canvas.remove(currentCropRect);
                    currentCropRect = null;
                }
                
                if (currentOverlay) {
                    canvas.remove(currentOverlay);
                    currentOverlay = null;
                }
                
                canvas.renderAll();
            }
        });
        
        // تهيئة الفرشاة
        canvas.freeDrawingBrush = new fabric.PencilBrush(canvas);
        updateBrush();
        
        // إظهار شريط إضافة الصور افتراضيًا
        showToolbar(addImageToolbar);
    </script>
</body>
</html>