"use client"

import { Checkbox } from "@/components/ui/checkbox"
import { Button } from "@/components/ui/button"
import { <PERSON>lider } from "@/components/ui/slider"

interface LeftSidebarProps {
  showGrid: boolean
  setShowGrid: (value: boolean) => void
  verticalLightDirection: number
  setVerticalLightDirection: (value: number) => void
  tableLightIntensity: number
  setTableLightIntensity: (value: number) => void
  fieldOfView: number
  setFieldOfView: (value: number) => void
  metalness: number
  setMetalness: (value: number) => void
  roughness: number
  setRoughness: (value: number) => void
  environmentReflection: number
  setEnvironmentReflection: (value: number) => void
  modelHorizontalPosition: number
  setModelHorizontalPosition: (value: number) => void
  modelVerticalPosition: number
  setModelVerticalPosition: (value: number) => void
  modelPosition: number
  setModelPosition: (value: number) => void
  modelColor: string
  setModelColor: (value: string) => void
}

export function LeftSidebar({
  showGrid,
  setShowGrid,
  verticalLightDirection,
  setVerticalLightDirection,
  tableLightIntensity,
  setTableLightIntensity,
  fieldOfView,
  setFieldOfView,
  metalness,
  setMetalness,
  roughness,
  setRoughness,
  environmentReflection,
  setEnvironmentReflection,
  modelHorizontalPosition,
  setModelHorizontalPosition,
  modelVerticalPosition,
  setModelVerticalPosition,
  modelPosition,
  setModelPosition,
  modelColor,
  setModelColor,
}: LeftSidebarProps) {
  return (
    <div className="flex w-[200px] flex-col gap-2 overflow-y-auto bg-[#182949] p-2">
      <div className="flex items-center gap-2 rounded-md border border-[#D6A25E] bg-[#1F2A45] p-3">
        <Checkbox id="grid-toggle" checked={showGrid} onCheckedChange={(checked) => setShowGrid(checked as boolean)} />
        <label htmlFor="grid-toggle" className="text-[#D6A25E]">
          Ground Grid
        </label>
      </div>

      <Button
        variant="outline"
        className="border-[#D6A25E] bg-[#1F2A45] text-[#D6A25E] hover:bg-[#D6A25E] hover:text-[#1F2A45]"
      >
        Ground
      </Button>

      <div className="rounded-md border border-[#D6A25E] bg-[#1F2A45] p-3">
        <div className="mb-2 text-center text-[#D6A25E]">Vertical Light Direction</div>
        <Slider
          value={[verticalLightDirection]}
          onValueChange={(value) => setVerticalLightDirection(value[0])}
          max={100}
          step={1}
          className="my-4"
        />
      </div>

      <div className="rounded-md border border-[#D6A25E] bg-[#1F2A45] p-3">
        <div className="mb-2 text-center text-[#D6A25E]">Table Light Intensity</div>
        <Slider
          value={[tableLightIntensity]}
          onValueChange={(value) => setTableLightIntensity(value[0])}
          max={100}
          step={1}
          className="my-4"
        />
      </div>

      <div className="rounded-md border border-[#D6A25E] bg-[#1F2A45] p-3">
        <div className="mb-2 text-center text-[#D6A25E]">Field of View</div>
        <Slider
          value={[fieldOfView]}
          onValueChange={(value) => setFieldOfView(value[0])}
          max={100}
          step={1}
          className="my-4"
        />
      </div>

      <div className="flex items-center justify-between rounded-md border border-[#D6A25E] bg-[#1F2A45] p-3">
        <div className="text-[#D6A25E]">Model Color</div>
        <div className="flex h-8 w-8 items-center justify-center rounded-md border border-[#D6A25E] bg-[#1F2A45]">
          <input
            type="color"
            value={modelColor}
            onChange={(e) => setModelColor(e.target.value)}
            className="h-6 w-6 cursor-pointer"
          />
        </div>
      </div>

      <div className="rounded-md border border-[#D6A25E] bg-[#1F2A45] p-3">
        <div className="mb-2 text-center text-[#D6A25E]">Metalness</div>
        <Slider
          value={[metalness]}
          onValueChange={(value) => setMetalness(value[0])}
          max={100}
          step={1}
          className="my-4"
        />
      </div>

      <div className="rounded-md border border-[#D6A25E] bg-[#1F2A45] p-3">
        <div className="mb-2 text-center text-[#D6A25E]">Roughness</div>
        <Slider
          value={[roughness]}
          onValueChange={(value) => setRoughness(value[0])}
          max={100}
          step={1}
          className="my-4"
        />
      </div>

      <div className="rounded-md border border-[#D6A25E] bg-[#1F2A45] p-3">
        <div className="mb-2 text-center text-[#D6A25E]">Environment Reflection Intensity</div>
        <Slider
          value={[environmentReflection]}
          onValueChange={(value) => setEnvironmentReflection(value[0])}
          max={100}
          step={1}
          className="my-4"
        />
      </div>

      <div className="rounded-md border border-[#D6A25E] bg-[#1F2A45] p-3">
        <div className="mb-2 text-center text-[#D6A25E]">Model Horizontal Position</div>
        <Slider
          value={[modelHorizontalPosition]}
          onValueChange={(value) => setModelHorizontalPosition(value[0])}
          max={100}
          step={1}
          className="my-4"
        />
      </div>

      <div className="rounded-md border border-[#D6A25E] bg-[#1F2A45] p-3">
        <div className="mb-2 text-center text-[#D6A25E]">Model Vertical Position</div>
        <Slider
          value={[modelVerticalPosition]}
          onValueChange={(value) => setModelVerticalPosition(value[0])}
          max={100}
          step={1}
          className="my-4"
        />
      </div>

      <div className="rounded-md border border-[#D6A25E] bg-[#1F2A45] p-3">
        <div className="mb-2 text-center text-[#D6A25E]">Model Position</div>
        <Slider
          value={[modelPosition]}
          onValueChange={(value) => setModelPosition(value[0])}
          max={100}
          step={1}
          className="my-4"
        />
      </div>
    </div>
  )
}
