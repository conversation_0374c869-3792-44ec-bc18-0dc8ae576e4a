// src/components/dashboard/UserDashboard.jsx
// [!] هذا المكون يعرض لوحة تحكم المستخدم مع معلومات الحساب والاشتراك
// [!] يجب تعديل متغيرات الاتصال بالخادم حسب إعدادات الموقع الخاص بك

import React, { useState, useEffect } from 'react';

const UserDashboard = ({ user }) => {
  const [subscription, setSubscription] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [cancelDialogOpen, setCancelDialogOpen] = useState(false);

  // جلب بيانات الاشتراك من الخادم
  useEffect(() => {
    const fetchSubscription = async () => {
      try {
        setLoading(true);
        
        // [!] في بيئة الإنتاج، يجب استبدال هذا بطلب API حقيقي
        // const response = await fetch('/api/user/subscription');
        // const data = await response.json();
        
        // للاختبار، نستخدم بيانات وهمية
        const mockSubscription = {
          id: 1,
          status: 'active',
          plan: {
            id: 2,
            name: 'الخطة السنوية',
            price: 99.99,
            duration_days: 365,
            features: ['ميزة 1', 'ميزة 2', 'ميزة 3', 'ميزة 4', 'دعم فني مميز']
          },
          start_date: '2025-01-15T00:00:00.000Z',
          end_date: '2026-01-15T00:00:00.000Z',
          paypal_subscription_id: 'I-ABCDEFGHIJK',
          auto_renew: true
        };
        
        // تأخير وهمي لمحاكاة طلب الشبكة
        setTimeout(() => {
          setSubscription(mockSubscription);
          setLoading(false);
        }, 1000);
        
      } catch (err) {
        console.error('Error fetching subscription:', err);
        setError('حدث خطأ أثناء جلب بيانات الاشتراك. يرجى المحاولة مرة أخرى لاحقًا.');
        setLoading(false);
      }
    };

    if (user) {
      fetchSubscription();
    }
  }, [user]);

  // تنسيق التاريخ
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('ar-SA', options);
  };

  // حساب الأيام المتبقية
  const calculateRemainingDays = (endDate) => {
    const end = new Date(endDate);
    const now = new Date();
    const diffTime = end - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays > 0 ? diffDays : 0;
  };

  // معالجة إلغاء الاشتراك
  const handleCancelSubscription = async () => {
    try {
      setLoading(true);
      
      // [!] في بيئة الإنتاج، يجب استبدال هذا بطلب API حقيقي
      // const response = await fetch('/api/subscription/cancel', {
      //   method: 'POST',
      //   headers: {
      //     'Content-Type': 'application/json',
      //   },
      //   body: JSON.stringify({
      //     subscription_id: subscription.id,
      //     paypal_subscription_id: subscription.paypal_subscription_id
      //   }),
      // });
      
      // const data = await response.json();
      
      // للاختبار، نستخدم استجابة وهمية
      setTimeout(() => {
        setSubscription({
          ...subscription,
          status: 'cancelled',
          auto_renew: false
        });
        setCancelDialogOpen(false);
        setLoading(false);
      }, 1000);
      
    } catch (err) {
      console.error('Error cancelling subscription:', err);
      setError('حدث خطأ أثناء إلغاء الاشتراك. يرجى المحاولة مرة أخرى لاحقًا.');
      setLoading(false);
    }
  };

  // معالجة تجديد الاشتراك
  const handleRenewSubscription = async () => {
    try {
      setLoading(true);
      
      // [!] في بيئة الإنتاج، يجب استبدال هذا بطلب API حقيقي
      // const response = await fetch('/api/subscription/renew', {
      //   method: 'POST',
      //   headers: {
      //     'Content-Type': 'application/json',
      //   },
      //   body: JSON.stringify({
      //     subscription_id: subscription.id,
      //     plan_id: subscription.plan.id
      //   }),
      // });
      
      // const data = await response.json();
      
      // للاختبار، نستخدم استجابة وهمية
      setTimeout(() => {
        // تحديث تاريخ انتهاء الاشتراك
        const newEndDate = new Date(subscription.end_date);
        newEndDate.setDate(newEndDate.getDate() + subscription.plan.duration_days);
        
        setSubscription({
          ...subscription,
          status: 'active',
          auto_renew: true,
          end_date: newEndDate.toISOString()
        });
        setLoading(false);
      }, 1000);
      
    } catch (err) {
      console.error('Error renewing subscription:', err);
      setError('حدث خطأ أثناء تجديد الاشتراك. يرجى المحاولة مرة أخرى لاحقًا.');
      setLoading(false);
    }
  };

  // عرض رسالة التحميل
  if (loading && !subscription) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // عرض رسالة الخطأ
  if (error && !subscription) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
        <strong className="font-bold">خطأ! </strong>
        <span className="block sm:inline">{error}</span>
      </div>
    );
  }

  return (
    <div className="bg-white shadow rounded-lg overflow-hidden">
      {/* معلومات المستخدم */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center">
          {user.profile_picture ? (
            <img 
              src={user.profile_picture} 
              alt={user.name} 
              className="h-16 w-16 rounded-full object-cover"
            />
          ) : (
            <div className="h-16 w-16 rounded-full bg-blue-100 flex items-center justify-center">
              <span className="text-2xl font-bold text-blue-600">
                {user.name.charAt(0).toUpperCase()}
              </span>
            </div>
          )}
          <div className="mr-4">
            <h2 className="text-xl font-bold text-gray-900">{user.name}</h2>
            <p className="text-gray-600">{user.email}</p>
          </div>
        </div>
      </div>

      {/* معلومات الاشتراك */}
      {subscription ? (
        <div className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">معلومات الاشتراك</h3>
          
          <div className="bg-gray-50 p-4 rounded-lg mb-6">
            <div className="flex justify-between items-center mb-2">
              <span className="text-gray-600">الخطة:</span>
              <span className="font-semibold">{subscription.plan.name}</span>
            </div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-gray-600">الحالة:</span>
              <span className={`font-semibold ${
                subscription.status === 'active' ? 'text-green-600' : 
                subscription.status === 'cancelled' ? 'text-red-600' : 
                'text-yellow-600'
              }`}>
                {subscription.status === 'active' ? 'نشط' : 
                 subscription.status === 'cancelled' ? 'ملغي' : 
                 subscription.status === 'expired' ? 'منتهي' : 
                 'معلق'}
              </span>
            </div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-gray-600">تاريخ البدء:</span>
              <span className="font-semibold">{formatDate(subscription.start_date)}</span>
            </div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-gray-600">تاريخ الانتهاء:</span>
              <span className="font-semibold">{formatDate(subscription.end_date)}</span>
            </div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-gray-600">الأيام المتبقية:</span>
              <span className="font-semibold">{calculateRemainingDays(subscription.end_date)} يوم</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">التجديد التلقائي:</span>
              <span className={`font-semibold ${subscription.auto_renew ? 'text-green-600' : 'text-red-600'}`}>
                {subscription.auto_renew ? 'مفعل' : 'غير مفعل'}
              </span>
            </div>
          </div>
          
          {/* أزرار إدارة الاشتراك */}
          <div className="flex flex-col sm:flex-row gap-4">
            {subscription.status === 'active' && (
              <button
                onClick={() => setCancelDialogOpen(true)}
                className="py-2 px-4 border border-red-300 rounded-md text-red-700 hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors duration-200"
                disabled={loading}
              >
                {loading ? 'جاري المعالجة...' : 'إلغاء الاشتراك'}
              </button>
            )}
            
            {(subscription.status === 'cancelled' || subscription.status === 'expired') && (
              <button
                onClick={handleRenewSubscription}
                className="py-2 px-4 bg-blue-600 border border-transparent rounded-md text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200"
                disabled={loading}
              >
                {loading ? 'جاري المعالجة...' : 'تجديد الاشتراك'}
              </button>
            )}
            
            <a
              href="/subscription"
              className="py-2 px-4 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200 text-center"
            >
              تغيير خطة الاشتراك
            </a>
          </div>
        </div>
      ) : (
        <div className="p-6 text-center">
          <p className="text-gray-600 mb-4">ليس لديك اشتراك نشط حاليًا</p>
          <a
            href="/subscription"
            className="inline-block py-2 px-4 bg-blue-600 border border-transparent rounded-md text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200"
          >
            اختيار خطة اشتراك
          </a>
        </div>
      )}
      
      {/* مربع حوار تأكيد إلغاء الاشتراك */}
      {cancelDialogOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">تأكيد إلغاء الاشتراك</h3>
            <p className="text-gray-600 mb-6">
              هل أنت متأكد من رغبتك في إلغاء اشتراكك؟ ستظل قادرًا على استخدام الخدمة حتى نهاية فترة الاشتراك الحالية.
            </p>
            <div className="flex justify-end gap-4">
              <button
                onClick={() => setCancelDialogOpen(false)}
                className="py-2 px-4 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200"
                disabled={loading}
              >
                إلغاء
              </button>
              <button
                onClick={handleCancelSubscription}
                className="py-2 px-4 bg-red-600 border border-transparent rounded-md text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors duration-200"
                disabled={loading}
              >
                {loading ? 'جاري المعالجة...' : 'تأكيد الإلغاء'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserDashboard;
