// ملف الترجمات متعدد اللغات
const translations = {
    ar: {
        'register-title': 'تسجيل مستخدم جديد',
        'fullname-label': 'الاسم الكامل',
        'email-label': 'البريد الإلكتروني',
        'mobile-label': 'رقم الهاتف',
        'password-label': 'كلمة المرور',
        'confirm-password-label': 'تأكيد كلمة المرور',
        'subscription-type-label': 'نوع الاشتراك',
        'monthly-subscription': 'اشتراك شهري',
        'yearly-subscription': 'اشتراك سنوي',
        'arab-subscription': 'اشتراك العرب',
        'free-subscription': 'اشتراك مجاني',
        'complete-subscription': 'أكمل الاشتراك لمتابعة التسجيل',
        'payment-test-button': 'اختبار الدفع (للتطوير فقط)',
        'register-submit-button': 'تسجيل',
        'register-button': 'التسجيل وإنشاء حساب جديد',
        'toggle-sidebar-button': 'تبديل الشريط الجانبي',
        'export-button': 'تصدير',
        'animate-button': 'حركة',
        'btn-model-rotate': 'دوران النموذج',
        'btn-model-reverse': 'دوران عكسي',
        'btn-scene-rotate': 'دوران المشهد',
        'btn-model-swing': 'تأرجح النموذج',
        'btn-model-hover': 'تحليق النموذج',
        'btn-model-jump': 'قفز النموذج',
        'btn-bring-front': 'إحضار للأمام',
        'btn-send-back': 'إرسال للخلف',
        'btn-copy': 'نسخ',
        'btn-delete': 'حذف',
        'btn-text-dir': 'اتجاه النص',
        'btn-text-color': 'لون النص',
        'btn-font': 'الخط',
        'model-color-picker': 'منتقي لون النموذج',
        'ground-color-picker': 'منتقي لون الأرضية',
        'background-color-picker': 'منتقي لون الخلفية',
        'show-texture-thumbnails': 'عرض معاينات النسيج',
        'ground-texture-picker': 'منتقي نسيج الأرضية',
        'reset-ground': 'إعادة تعيين الأرضية',
        'reset-model': 'إعادة تعيين النموذج',
        'reset-background': 'إعادة تعيين الخلفية',
        'background-gradient': 'تدرج الخلفية',
        'ground-gradient': 'تدرج الأرضية',
        'model-gradient': 'تدرج النموذج',
        'lang-auto': 'تلقائي',
        'lang-ar': 'العربية',
        'lang-en': 'الإنجليزية',
        'lang-es': 'الإسبانية',
        'lang-fr': 'الفرنسية',
        'lang-zh': 'الصينية',
        'lang-ru': 'الروسية',
        'lang-hi': 'الهندية'
    },
    en: {
        'register-title': 'Register New User',
        'fullname-label': 'Full Name',
        'email-label': 'Email Address',
        'mobile-label': 'Phone Number',
        'password-label': 'Password',
        'confirm-password-label': 'Confirm Password',
        'subscription-type-label': 'Subscription Type',
        'monthly-subscription': 'Monthly Subscription',
        'yearly-subscription': 'Yearly Subscription',
        'arab-subscription': 'Arab Subscription',
        'free-subscription': 'Free Subscription',
        'complete-subscription': 'Complete subscription to continue registration',
        'payment-test-button': 'Test Payment (Development Only)',
        'register-submit-button': 'Register',
        'register-button': 'Register and Create New Account',
        'toggle-sidebar-button': 'Toggle Sidebar',
        'export-button': 'Export',
        'animate-button': 'Animate',
        'btn-model-rotate': 'Rotate Model',
        'btn-model-reverse': 'Reverse Rotation',
        'btn-scene-rotate': 'Rotate Scene',
        'btn-model-swing': 'Swing Model',
        'btn-model-hover': 'Hover Model',
        'btn-model-jump': 'Jump Model',
        'btn-bring-front': 'Bring to Front',
        'btn-send-back': 'Send to Back',
        'btn-copy': 'Copy',
        'btn-delete': 'Delete',
        'btn-text-dir': 'Text Direction',
        'btn-text-color': 'Text Color',
        'btn-font': 'Font',
        'model-color-picker': 'Model Color Picker',
        'ground-color-picker': 'Ground Color Picker',
        'background-color-picker': 'Background Color Picker',
        'show-texture-thumbnails': 'Show Texture Thumbnails',
        'ground-texture-picker': 'Ground Texture Picker',
        'reset-ground': 'Reset Ground',
        'reset-model': 'Reset Model',
        'reset-background': 'Reset Background',
        'background-gradient': 'Background Gradient',
        'ground-gradient': 'Ground Gradient',
        'model-gradient': 'Model Gradient',
        'lang-auto': 'Auto',
        'lang-ar': 'Arabic',
        'lang-en': 'English',
        'lang-es': 'Spanish',
        'lang-fr': 'French',
        'lang-zh': 'Chinese',
        'lang-ru': 'Russian',
        'lang-hi': 'Hindi'
    },
    es: {
        'register-title': 'Registrar Nuevo Usuario',
        'fullname-label': 'Nombre Completo',
        'email-label': 'Correo Electrónico',
        'mobile-label': 'Número de Teléfono',
        'password-label': 'Contraseña',
        'confirm-password-label': 'Confirmar Contraseña',
        'subscription-type-label': 'Tipo de Suscripción',
        'monthly-subscription': 'Suscripción Mensual',
        'yearly-subscription': 'Suscripción Anual',
        'arab-subscription': 'Suscripción Árabe',
        'free-subscription': 'Suscripción Gratuita',
        'complete-subscription': 'Complete la suscripción para continuar el registro',
        'payment-test-button': 'Probar Pago (Solo Desarrollo)',
        'register-submit-button': 'Registrar',
        'register-button': 'Registrarse y Crear Nueva Cuenta',
        'toggle-sidebar-button': 'Alternar Barra Lateral',
        'export-button': 'Exportar',
        'animate-button': 'Animar',
        'btn-model-rotate': 'Rotar Modelo',
        'btn-model-reverse': 'Rotación Inversa',
        'btn-scene-rotate': 'Rotar Escena',
        'btn-model-swing': 'Balancear Modelo',
        'btn-model-hover': 'Flotar Modelo',
        'btn-model-jump': 'Saltar Modelo'
    },
    fr: {
        'register-title': 'Enregistrer un Nouvel Utilisateur',
        'fullname-label': 'Nom Complet',
        'email-label': 'Adresse E-mail',
        'mobile-label': 'Numéro de Téléphone',
        'password-label': 'Mot de Passe',
        'confirm-password-label': 'Confirmer le Mot de Passe',
        'subscription-type-label': 'Type d\'Abonnement',
        'monthly-subscription': 'Abonnement Mensuel',
        'yearly-subscription': 'Abonnement Annuel',
        'arab-subscription': 'Abonnement Arabe',
        'free-subscription': 'Abonnement Gratuit',
        'complete-subscription': 'Complétez l\'abonnement pour continuer l\'inscription',
        'payment-test-button': 'Tester le Paiement (Développement Uniquement)',
        'register-submit-button': 'S\'inscrire',
        'register-button': 'S\'inscrire et Créer un Nouveau Compte',
        'toggle-sidebar-button': 'Basculer la Barre Latérale',
        'export-button': 'Exporter',
        'animate-button': 'Animer'
    }
};

let currentLanguage = 'ar';

// تطبيق الترجمات
function applyTranslations(lang) {
    const elements = document.querySelectorAll('[data-translate]');
    
    elements.forEach(element => {
        const key = element.getAttribute('data-translate');
        if (translations[lang] && translations[lang][key]) {
            if (element.tagName === 'INPUT' && element.type === 'submit') {
                element.value = translations[lang][key];
            } else {
                element.textContent = translations[lang][key];
            }
        }
    });
    
    // تطبيق الترجمات على العناصر ذات data-i18n-tooltip
    const tooltipElements = document.querySelectorAll('[data-i18n-tooltip]');
    tooltipElements.forEach(element => {
        const key = element.getAttribute('data-i18n-tooltip');
        if (translations[lang] && translations[lang][key]) {
            element.title = translations[lang][key];
        }
    });
    
    currentLanguage = lang;
    
    // تحديث اتجاه الصفحة
    if (lang === 'ar') {
        document.documentElement.dir = 'rtl';
        document.documentElement.lang = 'ar';
    } else {
        document.documentElement.dir = 'ltr';
        document.documentElement.lang = lang;
    }
}

// تغيير اللغة
function changeLanguage(lang) {
    applyTranslations(lang);
    localStorage.setItem('selectedLanguage', lang);
}

// اكتشاف اللغة تلقائياً
function detectLanguage() {
    const savedLang = localStorage.getItem('selectedLanguage');
    if (savedLang && translations[savedLang]) {
        return savedLang;
    }
    
    const browserLang = navigator.language.split('-')[0];
    if (translations[browserLang]) {
        return browserLang;
    }
    
    return 'ar'; // اللغة الافتراضية
}

// إعداد منتقي اللغة
function setupLanguageSelector() {
    const languageSelector = document.getElementById('languageSelector');
    if (languageSelector) {
        languageSelector.addEventListener('change', function() {
            const selectedLang = this.value === 'auto' ? detectLanguage() : this.value;
            changeLanguage(selectedLang);
        });
        
        // تعيين اللغة المحفوظة
        const savedLang = localStorage.getItem('selectedLanguage');
        if (savedLang) {
            languageSelector.value = savedLang;
        }
    }
}

// تهيئة النظام متعدد اللغات
document.addEventListener('DOMContentLoaded', function() {
    const initialLang = detectLanguage();
    applyTranslations(initialLang);
    setupLanguageSelector();
});

// تصدير الوظائف
window.changeLanguage = changeLanguage;
window.applyTranslations = applyTranslations;

