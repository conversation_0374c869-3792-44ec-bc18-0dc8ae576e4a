/**
 * Main entry point for the Fabric.js editor
 */
import { EditorCore } from "./editor-core.js"
import { FontManager } from "./font-manager.js"

document.addEventListener("DOMContentLoaded", () => {
  // Initialize the editor
  const editor = new EditorCore({
    canvasId: "fabric-canvas",
    width: 1024,
    height: 1024,
    backgroundColor: "#ffffff",
  }).init()

  // Initialize font manager
  const fontManager = new FontManager(editor)

  // Add event listeners for toolbar buttons
  setupToolbarListeners(editor)

  // Add keyboard shortcut info
  addKeyboardShortcutInfo()

  console.log("Fabric.js editor initialized")
})

/**
 * Set up toolbar listeners
 */
function setupToolbarListeners(editor) {
  // Add text button
  document.getElementById("add-text-btn")?.addEventListener("click", () => {
    editor.addText("Your text here")
  })

  // Add image button
  document.getElementById("add-image-btn")?.addEventListener("click", () => {
    const input = document.createElement("input")
    input.type = "file"
    input.accept = "image/*"

    input.onchange = (e) => {
      const file = e.target.files[0]
      if (file) {
        const reader = new FileReader()
        reader.onload = (event) => {
          editor.addImage(event.target.result)
        }
        reader.readAsDataURL(file)
      }
    }

    input.click()
  })

  // Delete button
  document.getElementById("delete-btn")?.addEventListener("click", () => {
    editor.deleteSelected()
  })

  // Undo button
  document.getElementById("undo-btn")?.addEventListener("click", () => {
    editor.history.undo()
  })

  // Redo button
  document.getElementById("redo-btn")?.addEventListener("click", () => {
    editor.history.redo()
  })

  // Group button
  document.getElementById("group-btn")?.addEventListener("click", () => {
    editor.groupSelected()
  })

  // Ungroup button
  document.getElementById("ungroup-btn")?.addEventListener("click", () => {
    editor.ungroupSelected()
  })

  // Export button
  document.getElementById("export-btn")?.addEventListener("click", () => {
    const dataUrl = editor.exportAsImage()

    // Create download link
    const link = document.createElement("a")
    link.href = dataUrl
    link.download = "canvas-export.png"
    link.click()
  })

  // Export JSON button
  document.getElementById("export-json-btn")?.addEventListener("click", () => {
    const json = editor.exportAsJSON()

    // Create download link
    const blob = new Blob([JSON.stringify(json)], { type: "application/json" })
    const url = URL.createObjectURL(blob)

    const link = document.createElement("a")
    link.href = url
    link.download = "canvas-data.json"
    link.click()

    // Clean up
    setTimeout(() => URL.revokeObjectURL(url), 100)
  })

  // Import JSON button
  document.getElementById("import-json-btn")?.addEventListener("click", () => {
    const input = document.createElement("input")
    input.type = "file"
    input.accept = "application/json"

    input.onchange = (e) => {
      const file = e.target.files[0]
      if (file) {
        const reader = new FileReader()
        reader.onload = (event) => {
          try {
            const json = JSON.parse(event.target.result)
            editor.importFromJSON(json)
          } catch (error) {
            console.error("Error importing JSON:", error)
            alert("Invalid JSON file")
          }
        }
        reader.readAsText(file)
      }
    }

    input.click()
  })
}

/**
 * Add keyboard shortcut info
 */
function addKeyboardShortcutInfo() {
  const container = document.createElement("div")
  container.className = "keyboard-shortcuts-info"
  container.style.cssText = `
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px;
    border-radius: 5px;
    font-size: 12px;
    z-index: 1000;
    max-width: 300px;
  `

  container.innerHTML = `
    <h3 style="margin: 0 0 5px; font-size: 14px;">Keyboard Shortcuts</h3>
    <ul style="margin: 0; padding: 0 0 0 20px; list-style-type: disc;">
      <li>Delete: Delete selected object</li>
      <li>Ctrl+Z: Undo</li>
      <li>Ctrl+Y / Ctrl+Shift+Z: Redo</li>
      <li>Ctrl+D: Duplicate selected</li>
      <li>Ctrl+G: Group selected</li>
      <li>Ctrl+Shift+G: Ungroup selected</li>
    </ul>
  `

  // Add close button
  const closeButton = document.createElement("button")
  closeButton.innerHTML = "&times;"
  closeButton.style.cssText = `
    position: absolute;
    top: 5px;
    right: 5px;
    background: none;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
  `

  closeButton.addEventListener("click", () => {
    container.style.display = "none"
  })

  container.appendChild(closeButton)
  document.body.appendChild(container)
}
