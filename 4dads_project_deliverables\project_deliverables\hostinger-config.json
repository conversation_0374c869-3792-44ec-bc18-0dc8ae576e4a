# ملف تكوين Hostinger للنشر
# hostinger-config.json

{
  "app": {
    "name": "4dads-subscription-app",
    "type": "nodejs",
    "version": "18.x",
    "entry_point": "server.js",
    "build_command": "npm run build",
    "run_command": "npm start"
  },
  "environment": {
    "NODE_ENV": "production",
    "PORT": 3000,
    "DB_HOST": "REPLACE_WITH_YOUR_DB_HOST",
    "DB_USER": "REPLACE_WITH_YOUR_DB_USER",
    "DB_PASSWORD": "REPLACE_WITH_YOUR_DB_PASSWORD",
    "DB_NAME": "REPLACE_WITH_YOUR_DB_NAME",
    "GOOGLE_CLIENT_ID": "REPLACE_WITH_YOUR_GOOGLE_CLIENT_ID",
    "PAYPAL_CLIENT_ID": "REPLACE_WITH_YOUR_PAYPAL_CLIENT_ID",
    "PAYPAL_SECRET": "REPLACE_WITH_YOUR_PAYPAL_SECRET",
    "NEXTAUTH_URL": "https://4dads.pro",
    "NEXTAUTH_SECRET": "REPLACE_WITH_RANDOM_SECRET_KEY"
  },
  "domains": [
    "4dads.pro"
  ],
  "ssl": {
    "enabled": true,
    "auto_renew": true
  }
}
