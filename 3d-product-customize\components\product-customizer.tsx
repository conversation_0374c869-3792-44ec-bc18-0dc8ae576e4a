"use client"

import type React from "react"

import { useState, useRef, useCallback } from "react"
import { Canvas } from "@react-three/fiber"
import { OrbitControls, PerspectiveCamera, Environment } from "@react-three/drei"
import { SimpleTShirtModel } from "@/components/simple-t-shirt-model"
import { TopBar } from "@/components/top-bar"
import { BottomBar } from "@/components/bottom-bar"
import { LeftSidebar } from "@/components/left-sidebar"
import { RightSidebar } from "@/components/right-sidebar"
import { TextEditorToolbar } from "@/components/text-editor-toolbar"
import { LoginModal } from "@/components/login-modal"
import { RegistrationModal } from "@/components/registration-modal"
import { PartContextMenu } from "@/components/part-context-menu"
import { DebugPanel } from "@/components/debug-panel"

export function ProductCustomizer() {
  const [showGrid, setShowGrid] = useState(true)
  const [verticalLightDirection, setVerticalLightDirection] = useState(50)
  const [tableLightIntensity, setTableLightIntensity] = useState(50)
  const [fieldOfView, setFieldOfView] = useState(50)
  const [metalness, setMetalness] = useState(0)
  const [roughness, setRoughness] = useState(50)
  const [environmentReflection, setEnvironmentReflection] = useState(50)
  const [modelHorizontalPosition, setModelHorizontalPosition] = useState(50)
  const [modelVerticalPosition, setModelVerticalPosition] = useState(50)
  const [modelPosition, setModelPosition] = useState(50)
  const [modelColor, setModelColor] = useState("#ffffff")
  const [selectedEnvironment, setSelectedEnvironment] = useState(0)
  const [showLoginModal, setShowLoginModal] = useState(false)
  const [showRegistrationModal, setShowRegistrationModal] = useState(false)
  const [showTextEditor, setShowTextEditor] = useState(false)

  // Part selection and context menu
  const [contextMenuPosition, setContextMenuPosition] = useState<{ x: number; y: number } | null>(null)
  const [selectedPart, setSelectedPart] = useState<string | null>(null)
  const [partTextures, setPartTextures] = useState<Record<string, string>>({})
  const [partColors, setPartColors] = useState<Record<string, string>>({})

  const canvasRef = useRef<HTMLDivElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const environments = [
    "/placeholder.svg?height=80&width=60",
    "/placeholder.svg?height=80&width=60",
    "/placeholder.svg?height=80&width=60",
    "/placeholder.svg?height=80&width=60",
    "/placeholder.svg?height=80&width=60",
    "/placeholder.svg?height=80&width=60",
    "/placeholder.svg?height=80&width=60",
    "/placeholder.svg?height=80&width=60",
    "/placeholder.svg?height=80&width=60",
    "/placeholder.svg?height=80&width=60",
    "/placeholder.svg?height=80&width=60",
    "/placeholder.svg?height=80&width=60",
  ]

  // Handle part click
  const handlePartClick = useCallback((event: any, partName: string) => {
    // Get the click position relative to the canvas
    if (canvasRef.current) {
      const rect = canvasRef.current.getBoundingClientRect()
      const x = event.clientX - rect.left
      const y = event.clientY - rect.top

      // Set the context menu position and selected part
      setContextMenuPosition({ x, y })
      setSelectedPart(partName)
      console.log(`Selected part: ${partName}`)
    }
  }, [])

  // Handle upload image
  const handleUploadImage = useCallback(() => {
    if (fileInputRef.current && selectedPart) {
      fileInputRef.current.click()
    }
  }, [selectedPart])

  // Handle file change with improved error handling
  const handleFileChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0]
      if (!file || !selectedPart) return

      console.log(`Processing file for ${selectedPart}:`, file.name, file.type)

      const reader = new FileReader()

      reader.onload = (e) => {
        try {
          const result = e.target?.result
          if (typeof result !== "string") {
            console.error("FileReader result is not a string")
            return
          }

          console.log(`File loaded successfully for ${selectedPart}, data URL length:`, result.length)

          // Update the part textures with the new image
          setPartTextures((prev) => {
            const newTextures = { ...prev, [selectedPart as string]: result }
            console.log(`Updated textures for parts:`, Object.keys(newTextures))
            return newTextures
          })
        } catch (error) {
          console.error("Error processing file:", error)
        }
      }

      reader.onerror = (error) => {
        console.error("FileReader error:", error)
      }

      try {
        reader.readAsDataURL(file)
      } catch (error) {
        console.error("Error reading file:", error)
      }

      // Reset the file input value so the same file can be selected again
      if (fileInputRef.current) {
        fileInputRef.current.value = ""
      }

      // Close the context menu
      setContextMenuPosition(null)
    },
    [selectedPart],
  )

  // Handle select color
  const handleSelectColor = useCallback(
    (color: string) => {
      if (selectedPart) {
        console.log(`Setting color for ${selectedPart}:`, color)
        setPartColors((prev) => ({
          ...prev,
          [selectedPart]: color,
        }))
        setContextMenuPosition(null)
      }
    },
    [selectedPart],
  )

  // Handle remove texture
  const handleRemoveTexture = useCallback(() => {
    if (selectedPart) {
      console.log(`Removing texture for ${selectedPart}`)
      setPartTextures((prev) => {
        const newTextures = { ...prev }
        delete newTextures[selectedPart]
        return newTextures
      })
      setContextMenuPosition(null)
    }
  }, [selectedPart])

  // Check if selected part has texture
  const hasTexture = selectedPart ? !!partTextures[selectedPart] : false

  return (
    <div className="flex h-screen w-full overflow-hidden bg-[#182949]">
      {/* Left Sidebar */}
      <LeftSidebar
        showGrid={showGrid}
        setShowGrid={setShowGrid}
        verticalLightDirection={verticalLightDirection}
        setVerticalLightDirection={setVerticalLightDirection}
        tableLightIntensity={tableLightIntensity}
        setTableLightIntensity={setTableLightIntensity}
        fieldOfView={fieldOfView}
        setFieldOfView={setFieldOfView}
        metalness={metalness}
        setMetalness={setMetalness}
        roughness={roughness}
        setRoughness={setRoughness}
        environmentReflection={environmentReflection}
        setEnvironmentReflection={setEnvironmentReflection}
        modelHorizontalPosition={modelHorizontalPosition}
        setModelHorizontalPosition={setModelHorizontalPosition}
        modelVerticalPosition={modelVerticalPosition}
        setModelVerticalPosition={setModelVerticalPosition}
        modelPosition={modelPosition}
        setModelPosition={setModelPosition}
        modelColor={modelColor}
        setModelColor={setModelColor}
      />

      {/* Main 3D Viewer */}
      <div className="relative flex-1 overflow-hidden">
        <TopBar setShowLoginModal={setShowLoginModal} />

        <div ref={canvasRef} className="h-full w-full">
          <Canvas shadows>
            <PerspectiveCamera makeDefault position={[0, 1, 3]} fov={30 + fieldOfView / 2} />
            <OrbitControls enablePan={true} enableZoom={true} enableRotate={true} />
            <ambientLight intensity={0.5 + tableLightIntensity / 200} />
            <directionalLight
              position={[0, 2 + verticalLightDirection / 50, 0]}
              intensity={1 + tableLightIntensity / 100}
              castShadow
            />
            <Environment preset="sunset" />

            {/* Ground */}
            {showGrid && (
              <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -1, 0]} receiveShadow>
                <planeGeometry args={[10, 10]} />
                <meshStandardMaterial color="#8B4513" roughness={0.8} metalness={0.1} />
                <gridHelper args={[10, 10]} position={[0, 0.01, 0]} />
              </mesh>
            )}

            {/* T-Shirt Model */}
            <SimpleTShirtModel
              position={[
                (modelHorizontalPosition - 50) / 50,
                (modelVerticalPosition - 50) / 50,
                (modelPosition - 50) / 50,
              ]}
              color={modelColor}
              metalness={metalness / 100}
              roughness={roughness / 100}
              envMapIntensity={environmentReflection / 50}
              onPartClick={handlePartClick}
              partTextures={partTextures}
              partColors={partColors}
            />
          </Canvas>

          {/* Context Menu */}
          {contextMenuPosition && selectedPart && (
            <PartContextMenu
              position={contextMenuPosition}
              onClose={() => setContextMenuPosition(null)}
              onUploadImage={handleUploadImage}
              onSelectColor={handleSelectColor}
              onRemoveTexture={handleRemoveTexture}
              hasTexture={hasTexture}
              partName={selectedPart}
            />
          )}

          {/* Hidden file input for image upload */}
          <input type="file" ref={fileInputRef} className="hidden" accept="image/*" onChange={handleFileChange} />

          {showTextEditor && (
            <div className="absolute bottom-1/3 left-1/2 -translate-x-1/2 transform">
              <TextEditorToolbar />
            </div>
          )}

          {/* Debug Panel */}
          <DebugPanel partTextures={partTextures} partColors={partColors} />
        </div>

        <BottomBar setShowTextEditor={setShowTextEditor} />
      </div>

      {/* Right Sidebar */}
      <RightSidebar
        environments={environments}
        selectedEnvironment={selectedEnvironment}
        setSelectedEnvironment={setSelectedEnvironment}
      />

      {/* Modals */}
      {showLoginModal && (
        <LoginModal
          onClose={() => setShowLoginModal(false)}
          onRegister={() => {
            setShowLoginModal(false)
            setShowRegistrationModal(true)
          }}
        />
      )}

      {showRegistrationModal && (
        <RegistrationModal
          onClose={() => setShowRegistrationModal(false)}
          onLogin={() => {
            setShowRegistrationModal(false)
            setShowLoginModal(true)
          }}
        />
      )}
    </div>
  )
}
