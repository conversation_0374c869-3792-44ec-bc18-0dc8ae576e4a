/**
 * Enhanced ControlPanel - Unified, context-sensitive control panel for the editor
 * Dynamically adjusts based on the type of selected object with expanded functionality
 * Provides merged controls for text, images, and groups in one floating panel
 */
class ControlPanel {
  constructor(editorCore) {
    this.editor = editorCore
    this.panel = null
    this.activeObject = null
    this.isVisible = false

    this.init()
  }

  /**
   * Initialize the control panel
   */
  init() {
    // Create the panel element
    this.panel = document.createElement("div")
    this.panel.className = "fabric-control-panel"
    this.panel.style.cssText = `
      position: absolute;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.2);
      padding: 12px;
      z-index: 1000;
      display: none;
      min-width: 250px;
      user-select: none;
    `

    document.body.appendChild(this.panel)

    // Make panel draggable
    this.makeDraggable()
  }

  /**
   * Make the panel draggable
   */
  makeDraggable() {
    let isDragging = false
    let offsetX, offsetY

    const dragHandle = document.createElement("div")
    dragHandle.className = "control-panel-drag-handle"
    dragHandle.style.cssText = `
      height: 24px;
      cursor: move;
      background: #f5f5f5;
      border-radius: 4px 4px 0 0;
      margin: -12px -12px 8px -12px;
      padding: 4px 12px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    `

    const title = document.createElement("span")
    title.textContent = "Element Controls"
    title.style.fontWeight = "bold"

    const closeBtn = document.createElement("button")
    closeBtn.innerHTML = "&times;"
    closeBtn.style.cssText = `
      background: none;
      border: none;
      font-size: 18px;
      cursor: pointer;
      padding: 0 4px;
    `
    closeBtn.addEventListener("click", () => this.hide())

    dragHandle.appendChild(title)
    dragHandle.appendChild(closeBtn)
    this.panel.insertBefore(dragHandle, this.panel.firstChild)

    dragHandle.addEventListener("mousedown", (e) => {
      isDragging = true
      offsetX = e.clientX - this.panel.getBoundingClientRect().left
      offsetY = e.clientY - this.panel.getBoundingClientRect().top
    })

    document.addEventListener("mousemove", (e) => {
      if (!isDragging) return

      const x = e.clientX - offsetX
      const y = e.clientY - offsetY

      this.panel.style.left = `${x}px`
      this.panel.style.top = `${y}px`
    })

    document.addEventListener("mouseup", () => {
      isDragging = false
    })
  }

  /**
   * Show the control panel for the selected object
   */
  showForObject(object) {
    this.activeObject = object
    this.isVisible = true

    // Clear existing content
    while (this.panel.childElementCount > 1) {
      this.panel.removeChild(this.panel.lastChild)
    }

    // Add common controls
    this.addCommonControls()

    // Add type-specific controls
    if (object.type === "i-text" || object.type === "text") {
      this.addTextControls()
    } else if (object.type === "image") {
      this.addImageControls()
    } else if (object.type === "group") {
      this.addGroupControls()
    }

    // Position the panel near the object
    this.positionPanel()

    // Show the panel
    this.panel.style.display = "block"
  }

  /**
   * Hide the control panel
   */
  hide() {
    this.isVisible = false
    this.panel.style.display = "none"
    this.activeObject = null
  }

  /**
   * Position the panel near the selected object
   */
  positionPanel() {
    if (!this.activeObject) return

    const canvas = this.editor.canvas
    const zoom = canvas.getZoom()
    const obj = this.activeObject

    // Get object's bounding box
    const objBounds = obj.getBoundingRect()

    // Get canvas offset
    const canvasEl = canvas.getElement()
    const canvasRect = canvasEl.getBoundingClientRect()

    // Calculate position
    let left = canvasRect.left + objBounds.left + objBounds.width + 20
    let top = canvasRect.top + objBounds.top

    // Check if panel would go off-screen to the right
    if (left + 250 > window.innerWidth) {
      left = canvasRect.left + objBounds.left - 270
    }

    // Check if panel would go off-screen at the bottom
    if (top + 400 > window.innerHeight) {
      top = window.innerHeight - 420
    }

    // Check if panel would go off-screen at the top
    if (top < 10) {
      top = 10
    }

    this.panel.style.left = `${left}px`
    this.panel.style.top = `${top}px`
  }

  /**
   * Add common controls for all object types
   */
  addCommonControls() {
    const container = document.createElement("div")
    container.className = "common-controls"

    // Create action buttons
    const actionButtons = [
      { icon: "🗑️", label: "Delete", action: this.editor.deleteSelected, tooltip: "Delete (Del)" },
      { icon: "📋", label: "Duplicate", action: this.editor.duplicateSelected, tooltip: "Duplicate (Ctrl+D)" },
      { icon: "⬆️", label: "Bring to Front", action: this.editor.bringToFront, tooltip: "Bring to Front" },
      { icon: "⬇️", label: "Send to Back", action: this.editor.sendToBack, tooltip: "Send to Back" },
      { icon: "↩️", label: "Reset", action: this.editor.resetTransformation, tooltip: "Reset Transformation" },
    ]

    // Create button group
    const buttonGroup = document.createElement("div")
    buttonGroup.style.cssText = `
      display: flex;
      gap: 8px;
      margin-bottom: 12px;
      flex-wrap: wrap;
    `

    // Add buttons to group
    actionButtons.forEach((btn) => {
      const button = document.createElement("button")
      button.innerHTML = `${btn.icon} <span>${btn.label}</span>`
      button.title = btn.tooltip
      button.style.cssText = `
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 6px 10px;
        background: #f0f0f0;
        border: 1px solid #ddd;
        border-radius: 4px;
        cursor: pointer;
        font-size: 13px;
      `
      button.addEventListener("click", btn.action)
      buttonGroup.appendChild(button)
    })

    // Add lock toggle button
    const lockBtn = document.createElement("button")
    const isLocked = this.activeObject && this.activeObject.lockMovementX && this.activeObject.lockMovementY

    lockBtn.innerHTML = isLocked ? "🔒 Unlock" : "🔓 Lock"
    lockBtn.title = "Lock/Unlock Object"
    lockBtn.style.cssText = `
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 6px 10px;
      background: ${isLocked ? "#e6f7ff" : "#f0f0f0"};
      border: 1px solid ${isLocked ? "#91d5ff" : "#ddd"};
      border-radius: 4px;
      cursor: pointer;
      font-size: 13px;
    `
    lockBtn.addEventListener("click", this.editor.toggleLock)
    buttonGroup.appendChild(lockBtn)

    // Add opacity control
    const opacityContainer = document.createElement("div")
    opacityContainer.style.cssText = `
      margin-top: 12px;
      margin-bottom: 12px;
    `

    const opacityLabel = document.createElement("label")
    opacityLabel.textContent = "Opacity:"
    opacityLabel.style.cssText = `
      display: block;
      margin-bottom: 4px;
      font-size: 13px;
    `

    const opacitySlider = document.createElement("input")
    opacitySlider.type = "range"
    opacitySlider.min = "0"
    opacitySlider.max = "100"
    opacitySlider.value = (this.activeObject.opacity || 1) * 100
    opacitySlider.style.cssText = `
      width: 100%;
    `

    opacitySlider.addEventListener("input", (e) => {
      const opacity = Number.parseInt(e.target.value) / 100
      this.activeObject.set("opacity", opacity)
      this.editor.canvas.renderAll()
      this.editor.threeDSync.syncWithThreeJs()
    })

    opacitySlider.addEventListener("change", () => {
      this.editor.history.saveState()
    })

    opacityContainer.appendChild(opacityLabel)
    opacityContainer.appendChild(opacitySlider)

    // Add all elements to container
    container.appendChild(buttonGroup)
    container.appendChild(opacityContainer)

    // Add container to panel
    this.panel.appendChild(container)
  }

  /**
   * Add text-specific controls
   */
  addTextControls() {
    const container = document.createElement("div")
    container.className = "text-controls"

    // Text content
    const textContentContainer = document.createElement("div")
    textContentContainer.style.marginBottom = "12px"

    const textLabel = document.createElement("label")
    textLabel.textContent = "Text:"
    textLabel.style.cssText = `
      display: block;
      margin-bottom: 4px;
      font-size: 13px;
    `

    const textInput = document.createElement("textarea")
    textInput.value = this.activeObject.text
    textInput.style.cssText = `
      width: 100%;
      padding: 6px;
      border: 1px solid #ddd;
      border-radius: 4px;
      resize: vertical;
      min-height: 60px;
    `

    textInput.addEventListener("input", (e) => {
      this.activeObject.set("text", e.target.value)
      this.editor.canvas.renderAll()
      this.editor.threeDSync.syncWithThreeJs()
    })

    textInput.addEventListener("change", () => {
      this.editor.history.saveState()
    })

    textContentContainer.appendChild(textLabel)
    textContentContainer.appendChild(textInput)

    // Font size
    const fontSizeContainer = document.createElement("div")
    fontSizeContainer.style.marginBottom = "12px"

    const fontSizeLabel = document.createElement("label")
    fontSizeLabel.textContent = "Font Size:"
    fontSizeLabel.style.cssText = `
      display: block;
      margin-bottom: 4px;
      font-size: 13px;
    `

    const fontSizeInput = document.createElement("input")
    fontSizeInput.type = "number"
    fontSizeInput.min = "1"
    fontSizeInput.max = "500"
    fontSizeInput.value = this.activeObject.fontSize || 40
    fontSizeInput.style.cssText = `
      width: 100%;
      padding: 6px;
      border: 1px solid #ddd;
      border-radius: 4px;
    `

    fontSizeInput.addEventListener("input", (e) => {
      const fontSize = Number.parseInt(e.target.value)
      this.activeObject.set("fontSize", fontSize)
      this.editor.canvas.renderAll()
      this.editor.threeDSync.syncWithThreeJs()
    })

    fontSizeInput.addEventListener("change", () => {
      this.editor.history.saveState()
    })

    fontSizeContainer.appendChild(fontSizeLabel)
    fontSizeContainer.appendChild(fontSizeInput)

    // Text color
    const colorContainer = document.createElement("div")
    colorContainer.style.marginBottom = "12px"

    const colorLabel = document.createElement("label")
    colorLabel.textContent = "Text Color:"
    colorLabel.style.cssText = `
      display: block;
      margin-bottom: 4px;
      font-size: 13px;
    `

    const colorInput = document.createElement("input")
    colorInput.type = "color"
    colorInput.value = this.activeObject.fill || "#000000"
    colorInput.style.cssText = `
      width: 100%;
      height: 30px;
      border: 1px solid #ddd;
      border-radius: 4px;
    `

    colorInput.addEventListener("input", (e) => {
      this.activeObject.set("fill", e.target.value)
      this.editor.canvas.renderAll()
      this.editor.threeDSync.syncWithThreeJs()
    })

    colorInput.addEventListener("change", () => {
      this.editor.history.saveState()
    })

    colorContainer.appendChild(colorLabel)
    colorContainer.appendChild(colorInput)

    // Text alignment
    const alignmentContainer = document.createElement("div")
    alignmentContainer.style.marginBottom = "12px"

    const alignmentLabel = document.createElement("label")
    alignmentLabel.textContent = "Alignment:"
    alignmentLabel.style.cssText = `
      display: block;
      margin-bottom: 4px;
      font-size: 13px;
    `

    const alignmentButtons = document.createElement("div")
    alignmentButtons.style.cssText = `
      display: flex;
      gap: 4px;
    `

    const alignments = [
      { value: "left", icon: "⬅️", tooltip: "Align Left" },
      { value: "center", icon: "⬆️", tooltip: "Align Center" },
      { value: "right", icon: "➡️", tooltip: "Align Right" },
    ]

    alignments.forEach((align) => {
      const button = document.createElement("button")
      button.innerHTML = align.icon
      button.title = align.tooltip
      button.style.cssText = `
        flex: 1;
        padding: 6px;
        background: ${this.activeObject.textAlign === align.value ? "#e6f7ff" : "#f0f0f0"};
        border: 1px solid ${this.activeObject.textAlign === align.value ? "#91d5ff" : "#ddd"};
        border-radius: 4px;
        cursor: pointer;
      `

      button.addEventListener("click", () => {
        this.activeObject.set("textAlign", align.value)

        // Update button styles
        alignmentButtons.querySelectorAll("button").forEach((btn) => {
          btn.style.background = "#f0f0f0"
          btn.style.borderColor = "#ddd"
        })
        button.style.background = "#e6f7ff"
        button.style.borderColor = "#91d5ff"

        this.editor.canvas.renderAll()
        this.editor.history.saveState()
        this.editor.threeDSync.syncWithThreeJs()
      })

      alignmentButtons.appendChild(button)
    })

    alignmentContainer.appendChild(alignmentLabel)
    alignmentContainer.appendChild(alignmentButtons)

    // Text style (bold, italic)
    const styleContainer = document.createElement("div")
    styleContainer.style.marginBottom = "12px"

    const styleLabel = document.createElement("label")
    styleLabel.textContent = "Style:"
    styleLabel.style.cssText = `
      display: block;
      margin-bottom: 4px;
      font-size: 13px;
    `

    const styleButtons = document.createElement("div")
    styleButtons.style.cssText = `
      display: flex;
      gap: 4px;
    `

    // Bold button
    const boldButton = document.createElement("button")
    boldButton.innerHTML = "<b>B</b>"
    boldButton.title = "Bold"
    boldButton.style.cssText = `
      flex: 1;
      padding: 6px;
      background: ${this.activeObject.fontWeight === "bold" ? "#e6f7ff" : "#f0f0f0"};
      border: 1px solid ${this.activeObject.fontWeight === "bold" ? "#91d5ff" : "#ddd"};
      border-radius: 4px;
      cursor: pointer;
    `

    boldButton.addEventListener("click", () => {
      const isBold = this.activeObject.fontWeight === "bold"
      this.activeObject.set("fontWeight", isBold ? "normal" : "bold")
      boldButton.style.background = isBold ? "#f0f0f0" : "#e6f7ff"
      boldButton.style.borderColor = isBold ? "#ddd" : "#91d5ff"
      this.editor.canvas.renderAll()
      this.editor.history.saveState()
      this.editor.threeDSync.syncWithThreeJs()
    })

    // Italic button
    const italicButton = document.createElement("button")
    italicButton.innerHTML = "<i>I</i>"
    italicButton.title = "Italic"
    italicButton.style.cssText = `
      flex: 1;
      padding: 6px;
      background: ${this.activeObject.fontStyle === "italic" ? "#e6f7ff" : "#f0f0f0"};
      border: 1px solid ${this.activeObject.fontStyle === "italic" ? "#91d5ff" : "#ddd"};
      border-radius: 4px;
      cursor: pointer;
    `

    italicButton.addEventListener("click", () => {
      const isItalic = this.activeObject.fontStyle === "italic"
      this.activeObject.set("fontStyle", isItalic ? "normal" : "italic")
      italicButton.style.background = isItalic ? "#f0f0f0" : "#e6f7ff"
      italicButton.style.borderColor = isItalic ? "#ddd" : "#91d5ff"
      this.editor.canvas.renderAll()
      this.editor.history.saveState()
      this.editor.threeDSync.syncWithThreeJs()
    })

    styleButtons.appendChild(boldButton)
    styleButtons.appendChild(italicButton)

    styleContainer.appendChild(styleLabel)
    styleContainer.appendChild(styleButtons)

    // Text shadow
    const shadowContainer = document.createElement("div")
    shadowContainer.style.marginBottom = "12px"

    const shadowLabel = document.createElement("label")
    shadowLabel.textContent = "Shadow:"
    shadowLabel.style.cssText = `
      display: block;
      margin-bottom: 4px;
      font-size: 13px;
    `

    const shadowControls = document.createElement("div")
    shadowControls.style.cssText = `
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 8px;
    `

    // Shadow color
    const shadowColorContainer = document.createElement("div")

    const shadowColorLabel = document.createElement("label")
    shadowColorLabel.textContent = "Color:"
    shadowColorLabel.style.fontSize = "12px"

    const shadowColorInput = document.createElement("input")
    shadowColorInput.type = "color"
    shadowColorInput.value = this.activeObject.shadow?.color || "#000000"
    shadowColorInput.style.width = "100%"

    shadowColorContainer.appendChild(shadowColorLabel)
    shadowColorContainer.appendChild(shadowColorInput)

    // Shadow blur
    const shadowBlurContainer = document.createElement("div")

    const shadowBlurLabel = document.createElement("label")
    shadowBlurLabel.textContent = "Blur:"
    shadowBlurLabel.style.fontSize = "12px"

    const shadowBlurInput = document.createElement("input")
    shadowBlurInput.type = "number"
    shadowBlurInput.min = "0"
    shadowBlurInput.max = "50"
    shadowBlurInput.value = this.activeObject.shadow?.blur || "0"
    shadowBlurInput.style.width = "100%"

    shadowBlurContainer.appendChild(shadowBlurLabel)
    shadowBlurContainer.appendChild(shadowBlurInput)

    // Shadow offset X
    const shadowOffsetXContainer = document.createElement("div")

    const shadowOffsetXLabel = document.createElement("label")
    shadowOffsetXLabel.textContent = "Offset X:"
    shadowOffsetXLabel.style.fontSize = "12px"

    const shadowOffsetXInput = document.createElement("input")
    shadowOffsetXInput.type = "number"
    shadowOffsetXInput.min = "-50"
    shadowOffsetXInput.max = "50"
    shadowOffsetXInput.value = this.activeObject.shadow?.offsetX || "0"
    shadowOffsetXInput.style.width = "100%"

    shadowOffsetXContainer.appendChild(shadowOffsetXLabel)
    shadowOffsetXContainer.appendChild(shadowOffsetXInput)

    // Shadow offset Y
    const shadowOffsetYContainer = document.createElement("div")

    const shadowOffsetYLabel = document.createElement("label")
    shadowOffsetYLabel.textContent = "Offset Y:"
    shadowOffsetYLabel.style.fontSize = "12px"

    const shadowOffsetYInput = document.createElement("input")
    shadowOffsetYInput.type = "number"
    shadowOffsetYInput.min = "-50"
    shadowOffsetYInput.max = "50"
    shadowOffsetYInput.value = this.activeObject.shadow?.offsetY || "0"
    shadowOffsetYInput.style.width = "100%"

    shadowOffsetYContainer.appendChild(shadowOffsetYLabel)
    shadowOffsetYContainer.appendChild(shadowOffsetYInput)

    // Add shadow controls to container
    shadowControls.appendChild(shadowColorContainer)
    shadowControls.appendChild(shadowBlurContainer)
    shadowControls.appendChild(shadowOffsetXContainer)
    shadowControls.appendChild(shadowOffsetYContainer)

    // Apply shadow button
    const applyShadowButton = document.createElement("button")
    applyShadowButton.textContent = "Apply Shadow"
    applyShadowButton.style.cssText = `
      width: 100%;
      margin-top: 8px;
      padding: 6px;
      background: #f0f0f0;
      border: 1px solid #ddd;
      border-radius: 4px;
      cursor: pointer;
    `

    applyShadowButton.addEventListener("click", () => {
      const shadow = new fabric.Shadow({
        color: shadowColorInput.value,
        blur: Number.parseInt(shadowBlurInput.value),
        offsetX: Number.parseInt(shadowOffsetXInput.value),
        offsetY: Number.parseInt(shadowOffsetYInput.value),
      })

      this.activeObject.set("shadow", shadow)
      this.editor.canvas.renderAll()
      this.editor.history.saveState()
      this.editor.threeDSync.syncWithThreeJs()
    })

    shadowContainer.appendChild(shadowLabel)
    shadowContainer.appendChild(shadowControls)
    shadowContainer.appendChild(applyShadowButton)

    // Text stroke
    const strokeContainer = document.createElement("div")
    strokeContainer.style.marginBottom = "12px"

    const strokeLabel = document.createElement("label")
    strokeLabel.textContent = "Stroke:"
    strokeLabel.style.cssText = `
      display: block;
      margin-bottom: 4px;
      font-size: 13px;
    `

    const strokeControls = document.createElement("div")
    strokeControls.style.cssText = `
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 8px;
    `

    // Stroke color
    const strokeColorContainer = document.createElement("div")

    const strokeColorLabel = document.createElement("label")
    strokeColorLabel.textContent = "Color:"
    strokeColorLabel.style.fontSize = "12px"

    const strokeColorInput = document.createElement("input")
    strokeColorInput.type = "color"
    strokeColorInput.value = this.activeObject.stroke || "#000000"
    strokeColorInput.style.width = "100%"

    strokeColorContainer.appendChild(strokeColorLabel)
    strokeColorContainer.appendChild(strokeColorInput)

    // Stroke width
    const strokeWidthContainer = document.createElement("div")

    const strokeWidthLabel = document.createElement("label")
    strokeWidthLabel.textContent = "Width:"
    strokeWidthLabel.style.fontSize = "12px"

    const strokeWidthInput = document.createElement("input")
    strokeWidthInput.type = "number"
    strokeWidthInput.min = "0"
    strokeWidthInput.max = "20"
    strokeWidthInput.step = "0.5"
    strokeWidthInput.value = this.activeObject.strokeWidth || "0"
    strokeWidthInput.style.width = "100%"

    strokeWidthContainer.appendChild(strokeWidthLabel)
    strokeWidthContainer.appendChild(strokeWidthInput)

    // Add stroke controls to container
    strokeControls.appendChild(strokeColorContainer)
    strokeControls.appendChild(strokeWidthContainer)

    // Apply stroke button
    const applyStrokeButton = document.createElement("button")
    applyStrokeButton.textContent = "Apply Stroke"
    applyStrokeButton.style.cssText = `
      width: 100%;
      margin-top: 8px;
      padding: 6px;
      background: #f0f0f0;
      border: 1px solid #ddd;
      border-radius: 4px;
      cursor: pointer;
    `

    applyStrokeButton.addEventListener("click", () => {
      this.activeObject.set({
        stroke: strokeColorInput.value,
        strokeWidth: Number.parseFloat(strokeWidthInput.value),
      })

      this.editor.canvas.renderAll()
      this.editor.history.saveState()
      this.editor.threeDSync.syncWithThreeJs()
    })

    strokeContainer.appendChild(strokeLabel)
    strokeContainer.appendChild(strokeControls)
    strokeContainer.appendChild(applyStrokeButton)

    // Background color
    const bgColorContainer = document.createElement("div")
    bgColorContainer.style.marginBottom = "12px"

    const bgColorLabel = document.createElement("label")
    bgColorLabel.textContent = "Background Color:"
    bgColorLabel.style.cssText = `
      display: block;
      margin-bottom: 4px;
      font-size: 13px;
    `

    const bgColorInput = document.createElement("input")
    bgColorInput.type = "color"
    bgColorInput.value = this.activeObject.backgroundColor || "#ffffff"
    bgColorInput.style.cssText = `
      width: 100%;
      height: 30px;
      border: 1px solid #ddd;
      border-radius: 4px;
    `

    const bgOpacityInput = document.createElement("input")
    bgOpacityInput.type = "range"
    bgOpacityInput.min = "0"
    bgOpacityInput.max = "100"
    bgOpacityInput.value = this.activeObject.backgroundColor ? "100" : "0"
    bgOpacityInput.style.cssText = `
      width: 100%;
      margin-top: 4px;
    `

    const applyBgButton = document.createElement("button")
    applyBgButton.textContent = "Apply Background"
    applyBgButton.style.cssText = `
      width: 100%;
      margin-top: 8px;
      padding: 6px;
      background: #f0f0f0;
      border: 1px solid #ddd;
      border-radius: 4px;
      cursor: pointer;
    `

    applyBgButton.addEventListener("click", () => {
      const opacity = Number.parseInt(bgOpacityInput.value) / 100

      if (opacity === 0) {
        this.activeObject.set("backgroundColor", null)
      } else {
        // Convert hex to rgba
        const hex = bgColorInput.value
        const r = Number.parseInt(hex.slice(1, 3), 16)
        const g = Number.parseInt(hex.slice(3, 5), 16)
        const b = Number.parseInt(hex.slice(5, 7), 16)

        this.activeObject.set("backgroundColor", `rgba(${r}, ${g}, ${b}, ${opacity})`)
      }

      this.editor.canvas.renderAll()
      this.editor.history.saveState()
      this.editor.threeDSync.syncWithThreeJs()
    })

    bgColorContainer.appendChild(bgColorLabel)
    bgColorContainer.appendChild(bgColorInput)
    bgColorContainer.appendChild(bgOpacityInput)
    bgColorContainer.appendChild(applyBgButton)

    // Text border selection color
    const borderContainer = document.createElement('div')
    borderContainer.style.marginBottom = '12px'
    
    const borderLabel = document.createElement('label')
    borderLabel.textContent = 'Selection Border Color:'
    borderLabel.style.cssText = `
      display: block;
      margin-bottom: 4px;
      font-size: 13px;
    `
    
    const borderInput = document.createElement('input')
    borderInput.type = 'color'
    borderInput.value = this.activeObject.borderColor || '#4285f4'
    borderInput.style.cssText = `
      width: 100%;
      height: 30px;
      border: 1px solid #ddd;
      border-radius: 4px;
    `
    
    borderInput.addEventListener('input', (e) => {
      this.activeObject.set('borderColor', e.target.value)
      this.editor.canvas.renderAll()
    })
    
    borderInput.addEventListener('change', () => {
      this.editor.history.saveState()
    })
    
    borderContainer.appendChild(borderLabel)
    borderContainer.appendChild(borderInput)
    
    // Add all elements to container
    container.appendChild(textContentContainer)
    container.appendChild(fontSizeContainer)
    container.appendChild(colorContainer)
    container.appendChild(alignmentContainer)
    container.appendChild(styleContainer)
    container.appendChild(shadowContainer)
    container.appendChild(strokeContainer)
    container.appendChild(bgColorContainer)
    container.appendChild(borderContainer)

    // Add container to panel
    this.panel.appendChild(container)
  }

  /**
   * Add image-specific controls
   */
  addImageControls() {
    const container = document.createElement("div")
    container.className = "image-controls"

    // Image filters
    const filtersContainer = document.createElement("div")
    filtersContainer.style.marginBottom = "12px"

    const filtersLabel = document.createElement("label")
    filtersLabel.textContent = "Image Filters:"
    filtersLabel.style.cssText = `
      display: block;
      margin-bottom: 4px;
      font-size: 13px;
    `

    const filtersButtons = document.createElement("div")
    filtersButtons.style.cssText = `
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 8px;
    `

    // Filter buttons
    const filters = [
      { name: "Grayscale", filter: new fabric.Image.filters.Grayscale() },
      { name: "Sepia", filter: new fabric.Image.filters.Sepia() },
      { name: "Invert", filter: new fabric.Image.filters.Invert() },
      { name: "Brightness", filter: new fabric.Image.filters.Brightness({ brightness: 0.1 }) },
    ]

    filters.forEach((filter) => {
      const button = document.createElement("button")
      button.textContent = filter.name
      button.style.cssText = `
        padding: 6px;
        background: #f0f0f0;
        border: 1px solid #ddd;
        border-radius: 4px;
        cursor: pointer;
      `

      button.addEventListener("click", () => {
        // Check if filter is already applied
        const filterType = filter.filter.type.toLowerCase()
        const existingFilterIndex = this.activeObject.filters?.findIndex((f) => f.type.toLowerCase() === filterType)

        if (existingFilterIndex >= 0) {
          // Remove filter
          this.activeObject.filters.splice(existingFilterIndex, 1)
          button.style.background = "#f0f0f0"
        } else {
          // Add filter
          if (!this.activeObject.filters) {
            this.activeObject.filters = []
          }
          this.activeObject.filters.push(filter.filter)
          button.style.background = "#e6f7ff"
        }

        this.activeObject.applyFilters()
        this.editor.canvas.renderAll()
        this.editor.history.saveState()
        this.editor.threeDSync.syncWithThreeJs()
      })

      filtersButtons.appendChild(button)
    })

    // Reset filters button
    const resetFiltersButton = document.createElement("button")
    resetFiltersButton.textContent = "Reset Filters"
    resetFiltersButton.style.cssText = `
      grid-column: span 2;
      padding: 6px;
      background: #f0f0f0;
      border: 1px solid #ddd;
      border-radius: 4px;
      cursor: pointer;
      margin-top: 4px;
    `

    resetFiltersButton.addEventListener("click", () => {
      this.activeObject.filters = []
      this.activeObject.applyFilters()

      // Reset all filter buttons
      filtersButtons.querySelectorAll("button").forEach((btn) => {
        if (btn !== resetFiltersButton) {
          btn.style.background = "#f0f0f0"
        }
      })

      this.editor.canvas.renderAll()
      this.editor.history.saveState()
      this.editor.threeDSync.syncWithThreeJs()
    })

    filtersButtons.appendChild(resetFiltersButton)

    filtersContainer.appendChild(filtersLabel)
    filtersContainer.appendChild(filtersButtons)

    // Crop controls (placeholder - actual implementation would be more complex)
    const cropContainer = document.createElement("div")
    cropContainer.style.marginBottom = "12px"

    const cropLabel = document.createElement("label")
    cropLabel.textContent = "Crop Image:"
    cropLabel.style.cssText = `
      display: block;
      margin-bottom: 4px;
      font-size: 13px;
    `

    const cropButton = document.createElement("button")
    cropButton.textContent = "Enter Crop Mode"
    cropButton.style.cssText = `
      width: 100%;
      padding: 6px;
      background: #f0f0f0;
      border: 1px solid #ddd;
      border-radius: 4px;
      cursor: pointer;
    `

    cropButton.addEventListener("click", () => {
      alert("Crop functionality would be implemented here")
      // In a real implementation, this would activate a crop interface
    })

    cropContainer.appendChild(cropLabel)
    cropContainer.appendChild(cropButton)

    // Add all elements to container
    container.appendChild(filtersContainer)
    container.appendChild(cropContainer)

    // Add container to panel
    this.panel.appendChild(container)
  }

  /**
   * Add group-specific controls
   */
  addGroupControls() {
    const container = document.createElement("div")
    container.className = "group-controls"

    // Ungroup button
    const ungroupButton = document.createElement("button")
    ungroupButton.innerHTML = "🔗 Ungroup"
    ungroupButton.style.cssText = `
      width: 100%;
      padding: 8px;
      background: #f0f0f0;
      border: 1px solid #ddd;
      border-radius: 4px;
      cursor: pointer;
      margin-bottom: 12px;
    `

    ungroupButton.addEventListener("click", () => {
      this.editor.ungroupSelected()
    })

    // Group info
    const groupInfoContainer = document.createElement("div")
    groupInfoContainer.style.cssText = `
      padding: 8px;
      background: #f9f9f9;
      border: 1px solid #eee;
      border-radius: 4px;
      margin-bottom: 12px;
    `

    const itemCount = this.activeObject.getObjects().length
    groupInfoContainer.innerHTML = `
      <p style="margin: 0; font-size: 13px;">This group contains ${itemCount} items.</p>
      <p style="margin: 4px 0 0; font-size: 13px; color: #666;">Ungroup to edit individual items.</p>
    `

    // Add elements to container
    container.appendChild(ungroupButton)
    container.appendChild(groupInfoContainer)

    // Add container to panel
    this.panel.appendChild(container)
  }

  /**
   * Update lock state in the UI
   */
  updateLockState(isLocked) {
    const lockBtn = this.panel.querySelector(".common-controls button:last-child")
    if (lockBtn) {
      lockBtn.innerHTML = isLocked ? "🔒 Unlock" : "🔓 Lock"
      lockBtn.style.background = isLocked ? "#e6f7ff" : "#f0f0f0"
      lockBtn.style.borderColor = isLocked ? "#91d5ff" : "#ddd"
    }
  }
}
