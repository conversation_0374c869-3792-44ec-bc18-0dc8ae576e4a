<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <title>Fabric.js Drawing & Crop</title>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
  <style>
    body { font-family: sans-serif; direction: rtl; }
    #editor-container {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
    }
    #toolbar {
      display: flex;
      gap: 10px;
      padding: 10px;
      background: #f5f5f5;
      border-bottom: 1px solid #ccc;
      justify-content: flex-end;
    }
    #toolbar button svg {
      vertical-align: middle;
    }
    #drawing-controls,
    #crop-controls {
      display: none;
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: flex-end;
      gap: 8px;
      padding: 10px;
      background-color: #fff8f1;
      border-top: 1px solid #ddd;
    }
    #drawing-controls button,
    #crop-controls button {
      background: #f1f1f1;
      border: 1px solid #ccc;
      border-radius: 4px;
      padding: 4px 8px;
      white-space: nowrap;
      cursor: pointer;
    }
    canvas {
      border: 1px solid #ccc;
    }
  </style>
</head>
<body>
<div id="editor-container">
  <div id="toolbar">
    <button id="toggle-draw">رسم</button>
    <button id="toggle-crop">قص</button>
    <button id="add-image">إضافة صورة</button>
    <button id="clear-canvas">مسح</button>
  </div>

  <canvas id="c" width="800" height="500"></canvas>

  <div id="drawing-controls">
    <button id="toggle-drawing-mode">تشغيل / إيقاف الرسم</button>
  </div>

  <div id="crop-controls">
    <button class="preset" data-ratio="1:1">‏1:1</button>
    <button class="preset" data-ratio="4:3">‏4:3</button>
    <button class="preset" data-ratio="16:9">‏16:9</button>
    <button class="preset" data-ratio="3:2">‏3:2</button>
    <button class="preset" data-ratio="2:3">‏2:3</button>
    <button class="preset" data-ratio="5:7">‏5:7</button>
    <button class="preset" data-ratio="9:16">‏9:16</button>
    <button id="free-crop">قص حر</button>
    <button id="rotate-box">دوران الإطار</button>
    <button id="rotate-slider">دوران حر</button>
    <button id="apply-crop">تطبيق القص</button>
  </div>
</div>

<script>
  const canvas = new fabric.Canvas('c', { isDrawingMode: false });
  let cropRect = null;
  let activeImage = null;

  const drawingControls = document.getElementById('drawing-controls');
  const cropControls = document.getElementById('crop-controls');

  document.getElementById('toggle-draw').onclick = () => {
    drawingControls.style.display = drawingControls.style.display === 'flex' ? 'none' : 'flex';
    cropControls.style.display = 'none';
  };

  document.getElementById('toggle-crop').onclick = () => {
    if (!canvas.getActiveObject() || canvas.getActiveObject().type !== 'image') {
      alert('يرجى تحديد صورة للقص');
      return;
    }
    activeImage = canvas.getActiveObject();
    cropControls.style.display = 'flex';
    drawingControls.style.display = 'none';

    const bounds = activeImage.getBoundingRect();
    cropRect = new fabric.Rect({
      left: bounds.left,
      top: bounds.top,
      width: bounds.width / 2,
      height: bounds.height / 2,
      fill: 'rgba(255, 255, 255, 0.3)',
      stroke: 'red',
      strokeWidth: 1,
      hasBorders: true,
      hasControls: true,
      selectable: true,
      objectCaching: false,
      transparentCorners: false,
      angle: 0,
    });
    canvas.add(cropRect);
    canvas.setActiveObject(cropRect);
  };

  document.getElementById('toggle-drawing-mode').onclick = () => {
    canvas.isDrawingMode = !canvas.isDrawingMode;
  };

  document.getElementById('clear-canvas').onclick = () => canvas.clear();

  document.getElementById('add-image').onclick = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = (e) => {
      const file = e.target.files[0];
      if (!file) return;
      const reader = new FileReader();
      reader.onload = (f) => {
        fabric.Image.fromURL(f.target.result, (img) => {
          img.set({ left: 100, top: 100 });
          canvas.add(img);
          canvas.setActiveObject(img);
        });
      };
      reader.readAsDataURL(file);
    };
    input.click();
  };

  // Apply crop button
  document.getElementById('apply-crop').onclick = () => {
    if (!cropRect || !activeImage) return;
    const rect = cropRect.getBoundingRect();
    const left = (rect.left - activeImage.left + activeImage.width * activeImage.originX) / activeImage.scaleX;
    const top = (rect.top - activeImage.top + activeImage.height * activeImage.originY) / activeImage.scaleY;
    const width = rect.width / activeImage.scaleX;
    const height = rect.height / activeImage.scaleY;

    const cropped = new Image();
    cropped.src = activeImage.toDataURL({ left, top, width, height });

    cropped.onload = () => {
      const croppedImg = new fabric.Image(cropped);
      croppedImg.set({
        left: activeImage.left,
        top: activeImage.top,
        scaleX: activeImage.scaleX,
        scaleY: activeImage.scaleY,
      });
      canvas.remove(activeImage);
      canvas.remove(cropRect);
      canvas.add(croppedImg);
      canvas.setActiveObject(croppedImg);
      cropControls.style.display = 'none';
    };
  };

  // Crop preset ratio buttons
  document.querySelectorAll('.preset').forEach(btn => {
    btn.onclick = () => {
      if (!cropRect) return;
      const [w, h] = btn.dataset.ratio.split(':').map(Number);
      const currentHeight = cropRect.height;
      cropRect.set({
        width: currentHeight * (w / h)
      });
      canvas.requestRenderAll();
    };
  });
</script>
</body>
</html>
