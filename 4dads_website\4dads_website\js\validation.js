// ملف التحقق من صحة البيانات
function validateForm() {
    const fullname = document.getElementById('fullname').value.trim();
    const email = document.getElementById('email').value.trim();
    const mobile = document.getElementById('mobile').value.trim();
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    
    // التحقق من الاسم الكامل
    if (fullname.length < 2) {
        showMessage('يجب أن يكون الاسم الكامل أكثر من حرفين', 'error');
        return false;
    }
    
    // التحقق من البريد الإلكتروني
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        showMessage('يرجى إدخال بريد إلكتروني صحيح', 'error');
        return false;
    }
    
    // التحقق من رقم الهاتف
    const mobileRegex = /^[0-9+\-\s()]{8,15}$/;
    if (!mobileRegex.test(mobile)) {
        showMessage('يرجى إدخال رقم هاتف صحيح', 'error');
        return false;
    }
    
    // التحقق من كلمة المرور
    if (password.length < 6) {
        showMessage('يجب أن تكون كلمة المرور 6 أحرف على الأقل', 'error');
        return false;
    }
    
    // التحقق من تطابق كلمة المرور
    if (password !== confirmPassword) {
        showMessage('كلمات المرور غير متطابقة', 'error');
        return false;
    }
    
    return true;
}

// عرض الرسائل
function showMessage(message, type) {
    const messageDiv = document.getElementById('formMessage');
    messageDiv.textContent = message;
    messageDiv.className = `message ${type}`;
    messageDiv.style.display = 'block';
    
    // إخفاء الرسالة بعد 5 ثوان
    setTimeout(() => {
        messageDiv.style.display = 'none';
    }, 5000);
}

// التحقق من قوة كلمة المرور
function checkPasswordStrength(password) {
    let strength = 0;
    
    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    
    return strength;
}

// إضافة مؤشر قوة كلمة المرور
document.addEventListener('DOMContentLoaded', function() {
    const passwordInput = document.getElementById('password');
    
    if (passwordInput) {
        passwordInput.addEventListener('input', function() {
            const strength = checkPasswordStrength(this.value);
            const strengthText = ['ضعيفة جداً', 'ضعيفة', 'متوسطة', 'قوية', 'قوية جداً'];
            const strengthColors = ['#e74c3c', '#e67e22', '#f39c12', '#27ae60', '#2ecc71'];
            
            // يمكن إضافة مؤشر بصري هنا
            console.log(`قوة كلمة المرور: ${strengthText[strength - 1] || 'ضعيفة جداً'}`);
        });
    }
});

