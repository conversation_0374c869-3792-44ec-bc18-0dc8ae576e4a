import { But<PERSON> } from "@/components/ui/button"
import { Type, Upload, AlignCenter, Trash, Copy, Square, Grid } from "lucide-react"

export function TextEditorToolbar() {
  return (
    <div className="flex items-center gap-1 rounded-md bg-white/80 p-2 backdrop-blur-sm">
      <Button variant="ghost" size="icon" className="h-8 w-8 text-[#1F2A45]">
        <Type className="h-4 w-4" />
      </Button>
      <Button variant="ghost" size="icon" className="h-8 w-8 text-[#1F2A45]">
        <Upload className="h-4 w-4" />
      </Button>
      <Button variant="ghost" size="icon" className="h-8 w-8 text-[#1F2A45]">
        <AlignCenter className="h-4 w-4" />
      </Button>
      <Button variant="ghost" size="icon" className="h-8 w-8 text-[#1F2A45]">
        <Trash className="h-4 w-4" />
      </Button>
      <Button variant="ghost" size="icon" className="h-8 w-8 text-[#1F2A45]">
        <Copy className="h-4 w-4" />
      </Button>
      <Button variant="ghost" size="icon" className="h-8 w-8 text-[#1F2A45]">
        <Square className="h-4 w-4" />
      </Button>
      <Button variant="ghost" size="icon" className="h-8 w-8 text-[#1F2A45]">
        <Grid className="h-4 w-4" />
      </Button>
    </div>
  )
}
