<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fabric.js Editor</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.1/fabric.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/hammer.js/2.0.8/hammer.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #4a6bff;
            --secondary: #f8f9fa;
            --dark: #343a40;
            --light: #ffffff;
            --border: #dee2e6;
            --shadow: rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            height: 100vh;
            display: flex;
            flex-direction: column;
            background-color: var(--secondary);
            color: var(--dark);
        }

        .header {
            background-color: var(--light);
            padding: 0.5rem 1rem;
            box-shadow: 0 2px 4px var(--shadow);
            z-index: 10;
        }

        .main-toolbar {
            display: flex;
            overflow-x: auto;
            gap: 0.5rem;
            padding: 0.5rem 0;
        }

        .tool-btn {
            background: none;
            border: none;
            padding: 0.5rem;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 60px;
            transition: all 0.2s;
            position: relative;
        }

        .tool-btn:hover {
            background-color: rgba(74, 107, 255, 0.1);
        }

        .tool-btn.active {
            background-color: rgba(74, 107, 255, 0.2);
            color: var(--primary);
        }

        .tool-btn i {
            font-size: 1.2rem;
            margin-bottom: 0.2rem;
        }

        .tool-btn span {
            font-size: 0.7rem;
        }

        .sub-toolbar {
            background-color: var(--light);
            padding: 0.5rem 1rem;
            box-shadow: 0 2px 4px var(--shadow);
            display: flex;
            overflow-x: auto;
            gap: 0.5rem;
            z-index: 9;
        }

        .canvas-container {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            position: relative;
            background-color: #f0f0f0;
            background-image: 
                linear-gradient(45deg, #e5e5e5 25%, transparent 25%),
                linear-gradient(-45deg, #e5e5e5 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, #e5e5e5 75%),
                linear-gradient(-45deg, transparent 75%, #e5e5e5 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }

        #canvas {
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            background-color: white;
            touch-action: none;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 100;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background-color: var(--light);
            padding: 1rem;
            border-radius: 8px;
            max-width: 90%;
            max-height: 90%;
            overflow: auto;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--border);
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--dark);
        }

        .png-library {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: 1rem;
        }

        .png-item {
            cursor: pointer;
            border: 1px solid var(--border);
            border-radius: 4px;
            padding: 0.5rem;
            transition: transform 0.2s;
        }

        .png-item:hover {
            transform: scale(1.05);
        }

        .png-item img {
            width: 100%;
            height: auto;
        }

        .tooltip {
            position: absolute;
            bottom: -30px;
            left: 50%;
            transform: translateX(-50%);
            background-color: var(--dark);
            color: var(--light);
            padding: 0.3rem 0.6rem;
            border-radius: 4px;
            font-size: 0.8rem;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.2s;
        }

        .tool-btn:hover .tooltip {
            opacity: 1;
        }

        .color-picker {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .color-preview {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            border: 1px solid var(--border);
        }

        .slider-container {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            min-width: 150px;
        }

        .slider-container input[type="range"] {
            flex: 1;
        }

        .slider-container span {
            min-width: 30px;
            text-align: center;
        }

        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            background-color: var(--light);
            min-width: 160px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
            z-index: 1;
            border-radius: 4px;
            padding: 0.5rem;
            top: 100%;
            left: 0;
        }

        .dropdown:hover .dropdown-content {
            display: block;
        }

        .dropdown-item {
            padding: 0.5rem;
            cursor: pointer;
            border-radius: 4px;
        }

        .dropdown-item:hover {
            background-color: rgba(74, 107, 255, 0.1);
        }

        .grid-toggle {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: white;
            padding: 5px;
            border-radius: 4px;
            cursor: pointer;
            z-index: 5;
        }

        @media (max-width: 768px) {
            .tool-btn {
                min-width: 50px;
                padding: 0.3rem;
            }

            .tool-btn i {
                font-size: 1rem;
            }

            .tool-btn span {
                font-size: 0.6rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="main-toolbar">
            <button class="tool-btn" data-category="select">
                <i class="fas fa-mouse-pointer"></i>
                <span>Select</span>
                <span class="tooltip">Select objects</span>
            </button>
            <button class="tool-btn" data-category="draw">
                <i class="fas fa-pencil-alt"></i>
                <span>Draw</span>
                <span class="tooltip">Drawing tools</span>
            </button>
            <button class="tool-btn" data-category="shapes">
                <i class="fas fa-shapes"></i>
                <span>Shapes</span>
                <span class="tooltip">Add shapes</span>
            </button>
            <button class="tool-btn" data-category="text">
                <i class="fas fa-font"></i>
                <span>Text</span>
                <span class="tooltip">Add text</span>
            </button>
            <button class="tool-btn" data-category="images">
                <i class="fas fa-image"></i>
                <span>Images</span>
                <span class="tooltip">Image tools</span>
            </button>
            <button class="tool-btn" data-category="crop">
                <i class="fas fa-crop"></i>
                <span>Crop</span>
                <span class="tooltip">Crop image</span>
            </button>
            <button class="tool-btn" data-category="effects">
                <i class="fas fa-magic"></i>
                <span>Effects</span>
                <span class="tooltip">Image effects</span>
            </button>
            <button class="tool-btn" data-category="library">
                <i class="fas fa-icons"></i>
                <span>Library</span>
                <span class="tooltip">PNG library</span>
            </button>
            <button class="tool-btn" data-category="view">
                <i class="fas fa-eye"></i>
                <span>View</span>
                <span class="tooltip">View options</span>
            </button>
            <button class="tool-btn" data-category="export">
                <i class="fas fa-download"></i>
                <span>Export</span>
                <span class="tooltip">Export options</span>
            </button>
        </div>
    </header>

    <div class="sub-toolbar" id="subToolbar">
        <!-- Dynamic content will be loaded here -->
    </div>

    <div class="canvas-container">
        <canvas id="canvas" width="800" height="600"></canvas>
        <button class="grid-toggle" id="toggleGrid">
            <i class="fas fa-th"></i> Grid
        </button>
    </div>

    <!-- PNG Library Modal -->
    <div class="modal" id="pngLibraryModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>PNG Library</h3>
                <button class="modal-close" id="closePngLibrary">&times;</button>
            </div>
            <div class="png-library" id="pngLibraryGrid">
                <!-- PNG items will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Export Modal -->
    <div class="modal" id="exportModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Export Options</h3>
                <button class="modal-close" id="closeExportModal">&times;</button>
            </div>
            <div style="display: flex; flex-direction: column; gap: 1rem;">
                <button id="exportPNG" class="tool-btn">
                    <i class="fas fa-file-image"></i>
                    <span>Export as PNG</span>
                </button>
                <button id="exportJPG" class="tool-btn">
                    <i class="fas fa-file-image"></i>
                    <span>Export as JPG</span>
                </button>
                <button id="exportSVG" class="tool-btn">
                    <i class="fas fa-file-code"></i>
                    <span>Export as SVG</span>
                </button>
                <div class="slider-container">
                    <label for="exportScale">Scale:</label>
                    <input type="range" id="exportScale" min="0.5" max="3" step="0.1" value="1">
                    <span id="exportScaleValue">1x</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Hidden file inputs -->
    <input type="file" id="imageUpload" accept="image/*" multiple style="display: none;">
    <input type="file" id="svgUpload" accept=".svg" style="display: none;">

    <script>
        // Initialize Fabric.js canvas
        const canvas = new fabric.Canvas('canvas', {
            preserveObjectStacking: true,
            backgroundColor: 'white',
            selection: true,
            selectionColor: 'rgba(74, 107, 255, 0.3)',
            selectionBorderColor: '#4a6bff',
            selectionLineWidth: 1,
            selectionDashArray: [5, 5],
            selectionFullyContained: false
        });

        // Set canvas dimensions based on container
        function resizeCanvas() {
            const container = document.querySelector('.canvas-container');
            canvas.setWidth(container.clientWidth * 0.9);
            canvas.setHeight(container.clientHeight * 0.9);
            canvas.renderAll();
        }

        window.addEventListener('resize', resizeCanvas);
        resizeCanvas();

        // Initialize Hammer.js for touch gestures
        const canvasElement = document.getElementById('canvas');
        const hammer = new Hammer(canvasElement);

        // Variables to track state
        let currentTool = 'select';
        let isDrawing = false;
        let currentColor = '#000000';
        let currentBrushSize = 5;
        let currentFontFamily = 'Arial';
        let currentFontSize = 20;
        let currentOpacity = 1;
        let undoStack = [];
        let redoStack = [];
        let maxUndoSteps = 20;
        let isGridVisible = false;
        let isSnappingEnabled = false;
        let cropRect = null;
        let drawingPath = null;
        let currentFilter = null;
        let currentFilterValue = 100;

        // Track canvas state for undo/redo
        function saveState() {
            if (undoStack.length >= maxUndoSteps) {
                undoStack.shift();
            }
            undoStack.push(JSON.stringify(canvas));
            redoStack = [];
            updateUndoRedoButtons();
        }

        // Initialize with empty state
        saveState();

        // Undo/redo functionality
        function undo() {
            if (undoStack.length > 1) {
                redoStack.push(undoStack.pop());
                canvas.loadFromJSON(undoStack[undoStack.length - 1], () => {
                    canvas.renderAll();
                });
                updateUndoRedoButtons();
            }
        }

        function redo() {
            if (redoStack.length > 0) {
                const state = redoStack.pop();
                undoStack.push(state);
                canvas.loadFromJSON(state, () => {
                    canvas.renderAll();
                });
                updateUndoRedoButtons();
            }
        }

        function updateUndoRedoButtons() {
            const undoBtn = document.getElementById('undoBtn');
            const redoBtn = document.getElementById('redoBtn');
            if (undoBtn) undoBtn.disabled = undoStack.length <= 1;
            if (redoBtn) redoBtn.disabled = redoStack.length === 0;
        }

        // Setup event listeners for canvas changes
        canvas.on('object:added', () => saveState());
        canvas.on('object:modified', () => saveState());
        canvas.on('object:removed', () => saveState());

        // Tool categories and their sub-tools
        const toolCategories = {
            select: {
                name: 'Select',
                tools: [
                    { id: 'select', icon: 'mouse-pointer', label: 'Select', action: () => setCurrentTool('select') },
                    { id: 'moveUp', icon: 'arrow-up', label: 'Bring Forward', action: () => bringForward() },
                    { id: 'moveDown', icon: 'arrow-down', label: 'Send Backward', action: () => sendBackwards() },
                    { id: 'moveTop', icon: 'arrow-up', iconStyle: 'solid', label: 'Bring to Front', action: () => bringToFront() },
                    { id: 'moveBottom', icon: 'arrow-down', iconStyle: 'solid', label: 'Send to Back', action: () => sendToBack() },
                    { id: 'group', icon: 'object-group', label: 'Group', action: () => groupSelected() },
                    { id: 'ungroup', icon: 'object-ungroup', label: 'Ungroup', action: () => ungroupSelected() },
                    { id: 'delete', icon: 'trash-alt', label: 'Delete', action: () => deleteSelected() },
                    { id: 'duplicate', icon: 'clone', label: 'Duplicate', action: () => duplicateSelected() },
                    { id: 'lock', icon: 'lock', label: 'Lock', action: () => toggleLock() },
                    { id: 'alignLeft', icon: 'align-left', label: 'Align Left', action: () => alignObjects('left') },
                    { id: 'alignCenter', icon: 'align-center', label: 'Align Center', action: () => alignObjects('center') },
                    { id: 'alignRight', icon: 'align-right', label: 'Align Right', action: () => alignObjects('right') },
                    { id: 'alignTop', icon: 'align-top', label: 'Align Top', action: () => alignObjects('top') },
                    { id: 'alignMiddle', icon: 'align-middle', label: 'Align Middle', action: () => alignObjects('middle') },
                    { id: 'alignBottom', icon: 'align-bottom', label: 'Align Bottom', action: () => alignObjects('bottom') },
                    { id: 'undoBtn', icon: 'undo', label: 'Undo', action: () => undo() },
                    { id: 'redoBtn', icon: 'redo', label: 'Redo', action: () => redo() },
                    { id: 'toggleSnap', icon: 'magnet', label: 'Snap to Grid', action: () => toggleSnapping() }
                ]
            },
            draw: {
                name: 'Draw',
                tools: [
                    { id: 'pencil', icon: 'pencil-alt', label: 'Pencil', action: () => setCurrentTool('pencil') },
                    { id: 'brush', icon: 'paint-brush', label: 'Brush', action: () => setCurrentTool('brush') },
                    { id: 'marker', icon: 'highlighter', label: 'Marker', action: () => setCurrentTool('marker') },
                    { id: 'spray', icon: 'spray-can', label: 'Spray', action: () => setCurrentTool('spray') },
                    { id: 'dotted', icon: 'ellipsis-h', label: 'Dotted', action: () => setCurrentTool('dotted') },
                    { id: 'eraser', icon: 'eraser', label: 'Eraser', action: () => setCurrentTool('eraser') },
                    { 
                        id: 'colorPicker', 
                        icon: 'palette', 
                        label: 'Color', 
                        html: `
                            <div class="color-picker">
                                <input type="color" id="drawColor" value="${currentColor}" style="width: 30px; height: 30px;">
                                <div class="color-preview" style="background-color: ${currentColor};"></div>
                            </div>
                        `,
                        action: null
                    },
                    {
                        id: 'brushSize',
                        icon: 'ruler',
                        label: 'Size',
                        html: `
                            <div class="slider-container">
                                <input type="range" id="brushSizeInput" min="1" max="50" value="${currentBrushSize}">
                                <span id="brushSizeValue">${currentBrushSize}px</span>
                            </div>
                        `,
                        action: null
                    },
                    {
                        id: 'opacity',
                        icon: 'tint',
                        label: 'Opacity',
                        html: `
                            <div class="slider-container">
                                <input type="range" id="opacityInput" min="0.1" max="1" step="0.1" value="${currentOpacity}">
                                <span id="opacityValue">${Math.round(currentOpacity * 100)}%</span>
                            </div>
                        `,
                        action: null
                    }
                ]
            },
            shapes: {
                name: 'Shapes',
                tools: [
                    { id: 'rectangle', icon: 'square', label: 'Rectangle', action: () => setCurrentTool('rectangle') },
                    { id: 'circle', icon: 'circle', label: 'Circle', action: () => setCurrentTool('circle') },
                    { id: 'triangle', icon: 'play', label: 'Triangle', action: () => setCurrentTool('triangle') },
                    { id: 'ellipse', icon: 'ellipsis-h', label: 'Ellipse', action: () => setCurrentTool('ellipse') },
                    { id: 'line', icon: 'minus', label: 'Line', action: () => setCurrentTool('line') },
                    { id: 'polygon', icon: 'draw-polygon', label: 'Polygon', action: () => setCurrentTool('polygon') },
                    { id: 'polyline', icon: 'draw-polygon', iconStyle: 'solid', label: 'Polyline', action: () => setCurrentTool('polyline') },
                    { 
                        id: 'shapeColor', 
                        icon: 'palette', 
                        label: 'Fill', 
                        html: `
                            <div class="color-picker">
                                <input type="color" id="shapeFillColor" value="${currentColor}" style="width: 30px; height: 30px;">
                                <div class="color-preview" style="background-color: ${currentColor};"></div>
                            </div>
                        `,
                        action: null
                    },
                    { 
                        id: 'shapeStrokeColor', 
                        icon: 'pen', 
                        label: 'Stroke', 
                        html: `
                            <div class="color-picker">
                                <input type="color" id="shapeStrokeColorInput" value="#000000" style="width: 30px; height: 30px;">
                                <div class="color-preview" style="background-color: #000000;"></div>
                            </div>
                        `,
                        action: null
                    },
                    {
                        id: 'shapeStrokeWidth',
                        icon: 'border-style',
                        label: 'Stroke Width',
                        html: `
                            <div class="slider-container">
                                <input type="range" id="shapeStrokeWidthInput" min="1" max="20" value="1">
                                <span id="shapeStrokeWidthValue">1px</span>
                            </div>
                        `,
                        action: null
                    }
                ]
            },
            text: {
                name: 'Text',
                tools: [
                    { id: 'text', icon: 'font', label: 'Add Text', action: () => addText() },
                    { id: 'textbox', icon: 'paragraph', label: 'Text Box', action: () => addTextBox() },
                    {
                        id: 'fontFamily',
                        icon: 'font',
                        label: 'Font',
                        html: `
                            <select id="fontFamilySelect" style="padding: 0.3rem; border-radius: 4px; border: 1px solid var(--border);">
                                <option value="Arial">Arial</option>
                                <option value="Verdana">Verdana</option>
                                <option value="Helvetica">Helvetica</option>
                                <option value="Times New Roman">Times New Roman</option>
                                <option value="Courier New">Courier New</option>
                                <option value="Georgia">Georgia</option>
                                <option value="Palatino">Palatino</option>
                                <option value="Garamond">Garamond</option>
                                <option value="Comic Sans MS">Comic Sans</option>
                                <option value="Arial Unicode MS">Arial Unicode</option>
                                <option value="Tahoma">Tahoma</option>
                                <option value="Impact">Impact</option>
                                <option value="Lucida Console">Lucida Console</option>
                                <option value="Traditional Arabic">Arabic</option>
                                <option value="Arial Arabic">Arial Arabic</option>
                            </select>
                        `,
                        action: null
                    },
                    {
                        id: 'fontSize',
                        icon: 'text-height',
                        label: 'Size',
                        html: `
                            <div class="slider-container">
                                <input type="range" id="fontSizeInput" min="8" max="72" value="${currentFontSize}">
                                <span id="fontSizeValue">${currentFontSize}px</span>
                            </div>
                        `,
                        action: null
                    },
                    { 
                        id: 'textColor', 
                        icon: 'palette', 
                        label: 'Color', 
                        html: `
                            <div class="color-picker">
                                <input type="color" id="textColorInput" value="${currentColor}" style="width: 30px; height: 30px;">
                                <div class="color-preview" style="background-color: ${currentColor};"></div>
                            </div>
                        `,
                        action: null
                    },
                    { 
                        id: 'textBgColor', 
                        icon: 'fill-drip', 
                        label: 'Background', 
                        html: `
                            <div class="color-picker">
                                <input type="color" id="textBgColorInput" value="#ffffff" style="width: 30px; height: 30px;">
                                <div class="color-preview" style="background-color: #ffffff;"></div>
                            </div>
                        `,
                        action: null
                    },
                    { 
                        id: 'textStrokeColor', 
                        icon: 'pen', 
                        label: 'Outline', 
                        html: `
                            <div class="color-picker">
                                <input type="color" id="textStrokeColorInput" value="#000000" style="width: 30px; height: 30px;">
                                <div class="color-preview" style="background-color: #000000;"></div>
                            </div>
                        `,
                        action: null
                    },
                    {
                        id: 'textStrokeWidth',
                        icon: 'border-style',
                        label: 'Outline Width',
                        html: `
                            <div class="slider-container">
                                <input type="range" id="textStrokeWidthInput" min="0" max="10" value="0">
                                <span id="textStrokeWidthValue">0px</span>
                            </div>
                        `,
                        action: null
                    },
                    { id: 'bold', icon: 'bold', label: 'Bold', action: () => toggleTextStyle('bold') },
                    { id: 'italic', icon: 'italic', label: 'Italic', action: () => toggleTextStyle('italic') },
                    { id: 'underline', icon: 'underline', label: 'Underline', action: () => toggleTextStyle('underline') },
                    { id: 'alignLeft', icon: 'align-left', label: 'Align Left', action: () => setTextAlign('left') },
                    { id: 'alignCenter', icon: 'align-center', label: 'Align Center', action: () => setTextAlign('center') },
                    { id: 'alignRight', icon: 'align-right', label: 'Align Right', action: () => setTextAlign('right') },
                    { id: 'alignJustify', icon: 'align-justify', label: 'Justify', action: () => setTextAlign('justify') }
                ]
            },
            images: {
                name: 'Images',
                tools: [
                    { id: 'uploadImage', icon: 'upload', label: 'Upload', action: () => document.getElementById('imageUpload').click() },
                    { id: 'uploadSVG', icon: 'file-image', label: 'SVG', action: () => document.getElementById('svgUpload').click() },
                    { id: 'flipH', icon: 'exchange-alt', label: 'Flip H', action: () => flipSelected('horizontal') },
                    { id: 'flipV', icon: 'exchange-alt', iconStyle: 'solid', label: 'Flip V', action: () => flipSelected('vertical') },
                    { id: 'imageBorder', icon: 'square', label: 'Border', action: () => toggleImageBorder() },
                    { id: 'imageShadow', icon: 'shadow', label: 'Shadow', action: () => toggleImageShadow() },
                    { 
                        id: 'imageBorderColor', 
                        icon: 'palette', 
                        label: 'Border Color', 
                        html: `
                            <div class="color-picker">
                                <input type="color" id="imageBorderColorInput" value="#000000" style="width: 30px; height: 30px;">
                                <div class="color-preview" style="background-color: #000000;"></div>
                            </div>
                        `,
                        action: null
                    },
                    {
                        id: 'imageBorderWidth',
                        icon: 'border-style',
                        label: 'Border Width',
                        html: `
                            <div class="slider-container">
                                <input type="range" id="imageBorderWidthInput" min="1" max="20" value="1">
                                <span id="imageBorderWidthValue">1px</span>
                            </div>
                        `,
                        action: null
                    }
                ]
            },
            crop: {
                name: 'Crop',
                tools: [
                    { id: 'crop', icon: 'crop', label: 'Crop', action: () => startCrop() },
                    { id: 'cropApply', icon: 'check', label: 'Apply', action: () => applyCrop() },
                    { id: 'cropCancel', icon: 'times', label: 'Cancel', action: () => cancelCrop() },
                    { id: 'ratioFree', icon: 'expand', label: 'Free', action: () => setCropRatio(0) },
                    { id: 'ratio1_1', icon: 'square', label: '1:1', action: () => setCropRatio(1) },
                    { id: 'ratio4_3', icon: 'rectangle-landscape', label: '4:3', action: () => setCropRatio(4/3) },
                    { id: 'ratio16_9', icon: 'desktop', label: '16:9', action: () => setCropRatio(16/9) },
                    { id: 'ratioA4', icon: 'file-alt', label: 'A4', action: () => setCropRatio(210/297) },
                    { id: 'ratioA5', icon: 'file-alt', label: 'A5', action: () => setCropRatio(148/210) },
                    { id: 'ratioA6', icon: 'file-alt', label: 'A6', action: () => setCropRatio(105/148) },
                    { id: 'ratioInstagram', icon: 'instagram', label: 'Instagram', action: () => setCropRatio(1.91) },
                    { id: 'ratioFacebook', icon: 'facebook', label: 'Facebook', action: () => setCropRatio(1.91) },
                    { id: 'ratioTwitter', icon: 'twitter', label: 'Twitter', action: () => setCropRatio(16/9) },
                    { id: 'ratioPinterest', icon: 'pinterest', label: 'Pinterest', action: () => setCropRatio(2/3) },
                    { id: 'ratioTikTok', icon: 'music', label: 'TikTok', action: () => setCropRatio(9/16) }
                ]
            },
            effects: {
                name: 'Effects',
                tools: [
                    { id: 'brightness', icon: 'sun', label: 'Brightness', action: () => applyFilter('brightness') },
                    { id: 'contrast', icon: 'adjust', label: 'Contrast', action: () => applyFilter('contrast') },
                    { id: 'saturation', icon: 'tint', label: 'Saturation', action: () => applyFilter('saturation') },
                    { id: 'blur', icon: 'blur', label: 'Blur', action: () => applyFilter('blur') },
                    { id: 'grayscale', icon: 'moon', label: 'Grayscale', action: () => applyFilter('grayscale') },
                    { id: 'blackWhite', icon: 'adjust', iconStyle: 'solid', label: 'Black & White', action: () => applyFilter('blackWhite') },
                    { id: 'sharpen', icon: 'cut', label: 'Sharpen', action: () => applyFilter('sharpen') },
                    { id: 'invert', icon: 'exchange-alt', label: 'Invert', action: () => applyFilter('invert') },
                    { id: 'removeFilter', icon: 'ban', label: 'Remove Filter', action: () => removeFilter() },
                    {
                        id: 'filterValue',
                        icon: 'sliders-h',
                        label: 'Filter Value',
                        html: `
                            <div class="slider-container">
                                <input type="range" id="filterValueInput" min="0" max="200" value="100">
                                <span id="filterValueValue">100%</span>
                            </div>
                        `,
                        action: null
                    }
                ]
            },
            library: {
                name: 'Library',
                tools: [
                    { id: 'openLibrary', icon: 'folder-open', label: 'Open Library', action: () => openPngLibrary() },
                    { id: 'addToLibrary', icon: 'plus-circle', label: 'Add to Library', action: () => addToLibrary() }
                ]
            },
            view: {
                name: 'View',
                tools: [
                    { id: 'zoomIn', icon: 'search-plus', label: 'Zoom In', action: () => zoomCanvas(0.1) },
                    { id: 'zoomOut', icon: 'search-minus', label: 'Zoom Out', action: () => zoomCanvas(-0.1) },
                    { id: 'zoomReset', icon: 'expand-arrows-alt', label: 'Reset Zoom', action: () => resetZoom() },
                    { id: 'toggleGrid', icon: 'th', label: 'Toggle Grid', action: () => toggleGrid() },
                    { id: 'clearCanvas', icon: 'trash', label: 'Clear Canvas', action: () => clearCanvas() }
                ]
            },
            export: {
                name: 'Export',
                tools: [
                    { id: 'openExport', icon: 'file-export', label: 'Export Options', action: () => openExportModal() }
                ]
            }
        };

        // Initialize the UI
        function initUI() {
            // Set up main toolbar buttons
            const mainToolbar = document.querySelector('.main-toolbar');
            Object.keys(toolCategories).forEach(category => {
                const btn = document.querySelector(`[data-category="${category}"]`);
                if (btn) {
                    btn.addEventListener('click', () => {
                        document.querySelectorAll('.main-toolbar .tool-btn').forEach(b => b.classList.remove('active'));
                        btn.classList.add('active');
                        loadSubTools(category);
                    });
                }
            });

            // Set default active tool
            document.querySelector('[data-category="select"]').click();

            // Set up other UI elements
            setupEventListeners();
            setupHammerGestures();
            loadPngLibrary();
        }

        // Load sub-tools for a category
        function loadSubTools(category) {
            const subToolbar = document.getElementById('subToolbar');
            subToolbar.innerHTML = '';

            if (toolCategories[category]) {
                toolCategories[category].tools.forEach(tool => {
                    const toolElement = document.createElement('button');
                    toolElement.className = 'tool-btn';
                    toolElement.innerHTML = `
                        <i class="fa${tool.iconStyle || 'r'} fa-${tool.icon}"></i>
                        <span>${tool.label}</span>
                        <span class="tooltip">${tool.label}</span>
                    `;
                    if (tool.html) {
                        toolElement.innerHTML += tool.html;
                    }
                    toolElement.addEventListener('click', (e) => {
                        if (tool.action) {
                            tool.action();
                        }
                        e.stopPropagation();
                    });
                    subToolbar.appendChild(toolElement);
                });

                // Update any dynamic values
                updateToolValues();
            }
        }

        // Update tool values from state
        function updateToolValues() {
            // Drawing tools
            const drawColorInput = document.getElementById('drawColor');
            if (drawColorInput) {
                drawColorInput.value = currentColor;
                drawColorInput.addEventListener('input', (e) => {
                    currentColor = e.target.value;
                    document.querySelector('.color-picker .color-preview').style.backgroundColor = currentColor;
                });
            }

            const brushSizeInput = document.getElementById('brushSizeInput');
            if (brushSizeInput) {
                brushSizeInput.value = currentBrushSize;
                brushSizeInput.addEventListener('input', (e) => {
                    currentBrushSize = e.target.value;
                    document.getElementById('brushSizeValue').textContent = `${currentBrushSize}px`;
                });
            }

            const opacityInput = document.getElementById('opacityInput');
            if (opacityInput) {
                opacityInput.value = currentOpacity;
                opacityInput.addEventListener('input', (e) => {
                    currentOpacity = e.target.value;
                    document.getElementById('opacityValue').textContent = `${Math.round(currentOpacity * 100)}%`;
                });
            }

            // Shape tools
            const shapeFillColor = document.getElementById('shapeFillColor');
            if (shapeFillColor) {
                shapeFillColor.value = currentColor;
                shapeFillColor.addEventListener('input', (e) => {
                    currentColor = e.target.value;
                    document.querySelectorAll('.color-picker .color-preview')[1].style.backgroundColor = currentColor;
                });
            }

            const shapeStrokeColor = document.getElementById('shapeStrokeColorInput');
            if (shapeStrokeColor) {
                shapeStrokeColor.addEventListener('input', (e) => {
                    document.querySelectorAll('.color-picker .color-preview')[2].style.backgroundColor = e.target.value;
                });
            }

            const shapeStrokeWidth = document.getElementById('shapeStrokeWidthInput');
            if (shapeStrokeWidth) {
                shapeStrokeWidth.addEventListener('input', (e) => {
                    document.getElementById('shapeStrokeWidthValue').textContent = `${e.target.value}px`;
                });
            }

            // Text tools
            const fontFamilySelect = document.getElementById('fontFamilySelect');
            if (fontFamilySelect) {
                fontFamilySelect.value = currentFontFamily;
                fontFamilySelect.addEventListener('change', (e) => {
                    currentFontFamily = e.target.value;
                });
            }

            const fontSizeInput = document.getElementById('fontSizeInput');
            if (fontSizeInput) {
                fontSizeInput.value = currentFontSize;
                fontSizeInput.addEventListener('input', (e) => {
                    currentFontSize = e.target.value;
                    document.getElementById('fontSizeValue').textContent = `${currentFontSize}px`;
                });
            }

            const textColorInput = document.getElementById('textColorInput');
            if (textColorInput) {
                textColorInput.value = currentColor;
                textColorInput.addEventListener('input', (e) => {
                    document.querySelectorAll('.color-picker .color-preview')[3].style.backgroundColor = e.target.value;
                });
            }

            const textStrokeWidth = document.getElementById('textStrokeWidthInput');
            if (textStrokeWidth) {
                textStrokeWidth.addEventListener('input', (e) => {
                    document.getElementById('textStrokeWidthValue').textContent = `${e.target.value}px`;
                });
            }

            // Image tools
            const imageBorderWidth = document.getElementById('imageBorderWidthInput');
            if (imageBorderWidth) {
                imageBorderWidth.addEventListener('input', (e) => {
                    document.getElementById('imageBorderWidthValue').textContent = `${e.target.value}px`;
                });
            }

            // Filter tools
            const filterValueInput = document.getElementById('filterValueInput');
            if (filterValueInput) {
                filterValueInput.addEventListener('input', (e) => {
                    currentFilterValue = e.target.value;
                    document.getElementById('filterValueValue').textContent = `${currentFilterValue}%`;
                    updateFilter();
                });
            }

            // Export tools
            const exportScale = document.getElementById('exportScale');
            if (exportScale) {
                exportScale.addEventListener('input', (e) => {
                    document.getElementById('exportScaleValue').textContent = `${e.target.value}x`;
                });
            }
        }

        // Set up event listeners
        function setupEventListeners() {
            // Canvas events
            canvas.on('mouse:down', (options) => {
                if (currentTool === 'select') return;

                isDrawing = true;
                const pointer = canvas.getPointer(options.e);
                
                switch (currentTool) {
                    case 'pencil':
                    case 'brush':
                    case 'marker':
                    case 'spray':
                    case 'dotted':
                    case 'eraser':
                        startDrawing(pointer);
                        break;
                    case 'rectangle':
                        addRectangle(pointer);
                        break;
                    case 'circle':
                        addCircle(pointer);
                        break;
                    case 'triangle':
                        addTriangle(pointer);
                        break;
                    case 'ellipse':
                        addEllipse(pointer);
                        break;
                    case 'line':
                        addLine(pointer);
                        break;
                    case 'polygon':
                        addPolygon(pointer);
                        break;
                    case 'polyline':
                        addPolyline(pointer);
                        break;
                }
            });

            canvas.on('mouse:move', (options) => {
                if (!isDrawing) return;
                const pointer = canvas.getPointer(options.e);

                switch (currentTool) {
                    case 'pencil':
                    case 'brush':
                    case 'marker':
                    case 'spray':
                    case 'dotted':
                    case 'eraser':
                        continueDrawing(pointer);
                        break;
                    case 'rectangle':
                    case 'circle':
                    case 'triangle':
                    case 'ellipse':
                    case 'line':
                        resizeShape(pointer);
                        break;
                }
            });

            canvas.on('mouse:up', () => {
                if (isDrawing) {
                    isDrawing = false;
                    if (drawingPath) {
                        canvas.renderAll();
                        drawingPath = null;
                    }
                }
            });

            // Image upload
            document.getElementById('imageUpload').addEventListener('change', function(e) {
                const files = e.target.files;
                for (let i = 0; i < files.length; i++) {
                    const file = files[i];
                    if (file.type.match('image.*')) {
                        const reader = new FileReader();
                        reader.onload = function(f) {
                            fabric.Image.fromURL(f.target.result, function(img) {
                                img.set({
                                    left: 100 + (i * 20),
                                    top: 100 + (i * 20),
                                    angle: 0,
                                    padding: 10,
                                    cornerSize: 20,
                                    hasRotatingPoint: true
                                });
                                canvas.add(img);
                                canvas.setActiveObject(img);
                                canvas.renderAll();
                            });
                        };
                        reader.readAsDataURL(file);
                    }
                }
                this.value = '';
            });

            // SVG upload
            document.getElementById('svgUpload').addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file && file.type.match('svg.*')) {
                    const reader = new FileReader();
                    reader.onload = function(f) {
                        fabric.loadSVGFromURL(f.target.result, function(objects, options) {
                            const svg = fabric.util.groupSVGElements(objects, options);
                            svg.set({
                                left: 100,
                                top: 100,
                                angle: 0,
                                padding: 10,
                                cornerSize: 20,
                                hasRotatingPoint: true
                            });
                            canvas.add(svg);
                            canvas.setActiveObject(svg);
                            canvas.renderAll();
                        });
                    };
                    reader.readAsDataURL(file);
                }
                this.value = '';
            });

            // PNG library
            document.getElementById('closePngLibrary').addEventListener('click', () => {
                document.getElementById('pngLibraryModal').style.display = 'none';
            });

            // Export modal
            document.getElementById('closeExportModal').addEventListener('click', () => {
                document.getElementById('exportModal').style.display = 'none';
            });

            document.getElementById('exportPNG').addEventListener('click', () => {
                exportCanvas('png');
            });

            document.getElementById('exportJPG').addEventListener('click', () => {
                exportCanvas('jpeg');
            });

            document.getElementById('exportSVG').addEventListener('click', () => {
                exportCanvas('svg');
            });

            // Grid toggle
            document.getElementById('toggleGrid').addEventListener('click', toggleGrid);
        }

        // Set up Hammer.js gestures
        function setupHammerGestures() {
            // Pan
            hammer.get('pan').set({ direction: Hammer.DIRECTION_ALL });
            hammer.on('pan', (e) => {
                if (currentTool === 'select') {
                    const delta = new fabric.Point(e.deltaX, e.deltaY);
                    canvas.relativePan(delta);
                }
            });

            // Pinch to zoom
            hammer.get('pinch').set({ enable: true });
            hammer.on('pinch', (e) => {
                const zoom = canvas.getZoom();
                canvas.setZoom(zoom * e.scale);
            });

            // Double tap to reset zoom
            hammer.on('doubletap', () => {
                resetZoom();
            });
        }

        // Load PNG library
        function loadPngLibrary() {
            // In a real app, you would load these from a server
            const pngLibrary = [
                { name: 'Arrow', url: 'https://cdn-icons-png.flaticon.com/512/32/32213.png' },
                { name: 'Checkmark', url: 'https://cdn-icons-png.flaticon.com/512/32/32238.png' },
                { name: 'Heart', url: 'https://cdn-icons-png.flaticon.com/512/32/32239.png' },
                { name: 'Star', url: 'https://cdn-icons-png.flaticon.com/512/32/32249.png' },
                { name: 'Camera', url: 'https://cdn-icons-png.flaticon.com/512/32/32258.png' },
                { name: 'Gear', url: 'https://cdn-icons-png.flaticon.com/512/32/32263.png' }
            ];

            const pngLibraryGrid = document.getElementById('pngLibraryGrid');
            pngLibrary.forEach(item => {
                const pngItem = document.createElement('div');
                pngItem.className = 'png-item';
                pngItem.innerHTML = `
                    <img src="${item.url}" alt="${item.name}" title="${item.name}">
                    <span>${item.name}</span>
                `;
                pngItem.addEventListener('click', () => {
                    fabric.Image.fromURL(item.url, (img) => {
                        img.set({
                            left: canvas.width / 2,
                            top: canvas.height / 2,
                            originX: 'center',
                            originY: 'center',
                            hasControls: true,
                            hasBorders: true
                        });
                        canvas.add(img);
                        canvas.setActiveObject(img);
                        canvas.renderAll();
                        document.getElementById('pngLibraryModal').style.display = 'none';
                    });
                });
                pngLibraryGrid.appendChild(pngItem);
            });
        }

        // Tool functions
        function setCurrentTool(tool) {
            currentTool = tool;
            canvas.selection = tool === 'select';
            canvas.forEachObject(obj => {
                obj.selectable = tool === 'select';
            });
            canvas.renderAll();
        }

        // Drawing tools
        function startDrawing(pointer) {
            drawingPath = new fabric.Path(`M ${pointer.x} ${pointer.y}`, {
                stroke: currentTool === 'eraser' ? 'white' : currentColor,
                strokeWidth: currentBrushSize,
                fill: null,
                strokeLineCap: 'round',
                strokeLineJoin: 'round',
                strokeDashArray: currentTool === 'dotted' ? [currentBrushSize, currentBrushSize * 2] : null,
                opacity: currentOpacity,
                selectable: false
            });
            canvas.add(drawingPath);
        }

        function continueDrawing(pointer) {
            if (!drawingPath) return;
            
            const path = drawingPath.path;
            path.push(['L', pointer.x, pointer.y]);
            drawingPath.set({ path: path });
            canvas.renderAll();
        }

        // Shape tools
        function addRectangle(pointer) {
            const rect = new fabric.Rect({
                left: pointer.x,
                top: pointer.y,
                width: 1,
                height: 1,
                fill: currentColor,
                stroke: document.getElementById('shapeStrokeColorInput')?.value || '#000000',
                strokeWidth: document.getElementById('shapeStrokeWidthInput')?.value || 1,
                opacity: currentOpacity,
                selectable: false
            });
            canvas.add(rect);
            canvas.setActiveObject(rect);
        }

        function addCircle(pointer) {
            const circle = new fabric.Circle({
                left: pointer.x,
                top: pointer.y,
                radius: 1,
                fill: currentColor,
                stroke: document.getElementById('shapeStrokeColorInput')?.value || '#000000',
                strokeWidth: document.getElementById('shapeStrokeWidthInput')?.value || 1,
                opacity: currentOpacity,
                selectable: false
            });
            canvas.add(circle);
            canvas.setActiveObject(circle);
        }

        function addTriangle(pointer) {
            const triangle = new fabric.Triangle({
                left: pointer.x,
                top: pointer.y,
                width: 1,
                height: 1,
                fill: currentColor,
                stroke: document.getElementById('shapeStrokeColorInput')?.value || '#000000',
                strokeWidth: document.getElementById('shapeStrokeWidthInput')?.value || 1,
                opacity: currentOpacity,
                selectable: false
            });
            canvas.add(triangle);
            canvas.setActiveObject(triangle);
        }

        function addEllipse(pointer) {
            const ellipse = new fabric.Ellipse({
                left: pointer.x,
                top: pointer.y,
                rx: 1,
                ry: 1,
                fill: currentColor,
                stroke: document.getElementById('shapeStrokeColorInput')?.value || '#000000',
                strokeWidth: document.getElementById('shapeStrokeWidthInput')?.value || 1,
                opacity: currentOpacity,
                selectable: false
            });
            canvas.add(ellipse);
            canvas.setActiveObject(ellipse);
        }

        function addLine(pointer) {
            const line = new fabric.Line([pointer.x, pointer.y, pointer.x, pointer.y], {
                stroke: document.getElementById('shapeStrokeColorInput')?.value || '#000000',
                strokeWidth: document.getElementById('shapeStrokeWidthInput')?.value || 1,
                opacity: currentOpacity,
                selectable: false
            });
            canvas.add(line);
            canvas.setActiveObject(line);
        }

        function addPolygon(pointer) {
            // Polygon would need more complex handling for multiple points
            // This is a simplified version that creates a pentagon
            const polygon = new fabric.Polygon([
                { x: pointer.x, y: pointer.y },
                { x: pointer.x + 20, y: pointer.y + 20 },
                { x: pointer.x + 40, y: pointer.y },
                { x: pointer.x + 30, y: pointer.y - 30 },
                { x: pointer.x + 10, y: pointer.y - 30 }
            ], {
                fill: currentColor,
                stroke: document.getElementById('shapeStrokeColorInput')?.value || '#000000',
                strokeWidth: document.getElementById('shapeStrokeWidthInput')?.value || 1,
                opacity: currentOpacity,
                selectable: true
            });
            canvas.add(polygon);
            canvas.setActiveObject(polygon);
        }

        function addPolyline(pointer) {
            // Similar to polygon but doesn't close the shape
            const polyline = new fabric.Polyline([
                { x: pointer.x, y: pointer.y },
                { x: pointer.x + 20, y: pointer.y + 20 },
                { x: pointer.x + 40, y: pointer.y },
                { x: pointer.x + 60, y: pointer.y + 30 }
            ], {
                fill: null,
                stroke: document.getElementById('shapeStrokeColorInput')?.value || '#000000',
                strokeWidth: document.getElementById('shapeStrokeWidthInput')?.value || 1,
                opacity: currentOpacity,
                selectable: true
            });
            canvas.add(polyline);
            canvas.setActiveObject(polyline);
        }

        function resizeShape(pointer) {
            const activeObject = canvas.getActiveObject();
            if (!activeObject) return;

            switch (currentTool) {
                case 'rectangle':
                case 'triangle':
                    activeObject.set({
                        width: pointer.x - activeObject.left,
                        height: pointer.y - activeObject.top
                    });
                    break;
                case 'circle':
                    const radius = Math.sqrt(
                        Math.pow(pointer.x - activeObject.left, 2) +
                        Math.pow(pointer.y - activeObject.top, 2)
                    );
                    activeObject.set({ radius: radius });
                    break;
                case 'ellipse':
                    activeObject.set({
                        rx: Math.abs(pointer.x - activeObject.left),
                        ry: Math.abs(pointer.y - activeObject.top)
                    });
                    break;
                case 'line':
                    activeObject.set({
                        x2: pointer.x,
                        y2: pointer.y
                    });
                    break;
            }
            canvas.renderAll();
        }

        // Text tools
        function addText() {
            const text = new fabric.Textbox('Double click to edit', {
                left: 100,
                top: 100,
                width: 200,
                fontSize: currentFontSize,
                fontFamily: currentFontFamily,
                fill: document.getElementById('textColorInput')?.value || currentColor,
                backgroundColor: document.getElementById('textBgColorInput')?.value || 'transparent',
                stroke: document.getElementById('textStrokeColorInput')?.value || 'transparent',
                strokeWidth: document.getElementById('textStrokeWidthInput')?.value || 0,
                textAlign: 'left',
                hasControls: true
            });
            canvas.add(text);
            canvas.setActiveObject(text);
            text.enterEditing();
            text.selectAll();
        }

        function addTextBox() {
            const textbox = new fabric.Textbox('Double click to edit', {
                left: 100,
                top: 100,
                width: 200,
                fontSize: currentFontSize,
                fontFamily: currentFontFamily,
                fill: document.getElementById('textColorInput')?.value || currentColor,
                backgroundColor: document.getElementById('textBgColorInput')?.value || 'transparent',
                stroke: document.getElementById('textStrokeColorInput')?.value || 'transparent',
                strokeWidth: document.getElementById('textStrokeWidthInput')?.value || 0,
                textAlign: 'left',
                hasControls: true
            });
            canvas.add(textbox);
            canvas.setActiveObject(textbox);
        }

        function toggleTextStyle(style) {
            const activeObject = canvas.getActiveObject();
            if (activeObject && activeObject.type === 'textbox') {
                switch (style) {
                    case 'bold':
                        activeObject.set('fontWeight', activeObject.fontWeight === 'bold' ? '' : 'bold');
                        break;
                    case 'italic':
                        activeObject.set('fontStyle', activeObject.fontStyle === 'italic' ? '' : 'italic');
                        break;
                    case 'underline':
                        activeObject.set('underline', !activeObject.underline);
                        break;
                }
                canvas.renderAll();
            }
        }

        function setTextAlign(align) {
            const activeObject = canvas.getActiveObject();
            if (activeObject && activeObject.type === 'textbox') {
                activeObject.set('textAlign', align);
                canvas.renderAll();
            }
        }

        // Image tools
        function flipSelected(direction) {
            const activeObject = canvas.getActiveObject();
            if (activeObject) {
                if (direction === 'horizontal') {
                    activeObject.set('flipX', !activeObject.flipX);
                } else {
                    activeObject.set('flipY', !activeObject.flipY);
                }
                canvas.renderAll();
            }
        }

        function toggleImageBorder() {
            const activeObject = canvas.getActiveObject();
            if (activeObject && activeObject.type === 'image') {
                const hasBorder = activeObject.stroke && activeObject.strokeWidth > 0;
                activeObject.set({
                    stroke: hasBorder ? null : document.getElementById('imageBorderColorInput')?.value || '#000000',
                    strokeWidth: hasBorder ? 0 : document.getElementById('imageBorderWidthInput')?.value || 1
                });
                canvas.renderAll();
            }
        }

        function toggleImageShadow() {
            const activeObject = canvas.getActiveObject();
            if (activeObject) {
                if (activeObject.shadow) {
                    activeObject.set('shadow', null);
                } else {
                    activeObject.set('shadow', new fabric.Shadow({
                        color: 'rgba(0,0,0,0.5)',
                        blur: 10,
                        offsetX: 5,
                        offsetY: 5
                    }));
                }
                canvas.renderAll();
            }
        }

        // Crop tools
        function startCrop() {
            const activeObject = canvas.getActiveObject();
            if (activeObject && activeObject.type === 'image') {
                cropRect = new fabric.Rect({
                    left: activeObject.left,
                    top: activeObject.top,
                    width: activeObject.width,
                    height: activeObject.height,
                    fill: 'rgba(0,0,0,0.3)',
                    stroke: '#000000',
                    strokeWidth: 1,
                    hasControls: true,
                    hasBorders: true,
                    lockRotation: true,
                    originX: 'left',
                    originY: 'top'
                });
                canvas.add(cropRect);
                canvas.setActiveObject(cropRect);
                activeObject.selectable = false;
                canvas.renderAll();
            }
        }

        function applyCrop() {
            const activeObject = canvas.getActiveObject();
            if (activeObject && activeObject.type === 'image' && cropRect) {
                const scaleX = activeObject.scaleX || 1;
                const scaleY = activeObject.scaleY || 1;
                
                activeObject.set({
                    cropX: (cropRect.left - activeObject.left) / scaleX,
                    cropY: (cropRect.top - activeObject.top) / scaleY,
                    width: cropRect.width / scaleX,
                    height: cropRect.height / scaleY
                });
                
                canvas.remove(cropRect);
                cropRect = null;
                activeObject.selectable = true;
                canvas.renderAll();
            }
        }

        function cancelCrop() {
            if (cropRect) {
                canvas.remove(cropRect);
                cropRect = null;
                const activeObject = canvas.getActiveObject();
                if (activeObject && activeObject.type === 'image') {
                    activeObject.selectable = true;
                }
                canvas.renderAll();
            }
        }

        function setCropRatio(ratio) {
            if (cropRect) {
                if (ratio === 0) {
                    cropRect.set({
                        lockUniScaling: false
                    });
                } else {
                    cropRect.set({
                        lockUniScaling: true,
                        width: cropRect.height * ratio
                    });
                }
                canvas.renderAll();
            }
        }

        // Effect tools
        function applyFilter(type) {
            const activeObject = canvas.getActiveObject();
            if (activeObject && activeObject.type === 'image') {
                currentFilter = type;
                
                // Remove existing filters
                activeObject.filters = [];
                
                switch (type) {
                    case 'brightness':
                        activeObject.filters.push(new fabric.Image.filters.Brightness({
                            brightness: (currentFilterValue - 100) / 100
                        }));
                        break;
                    case 'contrast':
                        activeObject.filters.push(new fabric.Image.filters.Contrast({
                            contrast: (currentFilterValue - 100) / 100
                        }));
                        break;
                    case 'saturation':
                        activeObject.filters.push(new fabric.Image.filters.Saturation({
                            saturation: (currentFilterValue - 100) / 100
                        }));
                        break;
                    case 'blur':
                        activeObject.filters.push(new fabric.Image.filters.Blur({
                            blur: currentFilterValue / 100
                        }));
                        break;
                    case 'grayscale':
                        activeObject.filters.push(new fabric.Image.filters.Grayscale());
                        break;
                    case 'blackWhite':
                        activeObject.filters.push(new fabric.Image.filters.BlackWhite());
                        break;
                    case 'sharpen':
                        activeObject.filters.push(new fabric.Image.filters.Convolute({
                            matrix: [ 0, -1,  0,
                                     -1,  5, -1,
                                      0, -1,  0 ]
                        }));
                        break;
                    case 'invert':
                        activeObject.filters.push(new fabric.Image.filters.Invert());
                        break;
                }
                
                activeObject.applyFilters();
                canvas.renderAll();
            }
        }

        function updateFilter() {
            if (currentFilter) {
                applyFilter(currentFilter);
            }
        }

        function removeFilter() {
            const activeObject = canvas.getActiveObject();
            if (activeObject && activeObject.type === 'image') {
                activeObject.filters = [];
                activeObject.applyFilters();
                canvas.renderAll();
                currentFilter = null;
            }
        }

        // Library tools
        function openPngLibrary() {
            document.getElementById('pngLibraryModal').style.display = 'flex';
        }

        function addToLibrary() {
            // In a real app, this would save the selected object to the library
            alert('In a full implementation, this would add the selected object to your library');
        }

        // View tools
        function zoomCanvas(amount) {
            const zoom = canvas.getZoom();
            canvas.setZoom(zoom + amount);
            canvas.renderAll();
        }

        function resetZoom() {
            canvas.setZoom(1);
            canvas.renderAll();
        }

        function toggleGrid() {
            isGridVisible = !isGridVisible;
            if (isGridVisible) {
                canvas.setBackgroundColor({
                    source: 'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"><rect width="20" height="20" fill="white"/><path d="M 20 0 L 0 0 0 20" fill="none" stroke="gray" stroke-width="0.5"/></svg>',
                    repeat: 'repeat'
                }, () => {
                    canvas.renderAll();
                });
            } else {
                canvas.setBackgroundColor('white', () => {
                    canvas.renderAll();
                });
            }
        }

        function clearCanvas() {
            if (confirm('Are you sure you want to clear the canvas?')) {
                canvas.clear();
                canvas.setBackgroundColor('white', () => {
                    canvas.renderAll();
                });
                undoStack = [];
                redoStack = [];
                saveState();
            }
        }

        // Export tools
        function openExportModal() {
            document.getElementById('exportModal').style.display = 'flex';
        }

        function exportCanvas(type) {
            const scale = parseFloat(document.getElementById('exportScale').value);
            
            if (type === 'svg') {
                const svg = canvas.toSVG({
                    width: canvas.width * scale,
                    height: canvas.height * scale,
                    viewBox: {
                        x: 0,
                        y: 0,
                        width: canvas.width * scale,
                        height: canvas.height * scale
                    }
                });
                const blob = new Blob([svg], { type: 'image/svg+xml' });
                const url = URL.createObjectURL(blob);
                download(url, 'canvas.svg');
            } else {
                const dataURL = canvas.toDataURL({
                    format: type,
                    quality: 1,
                    multiplier: scale
                });
                download(dataURL, `canvas.${type}`);
            }
        }

        function download(url, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = url;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Object manipulation
        function bringForward() {
            const activeObject = canvas.getActiveObject();
            if (activeObject) {
                activeObject.bringForward();
                canvas.renderAll();
            }
        }

        function sendBackwards() {
            const activeObject = canvas.getActiveObject();
            if (activeObject) {
                activeObject.sendBackwards();
                canvas.renderAll();
            }
        }

        function bringToFront() {
            const activeObject = canvas.getActiveObject();
            if (activeObject) {
                activeObject.bringToFront();
                canvas.renderAll();
            }
        }

        function sendToBack() {
            const activeObject = canvas.getActiveObject();
            if (activeObject) {
                activeObject.sendToBack();
                canvas.renderAll();
            }
        }

        function groupSelected() {
            const activeObjects = canvas.getActiveObjects();
            if (activeObjects.length > 1) {
                const group = new fabric.Group(activeObjects, {
                    originX: 'center',
                    originY: 'center'
                });
                canvas.discardActiveObject();
                canvas.add(group);
                canvas.setActiveObject(group);
                canvas.renderAll();
            }
        }

        function ungroupSelected() {
            const activeObject = canvas.getActiveObject();
            if (activeObject && activeObject.type === 'group') {
                const objects = activeObject.getObjects();
                activeObject.toActiveSelection();
                canvas.discardActiveObject();
                const activeSelection = new fabric.ActiveSelection(objects, {
                    canvas: canvas
                });
                canvas.setActiveObject(activeSelection);
                canvas.remove(activeObject);
                canvas.renderAll();
            }
        }

        function deleteSelected() {
            const activeObjects = canvas.getActiveObjects();
            if (activeObjects && activeObjects.length > 0) {
                activeObjects.forEach(obj => {
                    canvas.remove(obj);
                });
                canvas.discardActiveObject();
                canvas.renderAll();
            }
        }

        function duplicateSelected() {
            const activeObject = canvas.getActiveObject();
            if (activeObject) {
                activeObject.clone(clone => {
                    clone.set({
                        left: clone.left + 10,
                        top: clone.top + 10
                    });
                    canvas.add(clone);
                    canvas.setActiveObject(clone);
                    canvas.renderAll();
                });
            }
        }

        function toggleLock() {
            const activeObject = canvas.getActiveObject();
            if (activeObject) {
                activeObject.set({
                    lockMovementX: !activeObject.lockMovementX,
                    lockMovementY: !activeObject.lockMovementY,
                    lockRotation: !activeObject.lockRotation,
                    lockScalingX: !activeObject.lockScalingX,
                    lockScalingY: !activeObject.lockScalingY
                });
                canvas.renderAll();
            }
        }

        function alignObjects(alignment) {
            const activeObjects = canvas.getActiveObjects();
            if (activeObjects.length > 1) {
                const boundingRect = new fabric.Group(activeObjects).getBoundingRect();
                
                activeObjects.forEach(obj => {
                    switch (alignment) {
                        case 'left':
                            obj.set('left', boundingRect.left);
                            break;
                        case 'center':
                            obj.set('left', boundingRect.left + boundingRect.width / 2 - obj.getScaledWidth() / 2);
                            break;
                        case 'right':
                            obj.set('left', boundingRect.left + boundingRect.width - obj.getScaledWidth());
                            break;
                        case 'top':
                            obj.set('top', boundingRect.top);
                            break;
                        case 'middle':
                            obj.set('top', boundingRect.top + boundingRect.height / 2 - obj.getScaledHeight() / 2);
                            break;
                        case 'bottom':
                            obj.set('top', boundingRect.top + boundingRect.height - obj.getScaledHeight());
                            break;
                    }
                });
                
                canvas.renderAll();
            }
        }

        function toggleSnapping() {
            isSnappingEnabled = !isSnappingEnabled;
            canvas.on('object:moving', snapToGrid);
            canvas.renderAll();
        }

        function snapToGrid(options) {
            if (isSnappingEnabled) {
                const gridSize = 20;
                options.target.set({
                    left: Math.round(options.target.left / gridSize) * gridSize,
                    top: Math.round(options.target.top / gridSize) * gridSize
                });
            }
        }

        // Initialize the editor
        initUI();
    </script>
</body>
</html>