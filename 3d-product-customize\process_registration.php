<?php
require_once 'config.php';

// Only process POST requests
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $response = ['success' => false, 'message' => ''];
    
    // Get form data
    $fullName = trim($_POST['full_name']);
    $email = trim($_POST['email']);
    $mobile = trim($_POST['mobile']);
    $password = $_POST['password'];
    $confirmPassword = $_POST['confirm_password'];
    
    // Basic validation
    if (empty($fullName) || empty($email) || empty($mobile) || empty($password)) {
        $response['message'] = 'All fields are required';
        echo json_encode($response);
        exit;
    }
    
    // Validate email format
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $response['message'] = 'Invalid email format';
        echo json_encode($response);
        exit;
    }
    
    // Validate password
    if (strlen($password) < 8) {
        $response['message'] = 'Password must be at least 8 characters long';
        echo json_encode($response);
        exit;
    }
    
    // Check if passwords match
    if ($password !== $confirmPassword) {
        $response['message'] = 'Passwords do not match';
        echo json_encode($response);
        exit;
    }
    
    // Check if email already exists
    $checkEmailSql = "SELECT user_id FROM users WHERE email = ?";
    if ($stmt = $conn->prepare($checkEmailSql)) {
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $stmt->store_result();
        
        if ($stmt->num_rows > 0) {
            $response['message'] = 'Email is already registered';
            echo json_encode($response);
            $stmt->close();
            exit;
        }
        $stmt->close();
    } else {
        $response['message'] = 'Database error: ' . $conn->error;
        echo json_encode($response);
        exit;
    }
    
    // Hash password
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
    
    // Insert user into database
    $insertSql = "INSERT INTO users (full_name, email, mobile_number, password) VALUES (?, ?, ?, ?)";
    if ($stmt = $conn->prepare($insertSql)) {
        $stmt->bind_param("ssss", $fullName, $email, $mobile, $hashedPassword);
        
        if ($stmt->execute()) {
            // Get the new user ID
            $userId = $conn->insert_id;
            
            // Start session and store user data
            $_SESSION['user_id'] = $userId;
            $_SESSION['full_name'] = $fullName;
            $_SESSION['email'] = $email;
            
            // Set success response
            $response['success'] = true;
            $response['message'] = 'Registration successful! Redirecting to subscription page...';
        } else {
            $response['message'] = 'Error registering user: ' . $stmt->error;
        }
        
        $stmt->close();
    } else {
        $response['message'] = 'Database error: ' . $conn->error;
    }
    
    // Return JSON response
    echo json_encode($response);
} else {
    // Redirect if accessed directly
    header("Location: register.php");
    exit;
}
?>
