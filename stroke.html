<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أدوات التحديد والظلال للنصوص والصور</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #1a2a6c, #b21f1f, #1a2a6c);
            color: #333;
            min-height: 100vh;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4);
            overflow: hidden;
            width: 100%;
            max-width: 1000px;
        }

        header {
            background: linear-gradient(to right, #2c3e50, #4a6491);
            color: white;
            padding: 20px;
            text-align: center;
        }

        h1 {
            font-size: 2.2rem;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 1rem;
            opacity: 0.9;
        }

        .controls {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            padding: 15px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            justify-content: center;
        }

        button {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        button i {
            font-size: 16px;
        }

        #uploadBtn {
            background: linear-gradient(to right, #2ecc71, #27ae60);
            color: white;
        }

        #addText {
            background: linear-gradient(to right, #3498db, #2980b9);
            color: white;
        }

        #fileInput {
            display: none;
        }

        .canvas-container {
            padding: 20px;
            text-align: center;
            background: #ecf0f1;
            position: relative;
        }

        #canvas {
            border: 2px solid #bdc3c7;
            border-radius: 8px;
            background: white;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            max-width: 100%;
        }

        .toolbar {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: none;
        }

        .toolbar-title {
            font-size: 1.1rem;
            color: #2c3e50;
            margin-bottom: 15px;
            text-align: center;
            font-weight: 600;
        }

        .tools-container {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            align-items: center;
            justify-content: center;
        }

        .tool {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 120px;
        }

        .tool input[type="color"] {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            padding: 0;
        }

        .tool input[type="color"]::-webkit-color-swatch {
            border: none;
            border-radius: 50%;
        }

        .tool input[type="color"]::-webkit-color-swatch-wrapper {
            padding: 0;
        }

        .tool label {
            margin-top: 5px;
            font-size: 0.8rem;
            color: #555;
            font-weight: 500;
        }

        .slider-control {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 120px;
        }

        .slider-control input {
            width: 100%;
        }

        .slider-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        input[type="range"] {
            flex: 1;
            height: 8px;
            border-radius: 4px;
            background: #e0e0e0;
            outline: none;
        }

        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #3498db;
            cursor: pointer;
        }

        .value-display {
            min-width: 40px;
            text-align: center;
            background: #ecf0f1;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: 600;
            font-size: 0.9rem;
        }

        select {
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid #bdc3c7;
            background: white;
            font-size: 0.9rem;
            width: 100%;
        }

        .object-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 15px;
            justify-content: center;
        }

        .instructions {
            background: #e8f4fc;
            border-left: 4px solid #3498db;
            padding: 15px;
            border-radius: 0 8px 8px 0;
            margin: 15px;
            font-size: 0.95rem;
            text-align: right;
        }

        .instructions h3 {
            color: #2980b9;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .instructions ul {
            padding-left: 20px;
            list-style-position: inside;
        }

        .instructions li {
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        footer {
            text-align: center;
            padding: 15px;
            color: #7f8c8d;
            font-size: 0.9rem;
            background: #f8f9fa;
            border-top: 1px solid #dee2e6;
        }

        .preview-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 1.8rem;
            font-weight: bold;
            color: rgba(255, 255, 255, 0.7);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            pointer-events: none;
            text-align: center;
            max-width: 90%;
        }

        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            margin: 15px;
            font-family: monospace;
            text-align: left;
            overflow-x: auto;
        }

        .code-title {
            color: #3498db;
            margin-bottom: 10px;
            font-weight: bold;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-image"></i> أدوات التحديد والظلال للنصوص والصور</h1>
            <p class="subtitle">أضف تحديدات ملونة وظلال مع التحكم الكامل بمظهرها</p>
        </header>

        <div class="controls">
            <button id="uploadBtn">
                <i class="fas fa-upload"></i> رفع صورة
            </button>
            <button id="addText">
                <i class="fas fa-font"></i> إضافة نص
            </button>
            <input type="file" id="fileInput" accept="image/*">
        </div>

        <div class="canvas-container">
            <div class="preview-text">اختر صورة أو أضف نصاً للبدء</div>
            <canvas id="canvas" width="700" height="500"></canvas>
        </div>

        <!-- شريط أدوات التحديد والظلال -->
        <div class="toolbar" id="styleToolbar">
            <div class="toolbar-title">أدوات التحديد والظلال</div>
            <div class="tools-container">
                <!-- تحديد ملون -->
                <div class="tool">
                    <input type="color" id="strokeColor" value="#ff0000">
                    <label for="strokeColor">لون التحديد</label>
                </div>

                <div class="slider-control">
                    <div class="slider-container">
                        <input type="range" min="0" max="20" value="2" id="strokeWidth">
                        <span class="value-display" id="strokeWidthValue">2px</span>
                    </div>
                    <label for="strokeWidth">سُمك التحديد</label>
                </div>

                <!-- ظل ملون -->
                <div class="tool">
                    <input type="color" id="shadowColor" value="#000000">
                    <label for="shadowColor">لون الظل</label>
                </div>

                <div class="slider-control">
                    <div class="slider-container">
                        <input type="range" min="0" max="50" value="5" id="shadowBlur">
                        <span class="value-display" id="shadowBlurValue">5px</span>
                    </div>
                    <label for="shadowBlur">حجم الظل</label>
                </div>

                <div class="slider-control">
                    <div class="slider-container">
                        <input type="range" min="0" max="20" value="3" id="shadowOffsetX">
                        <span class="value-display" id="shadowOffsetXValue">3px</span>
                    </div>
                    <label for="shadowOffsetX">إزاحة أفقية</label>
                </div>

                <div class="slider-control">
                    <div class="slider-container">
                        <input type="range" min="0" max="20" value="3" id="shadowOffsetY">
                        <span class="value-display" id="shadowOffsetYValue">3px</span>
                    </div>
                    <label for="shadowOffsetY">إزاحة رأسية</label>
                </div>
            </div>
        </div>

        <div class="instructions">
            <h3><i class="fas fa-lightbulb"></i> كيفية استخدام الأدوات:</h3>
            <ul>
                <li>اختر صورة أو أضف نصاً ثم انقر عليه لتحديده</li>
                <li>استخدم أدوات التحديد لإضافة حدود ملونة حول العنصر</li>
                <li>استخدم أدوات الظل لإضافة ظل ملون خلف العنصر</li>
                <li>غير الألوان والأحجام حسب رغبتك</li>
                <li>اسحب العنصر لتغيير موضعه، واستخدم الزوايا لتغيير حجمه</li>
            </ul>
        </div>

        <div class="code-block">
            <div class="code-title">الكود المسؤول عن إضافة التحديدات والظلال:</div>
            <pre>
// إضافة تحديد ملون للعنصر المحدد
function addStrokeToObject() {
    const activeObject = canvas.getActiveObject();
    if (!activeObject) return;

    activeObject.set({
        stroke: strokeColor.value, // لون التحديد
        strokeWidth: parseInt(strokeWidth.value) // سُمك التحديد
    });

    canvas.renderAll();
}

// إضافة ظل ملون للعنصر المحدد
function addShadowToObject() {
    const activeObject = canvas.getActiveObject();
    if (!activeObject) return;

    const blur = parseInt(shadowBlur.value);
    const offsetX = parseInt(shadowOffsetX.value);
    const offsetY = parseInt(shadowOffsetY.value);

    if (blur > 0) {
        activeObject.set('shadow', new fabric.Shadow({
            color: shadowColor.value, // لون الظل
            blur: blur, // حجم الظل
            offsetX: offsetX, // الإزاحة الأفقية
            offsetY: offsetY  // الإزاحة الرأسية
        }));
    } else {
        activeObject.set('shadow', null);
    }

    canvas.renderAll();
}

// تحديث القيم المعروضة عند تغيير المنزلقات
strokeWidth.addEventListener('input', function() {
    strokeWidthValue.textContent = this.value + 'px';
    addStrokeToObject();
});

shadowBlur.addEventListener('input', function() {
    shadowBlurValue.textContent = this.value + 'px';
    addShadowToObject();
});

shadowOffsetX.addEventListener('input', function() {
    shadowOffsetXValue.textContent = this.value + 'px';
    addShadowToObject();
});

shadowOffsetY.addEventListener('input', function() {
    shadowOffsetYValue.textContent = this.value + 'px';
    addShadowToObject();
});

// تحديث التحديد والظل عند تغيير الألوان
strokeColor.addEventListener('change', addStrokeToObject);
shadowColor.addEventListener('change', addShadowToObject);
            </pre>
        </div>

        <footer>
            <p>أدوات التحديد والظلال للنصوص والصور &copy; 2023</p>
        </footer>
    </div>

    <script>
        // تهيئة لوحة الرسم
        const canvas = new fabric.Canvas('canvas', {
            backgroundColor: '#ffffff',
            selection: true
        });

        let currentImage = null;

        // عناصر التحكم
        const strokeColor = document.getElementById('strokeColor');
        const strokeWidth = document.getElementById('strokeWidth');
        const strokeWidthValue = document.getElementById('strokeWidthValue');
        const shadowColor = document.getElementById('shadowColor');
        const shadowBlur = document.getElementById('shadowBlur');
        const shadowBlurValue = document.getElementById('shadowBlurValue');
        const shadowOffsetX = document.getElementById('shadowOffsetX');
        const shadowOffsetXValue = document.getElementById('shadowOffsetXValue');
        const shadowOffsetY = document.getElementById('shadowOffsetY');
        const shadowOffsetYValue = document.getElementById('shadowOffsetYValue');
        const styleToolbar = document.getElementById('styleToolbar');

        // إعداد مستمعي الأحداث
        document.getElementById('fileInput').addEventListener('change', handleFileSelect);
        document.getElementById('uploadBtn').addEventListener('click', function() {
            document.getElementById('fileInput').click();
        });

        document.getElementById('addText').addEventListener('click', addTextToCanvas);

        // إضافة تحديد ملون للعنصر المحدد
        function addStrokeToObject() {
            const activeObject = canvas.getActiveObject();
            if (!activeObject) return;

            activeObject.set({
                stroke: strokeColor.value,
                strokeWidth: parseInt(strokeWidth.value)
            });

            canvas.renderAll();
        }

        // إضافة ظل ملون للعنصر المحدد
        function addShadowToObject() {
            const activeObject = canvas.getActiveObject();
            if (!activeObject) return;

            const blur = parseInt(shadowBlur.value);
            const offsetX = parseInt(shadowOffsetX.value);
            const offsetY = parseInt(shadowOffsetY.value);

            if (blur > 0) {
                activeObject.set('shadow', new fabric.Shadow({
                    color: shadowColor.value,
                    blur: blur,
                    offsetX: offsetX,
                    offsetY: offsetY
                }));
            } else {
                activeObject.set('shadow', null);
            }

            canvas.renderAll();
        }

        // تحديث القيم المعروضة
        strokeWidth.addEventListener('input', function() {
            strokeWidthValue.textContent = this.value + 'px';
            addStrokeToObject();
        });

        shadowBlur.addEventListener('input', function() {
            shadowBlurValue.textContent = this.value + 'px';
            addShadowToObject();
        });

        shadowOffsetX.addEventListener('input', function() {
            shadowOffsetXValue.textContent = this.value + 'px';
            addShadowToObject();
        });

        shadowOffsetY.addEventListener('input', function() {
            shadowOffsetYValue.textContent = this.value + 'px';
            addShadowToObject();
        });

        // تحديث التحديد والظل عند تغيير الألوان
        strokeColor.addEventListener('change', addStrokeToObject);
        shadowColor.addEventListener('change', addShadowToObject);

        // التعامل مع اختيار الملف
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (!file || !file.type.match('image.*')) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                // إخفاء نص المعاينة
                document.querySelector('.preview-text').style.display = 'none';

                // تحميل الصورة إلى اللوحة
                fabric.Image.fromURL(e.target.result, function(img) {
                    if (currentImage) {
                        canvas.remove(currentImage);
                    }

                    // تحجيم الصورة لتناسب اللوحة
                    const scale = Math.min(
                        canvas.width / img.width,
                        canvas.height / img.height
                    );

                    img.set({
                        left: canvas.width / 2,
                        top: canvas.height / 2,
                        originX: 'center',
                        originY: 'center',
                        scaleX: scale * 0.9,
                        scaleY: scale * 0.9,
                        selectable: true
                    });

                    canvas.add(img);
                    canvas.setActiveObject(img);
                    currentImage = img;
                    canvas.renderAll();

                    // إظهار شريط الأدوات
                    styleToolbar.style.display = 'block';
                });
            };
            reader.readAsDataURL(file);
        }

        // إضافة نص إلى اللوحة
        function addTextToCanvas() {
            // إظهار شريط الأدوات
            styleToolbar.style.display = 'block';

            // إنشاء نص جديد
            const text = new fabric.Text('النص الخاص بك', {
                left: canvas.width / 2,
                top: canvas.height / 2,
                fontSize: 40,
                fontFamily: 'Arial',
                fill: '#000000',
                originX: 'center',
                originY: 'center',
                selectable: true,
                hasControls: true,
                padding: 10,
                textAlign: 'center'
            });

            // تطبيق التحديد والظل الأولي
            addStrokeToObject();
            addShadowToObject();

            canvas.add(text);
            canvas.setActiveObject(text);
            canvas.renderAll();

            // إخفاء نص المعاينة
            document.querySelector('.preview-text').style.display = 'none';
        }

        // إظهار شريط الأدوات عند تحديد عنصر
        canvas.on('object:selected', function() {
            styleToolbar.style.display = 'block';
        });

        // إخفاء شريط الأدوات عند إلغاء التحديد
        canvas.on('selection:cleared', function() {
            styleToolbar.style.display = 'none';
        });
    </script>
</body>
</html>
