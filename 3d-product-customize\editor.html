<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Fabric.js Image Editor</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: Arial, sans-serif;
      background-color: #f5f5f5;
    }
    
    .editor-container {
      display: flex;
      flex-direction: column;
      height: 100vh;
    }
    
    .editor-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 20px;
      background-color: #2c3e50;
      color: white;
    }
    
    .editor-title {
      font-size: 20px;
      font-weight: bold;
    }
    
    .toolbar {
      display: flex;
      padding: 10px;
      background-color: #34495e;
      color: white;
    }
    
    .toolbar-section {
      display: flex;
      margin-right: 20px;
    }
    
    .toolbar-section h3 {
      margin: 0 10px 0 0;
      font-size: 14px;
      line-height: 32px;
    }
    
    .toolbar button {
      margin-right: 5px;
      padding: 5px 10px;
      background-color: #555;
      color: white;
      border: none;
      border-radius: 3px;
      cursor: pointer;
    }
    
    .toolbar button:hover {
      background-color: #777;
    }
    
    .canvas-container {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 20px;
      overflow: auto;
    }
    
    #fabric-canvas {
      background-color: white;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }
    
    .preview-container {
      position: fixed;
      bottom: 20px;
      left: 20px;
      width: 300px;
      height: 200px;
      background-color: #333;
      border-radius: 5px;
      overflow: hidden;
    }
    
    .preview-container h3 {
      margin: 0;
      padding: 10px;
      background-color: #222;
      color: white;
      font-size: 14px;
    }
    
    .preview-content {
      height: calc(100% - 35px);
      display: flex;
      justify-content: center;
      align-items: center;
    }
    
    .preview-content canvas {
      max-width: 100%;
      max-height: 100%;
    }
  </style>
</head>
<body>
  <div class="editor-container">
    <div class="editor-header">
      <div class="editor-title">Fabric.js Image Editor</div>
    </div>
    
    <div class="toolbar">
      <div class="toolbar-section">
        <h3>Add</h3>
        <button id="add-text-btn">Text</button>
        <button id="add-image-btn">Image</button>
      </div>
      
      <div class="toolbar-section">
        <h3>Edit</h3>
        <button id="delete-btn">Delete</button>
        <button id="undo-btn">Undo</button>
        <button id="redo-btn">Redo</button>
      </div>
      
      <div class="toolbar-section">
        <h3>Arrange</h3>
        <button id="group-btn">Group</button>
        <button id="ungroup-btn">Ungroup</button>
      </div>
      
      <div class="toolbar-section">
        <h3>Export</h3>
        <button id="export-btn">Export Image</button>
        <button id="export-json-btn">Export JSON</button>
        <button id="import-json-btn">Import JSON</button>
      </div>
    </div>
    
    <div class="canvas-container">
      <canvas id="fabric-canvas" width="1024" height="1024"></canvas>
    </div>
    
    <div class="preview-container">
      <h3>3D Preview</h3>
      <div class="preview-content">
        <canvas id="three-preview"></canvas>
      </div>
    </div>
  </div>
  
  <!-- Load Fabric.js -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.1/fabric.min.js"></script>
  
  <!-- Load Three.js (for 3D preview) -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
  
  <!-- Load each script individually -->
  <script src="editor-core.js"></script>
  <script src="control-panel.js"></script>
  <script src="canvas-size-manager.js"></script>
  <script src="history.js"></script>
  <script src="three-d-sync.js"></script>
  <script src="font-manager.js"></script>
  <script src="alignment-guides.js"></script>
  <script src="fabric-editor.js"></script>
</body>
</html>
