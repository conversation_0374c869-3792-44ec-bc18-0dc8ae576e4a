"use client"

import { But<PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Camera, Video, Zap, ChevronDown } from "lucide-react"

interface TopBarProps {
  setShowLoginModal: (show: boolean) => void
}

export function TopBar({ setShowLoginModal }: TopBarProps) {
  return (
    <div className="absolute left-0 right-0 top-0 z-10 flex items-center justify-between p-2">
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="icon"
          className="h-10 w-10 border-[#D6A25E] bg-[#1F2A45] text-[#D6A25E] hover:bg-[#D6A25E] hover:text-[#1F2A45]"
        >
          <Camera className="h-5 w-5" />
        </Button>

        <Button
          variant="outline"
          size="icon"
          className="h-10 w-10 border-[#D6A25E] bg-[#1F2A45] text-[#D6A25E] hover:bg-[#D6A25E] hover:text-[#1F2A45]"
        >
          <Video className="h-5 w-5" />
        </Button>

        <Button
          variant="outline"
          size="icon"
          className="h-10 w-10 border-[#D6A25E] bg-[#1F2A45] text-[#D6A25E] hover:bg-[#D6A25E] hover:text-[#1F2A45]"
        >
          <Zap className="h-5 w-5" />
        </Button>
      </div>

      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          className="border-[#D6A25E] bg-[#1F2A45] text-[#D6A25E] hover:bg-[#D6A25E] hover:text-[#1F2A45]"
          onClick={() => setShowLoginModal(true)}
        >
          Login
        </Button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              className="border-[#D6A25E] bg-[#1F2A45] text-[#D6A25E] hover:bg-[#D6A25E] hover:text-[#1F2A45]"
            >
              En <ChevronDown className="ml-1 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem>English</DropdownMenuItem>
            <DropdownMenuItem>العربية</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  )
}
