// src/components/subscription/SubscriptionPlans.jsx
// [!] هذا المكون يعرض خطط الاشتراك المتاحة للمستخدمين
// [!] يمكن تعديل تفاصيل الخطط حسب احتياجات الموقع

import React, { useState, useEffect } from 'react';

const SubscriptionPlans = ({ onSelectPlan }) => {
  // حالة خطط الاشتراك
  const [plans, setPlans] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedPlan, setSelectedPlan] = useState(null);

  // جلب خطط الاشتراك من الخادم
  useEffect(() => {
    const fetchPlans = async () => {
      try {
        setLoading(true);
        
        // [!] في بيئة الإنتاج، يجب استبدال هذا بطلب API حقيقي
        // const response = await fetch('/api/subscription/plans');
        // const data = await response.json();
        
        // للاختبار، نستخدم بيانات وهمية
        const mockPlans = [
          {
            id: 1,
            name: 'الخطة الشهرية',
            description: 'اشتراك شهري بميزات كاملة',
            price: 9.99,
            duration_days: 30,
            features: ['ميزة 1', 'ميزة 2', 'ميزة 3'],
            is_popular: false
          },
          {
            id: 2,
            name: 'الخطة السنوية',
            description: 'اشتراك سنوي بميزات كاملة مع خصم 20%',
            price: 99.99,
            duration_days: 365,
            features: ['ميزة 1', 'ميزة 2', 'ميزة 3', 'ميزة 4', 'دعم فني مميز'],
            is_popular: true
          },
          {
            id: 3,
            name: 'الخطة التجريبية',
            description: 'اشتراك تجريبي لمدة أسبوع',
            price: 0.00,
            duration_days: 7,
            features: ['ميزة 1', 'ميزة 2'],
            is_popular: false
          }
        ];
        
        setPlans(mockPlans);
        setError('');
      } catch (err) {
        console.error('Error fetching subscription plans:', err);
        setError('حدث خطأ أثناء جلب خطط الاشتراك. يرجى المحاولة مرة أخرى لاحقًا.');
      } finally {
        setLoading(false);
      }
    };

    fetchPlans();
  }, []);

  // معالجة اختيار الخطة
  const handleSelectPlan = (plan) => {
    setSelectedPlan(plan);
    if (onSelectPlan) {
      onSelectPlan(plan);
    }
  };

  // عرض رسالة التحميل
  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // عرض رسالة الخطأ
  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
        <strong className="font-bold">خطأ! </strong>
        <span className="block sm:inline">{error}</span>
      </div>
    );
  }

  return (
    <div className="py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
            اختر خطة الاشتراك المناسبة لك
          </h2>
          <p className="mt-4 text-xl text-gray-600">
            نقدم لك مجموعة من الخطط لتختار ما يناسب احتياجاتك
          </p>
        </div>

        <div className="mt-12 space-y-4 sm:space-y-0 sm:grid sm:grid-cols-3 sm:gap-6 lg:max-w-4xl lg:mx-auto xl:max-w-none xl:mx-0">
          {plans.map((plan) => (
            <div 
              key={plan.id}
              className={`border rounded-lg shadow-sm divide-y divide-gray-200 ${
                plan.is_popular ? 'border-blue-500 ring-2 ring-blue-500' : 'border-gray-200'
              }`}
            >
              {plan.is_popular && (
                <div className="bg-blue-500 text-white text-center py-2 rounded-t-lg">
                  الأكثر شعبية
                </div>
              )}
              
              <div className="p-6">
                <h3 className="text-xl font-medium text-gray-900">{plan.name}</h3>
                <p className="mt-2 text-gray-500">{plan.description}</p>
                <p className="mt-4">
                  <span className="text-4xl font-extrabold text-gray-900">${plan.price}</span>
                  {plan.price > 0 && (
                    <span className="text-base font-medium text-gray-500">
                      {plan.duration_days === 30 && ' / شهريًا'}
                      {plan.duration_days === 365 && ' / سنويًا'}
                    </span>
                  )}
                </p>
                <p className="mt-1 text-sm text-gray-500">
                  {plan.duration_days === 7 && 'لمدة أسبوع'}
                  {plan.duration_days === 30 && 'لمدة شهر'}
                  {plan.duration_days === 365 && 'لمدة سنة'}
                </p>
                
                <button
                  type="button"
                  onClick={() => handleSelectPlan(plan)}
                  className={`mt-8 w-full py-3 px-4 rounded-md shadow ${
                    selectedPlan?.id === plan.id
                      ? 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 text-white'
                      : 'bg-white border border-gray-300 hover:bg-gray-50 text-gray-700'
                  } transition-colors duration-200 ease-in-out`}
                >
                  {selectedPlan?.id === plan.id ? 'تم الاختيار' : 'اختر هذه الخطة'}
                </button>
              </div>
              
              <div className="pt-6 pb-8 px-6">
                <h4 className="text-sm font-medium text-gray-900 tracking-wide">المميزات</h4>
                <ul className="mt-4 space-y-3">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <p className="mr-3 text-gray-700">{feature}</p>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default SubscriptionPlans;
