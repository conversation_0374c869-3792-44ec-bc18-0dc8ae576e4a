<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أسهل طريقة لإزالة خلفية الصور</title>
    <script src="https://cdn.jsdelivr.net/npm/removal.js@1.1/dist/removal.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: '<PERSON><PERSON><PERSON>', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            color: #fff;
            min-height: 100vh;
            padding: 20px;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            text-align: center;
            padding: 30px 0;
            margin-bottom: 20px;
        }
        
        header h1 {
            font-size: 2.5rem;
            margin-bottom: 15px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }
        
        header p {
            font-size: 1.2rem;
            max-width: 700px;
            margin: 0 auto;
            color: #e0f7ff;
        }
        
        .app-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            margin-bottom: 30px;
        }
        
        .upload-section {
            padding: 30px;
            text-align: center;
        }
        
        .upload-area {
            border: 3px dashed #4cc9f0;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: rgba(0, 100, 150, 0.2);
            margin-bottom: 20px;
        }
        
        .upload-area:hover {
            background: rgba(0, 120, 180, 0.3);
            border-color: #70e1ff;
        }
        
        .upload-icon {
            font-size: 4rem;
            color: #4cc9f0;
            margin-bottom: 20px;
        }
        
        .upload-text {
            font-size: 1.5rem;
            margin-bottom: 15px;
        }
        
        .upload-hint {
            color: #a0e5ff;
            margin-bottom: 20px;
            font-size: 1rem;
        }
        
        .btn {
            background: linear-gradient(to right, #4361ee, #3a0ca3);
            color: white;
            border: none;
            padding: 15px 40px;
            font-size: 1.1rem;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-block;
            box-shadow: 0 4px 15px rgba(67, 97, 238, 0.4);
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(67, 97, 238, 0.6);
        }
        
        .btn:active {
            transform: translateY(1px);
        }
        
        .btn:disabled {
            background: #5a6ab1;
            cursor: not-allowed;
            opacity: 0.7;
        }
        
        .result-section {
            display: flex;
            flex-wrap: wrap;
            padding: 20px;
            gap: 20px;
        }
        
        .image-container {
            flex: 1;
            min-width: 300px;
            background: rgba(10, 20, 50, 0.4);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
        }
        
        .image-container h3 {
            margin-bottom: 20px;
            color: #70e1ff;
            font-size: 1.4rem;
        }
        
        .image-preview {
            width: 100%;
            height: 300px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .image-preview img {
            max-width: 100%;
            max-height: 100%;
            display: none;
        }
        
        .placeholder {
            color: #4a8dac;
            text-align: center;
            padding: 20px;
        }
        
        .placeholder i {
            font-size: 4rem;
            margin-bottom: 15px;
            opacity: 0.6;
        }
        
        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 15px;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 5px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 5px solid #4361ee;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        .features {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 30px;
            justify-content: center;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            width: 300px;
            text-align: center;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.15);
        }
        
        .feature-icon {
            font-size: 2.5rem;
            color: #4cc9f0;
            margin-bottom: 15px;
        }
        
        .feature-title {
            font-size: 1.3rem;
            margin-bottom: 10px;
            color: #70e1ff;
        }
        
        footer {
            text-align: center;
            padding: 30px 0;
            color: #b3e5fc;
            font-size: 1.1rem;
            margin-top: 30px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .result-section {
                flex-direction: column;
            }
            
            header h1 {
                font-size: 2rem;
            }
            
            .upload-area {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>أسهل طريقة لإزالة خلفية الصور</h1>
            <p>لا حاجة لخبرة في البرمجة - فقط حمِّل صورتك واحصل على النتيجة خلال ثوانٍ</p>
        </header>
        
        <div class="app-card">
            <div class="upload-section">
                <div class="upload-area" id="dropArea">
                    <div class="upload-icon">📁</div>
                    <h3 class="upload-text">اسحب وأفلت الصورة هنا</h3>
                    <p class="upload-hint">أو انقر للاختيار من جهازك (JPG, PNG, WebP)</p>
                    <button class="btn" id="uploadBtn">اختر صورة</button>
                    <input type="file" id="fileInput" accept="image/*" style="display: none;">
                </div>
                
                <div class="loading" id="loadingIndicator">
                    <div class="spinner"></div>
                    <p>جاري إزالة الخلفية... هذه العملية تستغرق بضع ثوانٍ</p>
                </div>
            </div>
            
            <div class="result-section">
                <div class="image-container">
                    <h3>صورتك الأصلية</h3>
                    <div class="image-preview" id="originalPreview">
                        <div class="placeholder">
                            <div>🖼️</div>
                            <p>سيظهر معاينة الصورة هنا</p>
                        </div>
                        <img id="originalImage" alt="الصورة الأصلية">
                    </div>
                </div>
                
                <div class="image-container">
                    <h3>النتيجة بعد الإزالة</h3>
                    <div class="image-preview" id="resultPreview">
                        <div class="placeholder">
                            <div>✨</div>
                            <p>سيظهر نتيجة إزالة الخلفية هنا</p>
                        </div>
                        <img id="resultImage" alt="النتيجة بعد إزالة الخلفية">
                    </div>
                    <div class="action-buttons">
                        <button class="btn" id="downloadBtn" disabled>تحميل النتيجة</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="features">
            <div class="feature-card">
                <div class="feature-icon">⚡</div>
                <h3 class="feature-title">سهولة الاستخدام</h3>
                <p>لا حاجة لأي خبرة تقنية، فقط حمّل صورتك وانقر زر المعالجة</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🔒</div>
                <h3 class="feature-title">خصوصية كاملة</h3>
                <p>صورك لا تترك جهازك أبداً، كل المعالجة تتم في متصفحك</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🎨</div>
                <h3 class="feature-title">نتائج دقيقة</h3>
                <p>إزالة احترافية للخلفيات مع الحفاظ على أدق التفاصيل</p>
            </div>
        </div>
        
        <footer>
            <p>تم التطوير باستخدام مكتبة Removal.js المجانية | لا حاجة لتثبيت أي برامج</p>
        </footer>
    </div>

    <script>
        // عناصر DOM
        const fileInput = document.getElementById('fileInput');
        const uploadBtn = document.getElementById('uploadBtn');
        const dropArea = document.getElementById('dropArea');
        const originalImage = document.getElementById('originalImage');
        const resultImage = document.getElementById('resultImage');
        const downloadBtn = document.getElementById('downloadBtn');
        const loadingIndicator = document.getElementById('loadingIndicator');
        
        // تهيئة الأحداث
        uploadBtn.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', handleImageUpload);
        downloadBtn.addEventListener('click', downloadResult);
        
        // معالجة رفع الصورة
        function handleImageUpload() {
            const file = fileInput.files[0];
            
            if (!file || !file.type.match('image.*')) {
                alert('الرجاء اختيار ملف صورة صالح');
                return;
            }
            
            const reader = new FileReader();
            
            reader.onload = function(e) {
                // عرض الصورة الأصلية
                originalImage.src = e.target.result;
                originalImage.style.display = 'block';
                document.querySelector('#originalPreview .placeholder').style.display = 'none';
                
                // معالجة الصورة
                processImage(e.target.result);
            }
            
            reader.readAsDataURL(file);
        }
        
        // معالجة الصورة باستخدام Removal.js
        async function processImage(imageData) {
            try {
                // إظهار مؤشر التحميل
                loadingIndicator.style.display = 'block';
                downloadBtn.disabled = true;
                
                // استخدام مكتبة Removal.js البسيطة
                const result = await removal.removeBackground(imageData, {
                    model: 'medium', // يمكنك اختيار: 'small', 'medium', 'large'
                    outputFormat: 'image/png'
                });
                
                // عرض الصورة الناتجة
                resultImage.src = result.imageDataUrl;
                resultImage.style.display = 'block';
                document.querySelector('#resultPreview .placeholder').style.display = 'none';
                
                // تمكين زر التحميل
                downloadBtn.disabled = false;
                loadingIndicator.style.display = 'none';
                
            } catch (error) {
                console.error('حدث خطأ أثناء المعالجة:', error);
                alert('حدث خطأ أثناء إزالة الخلفية. الرجاء المحاولة مرة أخرى');
                loadingIndicator.style.display = 'none';
            }
        }
        
        // تنزيل النتيجة
        function downloadResult() {
            if (!resultImage.src) return;
            
            const link = document.createElement('a');
            link.download = 'صورة-بدون-خلفية.png';
            link.href = resultImage.src;
            link.click();
        }
        
        // دعم السحب والإفلات
        dropArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropArea.style.backgroundColor = 'rgba(0, 120, 180, 0.3)';
        });
        
        dropArea.addEventListener('dragleave', () => {
            dropArea.style.backgroundColor = 'rgba(0, 100, 150, 0.2)';
        });
        
        dropArea.addEventListener('drop', (e) => {
            e.preventDefault();
            dropArea.style.backgroundColor = 'rgba(0, 100, 150, 0.2)';
            
            if (e.dataTransfer.files && e.dataTransfer.files[0]) {
                fileInput.files = e.dataTransfer.files;
                handleImageUpload();
            }
        });
        
        // رسالة ترحيبية
        setTimeout(() => {
            alert('مرحباً! هذه أداة بسيطة لإزالة خلفية الصور. فقط قم بتحميل صورة لترى النتيجة!');
        }, 1000);
    </script>
</body>
</html>