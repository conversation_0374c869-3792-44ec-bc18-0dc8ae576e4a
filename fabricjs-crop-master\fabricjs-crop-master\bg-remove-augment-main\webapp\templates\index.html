<!DOCTYPE html>
<html>

<head>
    <title>Deep Learning in Action</title>
    <script src="static/js/jquery-3.5.1.min.js"></script>
    <link rel="stylesheet" href="static/css/bootstrap.min.css">
    <script src="static/js/bootstrap.bundle.min.js"></script>
    <link rel="stylesheet" href="static/css/styles.css">

</head>

<body>
    <div class="overlay">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>
    <nav class="navbar navbar-dark bg-primary">
        <h2> Background Remove using Deep Learning </h2>
    </nav>

    <div class="container overflow-hidden">
        <div class="col-sm-12">
            <div class="row">
                <div class="col-sm-3 gy-3"></div>
                <div class="col-sm-6 gy-3">
                    <form action="/remove-bg" method="post" id="input_form" enctype="multipart/form-data">
                        <input class="control" type="file" id="file" name="file" />
                        <div id="holder-empty">
                            <img id="image_droped" />
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary" id="btn-submit" style="display: none;">Remove Background</button>
                        </div>
                    </form>
                </div>
                <div class="col-sm-3 gy-3"></div>
            </div>
        </div>
    </div>
</body>
<script>
    $('#input_form').submit(function (event) {
        event.preventDefault();
        $(".overlay").show()
        $(this).unbind('submit').submit();
    })

    var reader = new FileReader();
    $('#file').change(function (ev) {
        file = $('#file').prop('files')[0];
        ev.preventDefault();
        reader.onload = function (event) {
            $('#image_droped').attr('src', event.target.result);
            $('#btn-submit').show()
        }
        reader.readAsDataURL(file);
    });
</script>

</html>