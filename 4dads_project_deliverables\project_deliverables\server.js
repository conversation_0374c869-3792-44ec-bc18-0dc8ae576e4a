// server.js
// [!] هذا الملف هو نقطة الدخول للتطبيق على سيرفر Hostinger

const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');

// [!] متغيرات يجب تعديلها حسب بيئة التشغيل
const dev = process.env.NODE_ENV !== 'production';
const hostname = 'localhost';
const port = process.env.PORT || 3000;

// تهيئة تطبيق Next.js
const app = next({ dev, hostname, port });
const handle = app.getRequestHandler();

// بدء تشغيل التطبيق
app.prepare().then(() => {
  createServer(async (req, res) => {
    try {
      // تحليل عنوان URL المطلوب
      const parsedUrl = parse(req.url, true);
      
      // معالجة الطلب باستخدام Next.js
      await handle(req, res, parsedUrl);
    } catch (err) {
      console.error('Error occurred handling', req.url, err);
      res.statusCode = 500;
      res.end('Internal Server Error');
    }
  }).listen(port, (err) => {
    if (err) throw err;
    console.log(`> Ready on http://${hostname}:${port}`);
    console.log(`> Environment: ${process.env.NODE_ENV}`);
  });
});
