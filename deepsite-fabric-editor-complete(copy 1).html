<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fabric.js Canvas Editor</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: #f0f0f0;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .toolbar {
            background: #333;
            color: white;
            padding: 10px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn {
            background: #555;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .btn:hover {
            background: #666;
        }

        .btn.active {
            background: #007bff;
        }

        .canvas-container {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        #canvas-wrapper {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .controls {
            background: #444;
            color: white;
            padding: 10px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .control-group {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .control-group label {
            font-size: 12px;
        }

        .control-group input, .control-group select {
            padding: 4px;
            border-radius: 3px;
            border: 1px solid #666;
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <!-- Top Toolbar -->
    <div class="toolbar">
        <button class="btn active" onclick="setMode('draw')">✏️ Draw</button>
        <button class="btn" onclick="setMode('shapes')">⬜ Shapes</button>
        <button class="btn" onclick="setMode('text')">📝 Text</button>
        <button class="btn" onclick="setMode('images')">🖼️ Images</button>
        <button class="btn" onclick="setMode('crop')">✂️ Crop</button>
        <button class="btn" onclick="undo()">↶ Undo</button>
        <button class="btn" onclick="redo()">↷ Redo</button>
        <button class="btn" onclick="exportCanvas()">💾 Export</button>
    </div>

    <!-- Canvas -->
    <div class="canvas-container">
        <div id="canvas-wrapper">
            <canvas id="canvas"></canvas>
        </div>
    </div>

    <!-- Controls -->
    <div class="controls" id="controls">
        <!-- Dynamic controls will be inserted here -->
    </div>

    <!-- Hidden file input -->
    <input type="file" id="imageInput" class="hidden" accept="image/*" multiple>

    <script>
        let canvas;
        let currentMode = 'draw';
        let history = [];
        let historyIndex = -1;
        let cropBox = null;
        let originalImage = null;

        // Initialize
        function init() {
            canvas = new fabric.Canvas('canvas', {
                width: 800,
                height: 600,
                backgroundColor: 'white'
            });

            // Event listeners
            canvas.on('object:added', saveState);
            canvas.on('object:removed', saveState);
            canvas.on('object:modified', saveState);

            document.getElementById('imageInput').addEventListener('change', handleImageUpload);

            // Set initial mode
            setMode('draw');
            saveState();
        }

        // Mode switching
        function setMode(mode) {
            currentMode = mode;
            
            // Update active button
            document.querySelectorAll('.btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // Update controls
            updateControls(mode);
            
            // Disable drawing mode for non-draw modes
            canvas.isDrawingMode = (mode === 'draw');
        }

        // Update controls based on mode
        function updateControls(mode) {
            const controls = document.getElementById('controls');
            
            switch(mode) {
                case 'draw':
                    controls.innerHTML = `
                        <div class="control-group">
                            <label>Size:</label>
                            <input type="range" min="1" max="50" value="5" onchange="setBrushSize(this.value)">
                        </div>
                        <div class="control-group">
                            <label>Color:</label>
                            <input type="color" value="#000000" onchange="setBrushColor(this.value)">
                        </div>
                        <button class="btn" onclick="toggleDrawing()">Toggle Draw</button>
                    `;
                    break;
                    
                case 'shapes':
                    controls.innerHTML = `
                        <button class="btn" onclick="addShape('rect')">Rectangle</button>
                        <button class="btn" onclick="addShape('circle')">Circle</button>
                        <button class="btn" onclick="addShape('triangle')">Triangle</button>
                        <div class="control-group">
                            <label>Fill:</label>
                            <input type="color" value="#3498db" onchange="setShapeFill(this.value)">
                        </div>
                    `;
                    break;
                    
                case 'text':
                    controls.innerHTML = `
                        <button class="btn" onclick="addText()">Add Text</button>
                        <div class="control-group">
                            <label>Size:</label>
                            <input type="number" min="8" max="100" value="24" onchange="setTextSize(this.value)">
                        </div>
                        <div class="control-group">
                            <label>Color:</label>
                            <input type="color" value="#000000" onchange="setTextColor(this.value)">
                        </div>
                    `;
                    break;
                    
                case 'images':
                    controls.innerHTML = `
                        <button class="btn" onclick="uploadImage()">Upload Image</button>
                        <button class="btn" onclick="uploadMultipleImages()">Upload Multiple</button>
                    `;
                    break;
                    
                case 'crop':
                    controls.innerHTML = `
                        <button class="btn" onclick="enableCrop()">Enable Crop</button>
                        <button class="btn" onclick="applyCrop()">Apply Crop</button>
                        <button class="btn" onclick="cancelCrop()">Cancel Crop</button>
                    `;
                    break;
            }
        }

        // Drawing functions
        function toggleDrawing() {
            canvas.isDrawingMode = !canvas.isDrawingMode;
            if (canvas.isDrawingMode) {
                canvas.freeDrawingBrush = new fabric.PencilBrush(canvas);
                canvas.freeDrawingBrush.width = 5;
                canvas.freeDrawingBrush.color = '#000000';
            }
        }

        function setBrushSize(size) {
            if (canvas.freeDrawingBrush) {
                canvas.freeDrawingBrush.width = parseInt(size);
            }
        }

        function setBrushColor(color) {
            if (canvas.freeDrawingBrush) {
                canvas.freeDrawingBrush.color = color;
            }
        }

        // Shape functions
        let shapeFill = '#3498db';

        function setShapeFill(color) {
            shapeFill = color;
        }

        function addShape(type) {
            let shape;
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            switch(type) {
                case 'rect':
                    shape = new fabric.Rect({
                        left: centerX - 50,
                        top: centerY - 25,
                        width: 100,
                        height: 50,
                        fill: shapeFill
                    });
                    break;
                case 'circle':
                    shape = new fabric.Circle({
                        left: centerX - 25,
                        top: centerY - 25,
                        radius: 25,
                        fill: shapeFill
                    });
                    break;
                case 'triangle':
                    shape = new fabric.Triangle({
                        left: centerX - 25,
                        top: centerY - 25,
                        width: 50,
                        height: 50,
                        fill: shapeFill
                    });
                    break;
            }
            
            if (shape) {
                canvas.add(shape);
                canvas.setActiveObject(shape);
                canvas.renderAll();
            }
        }

        // Text functions
        let textSize = 24;
        let textColor = '#000000';

        function setTextSize(size) {
            textSize = parseInt(size);
        }

        function setTextColor(color) {
            textColor = color;
        }

        function addText() {
            const text = new fabric.Text('Click to edit', {
                left: canvas.width / 2,
                top: canvas.height / 2,
                fontSize: textSize,
                fill: textColor,
                originX: 'center',
                originY: 'center'
            });
            
            canvas.add(text);
            canvas.setActiveObject(text);
            canvas.renderAll();
        }

        // Image functions
        function uploadImage() {
            document.getElementById('imageInput').click();
        }

        function uploadMultipleImages() {
            const input = document.getElementById('imageInput');
            input.multiple = true;
            input.click();
        }

        function handleImageUpload(e) {
            const files = Array.from(e.target.files);
            
            files.forEach((file, index) => {
                const reader = new FileReader();
                reader.onload = function(event) {
                    fabric.Image.fromURL(event.target.result, function(img) {
                        // Scale image to fit canvas
                        const scale = Math.min(
                            (canvas.width * 0.8) / img.width,
                            (canvas.height * 0.8) / img.height,
                            1
                        );
                        
                        img.set({
                            left: canvas.width / 2,
                            top: canvas.height / 2,
                            originX: 'center',
                            originY: 'center',
                            scaleX: scale,
                            scaleY: scale
                        });
                        
                        // If multiple images, arrange them
                        if (files.length > 1) {
                            const spacing = 120;
                            const cols = Math.ceil(Math.sqrt(files.length));
                            const row = Math.floor(index / cols);
                            const col = index % cols;
                            
                            img.set({
                                left: (canvas.width / 2) + (col - cols/2) * spacing,
                                top: (canvas.height / 2) + (row - Math.ceil(files.length/cols)/2) * spacing
                            });
                        }
                        
                        canvas.add(img);
                        canvas.renderAll();
                    });
                };
                reader.readAsDataURL(file);
            });
            
            e.target.value = '';
        }

        // Crop functions
        function enableCrop() {
            const activeObject = canvas.getActiveObject();
            if (!activeObject || activeObject.type !== 'image') {
                alert('Please select an image to crop');
                return;
            }
            
            originalImage = activeObject;
            const bounds = activeObject.getBoundingRect();
            
            cropBox = new fabric.Rect({
                left: bounds.left + 20,
                top: bounds.top + 20,
                width: bounds.width - 40,
                height: bounds.height - 40,
                fill: 'rgba(0,123,255,0.1)',
                stroke: '#007bff',
                strokeWidth: 2,
                strokeDashArray: [5, 5],
                selectable: true,
                evented: true
            });
            
            canvas.add(cropBox);
            canvas.setActiveObject(cropBox);
            canvas.renderAll();
        }

        function applyCrop() {
            if (!originalImage || !cropBox) {
                alert('Please enable crop mode first');
                return;
            }

            // Simple crop implementation
            const cropBounds = cropBox.getBoundingRect();
            const imageBounds = originalImage.getBoundingRect();
            
            // Create cropped version (simplified)
            canvas.remove(cropBox);
            canvas.remove(originalImage);
            
            // Add a placeholder for the cropped image
            const croppedPlaceholder = new fabric.Rect({
                left: cropBounds.left,
                top: cropBounds.top,
                width: cropBounds.width,
                height: cropBounds.height,
                fill: '#ddd',
                stroke: '#999',
                strokeWidth: 1
            });
            
            const croppedText = new fabric.Text('Cropped Image', {
                left: cropBounds.left + cropBounds.width/2,
                top: cropBounds.top + cropBounds.height/2,
                fontSize: 16,
                originX: 'center',
                originY: 'center'
            });
            
            canvas.add(croppedPlaceholder);
            canvas.add(croppedText);
            canvas.renderAll();
            
            cropBox = null;
            originalImage = null;
        }

        function cancelCrop() {
            if (cropBox) {
                canvas.remove(cropBox);
                cropBox = null;
                originalImage = null;
                canvas.renderAll();
            }
        }

        // History functions
        function saveState() {
            const state = JSON.stringify(canvas.toJSON());
            history = history.slice(0, historyIndex + 1);
            history.push(state);
            historyIndex++;
            
            if (history.length > 20) {
                history.shift();
                historyIndex--;
            }
        }

        function undo() {
            if (historyIndex > 0) {
                historyIndex--;
                canvas.loadFromJSON(history[historyIndex], function() {
                    canvas.renderAll();
                });
            }
        }

        function redo() {
            if (historyIndex < history.length - 1) {
                historyIndex++;
                canvas.loadFromJSON(history[historyIndex], function() {
                    canvas.renderAll();
                });
            }
        }

        // Export function
        function exportCanvas() {
            const dataURL = canvas.toDataURL('image/png');
            const link = document.createElement('a');
            link.download = 'canvas.png';
            link.href = dataURL;
            link.click();
        }

        // Initialize when page loads
        window.addEventListener('load', init);
    </script>
</body>
</html>
