<?php
require_once 'config.php';

// Only process POST requests
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $response = ['success' => false, 'message' => ''];
    
    // Get email from form
    $email = trim($_POST['email']);
    
    // Validate email
    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $response['message'] = 'Please enter a valid email address';
        echo json_encode($response);
        exit;
    }
    
    // Check if email exists in database
    $sql = "SELECT user_id, full_name FROM users WHERE email = ?";
    if ($stmt = $conn->prepare($sql)) {
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $stmt->store_result();
        
        if ($stmt->num_rows > 0) {
            $stmt->bind_result($userId, $fullName);
            $stmt->fetch();
            
            // Generate unique token
            $token = bin2hex(random_bytes(32));
            $expiry = date('Y-m-d H:i:s', strtotime('+1 hour'));
            
            // Delete any existing reset tokens for this user
            $deleteSql = "DELETE FROM password_resets WHERE user_id = ?";
            $deleteStmt = $conn->prepare($deleteSql);
            $deleteStmt->bind_param("i", $userId);
            $deleteStmt->execute();
            $deleteStmt->close();
            
            // Insert new reset token
            $insertSql = "INSERT INTO password_resets (user_id, token, expiry_date) VALUES (?, ?, ?)";
            $insertStmt = $conn->prepare($insertSql);
            $insertStmt->bind_param("iss", $userId, $token, $expiry);
            
            if ($insertStmt->execute()) {
                // Send password reset email
                $resetLink = "https://" . $_SERVER['HTTP_HOST'] . "/reset_password.php?token=" . $token;
                
                $subject = "Password Reset - 3D Product Customizer";
                
                $message = "
                <html>
                <head>
                    <title>Password Reset</title>
                </head>
                <body>
                    <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd;'>
                        <h2 style='color: #2c3e50;'>Password Reset Request</h2>
                        <p>Hello $fullName,</p>
                        <p>You requested a password reset for your 3D Product Customizer account. Please click the link below to reset your password:</p>
                        
                        <div style='margin: 30px 0; text-align: center;'>
                            <a href='$resetLink' style='background-color: #3498db; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;'>Reset Password</a>
                        </div>
                        
                        <p>This link will expire in 1 hour.</p>
                        
                        <p>If you didn't request this password reset, you can ignore this email.</p>
                        
                        <p>Best regards,<br>The 3D Product Customizer Team</p>
                    </div>
                </body>
                </html>
                ";
                
                // Headers for HTML email
                $headers = "MIME-Version: 1.0\r\n";
                $headers .= "Content-type: text/html; charset=UTF-8\r\n";
                $headers .= "From: 3D Product Customizer <<EMAIL>>\r\n";
                
                // Send the email
                if (mail($email, $subject, $message, $headers)) {
                    $response['success'] = true;
                    $response['message'] = 'Password reset link has been sent to your email address';
                } else {
                    $response['message'] = 'Failed to send password reset email. Please try again later.';
                }
            } else {
                $response['message'] = 'Error creating password reset token: ' . $insertStmt->error;
            }
            
            $insertStmt->close();
        } else {
            // For security reasons, don't reveal if email exists or not
            $response['success'] = true;
            $response['message'] = 'If your email is registered, you will receive a password reset link shortly';
        }
        
        $stmt->close();
    } else {
        $response['message'] = 'Database error: ' . $conn->error;
    }
    
    // Return JSON response
    echo json_encode($response);
} else {
    // Redirect if accessed directly
    header("Location: forgot_password.php");
    exit;
}
?>
