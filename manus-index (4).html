<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محرر Fabric.js</title>
    <style>
        * {
            box-sizing: border-box;
        }
        
        body {
            margin: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            flex-direction: column;
            height: 100vh;
            overflow: hidden;
            background-color: #f5f5f5;
        }
        
        .toolbar-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 16px;
            margin: 4px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 80px;
            justify-content: center;
            position: relative;
        }
        
        .toolbar-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        .toolbar-btn:active {
            transform: translateY(0);
        }
        
        .toolbar-btn.active {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .toolbar-btn::before {
            content: attr(data-tooltip);
            position: absolute;
            bottom: -35px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s;
            z-index: 1000;
        }
        
        .toolbar-btn:hover::before {
            opacity: 1;
        }
        
        #top-toolbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 12px;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
            gap: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        #sub-toolbar {
            background-color: #ffffff;
            padding: 8px 12px;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            flex-wrap: wrap;
            min-height: 50px;
            border-bottom: 1px solid #e0e0e0;
            gap: 8px;
        }
        
        .sub-toolbar-btn {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            color: #495057;
            padding: 8px 12px;
            margin: 2px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .sub-toolbar-btn:hover {
            background: #e9ecef;
            border-color: #adb5bd;
        }
        
        .sub-toolbar-btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        
        #canvas-container {
            flex-grow: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: auto;
            background: linear-gradient(45deg, #f0f2f5 25%, transparent 25%), 
                        linear-gradient(-45deg, #f0f2f5 25%, transparent 25%), 
                        linear-gradient(45deg, transparent 75%, #f0f2f5 75%), 
                        linear-gradient(-45deg, transparent 75%, #f0f2f5 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
            padding: 20px;
        }
        
        #fabricCanvas {
            border: 2px solid #dee2e6;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            background: white;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 12px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: #000;
        }
        
        .color-picker {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            margin: 4px;
        }
        
        .slider-container {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 8px 0;
        }
        
        .slider {
            flex-grow: 1;
            height: 6px;
            border-radius: 3px;
            background: #ddd;
            outline: none;
            -webkit-appearance: none;
        }
        
        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
        }
        
        .slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
            border: none;
        }
        
        /* Mobile responsiveness */
        @media (max-width: 768px) {
            .toolbar-btn {
                padding: 10px 12px;
                font-size: 12px;
                min-width: 60px;
            }
            
            #top-toolbar {
                padding: 8px;
                gap: 4px;
            }
            
            #sub-toolbar {
                padding: 6px 8px;
                gap: 4px;
            }
            
            .sub-toolbar-btn {
                padding: 6px 8px;
                font-size: 12px;
            }
            
            #canvas-container {
                padding: 10px;
            }
            
            .modal-content {
                margin: 10% auto;
                width: 95%;
                padding: 15px;
            }
        }
        
        @media (max-width: 480px) {
            .toolbar-btn {
                padding: 8px 10px;
                font-size: 11px;
                min-width: 50px;
            }
            
            #top-toolbar {
                padding: 6px;
            }
            
            .toolbar-btn::before {
                display: none; /* Hide tooltips on very small screens */
            }
        }
        
        /* Touch-friendly styles */
        @media (pointer: coarse) {
            .toolbar-btn, .sub-toolbar-btn {
                min-height: 44px; /* Apple's recommended touch target size */
            }
        }
    </style>
</head>
<body>
    <div id="top-toolbar">
        <button class="toolbar-btn" data-category="draw" data-tooltip="أدوات الرسم">
            ✏️ رسم
        </button>
        <button class="toolbar-btn" data-category="shapes" data-tooltip="الأشكال الهندسية">
            🔷 أشكال
        </button>
        <button class="toolbar-btn" data-category="text" data-tooltip="أدوات النص">
            📝 نص
        </button>
        <button class="toolbar-btn" data-category="images" data-tooltip="إدارة الصور">
            🖼️ صور
        </button>
        <button class="toolbar-btn" data-category="crop" data-tooltip="قص الصور">
            ✂️ قص
        </button>
        <button class="toolbar-btn" data-category="effects" data-tooltip="التأثيرات والفلاتر">
            ✨ تأثيرات
        </button>
        <button class="toolbar-btn" data-category="library" data-tooltip="مكتبة العناصر">
            📚 مكتبة
        </button>
        <button class="toolbar-btn" data-category="view" data-tooltip="خيارات العرض">
            👁️ عرض
        </button>
        <button class="toolbar-btn" data-category="export" data-tooltip="تصدير العمل">
            💾 تصدير
        </button>
    </div>
    <div id="sub-toolbar">
        <!-- Sub-toolbar for dynamic tools -->
    </div>
    <div id="canvas-container">
        <canvas id="fabricCanvas"></canvas>
    </div>

    <!-- PNG Library Modal -->
    <div id="pngLibraryModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closePNGLibrary()">&times;</span>
            <h2>مكتبة العناصر</h2>
            <div id="libraryGrid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(80px, 1fr)); gap: 10px; padding: 20px;">
                <!-- Library items will be populated here -->
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.1/fabric.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/hammer.js/2.0.8/hammer.min.js"></script>
    <script>
        // Global variables
        let canvas;
        let currentTool = 'select';
        let currentCategory = '';
        let isDrawing = false;
        let drawingBrush = null;
        let history = [];
        let historyStep = 0;
        let cropMode = false;
        let cropRect = null;

        // Initialize Fabric.js canvas
        function initCanvas() {
            canvas = new fabric.Canvas('fabricCanvas', {
                width: Math.min(window.innerWidth * 0.8, 800),
                height: Math.min(window.innerHeight * 0.7, 600),
                backgroundColor: 'white'
            });

            // Enable object controls
            canvas.selection = true;
            canvas.preserveObjectStacking = true;

            // Save initial state
            saveState();

            // Canvas event listeners
            canvas.on('object:added', saveState);
            canvas.on('object:removed', saveState);
            canvas.on('object:modified', saveState);
        }

        // History management
        function saveState() {
            if (historyStep < history.length - 1) {
                history = history.slice(0, historyStep + 1);
            }
            history.push(JSON.stringify(canvas.toJSON()));
            historyStep = history.length - 1;
            
            // Limit history to 50 steps
            if (history.length > 50) {
                history = history.slice(-50);
                historyStep = history.length - 1;
            }
        }

        function undo() {
            if (historyStep > 0) {
                historyStep--;
                canvas.loadFromJSON(history[historyStep], () => {
                    canvas.renderAll();
                });
            }
        }

        function redo() {
            if (historyStep < history.length - 1) {
                historyStep++;
                canvas.loadFromJSON(history[historyStep], () => {
                    canvas.renderAll();
                });
            }
        }

        // Toolbar management
        function setActiveCategory(category) {
            // Remove active class from all buttons
            document.querySelectorAll('.toolbar-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // Add active class to selected button
            document.querySelector(`[data-category="${category}"]`).classList.add('active');
            
            currentCategory = category;
            updateSubToolbar(category);
        }

        function updateSubToolbar(category) {
            const subToolbar = document.getElementById('sub-toolbar');
            subToolbar.innerHTML = '';

            switch(category) {
                case 'draw':
                    subToolbar.innerHTML = `
                        <button class="sub-toolbar-btn" onclick="setDrawingMode('pencil')">✏️ قلم رصاص</button>
                        <button class="sub-toolbar-btn" onclick="setDrawingMode('brush')">🖌️ فرشاة</button>
                        <button class="sub-toolbar-btn" onclick="setDrawingMode('marker')">🖊️ قلم تحديد</button>
                        <button class="sub-toolbar-btn" onclick="setDrawingMode('spray')">💨 رذاذ</button>
                        <button class="sub-toolbar-btn" onclick="setDrawingMode('eraser')">🧽 ممحاة</button>
                        <input type="color" id="brushColor" value="#000000" onchange="setBrushColor(this.value)">
                        <input type="range" id="brushSize" min="1" max="50" value="5" onchange="setBrushSize(this.value)">
                        <span id="brushSizeDisplay">5px</span>
                    `;
                    break;
                case 'shapes':
                    subToolbar.innerHTML = `
                        <button class="sub-toolbar-btn" onclick="addShape('rectangle')">⬜ مستطيل</button>
                        <button class="sub-toolbar-btn" onclick="addShape('circle')">⭕ دائرة</button>
                        <button class="sub-toolbar-btn" onclick="addShape('triangle')">🔺 مثلث</button>
                        <button class="sub-toolbar-btn" onclick="addShape('line')">📏 خط</button>
                        <button class="sub-toolbar-btn" onclick="addShape('polygon')">🔷 مضلع</button>
                        <input type="color" id="shapeColor" value="#3498db" onchange="setShapeColor(this.value)">
                        <input type="color" id="strokeColor" value="#2c3e50" onchange="setStrokeColor(this.value)">
                        <input type="range" id="strokeWidth" min="0" max="20" value="2" onchange="setStrokeWidth(this.value)">
                    `;
                    break;
                case 'text':
                    subToolbar.innerHTML = `
                        <button class="sub-toolbar-btn" onclick="addText()">📝 إضافة نص</button>
                        <button class="sub-toolbar-btn" onclick="addTextbox()">📄 صندوق نص</button>
                        <select id="fontFamily" onchange="setFontFamily(this.value)">
                            <option value="Arial">Arial</option>
                            <option value="Times New Roman">Times New Roman</option>
                            <option value="Helvetica">Helvetica</option>
                            <option value="Tahoma">Tahoma</option>
                            <option value="Amiri">Amiri (عربي)</option>
                        </select>
                        <input type="number" id="fontSize" value="20" min="8" max="200" onchange="setFontSize(this.value)">
                        <input type="color" id="textColor" value="#000000" onchange="setTextColor(this.value)">
                        <button class="sub-toolbar-btn" onclick="toggleBold()">🅱️ عريض</button>
                        <button class="sub-toolbar-btn" onclick="toggleItalic()">🅸 مائل</button>
                    `;
                    break;
                case 'images':
                    subToolbar.innerHTML = `
                        <input type="file" id="imageUpload" multiple accept="image/*" onchange="uploadImages(this.files)" style="display: none;">
                        <button class="sub-toolbar-btn" onclick="document.getElementById('imageUpload').click()">📁 رفع صور</button>
                        <button class="sub-toolbar-btn" onclick="duplicateObject()">📋 نسخ</button>
                        <button class="sub-toolbar-btn" onclick="deleteSelected()">🗑️ حذف</button>
                        <button class="sub-toolbar-btn" onclick="groupObjects()">📦 تجميع</button>
                        <button class="sub-toolbar-btn" onclick="ungroupObjects()">📤 إلغاء التجميع</button>
                        <button class="sub-toolbar-btn" onclick="bringToFront()">⬆️ للأمام</button>
                        <button class="sub-toolbar-btn" onclick="sendToBack()">⬇️ للخلف</button>
                        <button class="sub-toolbar-btn" onclick="flipHorizontal()">↔️ قلب أفقي</button>
                        <button class="sub-toolbar-btn" onclick="flipVertical()">↕️ قلب عمودي</button>
                    `;
                    break;
                case 'effects':
                    subToolbar.innerHTML = `
                        <button class="sub-toolbar-btn" onclick="applyFilter('brightness')">☀️ سطوع</button>
                        <button class="sub-toolbar-btn" onclick="applyFilter('contrast')">🌗 تباين</button>
                        <button class="sub-toolbar-btn" onclick="applyFilter('blur')">🌫️ ضبابية</button>
                        <button class="sub-toolbar-btn" onclick="applyFilter('saturation')">🎨 تشبع</button>
                        <button class="sub-toolbar-btn" onclick="applyFilter('grayscale')">⚫ رمادي</button>
                        <button class="sub-toolbar-btn" onclick="applyFilter('invert')">🔄 عكس</button>
                        <button class="sub-toolbar-btn" onclick="applyFilter('sepia')">🟤 سيبيا</button>
                        <button class="sub-toolbar-btn" onclick="removeFilters()">🧹 إزالة الفلاتر</button>
                        <button class="sub-toolbar-btn" onclick="addImageBorder()">🖼️ إطار</button>
                        <button class="sub-toolbar-btn" onclick="addImageShadow()">🌑 ظل</button>
                    `;
                    break;
                case 'crop':
                    subToolbar.innerHTML = `
                        <button class="sub-toolbar-btn" onclick="enableCropMode()">✂️ تفعيل القص</button>
                        <button class="sub-toolbar-btn" onclick="applyCrop()">✅ تطبيق القص</button>
                        <button class="sub-toolbar-btn" onclick="cancelCrop()">❌ إلغاء القص</button>
                        <button class="sub-toolbar-btn" onclick="setCropRatio('free')">🆓 حر</button>
                        <button class="sub-toolbar-btn" onclick="setCropRatio('1:1')">⬜ مربع (1:1)</button>
                        <button class="sub-toolbar-btn" onclick="setCropRatio('4:3')">📺 4:3</button>
                        <button class="sub-toolbar-btn" onclick="setCropRatio('16:9')">📱 16:9</button>
                        <button class="sub-toolbar-btn" onclick="setCropRatio('9:16')">📲 9:16 (Stories)</button>
                        <button class="sub-toolbar-btn" onclick="setCropRatio('a4')">📄 A4</button>
                        <button class="sub-toolbar-btn" onclick="setCropRatio('instagram')">📷 Instagram</button>
                        <button class="sub-toolbar-btn" onclick="setCropRatio('youtube')">🎥 YouTube</button>
                        <button class="sub-toolbar-btn" onclick="setCropRatio('facebook')">📘 Facebook</button>
                        <button class="sub-toolbar-btn" onclick="setCropRatio('tiktok')">🎵 TikTok</button>
                    `;
                    break;
                case 'library':
                    subToolbar.innerHTML = `
                        <button class="sub-toolbar-btn" onclick="openPNGLibrary()">📚 فتح المكتبة</button>
                        <button class="sub-toolbar-btn" onclick="addIcon('star')">⭐ نجمة</button>
                        <button class="sub-toolbar-btn" onclick="addIcon('heart')">❤️ قلب</button>
                        <button class="sub-toolbar-btn" onclick="addIcon('arrow')">➡️ سهم</button>
                        <button class="sub-toolbar-btn" onclick="addIcon('check')">✅ صح</button>
                        <button class="sub-toolbar-btn" onclick="addIcon('cross')">❌ خطأ</button>
                        <button class="sub-toolbar-btn" onclick="addIcon('info')">ℹ️ معلومات</button>
                        <button class="sub-toolbar-btn" onclick="addIcon('warning')">⚠️ تحذير</button>
                        <button class="sub-toolbar-btn" onclick="addIcon('home')">🏠 منزل</button>
                        <button class="sub-toolbar-btn" onclick="addIcon('user')">👤 مستخدم</button>
                        <button class="sub-toolbar-btn" onclick="addIcon('mail')">📧 بريد</button>
                    `;
                    break;
                case 'view':
                    subToolbar.innerHTML = `
                        <button class="sub-toolbar-btn" onclick="zoomIn()">🔍 تكبير</button>
                        <button class="sub-toolbar-btn" onclick="zoomOut()">🔍 تصغير</button>
                        <button class="sub-toolbar-btn" onclick="resetZoom()">🎯 إعادة تعيين</button>
                        <button class="sub-toolbar-btn" onclick="undo()">↶ تراجع</button>
                        <button class="sub-toolbar-btn" onclick="redo()">↷ إعادة</button>
                        <button class="sub-toolbar-btn" onclick="clearCanvas()">🧹 مسح الكل</button>
                        <button class="sub-toolbar-btn" onclick="toggleGrid()">⊞ الشبكة</button>
                    `;
                    break;
                case 'export':
                    subToolbar.innerHTML = `
                        <button class="sub-toolbar-btn" onclick="exportCanvas('png')">🖼️ PNG</button>
                        <button class="sub-toolbar-btn" onclick="exportCanvas('jpg')">📷 JPG</button>
                        <button class="sub-toolbar-btn" onclick="exportCanvas('svg')">📐 SVG</button>
                        <button class="sub-toolbar-btn" onclick="exportCanvas('json')">💾 JSON</button>
                        <button class="sub-toolbar-btn" onclick="importJSON()">📂 استيراد JSON</button>
                        <input type="file" id="jsonImport" accept=".json" onchange="loadJSON(this.files[0])" style="display: none;">
                    `;
                    break;
            }
        }

        // Enhanced drawing functions
        function setDrawingMode(mode) {
            canvas.isDrawingMode = true;
            currentTool = mode;
            
            switch(mode) {
                case 'pencil':
                    canvas.freeDrawingBrush = new fabric.PencilBrush(canvas);
                    canvas.freeDrawingBrush.width = parseInt(document.getElementById('brushSize')?.value || 5);
                    canvas.freeDrawingBrush.color = document.getElementById('brushColor')?.value || '#000000';
                    break;
                case 'brush':
                    canvas.freeDrawingBrush = new fabric.PencilBrush(canvas);
                    canvas.freeDrawingBrush.width = parseInt(document.getElementById('brushSize')?.value || 5);
                    canvas.freeDrawingBrush.color = document.getElementById('brushColor')?.value || '#000000';
                    break;
                case 'marker':
                    canvas.freeDrawingBrush = new fabric.PencilBrush(canvas);
                    canvas.freeDrawingBrush.width = parseInt(document.getElementById('brushSize')?.value || 15);
                    const markerColor = document.getElementById('brushColor')?.value || '#ffff00';
                    canvas.freeDrawingBrush.color = markerColor + '80'; // Add transparency
                    break;
                case 'spray':
                    canvas.freeDrawingBrush = new fabric.SprayBrush(canvas);
                    canvas.freeDrawingBrush.width = parseInt(document.getElementById('brushSize')?.value || 10);
                    canvas.freeDrawingBrush.color = document.getElementById('brushColor')?.value || '#000000';
                    canvas.freeDrawingBrush.density = 20;
                    canvas.freeDrawingBrush.dotWidth = 1;
                    break;
                case 'eraser':
                    canvas.freeDrawingBrush = new fabric.EraserBrush(canvas);
                    canvas.freeDrawingBrush.width = parseInt(document.getElementById('brushSize')?.value || 10);
                    break;
            }
            
            // Update active button
            document.querySelectorAll('.sub-toolbar-btn').forEach(btn => btn.classList.remove('active'));
            if (event && event.target) {
                event.target.classList.add('active');
            }
        }

        function setBrushColor(color) {
            if (canvas.freeDrawingBrush) {
                if (currentTool === 'marker') {
                    canvas.freeDrawingBrush.color = color + '80'; // Add transparency for marker
                } else {
                    canvas.freeDrawingBrush.color = color;
                }
            }
        }

        function setBrushSize(size) {
            if (canvas.freeDrawingBrush) {
                canvas.freeDrawingBrush.width = parseInt(size);
            }
            document.getElementById('brushSizeDisplay').textContent = size + 'px';
        }

        function setBrushOpacity(opacity) {
            if (canvas.freeDrawingBrush) {
                const color = canvas.freeDrawingBrush.color;
                const hex = color.replace('#', '');
                const alpha = Math.round(opacity * 255).toString(16).padStart(2, '0');
                canvas.freeDrawingBrush.color = '#' + hex.substring(0, 6) + alpha;
            }
        }

        // Text functions
        function addText() {
            canvas.isDrawingMode = false;
            const text = new fabric.Text('اكتب هنا', {
                left: 100,
                top: 100,
                fontFamily: document.getElementById('fontFamily')?.value || 'Arial',
                fontSize: parseInt(document.getElementById('fontSize')?.value || 20),
                fill: document.getElementById('textColor')?.value || '#000000'
            });
            canvas.add(text);
            canvas.setActiveObject(text);
        }

        function addTextbox() {
            canvas.isDrawingMode = false;
            const textbox = new fabric.Textbox('اكتب هنا', {
                left: 100,
                top: 100,
                width: 200,
                fontFamily: document.getElementById('fontFamily')?.value || 'Arial',
                fontSize: parseInt(document.getElementById('fontSize')?.value || 20),
                fill: document.getElementById('textColor')?.value || '#000000'
            });
            canvas.add(textbox);
            canvas.setActiveObject(textbox);
        }

        function setFontFamily(fontFamily) {
            const activeObject = canvas.getActiveObject();
            if (activeObject && (activeObject.type === 'text' || activeObject.type === 'textbox')) {
                activeObject.set('fontFamily', fontFamily);
                canvas.renderAll();
            }
        }

        function setFontSize(fontSize) {
            const activeObject = canvas.getActiveObject();
            if (activeObject && (activeObject.type === 'text' || activeObject.type === 'textbox')) {
                activeObject.set('fontSize', parseInt(fontSize));
                canvas.renderAll();
            }
        }

        function setTextColor(color) {
            const activeObject = canvas.getActiveObject();
            if (activeObject && (activeObject.type === 'text' || activeObject.type === 'textbox')) {
                activeObject.set('fill', color);
                canvas.renderAll();
            }
        }

        function toggleBold() {
            const activeObject = canvas.getActiveObject();
            if (activeObject && (activeObject.type === 'text' || activeObject.type === 'textbox')) {
                const currentWeight = activeObject.fontWeight;
                activeObject.set('fontWeight', currentWeight === 'bold' ? 'normal' : 'bold');
                canvas.renderAll();
            }
        }

        function toggleItalic() {
            const activeObject = canvas.getActiveObject();
            if (activeObject && (activeObject.type === 'text' || activeObject.type === 'textbox')) {
                const currentStyle = activeObject.fontStyle;
                activeObject.set('fontStyle', currentStyle === 'italic' ? 'normal' : 'italic');
                canvas.renderAll();
            }
        }

        function setTextAlign(alignment) {
            const activeObject = canvas.getActiveObject();
            if (activeObject && (activeObject.type === 'text' || activeObject.type === 'textbox')) {
                activeObject.set('textAlign', alignment);
                canvas.renderAll();
            }
        }

        function addTextStroke() {
            const activeObject = canvas.getActiveObject();
            if (activeObject && (activeObject.type === 'text' || activeObject.type === 'textbox')) {
                activeObject.set({
                    stroke: '#000000',
                    strokeWidth: 2
                });
                canvas.renderAll();
            }
        }

        function addTextShadow() {
            const activeObject = canvas.getActiveObject();
            if (activeObject && (activeObject.type === 'text' || activeObject.type === 'textbox')) {
                activeObject.set({
                    shadow: new fabric.Shadow({
                        color: 'rgba(0,0,0,0.3)',
                        blur: 5,
                        offsetX: 3,
                        offsetY: 3
                    })
                });
                canvas.renderAll();
            }
        }

        // Image upload function
        function uploadImages(files) {
            Array.from(files).forEach(file => {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        fabric.Image.fromURL(e.target.result, function(img) {
                            // Scale image to fit canvas if too large
                            const maxWidth = canvas.width * 0.5;
                            const maxHeight = canvas.height * 0.5;
                            
                            if (img.width > maxWidth || img.height > maxHeight) {
                                const scale = Math.min(maxWidth / img.width, maxHeight / img.height);
                                img.scale(scale);
                            }
                            
                            img.set({
                                left: Math.random() * (canvas.width - img.getScaledWidth()),
                                top: Math.random() * (canvas.height - img.getScaledHeight())
                            });
                            
                            canvas.add(img);
                            canvas.setActiveObject(img);
                        });
                    };
                    reader.readAsDataURL(file);
                }
            });
        }

        // Shape color functions
        function setShapeColor(color) {
            const activeObject = canvas.getActiveObject();
            if (activeObject && activeObject.fill !== undefined) {
                activeObject.set('fill', color);
                canvas.renderAll();
            }
        }

        function setStrokeColor(color) {
            const activeObject = canvas.getActiveObject();
            if (activeObject) {
                activeObject.set('stroke', color);
                canvas.renderAll();
            }
        }

        function setStrokeWidth(width) {
            const activeObject = canvas.getActiveObject();
            if (activeObject) {
                activeObject.set('strokeWidth', parseInt(width));
                canvas.renderAll();
            }
        }

        // Grid functionality
        let gridVisible = false;
        function toggleGrid() {
            gridVisible = !gridVisible;
            if (gridVisible) {
                addGrid();
            } else {
                removeGrid();
            }
        }

        function addGrid() {
            const gridSize = 20;
            const canvasWidth = canvas.width;
            const canvasHeight = canvas.height;
            
            // Remove existing grid
            removeGrid();
            
            // Create vertical lines
            for (let i = 0; i <= canvasWidth; i += gridSize) {
                const line = new fabric.Line([i, 0, i, canvasHeight], {
                    stroke: '#ddd',
                    strokeWidth: 1,
                    selectable: false,
                    evented: false,
                    excludeFromExport: true,
                    isGrid: true
                });
                canvas.add(line);
                canvas.sendToBack(line);
            }
            
            // Create horizontal lines
            for (let i = 0; i <= canvasHeight; i += gridSize) {
                const line = new fabric.Line([0, i, canvasWidth, i], {
                    stroke: '#ddd',
                    strokeWidth: 1,
                    selectable: false,
                    evented: false,
                    excludeFromExport: true,
                    isGrid: true
                });
                canvas.add(line);
                canvas.sendToBack(line);
            }
            
            canvas.renderAll();
        }

        function removeGrid() {
            const objects = canvas.getObjects();
            objects.forEach(obj => {
                if (obj.isGrid) {
                    canvas.remove(obj);
                }
            });
            canvas.renderAll();
        }

        // Hammer.js touch support
        function initTouchSupport() {
            const canvasElement = document.getElementById('fabricCanvas');
            const hammer = new Hammer(canvasElement);
            
            // Enable pinch and pan gestures
            hammer.get('pinch').set({ enable: true });
            hammer.get('pan').set({ direction: Hammer.DIRECTION_ALL });
            
            let lastScale = 1;
            let lastPanX = 0;
            let lastPanY = 0;
            
            // Pinch to zoom
            hammer.on('pinchstart', function(e) {
                lastScale = canvas.getZoom();
            });
            
            hammer.on('pinch', function(e) {
                const zoom = lastScale * e.scale;
                const clampedZoom = Math.min(Math.max(zoom, 0.1), 5);
                canvas.setZoom(clampedZoom);
                canvas.renderAll();
            });
            
            // Pan to move viewport
            hammer.on('panstart', function(e) {
                if (e.pointerType === 'touch' && e.touches && e.touches.length === 2) {
                    const vpt = canvas.viewportTransform;
                    lastPanX = vpt[4];
                    lastPanY = vpt[5];
                }
            });
            
            hammer.on('pan', function(e) {
                if (e.pointerType === 'touch' && e.touches && e.touches.length === 2) {
                    const vpt = canvas.viewportTransform;
                    vpt[4] = lastPanX + e.deltaX;
                    vpt[5] = lastPanY + e.deltaY;
                    canvas.requestRenderAll();
                }
            });
            
            // Double tap to reset zoom
            hammer.on('doubletap', function(e) {
                canvas.setZoom(1);
                canvas.viewportTransform = [1, 0, 0, 1, 0, 0];
                canvas.renderAll();
            });
            
            // Long press for context menu (mobile)
            hammer.on('press', function(e) {
                if (e.pointerType === 'touch') {
                    const pointer = canvas.getPointer(e.srcEvent);
                    const target = canvas.findTarget(e.srcEvent);
                    
                    if (target) {
                        canvas.setActiveObject(target);
                        showMobileContextMenu(e.center.x, e.center.y, target);
                    }
                }
            });
            
            // Add touch support to toolbar buttons
            document.querySelectorAll('.toolbar-btn, .sub-toolbar-btn').forEach(btn => {
                const btnHammer = new Hammer(btn);
                btnHammer.on('tap', function(e) {
                    e.preventDefault();
                    btn.click();
                });
            });
        }

        // Mobile context menu
        function showMobileContextMenu(x, y, target) {
            // Remove existing context menu
            const existingMenu = document.getElementById('mobileContextMenu');
            if (existingMenu) {
                existingMenu.remove();
            }
            
            const menu = document.createElement('div');
            menu.id = 'mobileContextMenu';
            menu.style.cssText = `
                position: fixed;
                left: ${x}px;
                top: ${y}px;
                background: white;
                border: 1px solid #ccc;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 1000;
                padding: 8px;
                display: flex;
                flex-direction: column;
                gap: 4px;
            `;
            
            const menuItems = [
                { text: 'نسخ', action: () => duplicateObject() },
                { text: 'حذف', action: () => deleteSelected() },
                { text: 'للأمام', action: () => bringToFront() },
                { text: 'للخلف', action: () => sendToBack() }
            ];
            
            if (target.type === 'image') {
                menuItems.push({ text: 'فلاتر', action: () => setActiveCategory('effects') });
            }
            
            if (target.type === 'text' || target.type === 'textbox') {
                menuItems.push({ text: 'تحرير النص', action: () => setActiveCategory('text') });
            }
            
            menuItems.forEach(item => {
                const btn = document.createElement('button');
                btn.textContent = item.text;
                btn.style.cssText = `
                    padding: 8px 12px;
                    border: none;
                    background: #f0f0f0;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 14px;
                `;
                btn.onclick = () => {
                    item.action();
                    menu.remove();
                };
                menu.appendChild(btn);
            });
            
            document.body.appendChild(menu);
            
            // Remove menu after 3 seconds or on outside click
            setTimeout(() => {
                if (menu.parentNode) {
                    menu.remove();
                }
            }, 3000);
            
            document.addEventListener('click', function removeMenu(e) {
                if (!menu.contains(e.target)) {
                    menu.remove();
                    document.removeEventListener('click', removeMenu);
                }
            });
        }

        // Enhanced mobile responsiveness
        function optimizeForMobile() {
            const isMobile = window.innerWidth <= 768;
            
            if (isMobile) {
                // Adjust canvas size for mobile
                const maxWidth = window.innerWidth - 20;
                const maxHeight = window.innerHeight - 200; // Account for toolbars
                
                if (canvas.width > maxWidth || canvas.height > maxHeight) {
                    const scale = Math.min(maxWidth / canvas.width, maxHeight / canvas.height);
                    canvas.setZoom(scale);
                }
                
                // Make toolbar buttons larger for touch
                document.querySelectorAll('.toolbar-btn, .sub-toolbar-btn').forEach(btn => {
                    btn.style.minHeight = '44px';
                    btn.style.padding = '12px 16px';
                });
                
                // Enable touch scrolling for sub-toolbar
                const subToolbar = document.getElementById('sub-toolbar');
                subToolbar.style.overflowX = 'auto';
                subToolbar.style.whiteSpace = 'nowrap';
            }
        }

        // Touch-friendly drawing
        function enhanceDrawingForTouch() {
            if ('ontouchstart' in window) {
                // Increase brush size for touch drawing
                const originalSetDrawingMode = setDrawingMode;
                setDrawingMode = function(mode) {
                    originalSetDrawingMode(mode);
                    if (canvas.freeDrawingBrush) {
                        const currentSize = canvas.freeDrawingBrush.width;
                        canvas.freeDrawingBrush.width = Math.max(currentSize, 8); // Minimum 8px for touch
                    }
                };
                
                // Add touch-specific drawing options
                canvas.on('path:created', function(e) {
                    // Smooth out touch paths
                    const path = e.path;
                    if (path && path.path) {
                        path.set({
                            strokeLineCap: 'round',
                            strokeLineJoin: 'round'
                        });
                    }
                });
            }
        }
        function changeCanvasSize() {
            const width = prompt('عرض Canvas الجديد (بكسل):', canvas.width);
            const height = prompt('ارتفاع Canvas الجديد (بكسل):', canvas.height);
            
            if (width && height && !isNaN(width) && !isNaN(height)) {
                canvas.setDimensions({
                    width: parseInt(width),
                    height: parseInt(height)
                });
                canvas.renderAll();
                saveState();
            }
        }

        function setCanvasBackground() {
            const color = prompt('لون خلفية Canvas:', canvas.backgroundColor || '#ffffff');
            if (color) {
                canvas.backgroundColor = color;
                canvas.renderAll();
                saveState();
            }
        }

        // Object alignment functions
        function alignObjects(alignment) {
            const activeObjects = canvas.getActiveObjects();
            if (activeObjects.length < 2) {
                alert('يرجى تحديد عنصرين أو أكثر للمحاذاة');
                return;
            }

            const canvasWidth = canvas.width;
            const canvasHeight = canvas.height;

            switch(alignment) {
                case 'left':
                    const leftMost = Math.min(...activeObjects.map(obj => obj.left));
                    activeObjects.forEach(obj => obj.set('left', leftMost));
                    break;
                case 'right':
                    const rightMost = Math.max(...activeObjects.map(obj => obj.left + obj.getScaledWidth()));
                    activeObjects.forEach(obj => obj.set('left', rightMost - obj.getScaledWidth()));
                    break;
                case 'top':
                    const topMost = Math.min(...activeObjects.map(obj => obj.top));
                    activeObjects.forEach(obj => obj.set('top', topMost));
                    break;
                case 'bottom':
                    const bottomMost = Math.max(...activeObjects.map(obj => obj.top + obj.getScaledHeight()));
                    activeObjects.forEach(obj => obj.set('top', bottomMost - obj.getScaledHeight()));
                    break;
                case 'center-horizontal':
                    activeObjects.forEach(obj => {
                        obj.set('left', (canvasWidth - obj.getScaledWidth()) / 2);
                    });
                    break;
                case 'center-vertical':
                    activeObjects.forEach(obj => {
                        obj.set('top', (canvasHeight - obj.getScaledHeight()) / 2);
                    });
                    break;
            }
            
            canvas.renderAll();
            saveState();
        }

        // Object distribution functions
        function distributeObjects(direction) {
            const activeObjects = canvas.getActiveObjects();
            if (activeObjects.length < 3) {
                alert('يرجى تحديد ثلاثة عناصر أو أكثر للتوزيع');
                return;
            }

            if (direction === 'horizontal') {
                activeObjects.sort((a, b) => a.left - b.left);
                const leftMost = activeObjects[0].left;
                const rightMost = activeObjects[activeObjects.length - 1].left + activeObjects[activeObjects.length - 1].getScaledWidth();
                const totalWidth = rightMost - leftMost;
                const spacing = totalWidth / (activeObjects.length - 1);

                activeObjects.forEach((obj, index) => {
                    if (index > 0 && index < activeObjects.length - 1) {
                        obj.set('left', leftMost + spacing * index);
                    }
                });
            } else {
                activeObjects.sort((a, b) => a.top - b.top);
                const topMost = activeObjects[0].top;
                const bottomMost = activeObjects[activeObjects.length - 1].top + activeObjects[activeObjects.length - 1].getScaledHeight();
                const totalHeight = bottomMost - topMost;
                const spacing = totalHeight / (activeObjects.length - 1);

                activeObjects.forEach((obj, index) => {
                    if (index > 0 && index < activeObjects.length - 1) {
                        obj.set('top', topMost + spacing * index);
                    }
                });
            }

            canvas.renderAll();
            saveState();
        }

        // Snapping functionality
        let snapToGrid = false;
        let snapToObjects = false;
        const snapDistance = 10;

        function toggleSnapToGrid() {
            snapToGrid = !snapToGrid;
            if (snapToGrid && !gridVisible) {
                toggleGrid();
            }
        }

        function toggleSnapToObjects() {
            snapToObjects = !snapToObjects;
        }

        // Enhanced object manipulation
        function lockObject() {
            const activeObject = canvas.getActiveObject();
            if (activeObject) {
                activeObject.set({
                    lockMovementX: true,
                    lockMovementY: true,
                    lockRotation: true,
                    lockScalingX: true,
                    lockScalingY: true,
                    selectable: false
                });
                canvas.renderAll();
            }
        }

        function unlockObject() {
            const activeObject = canvas.getActiveObject();
            if (activeObject) {
                activeObject.set({
                    lockMovementX: false,
                    lockMovementY: false,
                    lockRotation: false,
                    lockScalingX: false,
                    lockScalingY: false,
                    selectable: true
                });
                canvas.renderAll();
            }
        }

        function setObjectOpacity() {
            const activeObject = canvas.getActiveObject();
            if (activeObject) {
                const opacity = prompt('الشفافية (0-1):', activeObject.opacity || 1);
                if (opacity !== null && !isNaN(opacity)) {
                    activeObject.set('opacity', parseFloat(opacity));
                    canvas.renderAll();
                }
            }
        }

        // Layer management
        function getLayersList() {
            const objects = canvas.getObjects();
            return objects.map((obj, index) => ({
                index: index,
                type: obj.type,
                name: obj.name || `${obj.type} ${index + 1}`
            }));
        }

        function selectObjectByIndex(index) {
            const objects = canvas.getObjects();
            if (objects[index]) {
                canvas.setActiveObject(objects[index]);
                canvas.renderAll();
            }
        }

        // Enhanced text functions
        function addTextWithBackground() {
            const text = prompt('النص:', 'نص جديد');
            if (text) {
                const textObj = new fabric.Text(text, {
                    left: 100,
                    top: 100,
                    fontFamily: 'Arial',
                    fontSize: 20,
                    fill: '#000000',
                    backgroundColor: '#ffffff',
                    padding: 10
                });
                canvas.add(textObj);
                canvas.setActiveObject(textObj);
            }
        }
        function openPNGLibrary() {
            const modal = document.getElementById('pngLibraryModal');
            const grid = document.getElementById('libraryGrid');
            
            // Clear existing content
            grid.innerHTML = '';
            
            // Create library items
            const libraryItems = [
                { emoji: '⭐', name: 'نجمة', type: 'star' },
                { emoji: '❤️', name: 'قلب', type: 'heart' },
                { emoji: '➡️', name: 'سهم', type: 'arrow' },
                { emoji: '✅', name: 'صح', type: 'check' },
                { emoji: '❌', name: 'خطأ', type: 'cross' },
                { emoji: 'ℹ️', name: 'معلومات', type: 'info' },
                { emoji: '⚠️', name: 'تحذير', type: 'warning' },
                { emoji: '🏠', name: 'منزل', type: 'home' },
                { emoji: '👤', name: 'مستخدم', type: 'user' },
                { emoji: '📧', name: 'بريد', type: 'mail' },
                { emoji: '🔍', name: 'بحث', type: 'search' },
                { emoji: '⚙️', name: 'إعدادات', type: 'settings' },
                { emoji: '📱', name: 'هاتف', type: 'phone' },
                { emoji: '💻', name: 'كمبيوتر', type: 'computer' },
                { emoji: '🌟', name: 'نجمة مضيئة', type: 'sparkle' },
                { emoji: '🎨', name: 'فن', type: 'art' },
                { emoji: '📊', name: 'رسم بياني', type: 'chart' },
                { emoji: '🔒', name: 'قفل', type: 'lock' },
                { emoji: '🔓', name: 'مفتوح', type: 'unlock' },
                { emoji: '💡', name: 'فكرة', type: 'idea' }
            ];
            
            libraryItems.forEach(item => {
                const itemDiv = document.createElement('div');
                itemDiv.style.cssText = `
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    padding: 10px;
                    border: 1px solid #ddd;
                    border-radius: 8px;
                    cursor: pointer;
                    transition: all 0.2s;
                    background: white;
                `;
                
                itemDiv.innerHTML = `
                    <div style="font-size: 32px; margin-bottom: 5px;">${item.emoji}</div>
                    <div style="font-size: 12px; text-align: center;">${item.name}</div>
                `;
                
                itemDiv.addEventListener('click', () => {
                    addIcon(item.type);
                    closePNGLibrary();
                });
                
                itemDiv.addEventListener('mouseenter', () => {
                    itemDiv.style.background = '#f0f0f0';
                    itemDiv.style.transform = 'scale(1.05)';
                });
                
                itemDiv.addEventListener('mouseleave', () => {
                    itemDiv.style.background = 'white';
                    itemDiv.style.transform = 'scale(1)';
                });
                
                grid.appendChild(itemDiv);
            });
            
            modal.style.display = 'block';
        }

        function closePNGLibrary() {
            document.getElementById('pngLibraryModal').style.display = 'none';
        }

        function addIcon(iconType) {
            canvas.isDrawingMode = false;
            
            // Create SVG icon based on type
            let svgString = '';
            
            switch(iconType) {
                case 'star':
                    svgString = `<svg width="50" height="50" viewBox="0 0 24 24" fill="#FFD700">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>`;
                    break;
                case 'heart':
                    svgString = `<svg width="50" height="50" viewBox="0 0 24 24" fill="#FF6B6B">
                        <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
                    </svg>`;
                    break;
                case 'arrow':
                    svgString = `<svg width="50" height="50" viewBox="0 0 24 24" fill="#4ECDC4">
                        <path d="M5 12h14m-7-7l7 7-7 7"/>
                    </svg>`;
                    break;
                case 'check':
                    svgString = `<svg width="50" height="50" viewBox="0 0 24 24" fill="#4CAF50">
                        <path d="M20 6L9 17l-5-5"/>
                    </svg>`;
                    break;
                case 'cross':
                    svgString = `<svg width="50" height="50" viewBox="0 0 24 24" fill="#F44336">
                        <path d="M18 6L6 18M6 6l12 12"/>
                    </svg>`;
                    break;
                default:
                    // Create a text object for other icons
                    const iconMap = {
                        'info': 'ℹ️',
                        'warning': '⚠️',
                        'home': '🏠',
                        'user': '👤',
                        'mail': '📧',
                        'search': '🔍',
                        'settings': '⚙️',
                        'phone': '📱',
                        'computer': '💻',
                        'sparkle': '🌟',
                        'art': '🎨',
                        'chart': '📊',
                        'lock': '🔒',
                        'unlock': '🔓',
                        'idea': '💡'
                    };
                    
                    const iconText = new fabric.Text(iconMap[iconType] || '❓', {
                        left: 100,
                        top: 100,
                        fontSize: 50,
                        fontFamily: 'Arial'
                    });
                    
                    canvas.add(iconText);
                    canvas.setActiveObject(iconText);
                    return;
            }
            
            // Load SVG icon
            fabric.loadSVGFromString(svgString, function(objects, options) {
                const icon = fabric.util.groupSVGElements(objects, options);
                icon.set({
                    left: 100,
                    top: 100,
                    scaleX: 1,
                    scaleY: 1
                });
                canvas.add(icon);
                canvas.setActiveObject(icon);
            });
        }
        let currentCropRatio = 'free';
        
        function enableCropMode() {
            cropMode = true;
            canvas.isDrawingMode = false;
            
            // Create crop rectangle
            if (cropRect) {
                canvas.remove(cropRect);
            }
            
            cropRect = new fabric.Rect({
                left: 50,
                top: 50,
                width: canvas.width - 100,
                height: canvas.height - 100,
                fill: 'transparent',
                stroke: '#ff0000',
                strokeWidth: 2,
                strokeDashArray: [5, 5],
                selectable: true,
                evented: true,
                hasControls: true,
                hasBorders: true,
                lockRotation: true,
                cropRect: true
            });
            
            canvas.add(cropRect);
            canvas.setActiveObject(cropRect);
            canvas.bringToFront(cropRect);
        }

        function setCropRatio(ratio) {
            currentCropRatio = ratio;
            
            if (!cropRect) {
                enableCropMode();
            }
            
            let width, height;
            const canvasWidth = canvas.width;
            const canvasHeight = canvas.height;
            const maxSize = Math.min(canvasWidth, canvasHeight) - 100;
            
            switch(ratio) {
                case 'free':
                    // Keep current dimensions
                    return;
                case '1:1':
                    width = height = maxSize;
                    break;
                case '4:3':
                    width = maxSize;
                    height = (maxSize * 3) / 4;
                    break;
                case '16:9':
                    width = maxSize;
                    height = (maxSize * 9) / 16;
                    break;
                case '9:16':
                    height = maxSize;
                    width = (maxSize * 9) / 16;
                    break;
                case 'a4':
                    height = maxSize;
                    width = (maxSize * 210) / 297; // A4 ratio
                    break;
                case 'instagram':
                    width = height = maxSize; // Square for Instagram post
                    break;
                case 'youtube':
                    width = maxSize;
                    height = (maxSize * 9) / 16; // 16:9 for YouTube
                    break;
                case 'facebook':
                    width = maxSize;
                    height = (maxSize * 9) / 16; // 16:9 for Facebook
                    break;
                case 'tiktok':
                    height = maxSize;
                    width = (maxSize * 9) / 16; // 9:16 for TikTok
                    break;
            }
            
            if (width && height) {
                cropRect.set({
                    width: width,
                    height: height,
                    left: (canvasWidth - width) / 2,
                    top: (canvasHeight - height) / 2
                });
                canvas.renderAll();
            }
        }

        function applyCrop() {
            if (!cropRect) {
                alert('يرجى تفعيل وضع القص أولاً');
                return;
            }
            
            const cropLeft = cropRect.left;
            const cropTop = cropRect.top;
            const cropWidth = cropRect.width * cropRect.scaleX;
            const cropHeight = cropRect.height * cropRect.scaleY;
            
            // Create new canvas with cropped dimensions
            const croppedCanvas = new fabric.Canvas(null, {
                width: cropWidth,
                height: cropHeight
            });
            
            // Copy objects within crop area
            const objects = canvas.getObjects();
            objects.forEach(obj => {
                if (obj === cropRect) return;
                
                const objLeft = obj.left;
                const objTop = obj.top;
                const objWidth = obj.width * obj.scaleX;
                const objHeight = obj.height * obj.scaleY;
                
                // Check if object intersects with crop area
                if (objLeft < cropLeft + cropWidth && 
                    objLeft + objWidth > cropLeft &&
                    objTop < cropTop + cropHeight && 
                    objTop + objHeight > cropTop) {
                    
                    obj.clone(function(cloned) {
                        cloned.set({
                            left: cloned.left - cropLeft,
                            top: cloned.top - cropTop
                        });
                        croppedCanvas.add(cloned);
                    });
                }
            });
            
            // Update main canvas
            canvas.setDimensions({
                width: cropWidth,
                height: cropHeight
            });
            
            canvas.clear();
            canvas.backgroundColor = 'white';
            
            croppedCanvas.getObjects().forEach(obj => {
                canvas.add(obj);
            });
            
            cropMode = false;
            cropRect = null;
            canvas.renderAll();
            saveState();
        }

        function cancelCrop() {
            if (cropRect) {
                canvas.remove(cropRect);
                cropRect = null;
            }
            cropMode = false;
            canvas.renderAll();
        }
        function flipHorizontal() {
            const activeObject = canvas.getActiveObject();
            if (activeObject) {
                activeObject.set('flipX', !activeObject.flipX);
                canvas.renderAll();
            }
        }

        function flipVertical() {
            const activeObject = canvas.getActiveObject();
            if (activeObject) {
                activeObject.set('flipY', !activeObject.flipY);
                canvas.renderAll();
            }
        }

        // Image filters
        function applyFilter(filterType) {
            const activeObject = canvas.getActiveObject();
            if (activeObject && activeObject.type === 'image') {
                let filter;
                
                switch(filterType) {
                    case 'brightness':
                        filter = new fabric.Image.filters.Brightness({
                            brightness: 0.2
                        });
                        break;
                    case 'contrast':
                        filter = new fabric.Image.filters.Contrast({
                            contrast: 0.3
                        });
                        break;
                    case 'blur':
                        filter = new fabric.Image.filters.Blur({
                            blur: 0.1
                        });
                        break;
                    case 'saturation':
                        filter = new fabric.Image.filters.Saturation({
                            saturation: 0.5
                        });
                        break;
                    case 'grayscale':
                        filter = new fabric.Image.filters.Grayscale();
                        break;
                    case 'invert':
                        filter = new fabric.Image.filters.Invert();
                        break;
                    case 'sepia':
                        filter = new fabric.Image.filters.Sepia();
                        break;
                }
                
                if (filter) {
                    activeObject.filters.push(filter);
                    activeObject.applyFilters();
                    canvas.renderAll();
                }
            }
        }

        function removeFilters() {
            const activeObject = canvas.getActiveObject();
            if (activeObject && activeObject.type === 'image') {
                activeObject.filters = [];
                activeObject.applyFilters();
                canvas.renderAll();
            }
        }

        function addImageBorder() {
            const activeObject = canvas.getActiveObject();
            if (activeObject && activeObject.type === 'image') {
                activeObject.set({
                    stroke: '#000000',
                    strokeWidth: 5
                });
                canvas.renderAll();
            }
        }

        function addImageShadow() {
            const activeObject = canvas.getActiveObject();
            if (activeObject && activeObject.type === 'image') {
                activeObject.set({
                    shadow: new fabric.Shadow({
                        color: 'rgba(0,0,0,0.5)',
                        blur: 10,
                        offsetX: 5,
                        offsetY: 5
                    })
                });
                canvas.renderAll();
            }
        }

        // Shape functions
        function addShape(type) {
            canvas.isDrawingMode = false;
            let shape;
            
            switch(type) {
                case 'rectangle':
                    shape = new fabric.Rect({
                        left: 100,
                        top: 100,
                        width: 100,
                        height: 80,
                        fill: document.getElementById('shapeColor')?.value || '#3498db',
                        stroke: document.getElementById('strokeColor')?.value || '#2c3e50',
                        strokeWidth: parseInt(document.getElementById('strokeWidth')?.value || 2)
                    });
                    break;
                case 'circle':
                    shape = new fabric.Circle({
                        left: 100,
                        top: 100,
                        radius: 50,
                        fill: document.getElementById('shapeColor')?.value || '#3498db',
                        stroke: document.getElementById('strokeColor')?.value || '#2c3e50',
                        strokeWidth: parseInt(document.getElementById('strokeWidth')?.value || 2)
                    });
                    break;
                case 'triangle':
                    shape = new fabric.Triangle({
                        left: 100,
                        top: 100,
                        width: 100,
                        height: 100,
                        fill: document.getElementById('shapeColor')?.value || '#3498db',
                        stroke: document.getElementById('strokeColor')?.value || '#2c3e50',
                        strokeWidth: parseInt(document.getElementById('strokeWidth')?.value || 2)
                    });
                    break;
                case 'line':
                    shape = new fabric.Line([50, 100, 200, 100], {
                        stroke: document.getElementById('strokeColor')?.value || '#2c3e50',
                        strokeWidth: parseInt(document.getElementById('strokeWidth')?.value || 2)
                    });
                    break;
            }
            
            if (shape) {
                canvas.add(shape);
                canvas.setActiveObject(shape);
            }
        }

        // Object manipulation functions
        function duplicateObject() {
            const activeObject = canvas.getActiveObject();
            if (activeObject) {
                activeObject.clone(function(cloned) {
                    cloned.set({
                        left: cloned.left + 10,
                        top: cloned.top + 10
                    });
                    canvas.add(cloned);
                    canvas.setActiveObject(cloned);
                });
            }
        }

        function deleteSelected() {
            const activeObjects = canvas.getActiveObjects();
            if (activeObjects.length) {
                activeObjects.forEach(obj => canvas.remove(obj));
                canvas.discardActiveObject();
            }
        }

        function groupObjects() {
            const activeObjects = canvas.getActiveObjects();
            if (activeObjects.length > 1) {
                const group = new fabric.Group(activeObjects);
                canvas.remove(...activeObjects);
                canvas.add(group);
                canvas.setActiveObject(group);
            }
        }

        function ungroupObjects() {
            const activeObject = canvas.getActiveObject();
            if (activeObject && activeObject.type === 'group') {
                const items = activeObject._objects;
                activeObject._restoreObjectsState();
                canvas.remove(activeObject);
                items.forEach(item => canvas.add(item));
                canvas.renderAll();
            }
        }

        function bringToFront() {
            const activeObject = canvas.getActiveObject();
            if (activeObject) {
                canvas.bringToFront(activeObject);
            }
        }

        function sendToBack() {
            const activeObject = canvas.getActiveObject();
            if (activeObject) {
                canvas.sendToBack(activeObject);
            }
        }

        // Canvas control functions
        function zoomIn() {
            const zoom = canvas.getZoom();
            canvas.setZoom(zoom * 1.1);
        }

        function zoomOut() {
            const zoom = canvas.getZoom();
            canvas.setZoom(zoom * 0.9);
        }

        function resetZoom() {
            canvas.setZoom(1);
            canvas.viewportTransform = [1, 0, 0, 1, 0, 0];
            canvas.renderAll();
        }

        function clearCanvas() {
            if (confirm('هل أنت متأكد من مسح جميع العناصر؟')) {
                canvas.clear();
                canvas.backgroundColor = 'white';
                saveState();
            }
        }

        // Export functions
        function exportCanvas(format) {
            let dataURL;
            switch(format) {
                case 'png':
                    dataURL = canvas.toDataURL('image/png');
                    downloadFile(dataURL, 'canvas.png');
                    break;
                case 'jpg':
                    dataURL = canvas.toDataURL('image/jpeg', 0.9);
                    downloadFile(dataURL, 'canvas.jpg');
                    break;
                case 'svg':
                    const svg = canvas.toSVG();
                    downloadFile('data:image/svg+xml;charset=utf-8,' + encodeURIComponent(svg), 'canvas.svg');
                    break;
                case 'json':
                    const json = JSON.stringify(canvas.toJSON());
                    downloadFile('data:application/json;charset=utf-8,' + encodeURIComponent(json), 'canvas.json');
                    break;
            }
        }

        function downloadFile(dataURL, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = dataURL;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            initCanvas();
            initTouchSupport();
            enhanceDrawingForTouch();
            optimizeForMobile();
            
            // Add click listeners to main toolbar buttons
            document.querySelectorAll('.toolbar-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    setActiveCategory(this.dataset.category);
                });
            });

            // Keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey || e.metaKey) {
                    switch(e.key) {
                        case 'z':
                            e.preventDefault();
                            if (e.shiftKey) {
                                redo();
                            } else {
                                undo();
                            }
                            break;
                        case 'y':
                            e.preventDefault();
                            redo();
                            break;
                        case 'c':
                            e.preventDefault();
                            duplicateObject();
                            break;
                        case 'Delete':
                        case 'Backspace':
                            e.preventDefault();
                            deleteSelected();
                            break;
                    }
                }
            });

            // Window resize handler
            window.addEventListener('resize', function() {
                optimizeForMobile();
                const container = document.getElementById('canvas-container');
                const maxWidth = container.clientWidth - 40;
                const maxHeight = container.clientHeight - 40;
                
                canvas.setDimensions({
                    width: Math.min(canvas.width, maxWidth),
                    height: Math.min(canvas.height, maxHeight)
                });
                canvas.renderAll();
            });

            // Close modal when clicking outside
            window.addEventListener('click', function(e) {
                const modal = document.getElementById('pngLibraryModal');
                if (e.target === modal) {
                    closePNGLibrary();
                }
            });
        });

    </script>
</body>
</html>

