<!DOCTYPE html>
<html lang="ar">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Pixie + Three.js — تحسين جودة الصور</title>
  <link href="https://fonts.googleapis.com/css?family=Tajawal:400,700&display=swap" rel="stylesheet">
  <style>
    html, body { margin:0; padding:0; width:100%; height:100%; overflow:hidden; direction:rtl; font-family:"<PERSON><PERSON><PERSON>", sans-serif; }
    #app { position:relative; width:100%; height:100vh; }
    #scene-container, canvas { position:absolute; top:0; left:0; width:100%; height:100%; background:#10161c; }
    #toggle-editor-btn, #show-editor-btn {
      position:absolute; padding:6px 12px; background:rgba(0,0,0,0.5); color:#fff; border:none; border-radius:4px;
      cursor:pointer; font-size:14px; z-index:1000;
    }
    #toggle-editor-btn { top:8px; left:8px; }
    #show-editor-btn { bottom:8px; right:8px; display:none; }
    #show-editor-btn.visible { display:block; }
    #editor-container {
      position:absolute; left:0; right:0; bottom:0; height:30vh; min-height:150px; max-height:80vh;
      background:#fff; box-shadow:0 -2px 8px rgba(0,0,0,0.3);
      transform:translateY(0); transition:transform .3s ease; overflow:hidden; z-index:500;
    }
    #editor-container.hidden { transform:translateY(100%); }
    #editor-container > * { width:100%; height:100%; }
  </style>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r134/three.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/three/examples/js/controls/OrbitControls.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/three/examples/js/loaders/GLTFLoader.js"></script>
  <script src="js/pixie.umd.js?29"></script>
</head>
<body>
  <div id="app">
    <div id="scene-container">
      <button id="toggle-editor-btn">إخفاء المحرر</button>
      <button id="show-editor-btn">إظهار المحرر</button>
    </div>
    <div id="editor-container"></div>
  </div>

  <script>
    const dpr = window.devicePixelRatio || 1;
    const sceneContainer = document.getElementById("scene-container");
    const editorContainer = document.getElementById("editor-container");
    const toggleBtn = document.getElementById("toggle-editor-btn");
    const showBtn = document.getElementById("show-editor-btn");

    let scene, camera, renderer, controls, model;
    let pixieTexture = null, pixieInstance = null;
    const loader = new THREE.GLTFLoader();

    function initThree(){
      scene = new THREE.Scene();
      camera = new THREE.PerspectiveCamera(60, sceneContainer.clientWidth/sceneContainer.clientHeight, 1, 1000);
      camera.position.set(-1,2,2);
      renderer = new THREE.WebGLRenderer({antialias:true});
      renderer.setPixelRatio(dpr); // تحسين جودة الرندر  [oai_citation:0‡medium.com](https://medium.com/jedys-sharing/tips-for-setting-up-three-js-projects-1f1df05ed81e?utm_source=chatgpt.com)
      renderer.setSize(sceneContainer.clientWidth, sceneContainer.clientHeight);
      sceneContainer.appendChild(renderer.domElement);
      controls = new THREE.OrbitControls(camera, renderer.domElement);

      loader.load("model.gltf", gltf=>{
        model = gltf.scene;
        scene.add(model);
      });

      scene.add(new THREE.AmbientLight(0xffffff, .5));
      const light=new THREE.DirectionalLight(0xffffff, .5);
      light.position.set(5,5,5); scene.add(light);

      window.addEventListener("resize", onResize);
    }

    function onResize(){
      camera.aspect = sceneContainer.clientWidth/sceneContainer.clientHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(sceneContainer.clientWidth, sceneContainer.clientHeight);
    }

    function animate(){
      requestAnimationFrame(animate);
      controls.update();
      if(pixieTexture) pixieTexture.needsUpdate=true;
      renderer.render(scene, camera);
    }

    function fixPixieCanvas(){
      const canvas = editorContainer.querySelector("canvas");
      if(!canvas) return;
      const w = canvas.clientWidth;
      const h = canvas.clientHeight;
      canvas.width = w*dpr;
      canvas.height = h*dpr;
      canvas.style.width = w+"px";
      canvas.style.height = h+"px";
      const ctx = canvas.getContext("2d");
      if(ctx) ctx.scale(dpr, dpr);
      pixieInstance?.resize?.();
    }

    function initPixie(){
      pixieInstance = new Pixie({
        selector: "#editor-container",
        baseUrl: "../assets",
        onLoad(){
          setTimeout(fixPixieCanvas,100);
        }
      });
      window.pixie = pixieInstance;
    }

    function monitorTexture(){
      const canvas = editorContainer.querySelector("canvas");
      if(!canvas) return requestAnimationFrame(monitorTexture);
      if(!pixieTexture){
        pixieTexture = new THREE.CanvasTexture(canvas);
        pixieTexture.flipY=false;
        pixieTexture.generateMipmaps=true;
        pixieTexture.minFilter=THREE.LinearMipMapLinearFilter;
        pixieTexture.magFilter=THREE.LinearFilter;
        pixieTexture.anisotropy=renderer.capabilities.getMaxAnisotropy();
        pixieTexture.needsUpdate=true;
        if(model){
          model.traverse(c=>{
            if(c.isMesh && c.name==="print"){
              c.material.map = pixieTexture;
              c.material.needsUpdate=true;
            }
          });
        }
      }
      pixieTexture.image = canvas;
      pixieTexture.needsUpdate=true;
      requestAnimationFrame(monitorTexture);
    }

    function setupToggle(){
      toggleBtn.onclick = ()=>{
        editorContainer.classList.add("hidden");
        toggleBtn.style.display="none";
        showBtn.classList.add("visible");
        setTimeout(onResize,350);
      };
      showBtn.onclick = ()=>{
        editorContainer.classList.remove("hidden");
        toggleBtn.style.display="block";
        showBtn.classList.remove("visible");
        setTimeout(()=>{
          fixPixieCanvas();
          onResize();
        },350);
      };
    }

    document.addEventListener("DOMContentLoaded", ()=>{
      initThree();
      animate();
      initPixie();
      monitorTexture();
      setupToggle();
    });
  </script>
</body>
</html>