<?php
// Include authentication check
require_once 'auth_check.php';

// Check if user is logged in
if (!isLoggedIn()) {
    $response = [
        'success' => false,
        'message' => 'Authentication required'
    ];
    echo json_encode($response);
    exit;
}

// Get user ID
$userId = $_SESSION['user_id'];

// Check if file_type and file_name are provided
if (!isset($_POST['file_type']) || !isset($_POST['file_name'])) {
    $response = [
        'success' => false,
        'message' => 'File type and name are required'
    ];
    echo json_encode($response);
    exit;
}

$fileType = $_POST['file_type'];
$fileName = $_POST['file_name'];

// Validate file type
$validFileTypes = ['mp4', 'web', 'jpg', 'png'];
if (!in_array($fileType, $validFileTypes)) {
    $response = [
        'success' => false,
        'message' => 'Invalid file type'
    ];
    echo json_encode($response);
    exit;
}

// Check if user has reached download limit
$downloadCategory = ($fileType === 'mp4' || $fileType === 'web') ? 'video' : 'image';
if (hasReachedDownloadLimit($conn, $userId, $downloadCategory)) {
    $response = [
        'success' => false,
        'message' => 'Download limit reached for ' . $downloadCategory . ' files'
    ];
    echo json_encode($response);
    exit;
}

// Record the download
if (recordDownload($conn, $userId, $fileType, $fileName)) {
    $response = [
        'success' => true,
        'message' => 'Download recorded successfully'
    ];
} else {
    $response = [
        'success' => false,
        'message' => 'Failed to record download'
    ];
}

// Return JSON response
echo json_encode($response);
?>
