<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محرر الرسم المتقدم - Fabric.js</title>
    
    <!-- Load Fabric.js from multiple CDNs for reliability -->
    <script src="https://unpkg.com/fabric@5.3.0/dist/fabric.min.js"></script>
    <script>
        // Fallback if first CDN fails
        if (typeof fabric === 'undefined') {
            document.write('<script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"><\/script>');
        }
    </script>
    
    <!-- Hammer.js for touch support -->
    <script src="https://unpkg.com/hammerjs@2.0.8/hammer.min.js"></script>
    <script>
        // Fallback for Hammer.js
        if (typeof Hammer === 'undefined') {
            document.write('<script src="https://cdnjs.cloudflare.com/ajax/libs/hammer.js/2.0.8/hammer.min.js"><\/script>');
        }
    </script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            overflow: hidden;
            height: 100vh;
            direction: rtl;
        }

        .loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #2c3e50;
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 24px;
            z-index: 9999;
        }

        .app-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            opacity: 0;
            transition: opacity 0.5s ease;
        }

        .app-container.loaded {
            opacity: 1;
        }

        /* Top Toolbar */
        .top-toolbar {
            background: linear-gradient(90deg, #2c3e50, #34495e);
            color: white;
            padding: 12px 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap;
            min-height: 70px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            border-bottom: 3px solid #3498db;
        }

        .toolbar-group {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .toolbar-btn {
            background: linear-gradient(145deg, #3498db, #2980b9);
            border: none;
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            min-height: 45px;
            position: relative;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .toolbar-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.4);
            background: linear-gradient(145deg, #5dade2, #3498db);
        }

        .toolbar-btn.active {
            background: linear-gradient(145deg, #e74c3c, #c0392b);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.4);
        }

        .toolbar-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        /* Canvas Container */
        .canvas-container {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            padding: 20px;
            overflow: hidden;
            position: relative;
        }

        #canvas-wrapper {
            background: white;
            box-shadow: 0 10px 40px rgba(0,0,0,0.2);
            border-radius: 15px;
            overflow: hidden;
            position: relative;
            border: 3px solid #3498db;
        }

        #canvas {
            display: block;
            border-radius: 12px;
        }

        /* Bottom Sub-toolbar */
        .sub-toolbar {
            background: linear-gradient(90deg, #ecf0f1, #bdc3c7);
            border-top: 3px solid #3498db;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
            min-height: 70px;
            box-shadow: 0 -4px 15px rgba(0,0,0,0.1);
        }

        .sub-toolbar-group {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 12px;
            border-radius: 8px;
            background: white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: 2px solid #3498db;
        }

        .sub-toolbar label {
            font-size: 14px;
            color: #2c3e50;
            font-weight: 600;
        }

        .sub-toolbar input, .sub-toolbar select {
            border: 2px solid #3498db;
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 14px;
            min-width: 80px;
            background: white;
        }

        .sub-toolbar input:focus, .sub-toolbar select:focus {
            outline: none;
            border-color: #e74c3c;
            box-shadow: 0 0 10px rgba(231, 76, 60, 0.3);
        }

        .color-picker {
            width: 40px;
            height: 40px;
            border: 3px solid #3498db;
            border-radius: 8px;
            cursor: pointer;
        }

        .slider {
            width: 100px;
            height: 8px;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .modal.active {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 15px;
            padding: 25px;
            max-width: 90vw;
            max-height: 90vh;
            overflow: auto;
            position: relative;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            border: 3px solid #3498db;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #3498db;
        }

        .modal-header h2 {
            color: #2c3e50;
            font-size: 24px;
        }

        .close-btn {
            background: #e74c3c;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-btn:hover {
            background: #c0392b;
            transform: scale(1.1);
        }

        /* PNG Library Grid */
        .png-categories {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .png-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .png-item {
            width: 100px;
            height: 100px;
            border: 3px solid #3498db;
            border-radius: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(145deg, #f8f9fa, #e9ecef);
            transition: all 0.3s ease;
            font-size: 40px;
        }

        .png-item:hover {
            border-color: #e74c3c;
            transform: scale(1.1);
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);
        }

        /* File Input */
        .file-input {
            display: none;
        }

        /* Tooltip */
        .tooltip {
            position: relative;
        }

        .tooltip::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 120%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s;
            z-index: 1000;
        }

        .tooltip:hover::after {
            opacity: 1;
        }

        /* Status Bar */
        .status-bar {
            background: #2c3e50;
            color: white;
            padding: 8px 20px;
            font-size: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .top-toolbar {
                padding: 8px 12px;
                min-height: 60px;
            }
            
            .toolbar-btn {
                padding: 8px 12px;
                font-size: 12px;
                min-height: 40px;
            }
            
            .canvas-container {
                padding: 10px;
            }
            
            .sub-toolbar {
                padding: 10px 12px;
                min-height: 60px;
            }
            
            .sub-toolbar-group {
                padding: 6px 8px;
            }
            
            .modal-content {
                margin: 10px;
                padding: 20px;
            }

            body {
                direction: ltr; /* Switch to LTR on mobile for better UX */
            }
        }

        /* Animation for loading */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .fade-in {
            animation: fadeIn 0.5s ease-out;
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div class="loading" id="loadingScreen">
        <div>
            <div style="font-size: 48px; margin-bottom: 20px;">🎨</div>
            <div>جاري تحميل محرر الرسم...</div>
            <div style="font-size: 14px; margin-top: 10px; opacity: 0.7;">Loading Canvas Editor...</div>
        </div>
    </div>

    <div class="app-container" id="appContainer">
        <!-- Top Toolbar -->
        <div class="top-toolbar fade-in">
            <div class="toolbar-group">
                <button class="toolbar-btn tooltip active" data-category="draw" data-tooltip="أدوات الرسم">
                    🖌️ رسم
                </button>
                <button class="toolbar-btn tooltip" data-category="shapes" data-tooltip="الأشكال الهندسية">
                    🔷 أشكال
                </button>
                <button class="toolbar-btn tooltip" data-category="text" data-tooltip="أدوات النص">
                    📝 نص
                </button>
                <button class="toolbar-btn tooltip" data-category="images" data-tooltip="الصور">
                    🖼️ صور
                </button>
                <button class="toolbar-btn tooltip" data-category="effects" data-tooltip="التأثيرات والفلاتر">
                    ✨ تأثيرات
                </button>
                <button class="toolbar-btn tooltip" data-category="library" data-tooltip="مكتبة الرموز">
                    📚 مكتبة
                </button>
            </div>
            
            <div class="toolbar-group" style="margin-right: auto;">
                <button class="toolbar-btn tooltip" onclick="undo()" data-tooltip="تراجع">
                    ↶ تراجع
                </button>
                <button class="toolbar-btn tooltip" onclick="redo()" data-tooltip="إعادة">
                    ↷ إعادة
                </button>
                <button class="toolbar-btn tooltip" onclick="clearCanvas()" data-tooltip="مسح الكل">
                    🗑️ مسح
                </button>
                <button class="toolbar-btn tooltip" onclick="exportCanvas()" data-tooltip="تصدير">
                    💾 حفظ
                </button>
            </div>
        </div>

        <!-- Canvas Container -->
        <div class="canvas-container fade-in">
            <div id="canvas-wrapper">
                <canvas id="canvas" width="800" height="600"></canvas>
            </div>
        </div>

        <!-- Sub Toolbar -->
        <div class="sub-toolbar fade-in" id="subToolbar">
            <!-- سيتم ملء المحتوى ديناميكياً -->
        </div>

        <!-- Status Bar -->
        <div class="status-bar">
            <span id="statusText">جاهز للاستخدام</span>
            <span id="canvasInfo">800 × 600</span>
        </div>
    </div>

    <!-- PNG Library Modal -->
    <div class="modal" id="pngLibraryModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>مكتبة الرموز والأشكال</h2>
                <button class="close-btn" onclick="closePngLibrary()">&times;</button>
            </div>
            <div class="png-categories">
                <button class="toolbar-btn" onclick="loadPngCategory('icons')">أيقونات</button>
                <button class="toolbar-btn" onclick="loadPngCategory('shapes')">أشكال</button>
                <button class="toolbar-btn" onclick="loadPngCategory('arrows')">أسهم</button>
                <button class="toolbar-btn" onclick="loadPngCategory('symbols')">رموز</button>
            </div>
            <div class="png-grid" id="pngGrid">
                <!-- سيتم تحميل العناصر هنا -->
            </div>
        </div>
    </div>

    <!-- Hidden File Inputs -->
    <input type="file" id="imageUpload" class="file-input" accept="image/*" multiple>
    <input type="file" id="singleImageUpload" class="file-input" accept="image/*">

    <script>
        // Global Variables
        let canvas;
        let currentCategory = 'draw';
        let isDrawingMode = false;
        let history = [];
        let historyStep = 0;
        let isLoaded = false;

        // Check if libraries are loaded
        function checkLibraries() {
            let attempts = 0;
            const maxAttempts = 50;
            
            const checkInterval = setInterval(() => {
                attempts++;
                
                if (typeof fabric !== 'undefined') {
                    clearInterval(checkInterval);
                    initializeApp();
                } else if (attempts >= maxAttempts) {
                    clearInterval(checkInterval);
                    showError();
                }
            }, 100);
        }

        function showError() {
            document.getElementById('loadingScreen').innerHTML = `
                <div style="text-align: center;">
                    <div style="font-size: 48px; margin-bottom: 20px;">⚠️</div>
                    <div style="color: #e74c3c; font-size: 18px; margin-bottom: 10px;">خطأ في التحميل</div>
                    <div style="font-size: 14px; margin-bottom: 20px;">فشل في تحميل المكتبات المطلوبة</div>
                    <button onclick="location.reload()" style="background: #3498db; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                        إعادة المحاولة
                    </button>
                </div>
            `;
        }

        // Initialize Application
        function initializeApp() {
            try {
                initializeCanvas();
                initializeToolbars();
                updateSubToolbar();
                saveState();
                
                // Hide loading screen and show app
                setTimeout(() => {
                    document.getElementById('loadingScreen').style.display = 'none';
                    document.getElementById('appContainer').classList.add('loaded');
                    isLoaded = true;
                    updateStatus('جاهز للاستخدام - Ready to use');
                }, 500);
                
            } catch (error) {
                console.error('Initialization error:', error);
                showError();
            }
        }

        // Canvas Initialization
        function initializeCanvas() {
            canvas = new fabric.Canvas('canvas', {
                backgroundColor: 'white',
                selection: true,
                preserveObjectStacking: true,
                width: 800,
                height: 600
            });

            // Canvas event listeners
            canvas.on('path:created', function() {
                saveState();
                updateStatus('تم إضافة رسمة جديدة');
            });
            
            canvas.on('object:added', function() {
                saveState();
                updateStatus('تم إضافة عنصر جديد');
            });
            
            canvas.on('object:removed', function() {
                saveState();
                updateStatus('تم حذف عنصر');
            });
            
            canvas.on('object:modified', function() {
                saveState();
                updateStatus('تم تعديل العنصر');
            });

            // Make canvas responsive
            resizeCanvas();
            window.addEventListener('resize', resizeCanvas);
        }

        // Toolbar Initialization
        function initializeToolbars() {
            // Category buttons
            document.querySelectorAll('[data-category]').forEach(btn => {
                btn.addEventListener('click', function() {
                    const category = this.dataset.category;
                    switchCategory(category);
                });
            });

            // File input handlers
            document.getElementById('imageUpload').addEventListener('change', handleMultipleImageUpload);
            document.getElementById('singleImageUpload').addEventListener('change', handleSingleImageUpload);
        }

        // Category Switching
        function switchCategory(category) {
            currentCategory = category;
            
            // Update active button
            document.querySelectorAll('[data-category]').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-category="${category}"]`).classList.add('active');
            
            // Update sub-toolbar
            updateSubToolbar();
            
            // Handle drawing mode
            if (category === 'draw') {
                enableDrawingMode();
                updateStatus('وضع الرسم مفعل');
            } else {
                disableDrawingMode();
                updateStatus(`تم التبديل إلى ${getCategoryName(category)}`);
            }
        }

        function getCategoryName(category) {
            const names = {
                'draw': 'الرسم',
                'shapes': 'الأشكال',
                'text': 'النص',
                'images': 'الصور',
                'effects': 'التأثيرات',
                'library': 'المكتبة'
            };
            return names[category] || category;
        }

        // Sub-toolbar Updates
        function updateSubToolbar() {
            const subToolbar = document.getElementById('subToolbar');
            subToolbar.innerHTML = '';

            switch(currentCategory) {
                case 'draw':
                    subToolbar.innerHTML = `
                        <div class="sub-toolbar-group">
                            <label>الأداة:</label>
                            <select onchange="setDrawingTool(this.value)">
                                <option value="pencil">قلم رصاص</option>
                                <option value="brush">فرشاة</option>
                                <option value="marker">قلم تحديد</option>
                                <option value="spray">رذاذ</option>
                            </select>
                        </div>
                        <div class="sub-toolbar-group">
                            <label>اللون:</label>
                            <input type="color" class="color-picker" value="#000000" onchange="setBrushColor(this.value)">
                        </div>
                        <div class="sub-toolbar-group">
                            <label>الحجم:</label>
                            <input type="range" class="slider" min="1" max="50" value="5" onchange="setBrushWidth(this.value)">
                            <span id="brushSize">5</span>
                        </div>
                        <div class="sub-toolbar-group">
                            <label>الشفافية:</label>
                            <input type="range" class="slider" min="0" max="1" step="0.1" value="1" onchange="setBrushOpacity(this.value)">
                        </div>
                        <button class="toolbar-btn" onclick="setEraser()">🧽 ممحاة</button>
                    `;
                    break;

                case 'shapes':
                    subToolbar.innerHTML = `
                        <button class="toolbar-btn" onclick="addRectangle()">⬜ مستطيل</button>
                        <button class="toolbar-btn" onclick="addCircle()">⭕ دائرة</button>
                        <button class="toolbar-btn" onclick="addTriangle()">🔺 مثلث</button>
                        <button class="toolbar-btn" onclick="addLine()">📏 خط</button>
                        <div class="sub-toolbar-group">
                            <label>التعبئة:</label>
                            <input type="color" class="color-picker" value="#3498db" onchange="setShapeFill(this.value)">
                        </div>
                        <div class="sub-toolbar-group">
                            <label>الحدود:</label>
                            <input type="color" class="color-picker" value="#2c3e50" onchange="setShapeStroke(this.value)">
                        </div>
                        <div class="sub-toolbar-group">
                            <label>سمك الحدود:</label>
                            <input type="range" class="slider" min="0" max="10" value="2" onchange="setShapeStrokeWidth(this.value)">
                        </div>
                    `;
                    break;

                case 'text':
                    subToolbar.innerHTML = `
                        <button class="toolbar-btn" onclick="addText()">📝 إضافة نص</button>
                        <button class="toolbar-btn" onclick="addTextbox()">📄 صندوق نص</button>
                        <div class="sub-toolbar-group">
                            <label>الخط:</label>
                            <select onchange="setTextFont(this.value)">
                                <option value="Arial">Arial</option>
                                <option value="Tahoma">Tahoma</option>
                                <option value="Amiri">Amiri (عربي)</option>
                                <option value="Noto Sans Arabic">Noto Sans Arabic</option>
                                <option value="Times New Roman">Times New Roman</option>
                            </select>
                        </div>
                        <div class="sub-toolbar-group">
                            <label>الحجم:</label>
                            <input type="range" class="slider" min="8" max="72" value="20" onchange="setTextSize(this.value)">
                            <span id="textSize">20</span>
                        </div>
                        <div class="sub-toolbar-group">
                            <label>اللون:</label>
                            <input type="color" class="color-picker" value="#000000" onchange="setTextColor(this.value)">
                        </div>
                        <button class="toolbar-btn" onclick="toggleTextBold()">📝 عريض</button>
                        <button class="toolbar-btn" onclick="toggleTextItalic()">📝 مائل</button>
                    `;
                    break;

                case 'images':
                    subToolbar.innerHTML = `
                        <button class="toolbar-btn" onclick="document.getElementById('singleImageUpload').click()">📁 رفع صورة</button>
                        <button class="toolbar-btn" onclick="document.getElementById('imageUpload').click()">📁 رفع متعدد</button>
                        <div class="sub-toolbar-group">
                            <label>حدود الصورة:</label>
                            <input type="color" class="color-picker" value="#000000" onchange="setImageBorder(this.value)">
                            <input type="range" class="slider" min="0" max="20" value="0" onchange="setImageBorderWidth(this.value)">
                        </div>
                    `;
                    break;

                case 'effects':
                    subToolbar.innerHTML = `
                        <button class="toolbar-btn" onclick="applyFilter('brightness', 0.2)">☀️ إضاءة</button>
                        <button class="toolbar-btn" onclick="applyFilter('brightness', -0.2)">🌙 تعتيم</button>
                        <button class="toolbar-btn" onclick="applyFilter('blur', 0.1)">🌫️ ضبابية</button>
                        <button class="toolbar-btn" onclick="applyFilter('grayscale')">⚫ رمادي</button>
                        <button class="toolbar-btn" onclick="applyFilter('invert')">🔄 عكس</button>
                        <button class="toolbar-btn" onclick="removeFilters()">🧹 إزالة التأثيرات</button>
                    `;
                    break;

                case 'library':
                    subToolbar.innerHTML = `
                        <button class="toolbar-btn" onclick="openPngLibrary()">📚 فتح المكتبة</button>
                        <div class="sub-toolbar-group">
                            <label>بحث:</label>
                            <input type="text" placeholder="ابحث عن رمز..." onchange="searchPngs(this.value)">
                        </div>
                    `;
                    break;
            }
        }

        // Drawing Functions
        function enableDrawingMode() {
            if (!canvas) return;
            canvas.isDrawingMode = true;
            isDrawingMode = true;
            canvas.freeDrawingBrush.width = 5;
            canvas.freeDrawingBrush.color = '#000000';
        }

        function disableDrawingMode() {
            if (!canvas) return;
            canvas.isDrawingMode = false;
            isDrawingMode = false;
        }

        function setDrawingTool(tool) {
            if (!canvas) return;
            switch(tool) {
                case 'pencil':
                    canvas.freeDrawingBrush = new fabric.PencilBrush(canvas);
                    break;
                case 'brush':
                    canvas.freeDrawingBrush = new fabric.PencilBrush(canvas);
                    canvas.freeDrawingBrush.width = 10;
                    break;
                case 'marker':
                    canvas.freeDrawingBrush = new fabric.PencilBrush(canvas);
                    canvas.freeDrawingBrush.width = 15;
                    canvas.freeDrawingBrush.color = 'rgba(255, 255, 0, 0.7)';
                    break;
                case 'spray':
                    canvas.freeDrawingBrush = new fabric.SprayBrush(canvas);
                    break;
            }
            updateStatus(`تم تغيير الأداة إلى ${tool}`);
        }

        function setBrushColor(color) {
            if (!canvas) return;
            canvas.freeDrawingBrush.color = color;
            updateStatus('تم تغيير لون الفرشاة');
        }

        function setBrushWidth(width) {
            if (!canvas) return;
            canvas.freeDrawingBrush.width = parseInt(width);
            document.getElementById('brushSize').textContent = width;
            updateStatus(`حجم الفرشاة: ${width}`);
        }

        function setBrushOpacity(opacity) {
            if (!canvas) return;
            const color = canvas.freeDrawingBrush.color;
            const rgb = hexToRgb(color);
            if (rgb) {
                canvas.freeDrawingBrush.color = `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${opacity})`;
                updateStatus(`شفافية الفرشاة: ${Math.round(opacity * 100)}%`);
            }
        }

        function setEraser() {
            if (!canvas) return;
            // Simulate eraser by using white brush
            canvas.freeDrawingBrush = new fabric.PencilBrush(canvas);
            canvas.freeDrawingBrush.color = canvas.backgroundColor || 'white';
            canvas.freeDrawingBrush.width = 20;
            updateStatus('تم تفعيل الممحاة');
        }

        // Shape Functions
        let shapeFill = '#3498db';
        let shapeStroke = '#2c3e50';
        let shapeStrokeWidth = 2;

        function setShapeFill(color) { 
            shapeFill = color; 
            updateStatus('تم تغيير لون التعبئة');
        }
        
        function setShapeStroke(color) { 
            shapeStroke = color; 
            updateStatus('تم تغيير لون الحدود');
        }
        
        function setShapeStrokeWidth(width) { 
            shapeStrokeWidth = parseInt(width); 
            updateStatus(`سمك الحدود: ${width}`);
        }

        function addRectangle() {
            if (!canvas) return;
            const rect = new fabric.Rect({
                left: 100,
                top: 100,
                width: 100,
                height: 80,
                fill: shapeFill,
                stroke: shapeStroke,
                strokeWidth: shapeStrokeWidth
            });
            canvas.add(rect);
            canvas.setActiveObject(rect);
            updateStatus('تم إضافة مستطيل');
        }

        function addCircle() {
            if (!canvas) return;
            const circle = new fabric.Circle({
                left: 100,
                top: 100,
                radius: 50,
                fill: shapeFill,
                stroke: shapeStroke,
                strokeWidth: shapeStrokeWidth
            });
            canvas.add(circle);
            canvas.setActiveObject(circle);
            updateStatus('تم إضافة دائرة');
        }

        function addTriangle() {
            if (!canvas) return;
            const triangle = new fabric.Triangle({
                left: 100,
                top: 100,
                width: 80,
                height: 80,
                fill: shapeFill,
                stroke: shapeStroke,
                strokeWidth: shapeStrokeWidth
            });
            canvas.add(triangle);
            canvas.setActiveObject(triangle);
            updateStatus('تم إضافة مثلث');
        }

        function addLine() {
            if (!canvas) return;
            const line = new fabric.Line([50, 100, 200, 100], {
                stroke: shapeStroke,
                strokeWidth: shapeStrokeWidth
            });
            canvas.add(line);
            canvas.setActiveObject(line);
            updateStatus('تم إضافة خط');
        }

        // Text Functions
        let textFont = 'Arial';
        let textSize = 20;
        let textColor = '#000000';

        function setTextFont(font) { 
            textFont = font; 
            updateStatus(`تم تغيير الخط إلى ${font}`);
        }
        
        function setTextSize(size) { 
            textSize = parseInt(size); 
            document.getElementById('textSize').textContent = size;
            updateStatus(`حجم النص: ${size}`);
        }
        
        function setTextColor(color) { 
            textColor = color; 
            updateStatus('تم تغيير لون النص');
        }

        function addText() {
            if (!canvas) return;
            const text = new fabric.Text('نص تجريبي', {
                left: 100,
                top: 100,
                fontFamily: textFont,
                fontSize: textSize,
                fill: textColor
            });
            canvas.add(text);
            canvas.setActiveObject(text);
            updateStatus('تم إضافة نص');
        }

        function addTextbox() {
            if (!canvas) return;
            const textbox = new fabric.Textbox('صندوق نص قابل للتحرير', {
                left: 100,
                top: 100,
                width: 200,
                fontFamily: textFont,
                fontSize: textSize,
                fill: textColor
            });
            canvas.add(textbox);
            canvas.setActiveObject(textbox);
            updateStatus('تم إضافة صندوق نص');
        }

        function toggleTextBold() {
            if (!canvas) return;
            const activeObject = canvas.getActiveObject();
            if (activeObject && (activeObject.type === 'text' || activeObject.type === 'textbox')) {
                const isBold = activeObject.fontWeight === 'bold';
                activeObject.set('fontWeight', isBold ? 'normal' : 'bold');
                canvas.renderAll();
                updateStatus(isBold ? 'تم إلغاء النص العريض' : 'تم تفعيل النص العريض');
            }
        }

        function toggleTextItalic() {
            if (!canvas) return;
            const activeObject = canvas.getActiveObject();
            if (activeObject && (activeObject.type === 'text' || activeObject.type === 'textbox')) {
                const isItalic = activeObject.fontStyle === 'italic';
                activeObject.set('fontStyle', isItalic ? 'normal' : 'italic');
                canvas.renderAll();
                updateStatus(isItalic ? 'تم إلغاء النص المائل' : 'تم تفعيل النص المائل');
            }
        }

        // Image Functions
        function handleSingleImageUpload(e) {
            const file = e.target.files[0];
            if (file && canvas) {
                const reader = new FileReader();
                reader.onload = function(event) {
                    fabric.Image.fromURL(event.target.result, function(img) {
                        img.set({
                            left: 100,
                            top: 100,
                            scaleX: 0.5,
                            scaleY: 0.5
                        });
                        canvas.add(img);
                        canvas.setActiveObject(img);
                        canvas.renderAll();
                        updateStatus('تم رفع الصورة بنجاح');
                    });
                };
                reader.readAsDataURL(file);
            }
        }

        function handleMultipleImageUpload(e) {
            const files = Array.from(e.target.files);
            if (!canvas) return;
            
            let x = 50, y = 50;
            let loadedCount = 0;
            
            files.forEach((file, index) => {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(event) {
                        fabric.Image.fromURL(event.target.result, function(img) {
                            img.set({
                                left: x + (index % 5) * 120,
                                top: y + Math.floor(index / 5) * 120,
                                scaleX: 0.3,
                                scaleY: 0.3
                            });
                            canvas.add(img);
                            canvas.renderAll();
                            loadedCount++;
                            updateStatus(`تم رفع ${loadedCount} من ${files.length} صورة`);
                        });
                    };
                    reader.readAsDataURL(file);
                }
            });
        }

        function setImageBorder(color) {
            if (!canvas) return;
            const activeObject = canvas.getActiveObject();
            if (activeObject && activeObject.type === 'image') {
                activeObject.set('stroke', color);
                canvas.renderAll();
                updateStatus('تم تغيير لون حدود الصورة');
            }
        }

        function setImageBorderWidth(width) {
            if (!canvas) return;
            const activeObject = canvas.getActiveObject();
            if (activeObject && activeObject.type === 'image') {
                activeObject.set('strokeWidth', parseInt(width));
                canvas.renderAll();
                updateStatus(`سمك حدود الصورة: ${width}`);
            }
        }

        // Filter Functions
        function applyFilter(filterType, value) {
            if (!canvas) return;
            const activeObject = canvas.getActiveObject();
            if (!activeObject || activeObject.type !== 'image') {
                updateStatus('يرجى تحديد صورة أولاً');
                return;
            }

            let filter;
            switch(filterType) {
                case 'brightness':
                    filter = new fabric.Image.filters.Brightness({ brightness: value });
                    break;
                case 'blur':
                    filter = new fabric.Image.filters.Blur({ blur: value });
                    break;
                case 'grayscale':
                    filter = new fabric.Image.filters.Grayscale();
                    break;
                case 'invert':
                    filter = new fabric.Image.filters.Invert();
                    break;
            }

            if (filter) {
                activeObject.filters.push(filter);
                activeObject.applyFilters();
                canvas.renderAll();
                updateStatus(`تم تطبيق تأثير ${filterType}`);
            }
        }

        function removeFilters() {
            if (!canvas) return;
            const activeObject = canvas.getActiveObject();
            if (activeObject && activeObject.type === 'image') {
                activeObject.filters = [];
                activeObject.applyFilters();
                canvas.renderAll();
                updateStatus('تم إزالة جميع التأثيرات');
            }
        }

        // PNG Library Functions
        function openPngLibrary() {
            document.getElementById('pngLibraryModal').classList.add('active');
            loadPngCategory('icons');
            updateStatus('تم فتح مكتبة الرموز');
        }

        function closePngLibrary() {
            document.getElementById('pngLibraryModal').classList.remove('active');
        }

        function loadPngCategory(category) {
            const grid = document.getElementById('pngGrid');
            grid.innerHTML = '';

            const pngData = {
                icons: [
                    { name: 'منزل', emoji: '🏠' },
                    { name: 'مستخدم', emoji: '👤' },
                    { name: 'إعدادات', emoji: '⚙️' },
                    { name: 'قلب', emoji: '❤️' },
                    { name: 'نجمة', emoji: '⭐' },
                    { name: 'هاتف', emoji: '📞' },
                    { name: 'بريد', emoji: '📧' },
                    { name: 'كاميرا', emoji: '📷' }
                ],
                shapes: [
                    { name: 'دائرة', emoji: '⭕' },
                    { name: 'مربع', emoji: '⬜' },
                    { name: 'مثلث', emoji: '🔺' },
                    { name: 'ماس', emoji: '💎' },
                    { name: 'سداسي', emoji: '⬡' },
                    { name: 'خماسي', emoji: '⬟' }
                ],
                arrows: [
                    { name: 'يمين', emoji: '➡️' },
                    { name: 'يسار', emoji: '⬅️' },
                    { name: 'أعلى', emoji: '⬆️' },
                    { name: 'أسفل', emoji: '⬇️' },
                    { name: 'منحني', emoji: '↪️' },
                    { name: 'مزدوج', emoji: '⇄' }
                ],
                symbols: [
                    { name: 'صح', emoji: '✅' },
                    { name: 'خطأ', emoji: '❌' },
                    { name: 'تحذير', emoji: '⚠️' },
                    { name: 'معلومات', emoji: 'ℹ️' },
                    { name: 'سؤال', emoji: '❓' },
                    { name: 'تعجب', emoji: '❗' }
                ]
            };

            const items = pngData[category] || [];
            items.forEach(item => {
                const div = document.createElement('div');
                div.className = 'png-item';
                div.innerHTML = item.emoji;
                div.title = item.name;
                div.onclick = () => addPngToCanvas(item);
                grid.appendChild(div);
            });
        }

        function addPngToCanvas(item) {
            if (!canvas) return;
            const text = new fabric.Text(item.emoji, {
                left: 100,
                top: 100,
                fontSize: 48,
                fontFamily: 'Arial'
            });
            canvas.add(text);
            canvas.setActiveObject(text);
            closePngLibrary();
            updateStatus(`تم إضافة ${item.name}`);
        }

        function searchPngs(query) {
            updateStatus(`البحث عن: ${query}`);
        }

        // History Management
        function saveState() {
            if (!canvas) return;
            if (historyStep < history.length - 1) {
                history = history.slice(0, historyStep + 1);
            }
            history.push(JSON.stringify(canvas.toJSON()));
            historyStep = history.length - 1;
        }

        function undo() {
            if (!canvas) return;
            if (historyStep > 0) {
                historyStep--;
                canvas.loadFromJSON(history[historyStep], function() {
                    canvas.renderAll();
                    updateStatus('تم التراجع');
                });
            }
        }

        function redo() {
            if (!canvas) return;
            if (historyStep < history.length - 1) {
                historyStep++;
                canvas.loadFromJSON(history[historyStep], function() {
                    canvas.renderAll();
                    updateStatus('تم الإعادة');
                });
            }
        }

        // Utility Functions
        function clearCanvas() {
            if (!canvas) return;
            if (confirm('هل أنت متأكد من مسح جميع العناصر؟')) {
                canvas.clear();
                canvas.backgroundColor = 'white';
                canvas.renderAll();
                saveState();
                updateStatus('تم مسح اللوحة');
            }
        }

        function exportCanvas() {
            if (!canvas) return;
            try {
                const dataURL = canvas.toDataURL({
                    format: 'png',
                    quality: 1
                });
                
                const link = document.createElement('a');
                link.download = 'my-canvas-' + Date.now() + '.png';
                link.href = dataURL;
                link.click();
                updateStatus('تم تصدير الصورة بنجاح');
            } catch (error) {
                updateStatus('خطأ في التصدير');
                console.error('Export error:', error);
            }
        }

        function resizeCanvas() {
            if (!canvas) return;
            const container = document.getElementById('canvas-wrapper');
            const containerWidth = container.clientWidth - 6; // Account for border
            const containerHeight = container.clientHeight - 6;
            
            const canvasWidth = 800;
            const canvasHeight = 600;
            
            const scaleX = containerWidth / canvasWidth;
            const scaleY = containerHeight / canvasHeight;
            const scale = Math.min(scaleX, scaleY, 1);
            
            canvas.setZoom(scale);
            canvas.setDimensions({
                width: canvasWidth * scale,
                height: canvasHeight * scale
            });
        }

        function updateStatus(message) {
            const statusElement = document.getElementById('statusText');
            if (statusElement) {
                statusElement.textContent = message;
                setTimeout(() => {
                    if (statusElement.textContent === message) {
                        statusElement.textContent = 'جاهز للاستخدام';
                    }
                }, 3000);
            }
        }

        function hexToRgb(hex) {
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result ? {
                r: parseInt(result[1], 16),
                g: parseInt(result[2], 16),
                b: parseInt(result[3], 16)
            } : null;
        }

        // Keyboard Shortcuts
        document.addEventListener('keydown', function(e) {
            if (!isLoaded) return;
            
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'z':
                        e.preventDefault();
                        if (e.shiftKey) {
                            redo();
                        } else {
                            undo();
                        }
                        break;
                    case 's':
                        e.preventDefault();
                        exportCanvas();
                        break;
                }
            }
            
            if (e.key === 'Delete' || e.key === 'Backspace') {
                deleteSelected();
            }
        });

        function deleteSelected() {
            if (!canvas) return;
            const activeObjects = canvas.getActiveObjects();
            if (activeObjects.length) {
                activeObjects.forEach(obj => canvas.remove(obj));
                canvas.discardActiveObject();
                canvas.requestRenderAll();
                saveState();
                updateStatus('تم حذف العناصر المحددة');
            }
        }

        // Start the application
        document.addEventListener('DOMContentLoaded', function() {
            checkLibraries();
        });

        // Handle window load as backup
        window.addEventListener('load', function() {
            if (!isLoaded) {
                setTimeout(checkLibraries, 1000);
            }
        });
    </script>
</body>
</html>