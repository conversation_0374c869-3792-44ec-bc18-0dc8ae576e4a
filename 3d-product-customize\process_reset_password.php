<?php
require_once 'config.php';

// Only process POST requests
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $response = ['success' => false, 'message' => ''];
    
    // Get form data
    $token = $_POST['token'];
    $userId = $_POST['user_id'];
    $password = $_POST['password'];
    $confirmPassword = $_POST['confirm_password'];
    
    // Basic validation
    if (empty($token) || empty($userId) || empty($password) || empty($confirmPassword)) {
        $response['message'] = 'All fields are required';
        echo json_encode($response);
        exit;
    }
    
    // Validate password
    if (strlen($password) < 8) {
        $response['message'] = 'Password must be at least 8 characters long';
        echo json_encode($response);
        exit;
    }
    
    // Check if passwords match
    if ($password !== $confirmPassword) {
        $response['message'] = 'Passwords do not match';
        echo json_encode($response);
        exit;
    }
    
    // Verify token is valid
    $sql = "SELECT * FROM password_resets WHERE user_id = ? AND token = ? AND expiry_date > NOW()";
    if ($stmt = $conn->prepare($sql)) {
        $stmt->bind_param("is", $userId, $token);
        $stmt->execute();
        $stmt->store_result();
        
        if ($stmt->num_rows == 0) {
            $response['message'] = 'Invalid or expired token';
            echo json_encode($response);
            $stmt->close();
            exit;
        }
        
        $stmt->close();
    } else {
        $response['message'] = 'Database error: ' . $conn->error;
        echo json_encode($response);
        exit;
    }
    
    // Hash new password
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
    
    // Update user password
    $updateSql = "UPDATE users SET password = ? WHERE user_id = ?";
    if ($updateStmt = $conn->prepare($updateSql)) {
        $updateStmt->bind_param("si", $hashedPassword, $userId);
        
        if ($updateStmt->execute()) {
            // Delete used token
            $deleteSql = "DELETE FROM password_resets WHERE user_id = ?";
            $deleteStmt = $conn->prepare($deleteSql);
            $deleteStmt->bind_param("i", $userId);
            $deleteStmt->execute();
            $deleteStmt->close();
            
            $response['success'] = true;
            $response['message'] = 'Password has been reset successfully. Redirecting to login page...';
        } else {
            $response['message'] = 'Error updating password: ' . $updateStmt->error;
        }
        
        $updateStmt->close();
    } else {
        $response['message'] = 'Database error: ' . $conn->error;
    }
    
    // Return JSON response
    echo json_encode($response);
} else {
    // Redirect if accessed directly
    header("Location: login.php");
    exit;
}
?>
