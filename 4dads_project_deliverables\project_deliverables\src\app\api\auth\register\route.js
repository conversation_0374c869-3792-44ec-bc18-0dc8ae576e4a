// src/app/api/auth/register/route.js
// [!] هذا الملف يتعامل مع طلبات تسجيل المستخدمين الجدد
// [!] يجب تعديل متغيرات الاتصال بقاعدة البيانات حسب إعدادات Hostinger الخاصة بك

import { NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';

// [!] متغيرات يجب تعديلها: DB_HOST, DB_USER, DB_PASSWORD, DB_NAME
const DB_CONFIG = {
  host: 'DB_HOST',      // مثال: 'mysql-4dads.hostinger.com'
  user: 'DB_USER',      // مثال: 'u123456789_4dads'
  password: 'DB_PASSWORD', // كلمة المرور الخاصة بقاعدة البيانات
  database: 'DB_NAME',  // مثال: 'u123456789_4dads'
};

export async function POST(request) {
  try {
    const { name, email, phone, password } = await request.json();

    // التحقق من البيانات المدخلة
    if (!name || !email || !password) {
      return NextResponse.json(
        { success: false, message: 'جميع الحقول المطلوبة يجب ملؤها' },
        { status: 400 }
      );
    }

    // التحقق من صحة البريد الإلكتروني
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { success: false, message: 'البريد الإلكتروني غير صالح' },
        { status: 400 }
      );
    }

    // التحقق من طول كلمة المرور
    if (password.length < 8) {
      return NextResponse.json(
        { success: false, message: 'يجب أن تكون كلمة المرور 8 أحرف على الأقل' },
        { status: 400 }
      );
    }

    // [!] في بيئة الإنتاج، يجب استخدام اتصال حقيقي بقاعدة البيانات
    // هذا مثال لكيفية تنفيذ ذلك باستخدام mysql2
    
    /*
    // استيراد مكتبة mysql2
    import mysql from 'mysql2/promise';
    
    // إنشاء اتصال بقاعدة البيانات
    const connection = await mysql.createConnection(DB_CONFIG);
    
    // التحقق مما إذا كان البريد الإلكتروني موجودًا بالفعل
    const [existingUsers] = await connection.execute(
      'SELECT * FROM users WHERE email = ?',
      [email]
    );
    
    if (existingUsers.length > 0) {
      await connection.end();
      return NextResponse.json(
        { success: false, message: 'البريد الإلكتروني مسجل بالفعل' },
        { status: 400 }
      );
    }
    
    // تشفير كلمة المرور
    const hashedPassword = await bcrypt.hash(password, 10);
    
    // إدخال المستخدم الجديد في قاعدة البيانات
    await connection.execute(
      'INSERT INTO users (name, email, phone, password) VALUES (?, ?, ?, ?)',
      [name, email, phone || null, hashedPassword]
    );
    
    // إغلاق الاتصال بقاعدة البيانات
    await connection.end();
    */
    
    // للاختبار، نعيد استجابة نجاح
    return NextResponse.json(
      { 
        success: true, 
        message: 'تم تسجيل المستخدم بنجاح',
        user: { name, email, phone } // لا نعيد كلمة المرور أبدًا
      },
      { status: 201 }
    );
    
  } catch (error) {
    console.error('Registration error:', error);
    return NextResponse.json(
      { success: false, message: 'حدث خطأ في الخادم' },
      { status: 500 }
    );
  }
}
