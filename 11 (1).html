<!DOCTYPE html>
<html lang="ar">
<head>
<meta charset="UTF-8"/>
<meta name="viewport" content="width=device-width,initial-scale=1.0"/>
<title>Fabric.js Photo Editor + Three.js</title>
<link href="https://fonts.googleapis.com/css?family=Tajawal:400,700&display=swap" rel="stylesheet">
<style>
html,body{margin:0;padding:0;width:100%;height:100%;overflow:hidden;direction:rtl;font-family:"Tajawal",sans-serif}
#app{position:relative;width:100%;height:100vh}
#scene-container{position:absolute;top:0;left:0;right:0;bottom:0;background:#10161c;z-index:1}
#editor-container{position:absolute;left:0;right:0;bottom:0;height:35vh;min-height:180px;background:#f0f0f0;transform:translateY(0);transition:transform .3s;z-index:2;overflow:hidden}
#editor-container.hidden{transform:translateY(100%)}
#toolbar{display:flex;gap:8px;padding:6px;background:#ddd;border-bottom:1px solid #ccc}
#toolbar button, #toolbar select, #toolbar input{font-size:14px;padding:4px;}
canvas.fabric-canvas{width:100%;height:calc(100% - 40px);}
#toggle-editor-btn,#show-editor-btn{position:absolute;padding:6px 12px;background:rgba(0,0,0,0.5);color:#fff;border:none;border-radius:4px;cursor:pointer;font-size:14px;z-index:3}
#toggle-editor-btn{top:8px;right:8px}
#show-editor-btn{bottom:8px;right:8px;display:none}
#show-editor-btn.visible{display:block;}
</style>
<script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r134/three.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/three/examples/js/controls/OrbitControls.js"></script>
<script src="https://cdn.jsdelivr.net/npm/three/examples/js/loaders/GLTFLoader.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.2.4/fabric.min.js"></script>
</head>
<body>
<div id="app">
  <div id="scene-container">
    <button id="toggle-editor-btn">إخفاء المحرر</button>
    <button id="show-editor-btn">إظهار المحرر</button>
  </div>
  <div id="editor-container">
    <div id="toolbar">
      <button id="add-image-btn">📁 رفع صورة</button>
      <button id="add-text-btn">🅰️ نص</button>
      <button id="crop-btn">✂️ قص</button>
      <button id="free-draw-btn">✏️ رسم</button>
      <label>فلتر:
        <select id="filter-select">
          <option value="none">بلا</option>
          <option value="grayscale">Grayscale</option>
          <option value="sepia">Sepia</option>
          <option value="brightness">Brightness</option>
          <option value="contrast">Contrast</option>
          <option value="blur">Blur</option>
        </select>
      </label>
      <button id="undo-btn">↩️ تراجع</button>
      <button id="redo-btn">↪️ إعادة</button>
    </div>
    <canvas id="fabric-canvas" class="fabric-canvas"></canvas>
  </div>
</div>

<script>
const dpr = window.devicePixelRatio || 1;
const sceneContainer = document.getElementById("scene-container");
const editorContainer = document.getElementById("editor-container");
const toggleBtn = document.getElementById("toggle-editor-btn");
const showBtn = document.getElementById("show-editor-btn");
let scene, camera, renderer, controls, model;
let fabricCanvas, texture;
const gltfLoader = new THREE.GLTFLoader();

// === init Three.js ===
function initThree(){
  scene = new THREE.Scene();
  camera = new THREE.PerspectiveCamera(60, sceneContainer.clientWidth/sceneContainer.clientHeight,1,1000);
  camera.position.set(0,2,5);
  renderer = new THREE.WebGLRenderer({antialias:true});
  renderer.setPixelRatio(dpr);
  renderer.setSize(sceneContainer.clientWidth, sceneContainer.clientHeight);
  sceneContainer.appendChild(renderer.domElement);
  controls = new THREE.OrbitControls(camera, renderer.domElement);
  const light = new THREE.DirectionalLight(0xffffff,0.8);
  light.position.set(5,10,7);
  scene.add(new THREE.AmbientLight(0xffffff,0.4), light);
  gltfLoader.load("model.gltf", gltf => {
    model = gltf.scene; scene.add(model);
  });
  window.addEventListener("resize", () => {
    camera.aspect = sceneContainer.clientWidth/sceneContainer.clientHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(sceneContainer.clientWidth, sceneContainer.clientHeight);
  });
}

// === init Fabric ===
function initFabric(){
  fabricCanvas = new fabric.Canvas('fabric-canvas', {
    selection: true,
    backgroundColor: 'white'
  });
  function resizeFabric(){
    const w = editorContainer.clientWidth * dpr;
    const h = editorContainer.clientHeight * dpr - 40 * dpr;
    fabricCanvas.setWidth(w);
    fabricCanvas.setHeight(h);
    fabricCanvas.calcOffset();
  }
  resizeFabric();
  window.addEventListener('resize', resizeFabric);

  // أدوات:
  document.getElementById('add-image-btn').onclick = ()=>{
    const inp = document.createElement('input');
    inp.type='file'; inp.accept="image/*";
    inp.onchange = e=>{
      const file = e.target.files[0];
      const reader = new FileReader();
      reader.onload = ev=>{
        fabric.Image.fromURL(ev.target.result, img=>{
          img.set({ left:100*dpr, top:100*dpr, scaleX:0.5, scaleY:0.5 }).setControlsVisibility({
            mt:true,mb:true,ml:true,mr:true,tl:true,tr:true,bl:true,br:true
          });
          fabricCanvas.add(img).setActiveObject(img);
          saveState();
        });
      };
      reader.readAsDataURL(file);
    };
    inp.click();
  };

  document.getElementById('add-text-btn').onclick = ()=>{
    const txt = new fabric.IText('نص هنا', {
      left:150*dpr, top:150*dpr, fontFamily:'Tajawal, sans-serif',
      fontSize:48*dpr, fill:'#000000', shadow: new fabric.Shadow({color:'rgba(0,0,0,0.3)', blur:3}),
    });
    fabricCanvas.add(txt).setActiveObject(txt);
    saveState();
  };

  let cropping=false, cropRect;
  document.getElementById('crop-btn').onclick = ()=>{
    cropping = !cropping;
    if(cropping){
      cropRect = new fabric.Rect({ fill:'rgba(0,0,0,0.3)', originX:'left', originY:'top', selectable:false });
      fabricCanvas.add(cropRect);
      fabricCanvas.on('mouse:down', e=>{ if(cropping) cropRect.set({ left:e.pointer.x, top:e.pointer.y, width:0, height:0 });});
      fabricCanvas.on('mouse:move', e=>{ if(cropping && cropRect){ cropRect.set({ width:e.pointer.x - cropRect.left, height:e.pointer.y - cropRect.top }); fabricCanvas.renderAll();}});
      fabricCanvas.on('mouse:up', ()=>{
        if(cropping){
          const obj = fabricCanvas.getActiveObject();
          if(obj && obj.type==='image'){
            obj.clone(clone=>{
              clone.clipTo = ctx=>{
                const r = cropRect.getBoundingRect();
                ctx.rect(-clone.width/2 + (cropRect.left - obj.left)/obj.scaleX, -clone.height/2 + (cropRect.top - obj.top)/obj.scaleY, cropRect.width/obj.scaleX, cropRect.height/obj.scaleY);
              };
              clone.setCoords();
              fabricCanvas.remove(obj);
              fabricCanvas.add(clone).setActiveObject(clone);
              saveState();
            });
            fabricCanvas.remove(cropRect);
            cropping=false;
          }
        }
      });
    }
  };

  document.getElementById('free-draw-btn').onclick = ()=>{
    fabricCanvas.isDrawingMode = !fabricCanvas.isDrawingMode;
    document.getElementById('free-draw-btn').innerText = fabricCanvas.isDrawingMode ? '🖌️ رسم مفعل' : '✏️ رسم';
  };

  document.getElementById('filter-select').onchange = e=>{
    const f = e.target.value;
    const obj = fabricCanvas.getActiveObject();
    if(obj && obj.type==='image'){
      obj.filters = [];
      if(f==='grayscale') obj.filters.push(new fabric.Image.filters.Grayscale());
      if(f==='sepia') obj.filters.push(new fabric.Image.filters.Sepia());
      if(f==='brightness') obj.filters.push(new fabric.Image.filters.Brightness({ brightness:0.1 }));
      if(f==='contrast') obj.filters.push(new fabric.Image.filters.Contrast({ contrast:0.3 }));
      if(f==='blur') obj.filters.push(new fabric.Image.filters.Blur({ blur:0.5 }));
      obj.applyFilters(); fabricCanvas.requestRenderAll(); saveState();
    }
  };

  const history=[];
  let historyIndex=-1;
  function saveState(){
    history.splice(historyIndex+1);
    history.push(JSON.stringify(fabricCanvas));
    historyIndex = history.length -1;
  }
  document.getElementById('undo-btn').onclick = ()=>{
    if(historyIndex>0) fabricCanvas.loadFromJSON(history[--historyIndex], fabricCanvas.renderAll.bind(fabricCanvas));
  };
  document.getElementById('redo-btn').onclick = ()=>{
    if(historyIndex < history.length -1) fabricCanvas.loadFromJSON(history[++historyIndex], fabricCanvas.renderAll.bind(fabricCanvas));
  };

  fabricCanvas.on('object:modified', saveState);
  fabricCanvas.on('object:added', saveState);
  saveState();
}

// === texture update & apply to Three.js ===
function updateTexture(){
  if(!fabricCanvas) return;
  const canvasEl = fabricCanvas.lowerCanvasEl;
  if(!texture){
    texture = new THREE.CanvasTexture(canvasEl);
    texture.flipY=false;
    texture.generateMipmaps=true;
    texture.minFilter=THREE.LinearMipMapLinearFilter;
    texture.magFilter=THREE.LinearFilter;
    texture.anisotropy=renderer.capabilities.getMaxAnisotropy();
  }
  texture.needsUpdate = true;
  if(model){
    model.traverse(c=>{
      if(c.isMesh && c.name==='print'){
        c.material.map = texture;
        c.material.needsUpdate=true;
      }
    });
  }
}
function animate(){
  requestAnimationFrame(animate);
  updateTexture();
  controls.update();
  renderer.render(scene, camera);
}

// === toggle editor ===
toggleBtn.onclick =()=>{
  editorContainer.classList.add('hidden');
  toggleBtn.style.display='none';
  showBtn.classList.add('visible');
};
showBtn.onclick =()=>{
  editorContainer.classList.remove('hidden');
  toggleBtn.style.display='block';
  showBtn.classList.remove('visible');
};

document.addEventListener('DOMContentLoaded',()=>{
  initThree();
  initFabric();
  animate();
});
</script>
</body>
</html>