// ملف المصادقة وإدارة المستخدمين
let currentUser = null;

// عرض نافذة التسجيل
function showRegistrationModal() {
    const modal = document.getElementById('registration-modal');
    if (modal) {
        modal.style.display = 'flex';
    }
}

// إخفاء نافذة التسجيل
function hideRegistrationModal() {
    const modal = document.getElementById('registration-modal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// معالجة تسجيل المستخدم الجديد
function handleRegistration(event) {
    event.preventDefault();
    
    // التحقق من صحة البيانات
    if (!validateForm()) {
        return false;
    }
    
    // التحقق من وجود معرف الاشتراك
    const subscriptionId = document.getElementById('subscription_id').value;
    if (!subscriptionId) {
        showMessage('يرجى إكمال عملية الاشتراك أولاً', 'error');
        return false;
    }
    
    // جمع بيانات النموذج
    const formData = {
        fullname: document.getElementById('fullname').value.trim(),
        email: document.getElementById('email').value.trim(),
        mobile: document.getElementById('mobile').value.trim(),
        password: document.getElementById('password').value,
        plan: document.querySelector('input[name="plan"]:checked').value,
        subscription_id: subscriptionId
    };
    
    // محاكاة إرسال البيانات للخادم
    simulateRegistration(formData);
    
    return false;
}

// محاكاة عملية التسجيل
function simulateRegistration(userData) {
    showMessage('جاري التسجيل...', 'success');
    
    // محاكاة تأخير الشبكة
    setTimeout(() => {
        // حفظ بيانات المستخدم في التخزين المحلي
        localStorage.setItem('userData', JSON.stringify(userData));
        localStorage.setItem('isLoggedIn', 'true');
        
        currentUser = userData;
        
        showMessage('تم التسجيل بنجاح! مرحباً بك في التطبيق.', 'success');
        
        // إخفاء النافذة بعد ثانيتين
        setTimeout(() => {
            hideRegistrationModal();
            updateUIForLoggedInUser();
        }, 2000);
        
    }, 1500);
}

// تحديث واجهة المستخدم للمستخدم المسجل
function updateUIForLoggedInUser() {
    const loginBtn = document.getElementById('login-btn');
    if (loginBtn && currentUser) {
        loginBtn.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#d3a77b">
                <path d="M234-276q51-39 114-61.5T480-360q69 0 132 22.5T726-276q35-41 54.5-93T800-480q0-133-93.5-226.5T480-800q-133 0-226.5 93.5T160-480q0 59 19.5 111t54.5 93Zm246-164q-59 0-99.5-40.5T340-580q0-59 40.5-99.5T480-720q59 0 99.5 40.5T620-580q0 59-40.5 99.5T480-440Zm0 360q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Z"/>
            </svg>
        `;
        loginBtn.title = `مرحباً ${currentUser.fullname}`;
        
        // تغيير وظيفة الزر لعرض معلومات المستخدم
        loginBtn.onclick = showUserProfile;
    }
    
    // إظهار الميزات المخصصة للمشتركين
    showSubscriberFeatures();
}

// عرض ملف المستخدم
function showUserProfile() {
    if (currentUser) {
        alert(`الملف الشخصي:\nالاسم: ${currentUser.fullname}\nالبريد الإلكتروني: ${currentUser.email}\nنوع الاشتراك: ${currentUser.plan}`);
    }
}

// إظهار الميزات المخصصة للمشتركين
function showSubscriberFeatures() {
    const subscriberElements = document.querySelectorAll('.subscriber-only');
    subscriberElements.forEach(element => {
        element.classList.remove('hidden');
    });
}

// تسجيل الخروج
function logout() {
    localStorage.removeItem('userData');
    localStorage.removeItem('isLoggedIn');
    currentUser = null;
    
    // إعادة تعيين زر تسجيل الدخول
    const loginBtn = document.getElementById('login-btn');
    if (loginBtn) {
        loginBtn.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#d3a77b">
                <path d="M480-480q-66 0-113-47t-47-113q0-66 47-113t113-47q66 0 113 47t47 113q0 66-47 113t-113 47ZM160-160v-112q0-34 17.5-62.5T224-378q62-31 126-46.5T480-440q66 0 130 15.5T736-378q29 15 46.5 43.5T800-272v112H160Z"/>
            </svg>
        `;
        loginBtn.onclick = showRegistrationModal;
    }
    
    // إخفاء الميزات المخصصة للمشتركين
    const subscriberElements = document.querySelectorAll('.subscriber-only');
    subscriberElements.forEach(element => {
        element.classList.add('hidden');
    });
    
    showMessage('تم تسجيل الخروج بنجاح', 'success');
}

// التحقق من حالة تسجيل الدخول عند تحميل الصفحة
function checkLoginStatus() {
    const isLoggedIn = localStorage.getItem('isLoggedIn');
    const userData = localStorage.getItem('userData');
    
    if (isLoggedIn === 'true' && userData) {
        currentUser = JSON.parse(userData);
        updateUIForLoggedInUser();
    }
}

// إعداد مستمعي الأحداث
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من حالة تسجيل الدخول
    checkLoginStatus();
    
    // إعداد نموذج التسجيل
    const registrationForm = document.getElementById('registrationForm');
    if (registrationForm) {
        registrationForm.addEventListener('submit', handleRegistration);
    }
    
    // إعداد زر إغلاق النافذة
    const closeModal = document.querySelector('.close-modal');
    if (closeModal) {
        closeModal.addEventListener('click', hideRegistrationModal);
    }
    
    // إغلاق النافذة عند النقر خارجها
    const modal = document.getElementById('registration-modal');
    if (modal) {
        modal.addEventListener('click', function(event) {
            if (event.target === modal) {
                hideRegistrationModal();
            }
        });
    }
    
    // إعداد زر تسجيل الدخول
    const loginBtn = document.getElementById('login-btn');
    if (loginBtn && !currentUser) {
        loginBtn.addEventListener('click', showRegistrationModal);
    }
});

// تصدير الوظائف للاستخدام العام
window.showRegistrationModal = showRegistrationModal;
window.hideRegistrationModal = hideRegistrationModal;
window.logout = logout;

