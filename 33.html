<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Fabric.js Advanced Editor</title>
    <!-- Fabric.js Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.1/fabric.min.js"></script>
    <!-- Hammer.js Library (for touch gestures) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/hammer.js/2.0.8/hammer.min.js"></script>
    <!-- Cropper.js CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/cropperjs@1.5.12/dist/cropper.min.css"
      rel="stylesheet"
    />
    <style>
      /* ... (الأسلوب الأصلي يبقى كما هو) ... */
      
      /* إضافة أنماط جديدة لأدوات الرسم */
      .drawing-controls {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
        align-items: center;
      }

      .drawing-controls label {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 12px;
      }

      .drawing-controls .info {
        font-size: 11px;
      }

      .drawing-controls input[type="range"] {
        width: 80px;
      }

      .drawing-controls select {
        padding: 5px;
        border-radius: 4px;
        border: 1px solid #ced4da;
        min-width: 100px;
      }
    </style>
  </head>
  <body>
    <!-- ... (الهيكل الأصلي يبقى كما هو) ... -->
    
    <!-- تعديل محتوى sub-toolbar للرسم -->
    <script>
      // ... (الكود الأصلي يبقى كما هو) ...
      
      // --- Sub-Toolbar Dynamic Content ---
      // استبدال قالب أدوات الرسم بالكود الجديد
      const subToolbarTemplates = {
        Draw: `
            <div class="drawing-controls">
                <button id="draw-pencil" title="Pencil"><svg class="icon" viewBox="0 0 24 24"><path d="M12.9 6.87a1 1 0 0 0-1.42 0L2 15.35V22h6.65l9.48-9.48a1 1 0 0 0 0-1.41l-4.13-4.14Zm3.1-3.1L18 2l4 4-3.13 3.13-4.14-4.14ZM4 20v-3.35l7.1-7.1 3.35 3.35-7.1 7.1H4Z"/></svg>Pencil</button>
                <button id="draw-brush" title="Brush"><svg class="icon" viewBox="0 0 24 24"><path d="M19 19c-1.12.87-3.05.81-4.12-.58L5.75 9.77a3.5 3.5 0 0 1-.5-3.66c.2-.39.46-.75.76-1.07l2-2c1.78-1.78 4.67-1.78 6.46 0L19.5 7.5c1.78 1.78 1.78 4.67 0 6.46l-4.71 4.71c.71.36 1.48.56 2.29.56ZM17.41 5.92c-.78-.78-2.05-.78-2.83 0L7.05 13.45l2.83 2.83L17.41 8.75c.78-.78.78-2.05 0-2.83Z"/></svg>Brush</button>
                <button id="draw-marker" title="Marker"><svg class="icon" viewBox="0 0 24 24"><path d="M3 21v-4l9-9 4 4-9 9H3ZM16.62 4.41a2 2 0 0 0-2.83 0l-1.39 1.39 4 4 1.39-1.39a2 2 0 0 0 0-2.83Z"/></svg>Marker</button>
                <button id="draw-spray" title="Spray"><svg class="icon" viewBox="0 0 24 24"><path d="m16 9-2-2-4 4-2-2-4 4 11 11 11-11-4-4Zm0 0-2 2-4-4-2 2-4-4 11-11 11 11-4 4Z"/></svg>Spray</button>
                <button id="draw-dotted" title="Dotted Line"><svg class="icon" viewBox="0 0 24 24"><circle cx="4" cy="12" r="2"/><circle cx="12" cy="12" r="2"/><circle cx="20" cy="12" r="2"/></svg>Dotted</button>
                <button id="draw-eraser" title="Eraser"><svg class="icon" viewBox="0 0 24 24"><path d="M16 18H8c-.55 0-1-.45-1-1v-1c0-.55.45-1 1-1h8c.55 0 1 .45 1 1v1c0 .55-.45 1-1 1ZM17.84 5.37a2 2 0 0 0-2.83 0L6.77 13.6c-.25.25-.4.57-.44.92l-.35 3.52c-.06.6.35 1.18.94 1.25.17.02.34-.01.5-.07l3.5-.96c.33-.09.62-.25.87-.5l8.24-8.24a2 2 0 0 0 0-2.83l-2.65-2.65Z"/></svg>Eraser</button>
                <label for="brush-color">لون:</label><input type="color" id="brush-color" value="#000000" title="لون الفرشاة">
                <label for="brush-size">الحجم:</label><input type="range" id="brush-size" min="1" max="50" value="5" title="حجم الفرشاة">
                <label for="brush-opacity">الشفافية:</label><input type="range" id="brush-opacity" min="0" max="1" step="0.05" value="1" title="شفافية الفرشاة">
            </div>
            `,
            
        // ... (بقية القوالب تبقى كما هي) ...
      };
      
      // --- Drawing Tools ---
      /**
       * Activates the specified drawing mode and sets brush properties.
       * @param {string} mode - The drawing mode (e.g., 'pencil', 'brush', 'marker', 'spray', 'dotted', 'eraser').
       */
      function activateDrawingMode(mode) {
        canvas.isDrawingMode = true;
        canvas.freeDrawingBrush = null; // Clear existing brush

        // Get current brush settings from UI
        const brushColor = document.getElementById('brush-color').value;
        const brushSize = parseInt(document.getElementById('brush-size').value, 10);
        const brushOpacity = parseFloat(document.getElementById('brush-opacity').value);

        // Create and configure the brush based on mode
        switch (mode) {
          case "pencil":
            canvas.freeDrawingBrush = new fabric.PencilBrush(canvas);
            break;
          case "brush":
            canvas.freeDrawingBrush = new fabric.CircleBrush(canvas); // A round brush
            break;
          case "marker":
            canvas.freeDrawingBrush = new fabric.PencilBrush(canvas);
            canvas.freeDrawingBrush.strokeLineCap = "butt"; // Flat ends
            canvas.freeDrawingBrush.strokeLineJoin = "miter"; // Sharp corners
            canvas.freeDrawingBrush.blendMode = "multiply"; // For a marker-like transparency effect
            break;
          case "spray":
            canvas.freeDrawingBrush = new fabric.SprayBrush(canvas);
            canvas.freeDrawingBrush.density = 20; // More dense spray dots
            canvas.freeDrawingBrush.dotWidth = 1; // Size of individual dots
            break;
          case "dotted":
            canvas.freeDrawingBrush = new fabric.PencilBrush(canvas);
            canvas.freeDrawingBrush.strokeDashArray = [brushSize * 1.5, brushSize * 1.5]; // Dash and gap
            break;
          case "eraser":
            canvas.freeDrawingBrush = new fabric.EraserBrush(canvas);
            break;
          default:
            canvas.isDrawingMode = false; // Fallback to no drawing
            return;
        }

        // Apply common brush properties
        canvas.freeDrawingBrush.color = brushColor;
        canvas.freeDrawingBrush.width = brushSize;
        canvas.freeDrawingBrush.opacity = brushOpacity;

        console.log(`Drawing mode: ${mode} active.`);
      }
      
      // --- Attach Sub-Toolbar Listeners ---
      function attachSubToolbarListeners(category) {
        // ... (الكود الأصلي يبقى كما هو) ...
        
        switch (category) {
          case "Draw":
            elements["draw-pencil"].addEventListener("click", () => activateDrawingMode("pencil"));
            elements["draw-brush"].addEventListener("click", () => activateDrawingMode("brush"));
            elements["draw-marker"].addEventListener("click", () => activateDrawingMode("marker"));
            elements["draw-spray"].addEventListener("click", () => activateDrawingMode("spray"));
            elements["draw-dotted"].addEventListener("click", () => activateDrawingMode("dotted"));
            elements["draw-eraser"].addEventListener("click", () => activateDrawingMode("eraser"));

            const brushColorInput = elements["brush-color"];
            const brushSizeInput = elements["brush-size"];
            const brushOpacityInput = elements["brush-opacity"];

            const updateBrush = () => {
              if (canvas.freeDrawingBrush) {
                canvas.freeDrawingBrush.color = brushColorInput.value;
                canvas.freeDrawingBrush.width = parseInt(brushSizeInput.value, 10);
                canvas.freeDrawingBrush.opacity = parseFloat(brushOpacityInput.value);
                canvas.renderAll();
              }
            };

            brushColorInput.addEventListener("input", updateBrush);
            brushSizeInput.addEventListener("input", updateBrush);
            brushOpacityInput.addEventListener("input", updateBrush);

            // Initialize drawing mode to pencil when 'Draw' category is first displayed
            activateDrawingMode("pencil");
            updateBrush(); // Apply initial values to the brush
            break;
            
          // ... (بقية الحالات تبقى كما هي) ...
        }
      }
      
      // ... (بقية الكود يبقى كما هو) ...
    </script>
  </body>
</html>