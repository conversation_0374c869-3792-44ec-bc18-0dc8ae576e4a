<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أداة القص المتكاملة مع عرض النتيجة</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #1a2a6c, #b21f1f, #1a2a6c);
            color: #fff;
            min-height: 100vh;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .container {
            max-width: 1000px;
            width: 100%;
            margin: 0 auto;
            background: rgba(20, 30, 48, 0.85);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
        }
        
        header {
            text-align: center;
            padding: 25px;
            background: rgba(10, 15, 25, 0.7);
            border-bottom: 2px solid rgba(255, 255, 255, 0.1);
        }
        
        header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }
        
        header p {
            font-size: 1.1rem;
            max-width: 700px;
            margin: 0 auto;
            line-height: 1.6;
            opacity: 0.9;
            color: #a0c6ff;
        }
        
        .app-content {
            display: flex;
            flex-direction: column;
            gap: 25px;
            padding: 30px;
        }
        
        .image-section {
            position: relative;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 500px;
        }
        
        .image-container {
            position: relative;
            width: 100%;
            height: 400px;
            border: 2px dashed rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            background: rgba(10, 15, 25, 0.5);
            overflow: hidden;
            margin-top: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        #imagePreview, #croppedResult {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            display: none;
        }
        
        .crop-overlay {
            position: absolute;
            background: rgba(0, 0, 0, 0.5);
            z-index: 5;
            pointer-events: none;
            display: none;
        }
        
        .crop-rect {
            position: absolute;
            border: 2px dashed #4a9fe3;
            box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5);
            cursor: move;
            z-index: 6;
            display: none;
        }
        
        .crop-handle {
            position: absolute;
            width: 12px;
            height: 12px;
            background: #4a9fe3;
            border-radius: 50%;
            z-index: 7;
        }
        
        .crop-handle.nw { top: -6px; left: -6px; cursor: nw-resize; }
        .crop-handle.ne { top: -6px; right: -6px; cursor: ne-resize; }
        .crop-handle.sw { bottom: -6px; left: -6px; cursor: sw-resize; }
        .crop-handle.se { bottom: -6px; right: -6px; cursor: se-resize; }
        
        .crop-bar {
            width: 100%;
            background: rgba(30, 40, 60, 0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        .crop-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #4a9fe3;
        }
        
        .crop-header h2 {
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .crop-header i {
            color: #4a9fe3;
        }
        
        .crop-scroll {
            overflow-x: auto;
            padding: 10px 0;
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .crop-scroll::-webkit-scrollbar {
            height: 8px;
        }
        
        .crop-scroll::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }
        
        .crop-scroll::-webkit-scrollbar-thumb {
            background: #4a9fe3;
            border-radius: 4px;
        }
        
        .crop-btn {
            background: rgba(74, 159, 227, 0.2);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 12px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            min-width: 120px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            white-space: nowrap;
        }
        
        .crop-btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            background: linear-gradient(to right, #4a9fe3, #2a5a8c);
        }
        
        .crop-btn i {
            font-size: 1.8rem;
        }
        
        .crop-btn.active {
            background: linear-gradient(to right, #4a9fe3, #2a5a8c);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        
        .rotate-section {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .rotate-btn {
            background: rgba(155, 89, 182, 0.3);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .rotate-btn:hover {
            background: linear-gradient(to right, #9b59b6, #8e44ad);
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            margin-top: 10px;
        }
        
        .action-btn {
            flex: 1;
            padding: 15px;
            border-radius: 12px;
            border: none;
            color: white;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        
        #loadImageBtn {
            background: linear-gradient(to right, #4a9fe3, #2a5a8c);
        }
        
        #applyCropBtn {
            background: linear-gradient(to right, #27ae60, #219653);
        }
        
        #resetBtn {
            background: linear-gradient(to right, #e74c3c, #c0392b);
        }
        
        #backToCropBtn {
            background: linear-gradient(to right, #f39c12, #d35400);
            display: none;
        }
        
        .preview-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 1.8rem;
            font-weight: bold;
            color: rgba(255, 255, 255, 0.5);
            text-align: center;
            width: 100%;
            z-index: 2;
            padding: 0 20px;
        }
        
        .file-input-container {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }
        
        .file-input-label {
            background: linear-gradient(to right, #4a9fe3, #2a5a8c);
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .file-input-label:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
        }
        
        #imageLoader {
            display: none;
        }
        
        .ratio-indicator {
            display: inline-block;
            margin-top: 5px;
            padding: 3px 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            font-size: 0.9rem;
        }
        
        .result-actions {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 20px;
        }
        
        .result-btn {
            padding: 12px 25px;
            border-radius: 10px;
            border: none;
            color: white;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .result-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        
        #saveBtn {
            background: linear-gradient(to right, #27ae60, #219653);
        }
        
        #newCropBtn {
            background: linear-gradient(to right, #3498db, #2980b9);
        }
        
        @media (max-width: 768px) {
            header h1 {
                font-size: 2rem;
            }
            
            .image-container {
                height: 350px;
            }
            
            .crop-btn {
                min-width: 100px;
                padding: 12px 20px;
                font-size: 0.9rem;
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .rotate-btn {
                padding: 10px 15px;
                font-size: 0.9rem;
            }
        }
        
        @media (max-width: 480px) {
            .app-content {
                padding: 20px 15px;
            }
            
            header {
                padding: 20px 15px;
            }
            
            header h1 {
                font-size: 1.7rem;
            }
            
            .image-container {
                height: 300px;
            }
            
            .crop-btn {
                min-width: 85px;
                padding: 10px 15px;
            }
            
            .crop-btn i {
                font-size: 1.5rem;
            }
            
            .preview-text {
                font-size: 1.4rem;
            }
            
            .rotate-section {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-crop-alt"></i> أداة القص المتكاملة مع عرض النتيجة</h1>
            <p>قص الصور وعرض النتيجة في نفس الحاوية مع إمكانية الرجوع للصورة الأصلية</p>
        </header>
        
        <div class="app-content">
            <div class="image-section">
                <div class="preview-text" id="previewText">قم بتحميل صورة لبدء استخدام أداة القص</div>
                
                <div class="image-container">
                    <img id="imagePreview" alt="الصورة المحددة">
                    <img id="croppedResult" alt="الصورة بعد القص">
                    <div class="crop-overlay" id="cropOverlay"></div>
                    <div class="crop-rect" id="cropRect">
                        <div class="crop-handle nw"></div>
                        <div class="crop-handle ne"></div>
                        <div class="crop-handle sw"></div>
                        <div class="crop-handle se"></div>
                    </div>
                </div>
                
                <div class="file-input-container" id="fileInputContainer">
                    <label for="imageLoader" class="file-input-label">
                        <i class="fas fa-cloud-upload-alt"></i> تحميل صورة
                    </label>
                    <input type="file" id="imageLoader" accept="image/*">
                </div>
                
                <div class="result-actions" id="resultActions" style="display: none;">
                    <button id="saveBtn" class="result-btn">
                        <i class="fas fa-download"></i> حفظ الصورة
                    </button>
                    <button id="newCropBtn" class="result-btn">
                        <i class="fas fa-crop-alt"></i> قص صورة جديدة
                    </button>
                </div>
            </div>
            
            <div class="crop-bar" id="cropBar">
                <div class="crop-header">
                    <h2><i class="fas fa-crop-alt"></i> نسب القص</h2>
                    <div id="currentRatio">النسبة الحالية: <span class="ratio-indicator">-</span></div>
                </div>
                
                <div class="crop-scroll">
                    <button class="crop-btn" data-ratio="custom">
                        <i class="fas fa-ruler-combined"></i>
                        مخصص
                        <span class="ratio-indicator">-</span>
                    </button>
                    <button class="crop-btn" data-ratio="1:1">
                        <i class="fas fa-square"></i>
                        مربع
                        <span class="ratio-indicator">1:1</span>
                    </button>
                    <button class="crop-btn" data-ratio="4:3">
                        <i class="fas fa-desktop"></i>
                        شاشة
                        <span class="ratio-indicator">4:3</span>
                    </button>
                    <button class="crop-btn" data-ratio="16:9">
                        <i class="fas fa-tv"></i>
                        عريض
                        <span class="ratio-indicator">16:9</span>
                    </button>
                    <button class="crop-btn" data-ratio="3:2">
                        <i class="fas fa-camera"></i>
                        كاميرا
                        <span class="ratio-indicator">3:2</span>
                    </button>
                    <button class="crop-btn" data-ratio="5:4">
                        <i class="fas fa-image"></i>
                        تقليدي
                        <span class="ratio-indicator">5:4</span>
                    </button>
                    <button class="crop-btn" data-ratio="9:16">
                        <i class="fas fa-mobile-alt"></i>
                        عمودي
                        <span class="ratio-indicator">9:16</span>
                    </button>
                </div>
                
                <div class="rotate-section">
                    <button id="rotateRatioBtn" class="rotate-btn">
                        <i class="fas fa-sync-alt"></i> تدوير النسبة (أفقي/رأسي)
                    </button>
                    <button id="rotateImageBtn" class="rotate-btn">
                        <i class="fas fa-redo"></i> تدوير الصورة 90°
                    </button>
                </div>
                
                <div class="action-buttons">
                    <button id="applyCropBtn" class="action-btn">
                        <i class="fas fa-check"></i> تطبيق القص
                    </button>
                    <button id="backToCropBtn" class="action-btn">
                        <i class="fas fa-undo"></i> الرجوع للتعديل
                    </button>
                    <button id="resetBtn" class="action-btn">
                        <i class="fas fa-redo"></i> إعادة تعيين
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // متغيرات التطبيق
        let originalImage = null;
        let originalImageDataUrl = null;
        let cropRect = {
            x: 0,
            y: 0,
            width: 0,
            height: 0
        };
        let cropRatio = null;
        let isDragging = false;
        let activeHandle = null;
        let dragStartX, dragStartY;
        let cropStartX, cropStartY, cropStartWidth, cropStartHeight;
        let imageRotation = 0;
        
        // عناصر DOM
        const imageLoader = document.getElementById('imageLoader');
        const imagePreview = document.getElementById('imagePreview');
        const croppedResult = document.getElementById('croppedResult');
        const cropOverlay = document.getElementById('cropOverlay');
        const cropRectEl = document.getElementById('cropRect');
        const applyCropBtn = document.getElementById('applyCropBtn');
        const resetBtn = document.getElementById('resetBtn');
        const previewText = document.getElementById('previewText');
        const cropBtns = document.querySelectorAll('.crop-btn');
        const rotateRatioBtn = document.getElementById('rotateRatioBtn');
        const rotateImageBtn = document.getElementById('rotateImageBtn');
        const currentRatioSpan = document.querySelector('#currentRatio .ratio-indicator');
        const backToCropBtn = document.getElementById('backToCropBtn');
        const fileInputContainer = document.getElementById('fileInputContainer');
        const resultActions = document.getElementById('resultActions');
        const saveBtn = document.getElementById('saveBtn');
        const newCropBtn = document.getElementById('newCropBtn');
        
        // تحميل الصورة
        imageLoader.addEventListener('change', function(e) {
            if (e.target.files && e.target.files[0]) {
                const reader = new FileReader();
                
                reader.onload = function(event) {
                    originalImageDataUrl = event.target.result;
                    imagePreview.src = originalImageDataUrl;
                    imagePreview.style.display = 'block';
                    previewText.style.display = 'none';
                    croppedResult.style.display = 'none';
                    
                    // إظهار أدوات القص وإخفاء أدوات النتيجة
                    cropBar.style.display = 'block';
                    fileInputContainer.style.display = 'flex';
                    resultActions.style.display = 'none';
                    backToCropBtn.style.display = 'none';
                    
                    // تهيئة القص بعد تحميل الصورة
                    imagePreview.onload = function() {
                        originalImage = new Image();
                        originalImage.src = originalImageDataUrl;
                        initCropMode();
                        imageRotation = 0; // إعادة تعيين الدوران
                    };
                };
                
                reader.readAsDataURL(e.target.files[0]);
            }
        });
        
        // تهيئة وضع القص
        function initCropMode() {
            // إخفاء النتيجة
            croppedResult.style.display = 'none';
            imagePreview.style.display = 'block';
            
            // إظهار عناصر القص
            cropOverlay.style.display = 'block';
            cropRectEl.style.display = 'block';
            
            // تعيين حجم مستطيل القص الافتراضي
            const containerWidth = imagePreview.parentElement.clientWidth;
            const containerHeight = imagePreview.parentElement.clientHeight;
            
            const cropSize = Math.min(containerWidth, containerHeight) * 0.6;
            
            cropRect = {
                x: (containerWidth - cropSize) / 2,
                y: (containerHeight - cropSize) / 2,
                width: cropSize,
                height: cropSize
            };
            
            updateCropRect();
            initCropHandlers();
            
            // تحديد زر القص المخصص كنشط
            document.querySelector('.crop-btn[data-ratio="custom"]').classList.add('active');
            currentRatioSpan.textContent = 'مخصص';
        }
        
        // تحديث مستطيل القص على الشاشة
        function updateCropRect() {
            cropRectEl.style.left = cropRect.x + 'px';
            cropRectEl.style.top = cropRect.y + 'px';
            cropRectEl.style.width = cropRect.width + 'px';
            cropRectEl.style.height = cropRect.height + 'px';
            
            cropOverlay.style.left = '0';
            cropOverlay.style.top = '0';
            cropOverlay.style.width = '100%';
            cropOverlay.style.height = '100%';
        }
        
        // إعادة تعيين وضع القص
        function resetCropMode() {
            cropOverlay.style.display = 'none';
            cropRectEl.style.display = 'none';
            previewText.style.display = 'block';
            imagePreview.style.display = 'none';
            croppedResult.style.display = 'none';
            imageLoader.value = '';
            imageRotation = 0;
            
            // إزالة التحديد من أزرار القص
            cropBtns.forEach(btn => btn.classList.remove('active'));
            currentRatioSpan.textContent = '-';
            
            // إخفاء أدوات القص وإظهار زر التحميل
            cropBar.style.display = 'block';
            fileInputContainer.style.display = 'flex';
            resultActions.style.display = 'none';
            backToCropBtn.style.display = 'none';
        }
        
        // تهيئة معالجات السحب والتعديل
        function initCropHandlers() {
            // معالج بدء السحب
            function startDrag(e) {
                isDragging = true;
                dragStartX = e.clientX;
                dragStartY = e.clientY;
                cropStartX = cropRect.x;
                cropStartY = cropRect.y;
                
                // تحديد المقبض النشط
                if (e.target.classList.contains('crop-handle')) {
                    activeHandle = Array.from(e.target.classList).find(cls => 
                        cls === 'nw' || cls === 'ne' || cls === 'sw' || cls === 'se'
                    );
                    cropStartWidth = cropRect.width;
                    cropStartHeight = cropRect.height;
                } else {
                    activeHandle = null;
                }
            }
            
            // معالج السحب
            function doDrag(e) {
                if (!isDragging) return;
                
                const dx = e.clientX - dragStartX;
                const dy = e.clientY - dragStartY;
                
                if (activeHandle) {
                    // تعديل الحجم باستخدام المقابض
                    switch(activeHandle) {
                        case 'nw':
                            cropRect.x = cropStartX + dx;
                            cropRect.y = cropStartY + dy;
                            cropRect.width = cropStartWidth - dx;
                            cropRect.height = cropStartHeight - dy;
                            break;
                        case 'ne':
                            cropRect.y = cropStartY + dy;
                            cropRect.width = cropStartWidth + dx;
                            cropRect.height = cropStartHeight - dy;
                            break;
                        case 'sw':
                            cropRect.x = cropStartX + dx;
                            cropRect.width = cropStartWidth - dx;
                            cropRect.height = cropStartHeight + dy;
                            break;
                        case 'se':
                            cropRect.width = cropStartWidth + dx;
                            cropRect.height = cropStartHeight + dy;
                            break;
                    }
                    
                    // تطبيق نسبة القص
                    if (cropRatio && cropRatio !== 'custom') {
                        applyCropRatio();
                    }
                } else {
                    // نقل مستطيل القص
                    cropRect.x = cropStartX + dx;
                    cropRect.y = cropStartY + dy;
                }
                
                // التأكد من بقاء المستطيل داخل حدود الصورة
                constrainCropRect();
                updateCropRect();
            }
            
            // معالج إنهاء السحب
            function stopDrag() {
                isDragging = false;
                activeHandle = null;
            }
            
            // إضافة معالجي الأحداث
            cropRectEl.addEventListener('mousedown', startDrag);
            document.addEventListener('mousemove', doDrag);
            document.addEventListener('mouseup', stopDrag);
            
            // إضافة معالجي الأحداث للمقابض
            const handles = document.querySelectorAll('.crop-handle');
            handles.forEach(handle => {
                handle.addEventListener('mousedown', startDrag);
            });
        }
        
        // تطبيق نسبة القص الحالية
        function applyCropRatio() {
            if (!cropRatio || cropRatio === 'custom') return;
            
            const [w, h] = cropRatio.split(':').map(Number);
            const aspectRatio = w / h;
            
            // الحفاظ على المركز مع تغيير الحجم
            const centerX = cropRect.x + cropRect.width/2;
            const centerY = cropRect.y + cropRect.height/2;
            
            if (cropRect.width / cropRect.height > aspectRatio) {
                cropRect.width = cropRect.height * aspectRatio;
            } else {
                cropRect.height = cropRect.width / aspectRatio;
            }
            
            cropRect.x = centerX - cropRect.width/2;
            cropRect.y = centerY - cropRect.height/2;
        }
        
        // التأكد من بقاء مستطيل القص داخل حدود الصورة
        function constrainCropRect() {
            const container = imagePreview.parentElement;
            const containerWidth = container.clientWidth;
            const containerHeight = container.clientHeight;
            
            // التأكد من أن المستطيل داخل حدود الحاوية
            cropRect.x = Math.max(0, Math.min(containerWidth - cropRect.width, cropRect.x));
            cropRect.y = Math.max(0, Math.min(containerHeight - cropRect.height, cropRect.y));
            
            // التأكد من أن الأبعاد لا تقل عن 50 بكسل
            cropRect.width = Math.max(50, cropRect.width);
            cropRect.height = Math.max(50, cropRect.height);
        }
        
        // معالجات أزرار القص
        cropBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const ratio = this.getAttribute('data-ratio');
                cropRatio = ratio;
                
                // تطبيق نسبة القص على مستطيل القص الحالي
                if (cropRatio && cropRatio !== 'custom') {
                    applyCropRatio();
                    constrainCropRect();
                    updateCropRect();
                }
                
                // إزالة التحديد من جميع الأزرار
                cropBtns.forEach(b => b.classList.remove('active'));
                
                // تحديد الزر الحالي
                this.classList.add('active');
                
                // تحديث مؤشر النسبة الحالية
                if (ratio === 'custom') {
                    currentRatioSpan.textContent = 'مخصص';
                } else {
                    currentRatioSpan.textContent = ratio;
                }
            });
        });
        
        // تدوير النسبة (أفقي/رأسي)
        rotateRatioBtn.addEventListener('click', function() {
            if (!cropRatio || cropRatio === 'custom') {
                alert('الرجاء تحديد نسبة قص أولاً');
                return;
            }
            
            // قلب النسبة (تحويل 4:3 إلى 3:4)
            const [w, h] = cropRatio.split(':').map(Number);
            const newRatio = `${h}:${w}`;
            cropRatio = newRatio;
            
            // تحديث الزر النشط
            const activeBtn = document.querySelector('.crop-btn.active');
            if (activeBtn) {
                activeBtn.setAttribute('data-ratio', newRatio);
                activeBtn.querySelector('.ratio-indicator').textContent = newRatio;
            }
            
            // تطبيق النسبة الجديدة
            applyCropRatio();
            constrainCropRect();
            updateCropRect();
            
            // تحديث مؤشر النسبة الحالية
            currentRatioSpan.textContent = newRatio;
        });
        
        // تدوير الصورة 90 درجة
        rotateImageBtn.addEventListener('click', function() {
            if (!imagePreview.src || imagePreview.src === window.location.href) {
                alert('الرجاء تحميل صورة أولاً');
                return;
            }
            
            // زيادة زاوية الدوران
            imageRotation = (imageRotation + 90) % 360;
            
            // تدوير الصورة باستخدام canvas
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            // تحديد أبعاد canvas بناءً على اتجاه الدوران
            if (imageRotation % 180 === 0) {
                canvas.width = imagePreview.naturalWidth;
                canvas.height = imagePreview.naturalHeight;
            } else {
                canvas.width = imagePreview.naturalHeight;
                canvas.height = imagePreview.naturalWidth;
            }
            
            // نقل مركز الدوران
            ctx.translate(canvas.width / 2, canvas.height / 2);
            ctx.rotate(imageRotation * Math.PI / 180);
            
            // رسم الصورة مع تصحيح المركز بعد الدوران
            ctx.drawImage(
                imagePreview, 
                -imagePreview.naturalWidth / 2, 
                -imagePreview.naturalHeight / 2
            );
            
            // تحديث الصورة المعروضة
            imagePreview.src = canvas.toDataURL('image/png');
            
            // إعادة تهيئة القص
            imagePreview.onload = initCropMode;
        });
        
        // تطبيق القص وعرض النتيجة في نفس الحاوية
        applyCropBtn.addEventListener('click', function() {
            if (!imagePreview.src || imagePreview.src === window.location.href) {
                alert('الرجاء تحميل صورة أولاً');
                return;
            }
            
            // إنشاء عنصر canvas مؤقت للقص
            const tempCanvas = document.createElement('canvas');
            const tempCtx = tempCanvas.getContext('2d');
            
            // تعيين أبعاد canvas المؤقت
            tempCanvas.width = cropRect.width;
            tempCanvas.height = cropRect.height;
            
            // حساب الإحداثيات النسبية داخل الصورة
            const container = imagePreview.parentElement;
            const scaleX = imagePreview.naturalWidth / container.clientWidth;
            const scaleY = imagePreview.naturalHeight / container.clientHeight;
            
            // رسم الجزء المطلوب قصه
            tempCtx.drawImage(
                imagePreview,
                cropRect.x * scaleX, 
                cropRect.y * scaleY,
                cropRect.width * scaleX, 
                cropRect.height * scaleY,
                0, 0, 
                cropRect.width, 
                cropRect.height
            );
            
            // عرض الصورة المقصوصة في نفس الحاوية
            croppedResult.src = tempCanvas.toDataURL('image/png');
            croppedResult.style.display = 'block';
            imagePreview.style.display = 'none';
            
            // إخفاء أدوات القص وإظهار أدوات النتيجة
            cropOverlay.style.display = 'none';
            cropRectEl.style.display = 'none';
            fileInputContainer.style.display = 'none';
            resultActions.style.display = 'flex';
            backToCropBtn.style.display = 'flex';
        });
        
        // الرجوع لوضع التعديل
        backToCropBtn.addEventListener('click', function() {
            // إظهار الصورة الأصلية وأدوات القص
            imagePreview.style.display = 'block';
            croppedResult.style.display = 'none';
            cropOverlay.style.display = 'block';
            cropRectEl.style.display = 'block';
            fileInputContainer.style.display = 'none';
            resultActions.style.display = 'none';
            backToCropBtn.style.display = 'none';
        });
        
        // حفظ الصورة
        saveBtn.addEventListener('click', function() {
            if (!croppedResult.src) return;
            
            const link = document.createElement('a');
            link.download = 'صورة-مقصودة.png';
            link.href = croppedResult.src;
            link.click();
        });
        
        // قص صورة جديدة
        newCropBtn.addEventListener('click', resetCropMode);
        
        // إعادة تعيين
        resetBtn.addEventListener('click', resetCropMode);
    </script>
</body>
</html>