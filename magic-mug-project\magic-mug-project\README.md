# مشروع محاكاة الأكواب السحرية الحرارية

هذا المشروع يقدم محاكاة تفاعلية لتأثير الأكواب السحرية الحرارية باستخدام Three.js و Fabric.js. يمكن للمستخدم تصميم الكوب باستخدام Fabric.js ومشاهدة تأثير الكشف الحراري على الكوب ثلاثي الأبعاد.

## الميزات

- تحميل نموذج كوب ثلاثي الأبعاد باستخدام Three.js
- تصميم الكوب باستخدام Fabric.js (إضافة نص وصور)
- ثلاثة أنواع من تأثيرات الكشف الحراري:
  - قناع الشيدر: يستخدم effect.png كقناع ألفا
  - التراكب الأسود المتلاشي: يستخدم طبقة سوداء تتلاشى تدريجيًا
  - نسيج التدرج الشفاف: يستخدم نسيج تدرج شفاف
- أزرار تحكم لتشغيل وإيقاف وإعادة تعيين التأثير
- تدوير الكوب باستخدام الماوس أو اللمس
- دعم الأجهزة المحمولة (تصميم متجاوب)

## كيفية الاستخدام

1. **تصميم الكوب**:
   - استخدم زر "إضافة نص" لإضافة نص إلى التصميم
   - استخدم زر "إضافة صورة" لإضافة صورة إلى التصميم
   - استخدم زر "مسح القماش" لمسح التصميم وبدء تصميم جديد

2. **تطبيق التأثير الحراري**:
   - اختر نوع التأثير من القائمة المنسدلة
   - انقر على زر "تشغيل" لبدء تأثير الكشف الحراري
   - انقر على زر "إيقاف مؤقت" لإيقاف التأثير مؤقتًا
   - انقر على زر "إيقاف" لإعادة تعيين التأثير

3. **تدوير الكوب**:
   - اسحب الماوس (أو إصبعك على الأجهزة اللمسية) فوق الكوب لتدويره

## هيكل المشروع

- `index.html`: هيكل الصفحة الرئيسية
- `style.css`: تنسيق الصفحة
- `main.js`: كود JavaScript الرئيسي
- `models/`: مجلد يحتوي على نموذج الكوب (model.gltf)
- `textures/`: مجلد يحتوي على الصور والقوام (texture.jpg و effect.png)

## التقنيات المستخدمة

- Three.js: لعرض النموذج ثلاثي الأبعاد
- GLTFLoader: لتحميل نموذج الكوب
- Fabric.js: لإنشاء وتحرير التصميم
- JavaScript: للتفاعل والتحكم
- CSS: للتنسيق والتصميم المتجاوب

## ملاحظات تقنية

- تم تنفيذ تأثير الكشف الحراري باستخدام ثلاث طرق مختلفة:
  1. **قناع الشيدر**: يستخدم شيدر مخصص مع قناع التأثير
  2. **التراكب الأسود المتلاشي**: ينشئ طبقة سوداء فوق النموذج تتلاشى تدريجيًا
  3. **نسيج التدرج الشفاف**: يستخدم خريطة ألفا مع تدرج شفاف

- تم تحسين الأداء من خلال:
  - تحديث المشهد فقط عند الحاجة
  - إيقاف التأثير تلقائيًا عند اكتمال الكشف
  - معالجة أخطاء تحميل النموذج
