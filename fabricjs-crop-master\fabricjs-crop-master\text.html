<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Image Editor with Text</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #1a2a6c, #b21f1f, #1a2a6c);
            color: #333;
            min-height: 100vh;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4);
            overflow: hidden;
            width: 100%;
            max-width: 1000px;
        }
        
        header {
            background: linear-gradient(to right, #2c3e50, #4a6491);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        h1 {
            font-size: 2.2rem;
            margin-bottom: 10px;
        }
        
        .subtitle {
            font-size: 1rem;
            opacity: 0.9;
        }
        
        .controls {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            padding: 15px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }
        
        button i {
            font-size: 16px;
        }
        
        #uploadBtn {
            background: linear-gradient(to right, #2ecc71, #27ae60);
            color: white;
        }
        
        #startCrop {
            background: linear-gradient(to right, #f39c12, #e67e22);
            color: white;
        }
        
        #startDrawing {
            background: linear-gradient(to right, #9b59b6, #8e44ad);
            color: white;
        }
        
        #addText {
            background: linear-gradient(to right, #3498db, #2980b9);
            color: white;
        }
        
        #fileInput {
            display: none;
        }
        
        .canvas-container {
            padding: 20px;
            text-align: center;
            background: #ecf0f1;
        }
        
        #canvas {
            border: 2px solid #bdc3c7;
            border-radius: 8px;
            background: white;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            max-width: 100%;
        }
        
        .tool-submenu {
            display: none;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .toolbar-title {
            font-size: 1.1rem;
            color: #2c3e50;
            margin-bottom: 15px;
            text-align: center;
            font-weight: 600;
        }
        
        .toolbar-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 15px;
        }
        
        .tool-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .tool-label {
            font-size: 0.9rem;
            color: #7f8c8d;
            font-weight: 600;
        }
        
        .color-preview {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            border: 2px solid #bdc3c7;
            cursor: pointer;
        }
        
        .slider-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        input[type="range"] {
            flex: 1;
            height: 8px;
            border-radius: 4px;
            background: #e0e0e0;
            outline: none;
        }
        
        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #3498db;
            cursor: pointer;
        }
        
        .value-display {
            min-width: 40px;
            text-align: center;
            background: #ecf0f1;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        select {
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid #bdc3c7;
            background: white;
            font-size: 0.9rem;
            width: 100%;
        }
        
        .instructions {
            background: #e8f4fc;
            border-left: 4px solid #3498db;
            padding: 15px;
            border-radius: 0 8px 8px 0;
            margin: 15px;
            font-size: 0.95rem;
        }
        
        .instructions h3 {
            color: #2980b9;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }
        
        .instructions ul {
            padding-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
            font-size: 0.9rem;
        }
        
        footer {
            text-align: center;
            padding: 15px;
            color: #7f8c8d;
            font-size: 0.9rem;
            background: #f8f9fa;
            border-top: 1px solid #dee2e6;
        }
        
        .preview-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 1.8rem;
            font-weight: bold;
            color: rgba(255, 255, 255, 0.7);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            pointer-events: none;
            text-align: center;
            max-width: 90%;
        }
        
        .text-tools-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            align-items: center;
            justify-content: center;
        }
        
        .text-tool {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 120px;
        }
        
        .text-tool input[type="color"] {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            padding: 0;
        }
        
        .text-tool input[type="color"]::-webkit-color-swatch {
            border: none;
            border-radius: 50%;
        }
        
        .text-tool input[type="color"]::-webkit-color-swatch-wrapper {
            padding: 0;
        }
        
        .text-tool label {
            margin-top: 5px;
            font-size: 0.8rem;
            color: #555;
            font-weight: 500;
        }
        
        .text-slider {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 120px;
        }
        
        .text-slider input {
            width: 100%;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-image"></i> Advanced Image Editor</h1>
            <p class="subtitle">Crop, Draw, and Add Text to Your Images</p>
        </header>
        
        <div class="controls">
            <button id="uploadBtn">
                <i class="fas fa-upload"></i> Upload Photo
            </button>
            <button id="startCrop">
                <i class="fas fa-crop"></i> Crop
            </button>
            <button id="startDrawing">
                <i class="fas fa-paint-brush"></i> Draw
            </button>
            <button id="addText">
                <i class="fas fa-font"></i> Add Text
            </button>
            <input type="file" id="fileInput" accept="image/*">
        </div>
        
        <div class="canvas-container">
            <div class="preview-text">Upload an image to start editing</div>
            <canvas id="canvas" width="700" height="500"></canvas>
        </div>
        
        <!-- Text Editing Toolbar -->
        <div class="tool-submenu" id="textToolbar">
            <div class="toolbar-title">Text Editing Tools</div>
            <div class="text-tools-container">
                <div class="text-tool">
                    <input type="color" id="textColor" value="#000000">
                    <label for="textColor">Font Color</label>
                </div>
                
                <div class="text-tool">
                    <input type="color" id="bgColor" value="#ffffff">
                    <label for="bgColor">Background</label>
                </div>
                
                <div class="text-slider">
                    <div class="slider-container">
                        <input type="range" min="0" max="100" value="100" id="textOpacity">
                        <span class="value-display" id="opacityValue">100%</span>
                    </div>
                    <label for="textOpacity">Transparency</label>
                </div>
                
                <div class="text-slider">
                    <div class="slider-container">
                        <input type="range" min="10" max="120" value="40" id="fontSize">
                        <span class="value-display" id="sizeValue">40px</span>
                    </div>
                    <label for="fontSize">Font Size</label>
                </div>
                
                <div class="text-slider">
                    <div class="slider-container">
                        <input type="range" min="0" max="20" value="0" id="textShadow">
                        <span class="value-display" id="shadowValue">0px</span>
                    </div>
                    <label for="textShadow">Shadow</label>
                </div>
                
                <div class="text-tool">
                    <select id="fontFamily">
                        <option value="Arial">Arial</option>
                        <option value="Verdana">Verdana</option>
                        <option value="Georgia">Georgia</option>
                        <option value="Times New Roman">Times New Roman</option>
                        <option value="Impact">Impact</option>
                        <option value="Comic Sans MS">Comic Sans</option>
                    </select>
                    <label for="fontFamily">Font Type</label>
                </div>
            </div>
        </div>
        
        <!--
        <div class="instructions">
            <h3><i class="fas fa-lightbulb"></i> How to use text tools:</h3>
            <ul>
                <li>Click "Add Text" to insert editable text onto your image</li>
                <li>Double-click on text to edit the content</li>
                <li>Drag text to reposition it on the canvas</li>
                <li>Use the corners to resize the text box</li>
                <li>Customize appearance using the text tools above</li>
                <li>Click outside the text to apply changes</li>
            </ul>
        </div>
        -->
        
        <!--
        <footer>
            <p>Advanced Image Editor &copy; 2023 | Created with Fabric.js</p>
        </footer>
        -->
    </div>

    <script>
        // Initialize canvas
        var canvas = new fabric.Canvas('canvas', {
            backgroundColor: '#ffffff',
            selection: true
        });
        
        var currentImage = null;
        var activeText = null;
        var isTextMode = false;
        
        // Set up event listeners
        $(document).ready(function() {
            // File upload handling
            $('#fileInput').on('change', handleFileSelect);
            $('#uploadBtn').click(function() {
                $('#fileInput').click();
            });
            
            // Text button
            $('#addText').click(addTextToCanvas);
            
            // Text editing controls
            $('#textColor').change(updateTextStyle);
            $('#bgColor').change(updateTextStyle);
            $('#textOpacity').on('input', updateTextStyle);
            $('#fontSize').on('input', updateTextStyle);
            $('#textShadow').on('input', updateTextStyle);
            $('#fontFamily').change(updateTextStyle);
            
            // Canvas events
            canvas.on('object:selected', handleObjectSelected);
            canvas.on('selection:cleared', handleSelectionCleared);
            canvas.on('text:editing:exited', saveState);
        });
        
        // Handle file selection
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (!file || !file.type.match('image.*')) return;
            
            const reader = new FileReader();
            reader.onload = function(e) {
                // Remove preview text
                $('.preview-text').hide();
                
                // Load image onto canvas
                fabric.Image.fromURL(e.target.result, function(img) {
                    if (currentImage) {
                        canvas.remove(currentImage);
                    }
                    
                    // Scale image to fit canvas
                    const scale = Math.min(
                        canvas.width / img.width,
                        canvas.height / img.height
                    );
                    
                    img.set({
                        left: canvas.width / 2,
                        top: canvas.height / 2,
                        originX: 'center',
                        originY: 'center',
                        scaleX: scale * 0.9,
                        scaleY: scale * 0.9,
                        selectable: true
                    });
                    
                    canvas.add(img);
                    canvas.setActiveObject(img);
                    currentImage = img;
                    canvas.renderAll();
                    saveState();
                });
            };
            reader.readAsDataURL(file);
        }
        
        // Add text to canvas
        function addTextToCanvas() {
            if (!currentImage) {
                alert("Please upload an image first!");
                return;
            }
            
            // Show text toolbar
            $('#textToolbar').slideDown(300);
            isTextMode = true;
            
            // Create new text object
            const text = new fabric.Text('Edit me', {
                left: canvas.width / 2,
                top: canvas.height / 2,
                fontSize: 40,
                fontFamily: 'Arial',
                fill: $('#textColor').val(),
                backgroundColor: $('#bgColor').val(),
                opacity: $('#textOpacity').val() / 100,
                shadow: '0px 0px 0px rgba(0,0,0,0)',
                originX: 'center',
                originY: 'center',
                selectable: true,
                hasControls: true,
                padding: 10,
                textAlign: 'center'
            });
            
            canvas.add(text);
            canvas.setActiveObject(text);
            activeText = text;
            canvas.renderAll();
            saveState();
        }
        
        // Update text style based on controls
        function updateTextStyle() {
            if (!activeText) return;
            
            // Update preview elements
            $('#opacityValue').text($('#textOpacity').val() + '%');
            $('#sizeValue').text($('#fontSize').val() + 'px');
            $('#shadowValue').text($('#textShadow').val() + 'px');
            
            // Update text object
            activeText.set({
                fill: $('#textColor').val(),
                backgroundColor: $('#bgColor').val(),
                opacity: $('#textOpacity').val() / 100,
                fontSize: parseInt($('#fontSize').val()),
                fontFamily: $('#fontFamily').val(),
                shadow: $('#textShadow').val() + 'px ' + $('#textShadow').val() + 'px 3px rgba(0,0,0,0.3)'
            });
            
            canvas.renderAll();
            saveState();
        }
        
        // Handle object selection
        function handleObjectSelected(e) {
            if (e.target.type === 'text') {
                activeText = e.target;
                isTextMode = true;
                $('#textToolbar').slideDown(300);
                
                // Update controls to match selected text
                $('#textColor').val(activeText.fill);
                $('#bgColor').val(activeText.backgroundColor || '#ffffff');
                $('#textOpacity').val(Math.round(activeText.opacity * 100));
                $('#fontSize').val(activeText.fontSize);
                $('#fontFamily').val(activeText.fontFamily);
                
                // Extract shadow size if exists
                if (activeText.shadow) {
                    const shadowSize = parseInt(activeText.shadow.match(/\d+/)[0]);
                    $('#textShadow').val(shadowSize);
                }
            } else {
                isTextMode = false;
                $('#textToolbar').slideUp(300);
            }
        }
        
        // Handle selection cleared
        function handleSelectionCleared() {
            activeText = null;
            isTextMode = false;
            $('#textToolbar').slideUp(300);
        }
        
        // Save canvas state
        function saveState() {
            // In a real implementation, this would save to a history stack
            // For simplicity, we'll just note that changes were made
            console.log("Canvas state updated");
        }
        
        // Demo text for preview
        const demoTexts = [
            "Add text to your images",
            "Drag to reposition",
            "Resize using corners",
            "Double-click to edit"
        ];
        
        let currentDemo = 0;
        
        function rotateDemoText() {
            $('.preview-text').text(demoTexts[currentDemo]);
            currentDemo = (currentDemo + 1) % demoTexts.length;
        }
        
        // Rotate demo text every 3 seconds
        setInterval(rotateDemoText, 3000);
    </script>
</body>
</html>