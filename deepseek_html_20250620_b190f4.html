<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أداة قص الصور المتطورة</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #1a2a6c, #b21f1f, #1a2a6c);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1200px;
            width: 100%;
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(to right, #1a2a6c, #b21f1f);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .app-content {
            display: flex;
            flex-direction: column;
            padding: 20px;
        }

        .image-section {
            position: relative;
            margin-bottom: 30px;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            padding: 20px;
            background-color: #f9f9f9;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .preview-text {
            text-align: center;
            padding: 40px;
            font-size: 1.2rem;
            color: #777;
        }

        .image-container {
            position: relative;
            width: 100%;
            height: 450px;
            border: 2px dashed #ddd;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 20px;
            background-color: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .image-container img {
            max-width: 100%;
            max-height: 100%;
            display: none;
        }

        .crop-controls-top {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 10;
            display: flex;
            gap: 10px;
        }

        .crop-btn-top {
            background: linear-gradient(to right, #1a2a6c, #b21f1f);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 50px;
            font-weight: bold;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .crop-btn-top:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
        }

        .crop-btn-top:active {
            transform: translateY(1px);
        }

        .file-input-container {
            text-align: center;
            margin: 20px 0;
        }

        .file-input-label {
            background: linear-gradient(to right, #1a2a6c, #b21f1f);
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        }

        .file-input-label:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
        }

        #imageLoader {
            display: none;
        }

        .crop-bar {
            background-color: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            border: 1px solid #e0e0e0;
            display: none;
        }

        .crop-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }

        .crop-header h2 {
            color: #1a2a6c;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        #currentRatio {
            font-weight: bold;
            color: #b21f1f;
        }

        .ratio-indicator {
            background-color: #1a2a6c;
            color: white;
            padding: 3px 10px;
            border-radius: 20px;
            font-size: 0.9rem;
        }

        .crop-scroll {
            display: flex;
            overflow-x: auto;
            gap: 15px;
            padding: 15px 5px;
            margin-bottom: 25px;
            scrollbar-width: thin;
        }

        .crop-scroll::-webkit-scrollbar {
            height: 8px;
        }

        .crop-scroll::-webkit-scrollbar-thumb {
            background: linear-gradient(to right, #1a2a6c, #b21f1f);
            border-radius: 4px;
        }

        .crop-scroll::-webkit-scrollbar-track {
            background: #f0f0f0;
            border-radius: 4px;
        }

        .crop-btn {
            flex: 0 0 auto;
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            padding: 15px;
            min-width: 120px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }

        .crop-btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
            border-color: #1a2a6c;
        }

        .crop-btn i {
            font-size: 1.8rem;
            color: #1a2a6c;
        }

        .crop-btn.active {
            background: linear-gradient(to bottom, #1a2a6c, #b21f1f);
            color: white;
            border-color: transparent;
        }

        .crop-btn.active i {
            color: white;
        }

        .rotate-section {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 25px;
        }

        .rotate-btn {
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            padding: 15px 25px;
            cursor: pointer;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }

        .rotate-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
            border-color: #1a2a6c;
            color: #1a2a6c;
        }

        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
        }

        .action-btn {
            background: linear-gradient(to right, #1a2a6c, #b21f1f);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-weight: bold;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        }

        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
        }

        .action-btn:active {
            transform: translateY(1px);
        }

        .action-btn:nth-child(2) {
            background: linear-gradient(to right, #6c757d, #495057);
        }

        .action-btn:nth-child(3) {
            background: linear-gradient(to right, #dc3545, #a71d2a);
        }

        .result-actions {
            display: none;
            justify-content: center;
            gap: 20px;
            margin-top: 20px;
        }

        .result-btn {
            background: linear-gradient(to right, #1a2a6c, #b21f1f);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 50px;
            font-weight: bold;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }

        .result-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .crop-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: none;
        }

        .crop-rect {
            position: absolute;
            border: 2px dashed white;
            box-shadow: 0 0 0 1000px rgba(0, 0, 0, 0.5);
            cursor: move;
            display: none;
        }

        .crop-handle {
            position: absolute;
            width: 12px;
            height: 12px;
            background-color: white;
            border: 2px solid #1a2a6c;
            border-radius: 50%;
        }

        .crop-handle.nw { top: -6px; left: -6px; cursor: nw-resize; }
        .crop-handle.ne { top: -6px; right: -6px; cursor: ne-resize; }
        .crop-handle.sw { bottom: -6px; left: -6px; cursor: sw-resize; }
        .crop-handle.se { bottom: -6px; right: -6px; cursor: se-resize; }

        .instructions {
            background-color: #eef7ff;
            border-left: 4px solid #1a2a6c;
            padding: 15px;
            border-radius: 0 8px 8px 0;
            margin: 25px 0;
        }

        .instructions h3 {
            color: #1a2a6c;
            margin-bottom: 10px;
        }

        .instructions ol {
            padding-right: 20px;
        }

        .instructions li {
            margin-bottom: 8px;
            line-height: 1.5;
        }

        @media (max-width: 768px) {
            .crop-scroll {
                gap: 10px;
            }
            
            .crop-btn {
                min-width: 100px;
                padding: 12px;
            }
            
            .rotate-section {
                flex-direction: column;
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .image-container {
                height: 350px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-crop-alt"></i> أداة قص الصور المتطورة</h1>
            <p>قص وتدوير الصور بسهولة مع مجموعة متنوعة من المقاسات</p>
        </div>
        
        <div class="app-content">
            <div class="image-section">
                <div class="preview-text" id="previewText">قم بتحميل صورة لبدء استخدام أداة القص</div>
                
                <div class="image-container">
                    <img id="imagePreview" alt="الصورة المحددة">
                    <img id="croppedResult" alt="الصورة بعد القص">
                    
                    <div class="crop-controls-top">
                        <button id="toggleCropBtn" class="crop-btn-top">
                            <i class="fas fa-crop-alt"></i> قص الصورة
                        </button>
                    </div>
                    
                    <div class="crop-overlay" id="cropOverlay"></div>
                    <div class="crop-rect" id="cropRect">
                        <div class="crop-handle nw"></div>
                        <div class="crop-handle ne"></div>
                        <div class="crop-handle sw"></div>
                        <div class="crop-handle se"></div>
                    </div>
                </div>
                
                <div class="file-input-container" id="fileInputContainer">
                    <label for="imageLoader" class="file-input-label">
                        <i class="fas fa-cloud-upload-alt"></i> تحميل صورة
                    </label>
                    <input type="file" id="imageLoader" accept="image/*">
                </div>
                
                <div class="result-actions" id="resultActions">
                    <button id="saveBtn" class="result-btn">
                        <i class="fas fa-download"></i> حفظ الصورة
                    </button>
                    <button id="newCropBtn" class="result-btn">
                        <i class="fas fa-crop-alt"></i> قص صورة جديدة
                    </button>
                </div>
            </div>
            
            <!--
            <div class="instructions">
                <h3><i class="fas fa-info-circle"></i> كيفية الاستخدام:</h3>
                <ol>
                    <li>قم بتحميل صورة باستخدام زر "تحميل صورة"</li>
                    <li>انقر على زر "قص الصورة" لبدء عملية القص</li>
                    <li>اختر مقاس القص المناسب من القائمة المنزلقة</li>
                    <li>استخدم أزرار التدوير إذا كنت بحاجة إلى تدوير النسبة أو الصورة</li>
                    <li>انقر على "تطبيق القص" عند الانتهاء</li>
                    <li>احفظ صورتك بعد القص باستخدام زر "حفظ الصورة"</li>
                </ol>
            </div>
            -->
            
            <div class="crop-bar" id="cropBar">
                <div class="crop-header">
                    <h2><i class="fas fa-ruler-combined"></i> نسب القص</h2>
                    <div id="currentRatio">النسبة الحالية: <span class="ratio-indicator">-</span></div>
                </div>
                
                <div class="crop-scroll">
                    <button class="crop-btn" data-ratio="custom">
                        <i class="fas fa-edit"></i>
                        مخصص
                        <span class="ratio-indicator">-</span>
                    </button>
                    <button class="crop-btn" data-ratio="1:1">
                        <i class="fas fa-square"></i>
                        مربع
                        <span class="ratio-indicator">1:1</span>
                    </button>
                    <button class="crop-btn" data-ratio="4:3">
                        <i class="fas fa-desktop"></i>
                        شاشة
                        <span class="ratio-indicator">4:3</span>
                    </button>
                    <button class="crop-btn" data-ratio="16:9">
                        <i class="fas fa-tv"></i>
                        عريض
                        <span class="ratio-indicator">16:9</span>
                    </button>
                    <button class="crop-btn" data-ratio="3:2">
                        <i class="fas fa-camera"></i>
                        كاميرا
                        <span class="ratio-indicator">3:2</span>
                    </button>
                    <button class="crop-btn" data-ratio="5:4">
                        <i class="fas fa-image"></i>
                        تقليدي
                        <span class="ratio-indicator">5:4</span>
                    </button>
                    <button class="crop-btn" data-ratio="9:16">
                        <i class="fas fa-mobile-alt"></i>
                        عمودي
                        <span class="ratio-indicator">9:16</span>
                    </button>
                    <button class="crop-btn" data-ratio="2:3">
                        <i class="fas fa-portrait"></i>
                        صورة شخصية
                        <span class="ratio-indicator">2:3</span>
                    </button>
                    <button class="crop-btn" data-ratio="1:2">
                        <i class="fas fa-banner"></i>
                        بانر
                        <span class="ratio-indicator">1:2</span>
                    </button>
                </div>
                
                <div class="rotate-section">
                    <button id="rotateRatioBtn" class="rotate-btn">
                        <i class="fas fa-sync-alt"></i> تدوير النسبة (أفقي/رأسي)
                    </button>
                    <button id="rotateImageBtn" class="rotate-btn">
                        <i class="fas fa-redo"></i> تدوير الصورة 90°
                    </button>
                </div>
                
                <div class="action-buttons">
                    <button id="applyCropBtn" class="action-btn">
                        <i class="fas fa-check"></i> تطبيق القص
                    </button>
                    <button id="backToCropBtn" class="action-btn">
                        <i class="fas fa-undo"></i> الرجوع للتعديل
                    </button>
                    <button id="resetBtn" class="action-btn">
                        <i class="fas fa-redo"></i> إعادة تعيين
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // عناصر DOM
            const imageLoader = document.getElementById('imageLoader');
            const imagePreview = document.getElementById('imagePreview');
            const croppedResult = document.getElementById('croppedResult');
            const previewText = document.getElementById('previewText');
            const fileInputContainer = document.getElementById('fileInputContainer');
            const resultActions = document.getElementById('resultActions');
            const cropBar = document.getElementById('cropBar');
            const toggleCropBtn = document.getElementById('toggleCropBtn');
            const cropBtns = document.querySelectorAll('.crop-btn');
            const currentRatioIndicator = document.querySelector('#currentRatio .ratio-indicator');
            const applyCropBtn = document.getElementById('applyCropBtn');
            const backToCropBtn = document.getElementById('backToCropBtn');
            const resetBtn = document.getElementById('resetBtn');
            const saveBtn = document.getElementById('saveBtn');
            const newCropBtn = document.getElementById('newCropBtn');
            const rotateRatioBtn = document.getElementById('rotateRatioBtn');
            const rotateImageBtn = document.getElementById('rotateImageBtn');
            const cropOverlay = document.getElementById('cropOverlay');
            const cropRect = document.getElementById('cropRect');
            
            // متغيرات الحالة
            let currentImage = null;
            let currentRatio = null;
            let rotationAngle = 0;
            
            // تحميل الصورة
            imageLoader.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(event) {
                        imagePreview.src = event.target.result;
                        imagePreview.style.display = 'block';
                        previewText.style.display = 'none';
                        fileInputContainer.style.display = 'none';
                        toggleCropBtn.style.display = 'block';
                        
                        // إخفاء نتيجة القص السابقة
                        croppedResult.style.display = 'none';
                        resultActions.style.display = 'none';
                        
                        currentImage = imagePreview;
                    };
                    reader.readAsDataURL(file);
                }
            });
            
            // تبديل شريط القص
            toggleCropBtn.addEventListener('click', function() {
                if (currentImage) {
                    cropBar.style.display = cropBar.style.display === 'block' ? 'none' : 'block';
                    
                    if (cropBar.style.display === 'block') {
                        // إظهار أدوات القص
                        cropOverlay.style.display = 'block';
                        cropRect.style.display = 'block';
                        
                        // تعيين حجم مستطيل القص الافتراضي
                        updateCropRect(100, 100, 200, 200);
                    } else {
                        // إخفاء أدوات القص
                        cropOverlay.style.display = 'none';
                        cropRect.style.display = 'none';
                    }
                }
            });
            
            // اختيار نسبة القص
            cropBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // إزالة النشاط من جميع الأزرار
                    cropBtns.forEach(b => b.classList.remove('active'));
                    
                    // إضافة النشاط للزر المحدد
                    this.classList.add('active');
                    
                    // تعيين النسبة الحالية
                    currentRatio = this.getAttribute('data-ratio');
                    currentRatioIndicator.textContent = currentRatio;
                    
                    // تطبيق نسبة القص على مستطيل القص
                    applyCropRatio(currentRatio);
                });
            });
            
            // تدوير النسبة
            rotateRatioBtn.addEventListener('click', function() {
                if (currentRatio && currentRatio !== 'custom') {
                    const parts = currentRatio.split(':');
                    if (parts.length === 2) {
                        currentRatio = parts[1] + ':' + parts[0];
                        currentRatioIndicator.textContent = currentRatio;
                        applyCropRatio(currentRatio);
                        
                        // تحديث الزر النشط
                        cropBtns.forEach(btn => {
                            if (btn.getAttribute('data-ratio') === currentRatio) {
                                cropBtns.forEach(b => b.classList.remove('active'));
                                btn.classList.add('active');
                            }
                        });
                    }
                }
            });
            
            // تدوير الصورة
            rotateImageBtn.addEventListener('click', function() {
                rotationAngle = (rotationAngle + 90) % 360;
                currentImage.style.transform = `rotate(${rotationAngle}deg)`;
            });
            
            // تطبيق القص
            applyCropBtn.addEventListener('click', function() {
                if (currentImage) {
                    // محاكاة عملية القص (في تطبيق حقيقي سيتم تنفيذ القص الفعلي)
                    cropOverlay.style.display = 'none';
                    cropRect.style.display = 'none';
                    cropBar.style.display = 'none';
                    
                    // إظهار نتيجة القص
                    croppedResult.src = currentImage.src;
                    croppedResult.style.display = 'block';
                    currentImage.style.display = 'none';
                    
                    // إظهار أزرار النتيجة
                    resultActions.style.display = 'flex';
                }
            });
            
            // الرجوع للتعديل
            backToCropBtn.addEventListener('click', function() {
                croppedResult.style.display = 'none';
                resultActions.style.display = 'none';
                
                if (currentImage) {
                    currentImage.style.display = 'block';
                    cropBar.style.display = 'block';
                    cropOverlay.style.display = 'block';
                    cropRect.style.display = 'block';
                }
            });
            
            // إعادة تعيين
            resetBtn.addEventListener('click', function() {
                // إعادة تعيين كل شيء
                imagePreview.style.display = 'none';
                croppedResult.style.display = 'none';
                previewText.style.display = 'block';
                fileInputContainer.style.display = 'block';
                resultActions.style.display = 'none';
                cropBar.style.display = 'none';
                toggleCropBtn.style.display = 'none';
                cropOverlay.style.display = 'none';
                cropRect.style.display = 'none';
                
                // إعادة تعيين الحالات
                currentImage = null;
                currentRatio = null;
                rotationAngle = 0;
                imagePreview.style.transform = 'rotate(0deg)';
                
                // إلغاء تحديد أي نسبة قص
                cropBtns.forEach(btn => btn.classList.remove('active'));
                currentRatioIndicator.textContent = '-';
                
                // إعادة تعيين مدخل الملف
                imageLoader.value = '';
            });
            
            // حفظ الصورة (محاكاة)
            saveBtn.addEventListener('click', function() {
                alert('تم حفظ الصورة بنجاح! في تطبيق حقيقي، سيتم تنزيل الصورة.');
            });
            
            // قص صورة جديدة
            newCropBtn.addEventListener('click', function() {
                resetBtn.click();
            });
            
            // وظيفة مساعدة: تطبيق نسبة القص
            function applyCropRatio(ratio) {
                if (ratio === 'custom') {
                    // لا شيء خاص للنسبة المخصصة
                    return;
                }
                
                // تحليل النسبة
                const [ratioX, ratioY] = ratio.split(':').map(Number);
                const aspectRatio = ratioX / ratioY;
                
                // محاكاة تغيير حجم مستطيل القص بناءً على النسبة
                const containerWidth = imagePreview.offsetWidth;
                const containerHeight = imagePreview.offsetHeight;
                
                let cropWidth, cropHeight;
                
                if (containerWidth / containerHeight > aspectRatio) {
                    cropHeight = containerHeight * 0.7;
                    cropWidth = cropHeight * aspectRatio;
                } else {
                    cropWidth = containerWidth * 0.7;
                    cropHeight = cropWidth / aspectRatio;
                }
                
                const cropX = (containerWidth - cropWidth) / 2;
                const cropY = (containerHeight - cropHeight) / 2;
                
                updateCropRect(cropX, cropY, cropWidth, cropHeight);
            }
            
            // وظيفة مساعدة: تحديث مستطيل القص
            function updateCropRect(x, y, width, height) {
                cropRect.style.left = `${x}px`;
                cropRect.style.top = `${y}px`;
                cropRect.style.width = `${width}px`;
                cropRect.style.height = `${height}px`;
            }
            
            // محاكاة سحب مستطيل القص
            let isDragging = false;
            let dragStartX, dragStartY, rectStartX, rectStartY;
            
            cropRect.addEventListener('mousedown', function(e) {
                isDragging = true;
                dragStartX = e.clientX;
                dragStartY = e.clientY;
                rectStartX = parseInt(cropRect.style.left) || 0;
                rectStartY = parseInt(cropRect.style.top) || 0;
                
                e.preventDefault();
            });
            
            document.addEventListener('mousemove', function(e) {
                if (isDragging) {
                    const deltaX = e.clientX - dragStartX;
                    const deltaY = e.clientY - dragStartY;
                    
                    cropRect.style.left = `${rectStartX + deltaX}px`;
                    cropRect.style.top = `${rectStartY + deltaY}px`;
                }
            });
            
            document.addEventListener('mouseup', function() {
                isDragging = false;
            });
        });
    </script>
</body>
</html>