<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محاكاة الأكواب السحرية الحرارية</title>
    <link rel="stylesheet" href="style.css">
    <!-- تحميل المكتبات من CDN -->
    <script src="https://cdn.jsdelivr.net/npm/three@0.132.2/build/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.132.2/examples/js/loaders/GLTFLoader.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/fabric@5.2.1/dist/fabric.min.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>محاكاة الأكواب السحرية الحرارية</h1>
        </header>
        
        <main>
            <div class="workspace">
                <!-- منطقة تصميم Fabric.js -->
                <div class="design-area">
                    <h2>منطقة التصميم</h2>
                    <div class="canvas-container">
                        <canvas id="fabric-canvas"></canvas>
                    </div>
                    <div class="design-controls">
                        <button id="add-text">إضافة نص</button>
                        <button id="add-image">إضافة صورة</button>
                        <button id="clear-canvas">مسح القماش</button>
                    </div>
                </div>
                
                <!-- منطقة عرض الكوب ثلاثي الأبعاد -->
                <div class="preview-area">
                    <h2>معاينة الكوب</h2>
                    <div class="three-container">
                        <canvas id="three-canvas"></canvas>
                    </div>
                    
                    <!-- أزرار التحكم بالتأثير الحراري -->
                    <div class="effect-controls">
                        <div class="effect-selector">
                            <label for="effect-type">نوع التأثير:</label>
                            <select id="effect-type">
                                <option value="shader">قناع الشيدر</option>
                                <option value="overlay">التراكب الأسود المتلاشي</option>
                                <option value="gradient">نسيج التدرج الشفاف</option>
                            </select>
                        </div>
                        
                        <div class="animation-controls">
                            <button id="play-btn">تشغيل</button>
                            <button id="pause-btn">إيقاف مؤقت</button>
                            <button id="stop-btn">إيقاف</button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        
        <footer>
            <p>مشروع محاكاة الأكواب السحرية الحرارية باستخدام Three.js و Fabric.js</p>
        </footer>
    </div>
    
    <!-- تحميل ملف JavaScript الرئيسي -->
    <script src="main.js"></script>
</body>
</html>
