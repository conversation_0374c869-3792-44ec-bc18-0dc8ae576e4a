<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }

        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }

        button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        button:hover {
            background-color: #0056b3;
        }

        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }

        #uploadBtn {
            background-color: #28a745;
        }

        #uploadBtn:hover {
            background-color: #1e7e34;
        }

        #startCrop {
            background-color: #ffc107;
            color: #212529;
        }

        #startCrop:hover {
            background-color: #e0a800;
        }

        #crop {
            background-color: #dc3545;
        }

        #crop:hover {
            background-color: #c82333;
        }

        .file-input-wrapper {
            position: relative;
            overflow: hidden;
            display: inline-block;
        }

        .file-input-wrapper input[type=file] {
            position: absolute;
            left: -9999px;
        }

        .canvas-container {
            text-align: center;
            margin-top: 20px;
        }

        #canvas {
            border: 2px solid #dee2e6;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .instructions {
            margin-top: 15px;
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 4px;
            font-size: 14px;
            color: #495057;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>Canvas Crop Tools Editor</h1>

        <div class="controls">
            <div class="file-input-wrapper">
                <button id="uploadBtn">Upload Photo</button>
                <input type="file" id="fileInput" accept="image/*">
            </div>
            <button id="startCrop">Start Crop</button>
            <button id="crop" style="display: none;">Crop Image</button>
        </div>

        <div class="canvas-container">
            <canvas id="canvas"></canvas>
        </div>

        <div class="instructions">
            <strong>Instructions:</strong>
            1. Click "Upload Photo" to select an image from your device
            2. Click "Start Crop" to begin cropping mode
            3. Resize and position the crop area as needed
            4. Click "Crop Image" to apply the crop
        </div>
    </div>

    <canvas style="display: none;" id="canvas_crop"></canvas>
</body>

<script>
    var canvas = new fabric.Canvas('canvas', {
        width: 600,
        height: 400,
        strokeWidth: 5,
        stroke: 'rgba(100,200,200,0.5)',
        backgroundColor: '#ffffff'
    });

    var el;
    var object, lastActive;
    var selection_object_left = 0;
    var selection_object_top = 0;
    var isCropping = false;
    var currentImage = null;

    // Wait for both jQuery and Fabric.js to load
    $(document).ready(function() {
        // Check if fabric is loaded
        if (typeof fabric === 'undefined') {
            alert('Fabric.js library failed to load. Please refresh the page.');
            return;
        }

        // Initialize canvas
        canvas.selection = false;
        canvas.renderAll();

        // Set up file input change handler
        $('#fileInput').on('change', handleFileSelect);

        // Set up upload button click handler
        $('#uploadBtn').on('click', function() {
            $('#fileInput').click();
        });
    });

    function handleFileSelect(event) {
        const file = event.target.files[0];
        if (file && file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = function(e) {
                loadImageToCanvas(e.target.result);
            };
            reader.readAsDataURL(file);
        } else {
            alert('Please select a valid image file.');
        }
    }

    function loadImageToCanvas(imageSrc) {
        // Clear existing objects
        canvas.clear();

        fabric.Image.fromURL(imageSrc, function(img) {
            // Scale image to fit canvas while maintaining aspect ratio
            const canvasWidth = canvas.getWidth();
            const canvasHeight = canvas.getHeight();
            const imgWidth = img.width;
            const imgHeight = img.height;

            const scaleX = canvasWidth / imgWidth;
            const scaleY = canvasHeight / imgHeight;
            const scale = Math.min(scaleX, scaleY, 1); // Don't upscale

            img.set({
                scaleX: scale,
                scaleY: scale,
                selectable: true,
                left: getObjLeft(imgWidth * scale),
                top: getObjTop(imgHeight * scale)
            });

            canvas.add(img);
            canvas.selection = false;
            canvas.renderAll();

            currentImage = img;
        });
    }

    $('#crop').on('click', function(event) {
        $('#crop').hide();
        if (!isCropping) {
            alert("Please select the cropping area first.");
            return;
        }

        var left = el.left - object.left;
        var top = el.top - object.top;

        left *= 1;
        top *= 1;

        var width = el.width * 1;
        var height = el.height * 1;

        // Export the current frame to a new canvas and perform the crop
        cropImage(object, el.left, el.top, parseInt(el.scaleY * height), parseInt(width * el.scaleX));

        canvas.remove(object);
        canvas.remove(canvas.getActiveObject());
        lastActive = object;
        canvas.renderAll();

        isCropping = false;
    });

    $('#startCrop').on('click', function() {
        if (!currentImage) {
            alert("Please upload an image first.");
            return;
        }

        $('#crop').show();
        canvas.remove(el);

        if (canvas.getActiveObject()) {
            if (canvas.getActiveObject().type === 'sprite') {
                alert("Selected object cannot be cropped.");
                return;
            }

            object = canvas.getActiveObject();
            if (lastActive !== object) {
                console.log('different object');
            } else {
                console.log('same object');
            }

            if (lastActive && lastActive !== object) {
                lastActive.clipTo = null;
            }

            // Generate a rectangle of the same size as the element to be cropped
            el = new fabric.Rect({
                fill: 'rgba(0,0,0,0)',
                originX: 'left',
                originY: 'top',
                stroke: '#36fd00',
                strokeWidth: 3,
                strokeDashArray: [5, 5],
                width: 1,
                height: 1,
                borderColor: '#36fd00',
                cornerColor: '#36fd00',
                cornerSize: 12,
                hasRotatingPoint: false,
                selectable: true,
                transparentCorners: false
            });

            const activeObj = canvas.getActiveObject();
            el.left = activeObj.left;
            selection_object_left = activeObj.left;
            selection_object_top = activeObj.top;
            el.top = activeObj.top;
            el.width = activeObj.width * activeObj.scaleX;
            el.height = activeObj.height * activeObj.scaleY;

            canvas.add(el);
            canvas.setActiveObject(el);

            // Make other objects non-selectable during cropping
            canvas.forEachObject(function(obj) {
                if (obj !== el) {
                    obj.selectable = false;
                }
            });
        } else {
            alert("Please select an image element first.");
        }
        isCropping = true;
    });

    function cropImage(png, left, top, height, width) {
        // Adjust crop area if it extends beyond the original image
        if (top < png.top) {
            height = height - (png.top - top);
            top = png.top;
        }
        if (left < png.left) {
            width = width - (png.left - left);
            left = png.left;
        }
        if (top + height > png.top + png.height * png.scaleY)
            height = png.top + png.height * png.scaleY - top;
        if (left + width > png.left + png.width * png.scaleX)
            width = png.left + png.width * png.scaleX - left;

        var canvas_crop = new fabric.Canvas("canvas_crop");

        fabric.Image.fromURL(canvas.toDataURL('png'), function(img) {
            img.set('left', -left);
            img.set('top', -top);
            canvas_crop.add(img);
            canvas_crop.setHeight(height);
            canvas_crop.setWidth(width);
            canvas_crop.renderAll();
            fabric.Image.fromURL(canvas_crop.toDataURL('png'), function(croppedImg) {
                croppedImg.set('left', left);
                croppedImg.set('top', top);
                canvas.add(croppedImg);
                canvas.renderAll();

                // Re-enable selection for all objects
                canvas.forEachObject(function(obj) {
                    obj.selectable = true;
                });

                currentImage = croppedImg;
            });
        });
    }

    function getObjLeft(objWidth) {
        return canvas.getWidth() / 2 - objWidth / 2;
    }

    function getObjTop(objHeight) {
        return canvas.getHeight() / 2 - objHeight / 2;
    }
</script>

</html>
