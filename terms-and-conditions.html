<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>شروط الاستخدام وسياسة الخصوصية - 4dads.pro</title>
    <style>
        :root {
            --primary-color: #182949;
            --secondary-color: #d3a77b;
            --accent-color: #e74c3c;
            --light-color: #d3a77b;
            --dark-color: #182949;
            --border-color: #d3a77b;
            --shadow: 0 4px 6px rgba(211, 167, 123, 0.55);
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.7;
            color: var(--dark-color);
            background-color: #182949;
            direction: rtl;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header Styles */
        header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: #182949;
            padding: 2rem 0;
            text-align: center;
            box-shadow: var(--shadow);
            position: relative;
            overflow: hidden;
        }

        header::before {
            content: "";
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
            z-index: 0;
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .logo {
            width: 80px;
            height: 80px;
            background-color: #d3a77b;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            box-shadow: 0 4px 8px #d3a77b;
        }

        .logo span {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        header h1 {
            font-size: 2.2rem;
            margin-bottom: 0.5rem;
        }

        header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        /* Navigation */
        .nav-tabs {
            display: flex;
            background-color: #d3a77b;
            box-shadow: var(--shadow);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .tab {
            flex: 1;
            text-align: center;
            padding: 15px 0;
            cursor: pointer;
            font-weight: 600;
            transition: var(--transition);
            border-bottom: 3px solid transparent;
        }

        .tab.active {
            border-bottom: 3px solid var(--primary-color);
            color: var(--primary-color);
        }

        .tab:hover:not(.active) {
            background-color: #d3a77b;
        }

        /* Content Sections */
        .content-section {
            display: none;
            padding: 40px 0;
        }

        .content-section.active {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .section-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .section-header h2 {
            font-size: 1.8rem;
            color: var(--secondary-color);
            margin-bottom: 15px;
            position: relative;
            display: inline-block;
        }

        .section-header h2::after {
            content: "";
            position: absolute;
            bottom: -10px;
            right: 50%;
            transform: translateX(50%);
            width: 80px;
            height: 3px;
            background: var(--primary-color);
        }

        .section-header p {
            color: #666;
            max-width: 700px;
            margin: 20px auto 0;
        }

        /* Card Styles */
        .card {
            background: #d3a77b;
            border-radius: 10px;
            box-shadow: var(--shadow);
            padding: 25px;
            margin-bottom: 30px;
            transition: var(--transition);
            border-left: 4px solid var(--primary-color);
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgb(211, 167, 123);
        }

        .card h3 {
            color: var(--secondary-color);
            margin-bottom: 15px;
            font-size: 1.4rem;
            display: flex;
            align-items: center;
        }

        .card h3 i {
            margin-left: 10px;
            color: var(--primary-color);
        }

        .card ul {
            padding-right: 20px;
            margin: 15px 0;
        }

        .card ul li {
            margin-bottom: 10px;
            position: relative;
            padding-right: 20px;
        }

        .card ul li::before {
            content: "•";
            color: var(--primary-color);
            font-weight: bold;
            position: absolute;
            right: 0;
        }

        /* Highlight Box */
        .highlight-box {
            background:  #d3a77b;
            border-left: 4px solid var(--primary-color);
            padding: 20px;
            border-radius: 5px;
            margin: 30px 0;
        }

        /* Contact Box */
        .contact-box {
            background: var(--secondary-color);
            color: #182949;
            border-radius: 10px;
            padding: 25px;
            text-align: center;
            margin-top: 40px;
        }

        .contact-box h3 {
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .contact-box a {
            color: var(--pr#imary-color);
            background: #687ea7;
            text-decoration: #d3a77b;
            padding: 12px 30px;
            border-radius: 30px;
            font-weight: 600;
            display: inline-block;
            transition: var(--transition);
            margin-top: 15px;
        }

        .contact-box a:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        /* Footer */
        footer {
            background: var(--secondary-color);
            color: #182949;
            text-align: center;
            padding: 30px 0;
            margin-top: 50px;
        }

        .footer-content {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .copyright {
            margin-top: 20px;
            font-size: 0.9rem;
            opacity: 0.8;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .nav-tabs {
                flex-direction: column;
            }

            .section-header h2 {
                font-size: 1.5rem;
            }

            .card {
                padding: 20px;
            }

            header h1 {
                font-size: 1.8rem;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 0 15px;
            }

            header {
                padding: 1.5rem 0;
            }

            header h1 {
                font-size: 1.5rem;
            }

            .section-header h2 {
                font-size: 1.3rem;
            }

            .card h3 {
                font-size: 1.2rem;
            }
        }

        /* Print Styles */
        @media print {
            .nav-tabs, .contact-box, footer {
                display: none;
            }

            body {
                background: #677eaa;
            }

            .container {
                max-width: 100%;
                padding: 0;
            }

            .card {
                box-shadow: none;
                border-left: none;
                border: 1px solid #ddd;
                page-break-inside: avoid;
            }

            header {
                background: #d3a77b;
                color: var(--dark-color);
                padding: 1rem 0;
            }

            .logo {
                background: #eee;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
               <div class="logo-container">
                <!-- استبدل هذا بعنصر الصورة -->
                <img src="logo.png" alt="4dads.pro Logo" class="logo-img" width="80" height="80">
            </div>
                <h1>شروط الاستخدام وسياسة الخصوصية</h1>
                <p>للمنصة: 4dads.pro | تاريخ التحديث: 24 يونيو 2025</p>
            </div>
        </div>
    </header>

    <div class="nav-tabs">
        <div class="tab active" data-tab="terms">شروط الاستخدام</div>
        <div class="tab" data-tab="privacy">سياسة الخصوصية</div>
        <div class="tab" data-tab="subscriptions">الاشتراكات والدفع</div>
    </div>

    <div class="container">
        <!-- شروط الاستخدام -->
        <section id="terms" class="content-section active">
            <div class="section-header">
                <h2>شروط استخدام منصة 4dads.pro</h2>
                <p>يُشكّل استخدامك للتطبيق الويب الموافقَة على هذه الشروط والأحكام. يُرجى قراءتها بعناية قبل استخدام الخدمة.</p>
            </div>

            <div class="card">
                <h3><i class="fas fa-book"></i> مقدمة</h3>
                <p>تُقدّم منصة <strong>4dads.pro</strong> خدماتها عبر اشتراك شهري/سنوي، وتُعدّ أداةً لتصميم المنتجات وإنشاء نماذج ثلاثية الأبعاد قابلة للتخصيص. يُشكّل استخدامك للمنصة الموافقة على هذه الشروط والأحكام.</p>
            </div>

            <div class="card">
                <h3><i class="fas fa-laptop"></i> وصف الخدمة</h3>
                <ul>
                    <li><strong>نوع الخدمة</strong>: برنامج كخدمة (SaaS)</li>
                    <li><strong>الوصول</strong>: عبر المتصفح فقط على الرابط <a href="https://4dads.pro/">https://4dads.pro/</a>، بدون حاجة لتنزيل برامج إضافية</li>
                    <li><strong>التوفر</strong>:
                        <ul>
                            <li>تطبيق ويب تقدمي (PWA) قابِل للتثبيت على الهواتف وWindows وجميع أنظمة التشغيل</li>
                            <li>قريبًا على متجر Google Play وApple App Store</li>
                        </ul>
                    </li>
                    <li><strong>الميزات</strong>:
                        <ul>
                            <li>تصميم مرئيات ثلاثية الأبعاد وتصديرها كصور/فيديو عالي الجودة</li>
                            <li>إدارة الاشتراكات وتجربة لوحة التحكم</li>
                        </ul>
                    </li>
                </ul>
            </div>

            <div class="card">
                <h3><i class="fas fa-user"></i> حقوق المستخدم والملكية الفكرية</h3>
                <ul>
                    <li>يَحمِل المستخدم كاملَ حقوق مخرجات التصاميم التي ينشئها</li>
                    <li>جميع حقوق الملكية الفكرية للمنصة (التقنية، الواجهات، الشعارات) محفوظة لمالكي <strong>4dads.pro</strong></li>
                    <li>يُمنع نسخ أو عكس هندسة أي جزء من الخدمة</li>
                    <li>يُمنع استخدام الخدمة لأي أغراض غير قانونية أو تنتهك حقوق الآخرين</li>
                </ul>
            </div>

            <div class="card">
                <h3><i class="fas fa-shield-alt"></i> المسؤوليات والقبول</h3>
                <ul>
                    <li>أنت مسؤول عن:
                        <ul>
                            <li>سرية بيانات حسابك وكلمة المرور</li>
                            <li>استخدام الخدمة لأغراض قانونية فقط</li>
                            <li>عدم إساءة استخدام الخدمة (اختراق، إرسال برمجيات ضارة، إلخ)</li>
                        </ul>
                    </li>
                    <li>يحق للمنصة تعليق الحساب عند المخالفة دون سابق إنذار</li>
                    <li>يجب أن تكون بالغًا (18 عامًا فأكثر) لاستخدام الخدمة</li>
                </ul>
            </div>

            <div class="highlight-box">
                <h3>ملاحظة هامة:</h3>
                <p>الخدمة تُقدَّم "كما هي"، دون ضمانات صريحة أو ضمنية. لا تتحمل المنصة مسؤولية أي خسائر غير مباشرة ناتجة عن استخدام الخدمة أو أعطال فنية خارجة عن سيطرتها.</p>
            </div>
        </section>

        <!-- سياسة الخصوصية -->
        <section id="privacy" class="content-section">
            <div class="section-header">
                <h2>سياسة الخصوصية</h2>
                <p>نحن في 4dads.pro نُقدّر خصوصيتك ونلتزم بحماية معلوماتك الشخصية. توضّح هذه السياسة كيفية جمعنا واستخدامنا لمعلوماتك.</p>
            </div>

            <div class="card">
                <h3><i class="fas fa-database"></i> المعلومات التي نجمعها</h3>
                <ul>
                    <li><strong>معلومات التسجيل</strong>: الاسم، البريد الإلكتروني، كلمة المرور عند إنشاء الحساب</li>
                    <li><strong>معلومات الدفع</strong>: تفاصيل بطاقة الائتمان (تتم معالجتها عبر بوابات دفع آمنة)</li>
                    <li><strong>معلومات الاستخدام</strong>: سجلات الوصول، الأجهزة المستخدمة، نمط الاستخدام</li>
                    <li><strong>المحتوى الذي تنشئه</strong>: التصاميم والمشاريع التي تنشئها على المنصة</li>
                </ul>
            </div>

            <div class="card">
                <h3><i class="fas fa-cog"></i> كيفية استخدام المعلومات</h3>
                <ul>
                    <li>تقديم الخدمات المطلوبة وتشغيل المنصة</li>
                    <li>معالجة المدفوعات وتجديد الاشتراكات</li>
                    <li>تحسين تجربة المستخدم وتطوير الخدمات الجديدة</li>
                    <li>إرسال تحديثات وإشعارات مهمة حول الخدمة</li>
                    <li>الرد على استفسارات الدعم الفني</li>
                </ul>
            </div>

            <div class="card">
                <h3><i class="fas fa-lock"></i> حماية المعلومات</h3>
                <ul>
                    <li>نستخدم تشفير SSL/TLS لحماية البيانات المنقولة</li>
                    <li>نخزن البيانات على خوادم آمنة مع تدابير حماية متقدمة</li>
                    <li>نلتزم بمعايير أمان صناعة الدفع (PCI DSS) لبيانات الدفع</li>
                    <li>نقوم بمراجعة أنظمتنا الأمنية وتحديثها بانتظام</li>
                </ul>
            </div>

            <div class="card">
                <h3><i class="fas fa-share-alt"></i> مشاركة المعلومات</h3>
                <p>لا نبيع أو نؤجر معلوماتك الشخصية لأطراف ثالثة. قد نشارك معلومات محدودة مع:</p>
                <ul>
                    <li>مزودي خدمات الدفع لمعالجة المعاملات</li>
                    <li>مقدمي خدمات التحليلات لفهم استخدام المنصة</li>
                    <li>الجهات القانونية عند وجود طلب قانوني صالح</li>
                </ul>
            </div>

            <div class="highlight-box">
                <h3>حقوقك في خصوصيتك:</h3>
                <p>لديك الحق في الوصول إلى معلوماتك الشخصية وتحديثها أو حذفها. يمكنك إدارة تفضيلات المراسلة في إعدادات حسابك. للاستفسارات حول الخصوصية، راسلنا على <a href="mailto:<EMAIL>"><EMAIL></a></p>
            </div>
        </section>

        <!-- الاشتراكات والدفع -->
        <section id="subscriptions" class="content-section">
            <div class="section-header">
                <h2>سياسة الاشتراكات والدفع</h2>
                <p>تفصيل سياسات الاشتراك الشهري والسنوي، التجديد، الإلغاء، والاسترداد.</p>
            </div>

            <div class="card">
                <h3><i class="fas fa-credit-card"></i> خطط الاشتراك</h3>
                <ul>
                    <li><strong>الاشتراك الشهري</strong>: يتم تجديده تلقائيًا كل 30 يومًا</li>
                    <li><strong>الاشتراك السنوي</strong>: يتم تجديده تلقائيًا كل 365 يومًا</li>
                    <li>يُعتبر الدفع موافقةً على الأسعار المعلنة وقت الشراء</li>
                    <li>يحق للمنصة تعديل الأسعار مع إشعار مُسبَق قبل 30 يومًا</li>
                </ul>
            </div>

            <div class="card">
                <h3><i class="fas fa-sync-alt"></i> التجديد والإلغاء</h3>
                <ul>
                    <li>يتم التجديد التلقائي في نهاية كل فترة اشتراك</li>
                    <li>يحق لك إلغاء الاشتراك في أي وقت عبر لوحة التحكم</li>
                    <li>الإلغاء يوقف التجديد التالي ولا يسترد المبالغ المدفوعة مسبقًا</li>
                    <li>يحق للمنصة إنهاء الاشتراك للمخالفين دون سابق إنذار</li>
                </ul>
            </div>

            <div class="card">
                <h3><i class="fas fa-undo"></i> سياسة الاسترداد</h3>
                <ul>
                    <li>نحن نقدم ضمان استرداد الأموال خلال 14 يومًا من الاشتراك الأول</li>
                    <li>للاستفادة من الضمان، يجب ألا يتجاوز استخدام الخدمة 10% من الميزات</li>
                    <li>لا تنطبق سياسة الاسترداد على التجديدات التلقائية</li>
                    <li>لطلب استرداد، راسلنا على <a href="mailto:<EMAIL>"><EMAIL></a></li>
                </ul>
            </div>

            <div class="card">
                <h3><i class="fas fa-exclamation-triangle"></i> حالات التعليق والإيقاف</h3>
                <ul>
                    <li>قد نعلق الحساب مؤقتًا في حالة عدم سداد الاشتراك</li>
                    <li>بعد 7 أيام من التعليق، يتم إيقاف الحساب نهائيًا</li>
                    <li>يتم حذف التصاميم المخزنة بعد 30 يومًا من الإيقاف</li>
                    <li>يمكن استعادة الحساب خلال 30 يومًا من الإيقاف بسداد المستحقات</li>
                </ul>
            </div>

            <div class="highlight-box">
                <h3>معلومات الدفع الآمن:</h3>
                <p>جميع معاملات الدفع تتم عبر بوابات دفع معتمدة ومشفرة. لا نخزن تفاصيل بطاقات الائتمان على خوادمنا. نستخدم معالجات دفع خارجية تتوافق مع معايير أمان صناعة بطاقات الدفع (PCI DSS).</p>
            </div>
        </section>

        <!-- Contact Box -->
        <div class="contact-box">
            <h3>هل لديك استفسارات أو تحتاج إلى دعم؟</h3>
            <p>فريق الدعم لدينا جاهز للإجابة على جميع أسئلتك</p>
            <a href="mailto:<EMAIL>">تواصل معنا عبر البريد الإلكتروني</a>
        </div>
    </div>

    <footer>
        <div class="container">
            <div class="footer-content">
                <h3>4dads.pro</h3>
                <p>منصة متكاملة لتصميم المنتجات وإنشاء نماذج ثلاثية الأبعاد قابلة للتخصيص</p>
                <div class="copyright">
                    © 2025 4dads.pro — جميع الحقوق محفوظة. تاريخ التحديث: 24 يونيو 2025
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Tab switching functionality
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', () => {
                // Remove active class from all tabs
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                // Add active class to clicked tab
                tab.classList.add('active');

                // Hide all content sections
                document.querySelectorAll('.content-section').forEach(section => {
                    section.classList.remove('active');
                });

                // Show the selected content section
                const tabId = tab.getAttribute('data-tab');
                document.getElementById(tabId).classList.add('active');
            });
        });

        // Print functionality
        document.addEventListener('keydown', (e) => {
            if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
        });

        // Simulating icons (since we're not using an icon library)
        document.querySelectorAll('h3 i').forEach(icon => {
            const iconName = icon.className.split(' ')[1];
            let iconText = '';

            switch(iconName) {
                case 'fa-book': iconText = '📘'; break;
                case 'fa-laptop': iconText = '💻'; break;
                case 'fa-user': iconText = '👤'; break;
                case 'fa-shield-alt': iconText = '🛡️'; break;
                case 'fa-database': iconText = '💾'; break;
                case 'fa-cog': iconText = '⚙️'; break;
                case 'fa-lock': iconText = '🔒'; break;
                case 'fa-share-alt': iconText = '🔗'; break;
                case 'fa-credit-card': iconText = '💳'; break;
                case 'fa-sync-alt': iconText = '🔄'; break;
                case 'fa-undo': iconText = '↩️'; break;
                case 'fa-exclamation-triangle': iconText = '⚠️'; break;
                default: iconText = '•';
            }

            icon.textContent = iconText;
        });
    </script>
</body>
</html>
