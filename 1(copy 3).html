<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أداة الرسم على الصور</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }
        header {
            background: linear-gradient(90deg, #4b6cb7 0%, #182848 100%);
            color: white;
            padding: 25px 30px;
            text-align: center;
        }
        h1 {
            font-size: 2.2rem;
            margin-bottom: 10px;
        }
        .subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            max-width: 700px;
            margin: 0 auto;
        }
        .main-content {
            display: flex;
            flex-wrap: wrap;
            padding: 20px;
        }
        .tools-panel {
            flex: 1;
            min-width: 300px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 12px;
            margin-right: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        }
        .canvas-container {
            flex: 2;
            min-width: 400px;
            position: relative;
            background: #e9ecef;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
        }
        #canvas {
            border: 1px solid #dee2e6;
            background: white;
            cursor: crosshair;
        }
        .tool-section {
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid #dee2e6;
        }
        .tool-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        .tool-section h2 {
            font-size: 1.3rem;
            margin-bottom: 15px;
            color: #2c3e50;
            display: flex;
            align-items: center;
        }
        .tool-section h2 i {
            margin-right: 10px;
            color: #4b6cb7;
        }
        .controls-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }
        .control-group {
            display: flex;
            flex-direction: column;
        }
        label {
            font-size: 0.9rem;
            font-weight: 500;
            margin-bottom: 6px;
            color: #495057;
        }
        input[type="color"] {
            width: 100%;
            height: 40px;
            border: 1px solid #ced4da;
            border-radius: 8px;
            cursor: pointer;
            padding: 3px;
        }
        input[type="range"] {
            width: 100%;
            height: 6px;
            border-radius: 5px;
            background: #dee2e6;
            outline: none;
            -webkit-appearance: none;
        }
        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: #4b6cb7;
            cursor: pointer;
        }
        select {
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 8px;
            background: white;
            font-size: 0.9rem;
            width: 100%;
        }
        .value-display {
            font-size: 0.85rem;
            color: #6c757d;
            text-align: center;
            margin-top: 5px;
        }
        .buttons-row {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            flex-wrap: wrap;
        }
        button {
            padding: 10px 18px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            font-size: 0.95rem;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        button i {
            font-size: 1rem;
        }
        .btn-primary {
            background: linear-gradient(90deg, #4b6cb7 0%, #182848 100%);
            color: white;
            flex: 1;
        }
        .btn-primary:hover {
            opacity: 0.9;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .btn-danger {
            background: linear-gradient(90deg, #ff416c 0%, #ff4b2b 100%);
            color: white;
        }
        .btn-danger:hover {
            opacity: 0.9;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .btn-success {
            background: linear-gradient(90deg, #0f9b0f 0%, #3dd65a 100%);
            color: white;
        }
        .btn-success:hover {
            opacity: 0.9;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .instructions {
            background: #e9f7fe;
            border-radius: 10px;
            padding: 15px;
            margin-top: 25px;
            border-left: 4px solid #3498db;
        }
        .instructions h3 {
            color: #3498db;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .instructions ul {
            padding-left: 20px;
        }
        .instructions li {
            margin-bottom: 8px;
            font-size: 0.95rem;
        }
        .preview-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 2rem;
            font-weight: bold;
            color: rgba(0, 0, 0, 0.1);
            pointer-events: none;
            text-align: center;
            max-width: 90%;
        }
        .mode-indicator {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(75, 108, 183, 0.9);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            z-index: 10;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .empty-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            text-align: center;
            padding: 20px;
            z-index: 2;
        }
        .empty-canvas i {
            font-size: 5rem;
            margin-bottom: 20px;
            color: #ced4da;
        }
        .empty-canvas p {
            max-width: 350px;
            font-size: 1.1rem;
            margin-bottom: 20px;
        }
        footer {
            text-align: center;
            padding: 20px;
            color: white;
            font-size: 0.9rem;
            background: rgba(0, 0, 0, 0.2);
            margin-top: 30px;
            border-radius: 0 0 16px 16px;
        }
        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
            }
            .tools-panel {
                margin-right: 0;
                margin-bottom: 20px;
            }
            .controls-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-paint-brush"></i> أداة الرسم على الصور</h1>
            <p class="subtitle">أداة متقدمة للرسم على الصور مع خيارات متعددة للألوان والفرش وأنماط الرسم</p>
        </header>
        
        <div class="main-content">
            <div class="tools-panel">
                <div class="tool-section">
                    <h2><i class="fas fa-upload"></i> تحميل الصورة</h2>
                    <div class="buttons-row">
                        <button id="uploadBtn" class="btn-primary">
                            <i class="fas fa-upload"></i> تحميل صورة
                        </button>
                        <input type="file" id="fileInput" accept="image/*" style="display: none;">
                    </div>
                </div>
                
                <div class="tool-section">
                    <h2><i class="fas fa-sliders-h"></i> إعدادات الرسم</h2>
                    <div class="controls-grid">
                        <div class="control-group">
                            <label for="drawing-mode-selector">نمط الرسم:</label>
                            <select id="drawing-mode-selector">
                                <option value="Pencil">قلم رصاص</option>
                                <option value="Circle">دائرة</option>
                                <option value="Spray">رذاذ</option>
                                <option value="hline">خطوط أفقية</option>
                                <option value="vline">خطوط رأسية</option>
                                <option value="square">مربعات</option>
                                <option value="diamond">ألماس</option>
                            </select>
                        </div>
                        
                        <div class="control-group">
                            <label for="drawing-line-width">سمك الخط:</label>
                            <input type="range" min="1" max="50" id="drawing-line-width" value="5">
                            <div class="value-display" id="line-width-info">5px</div>
                        </div>
                        
                        <div class="control-group">
                            <label for="drawing-color">لون الخط:</label>
                            <input type="color" id="drawing-color" value="#ff3e3e">
                        </div>
                        
                        <div class="control-group">
                            <label for="drawing-shadow-color">لون الظل:</label>
                            <input type="color" id="drawing-shadow-color" value="#333333">
                        </div>
                        
                        <div class="control-group">
                            <label for="drawing-shadow-width">حجم الظل:</label>
                            <input type="range" min="0" max="20" id="drawing-shadow-width" value="0">
                            <div class="value-display" id="shadow-width-info">0px</div>
                        </div>
                        
                        <div class="control-group">
                            <label for="drawing-shadow-offset">إزاحة الظل:</label>
                            <input type="range" min="0" max="20" id="drawing-shadow-offset" value="0">
                            <div class="value-display" id="shadow-offset-info">0px</div>
                        </div>
                    </div>
                </div>
                
                <div class="tool-section">
                    <h2><i class="fas fa-tools"></i> أدوات التحكم</h2>
                    <div class="buttons-row">
                        <button id="clear-drawings" class="btn-danger">
                            <i class="fas fa-trash-alt"></i> مسح الرسومات
                        </button>
                        <button id="save-image" class="btn-success">
                            <i class="fas fa-save"></i> حفظ الصورة
                        </button>
                    </div>
                </div>
                
                <div class="instructions">
                    <h3><i class="fas fa-info-circle"></i> تعليمات الاستخدام</h3>
                    <ul>
                        <li>قم بتحميل صورة باستخدام زر "تحميل صورة"</li>
                        <li>اختر نمط الرسم المناسب من القائمة المنسدلة</li>
                        <li>اضبط سمك الخط ولونه حسب رغبتك</li>
                        <li>يمكنك إضافة ظل للرسم وتعديل إعداداته</li>
                        <li>استخدم زر "مسح الرسومات" لمسح كل ما رسمته</li>
                        <li>احفظ صورتك بعد الانتهاء باستخدام زر "حفظ الصورة"</li>
                    </ul>
                </div>
            </div>
            
            <div class="canvas-container">
                <div class="mode-indicator" id="modeIndicator">وضع الرسم: قلم رصاص</div>
                <canvas id="canvas" width="600" height="500"></canvas>
                <div class="empty-canvas" id="emptyCanvas">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <p>قم بتحميل صورة لبدء الرسم عليها</p>
                    <button id="uploadBtn2" class="btn-primary">
                        <i class="fas fa-upload"></i> تحميل صورة
                    </button>
                </div>
                <div class="preview-text">لوحة الرسم</div>
            </div>
        </div>
        
        <footer>
            <p>أداة الرسم على الصور | تم التطوير باستخدام HTML5 Canvas و Fabric.js</p>
        </footer>
    </div>

    <script>
        // تهيئة لوحة الرسم
        var canvas = new fabric.Canvas('canvas', {
            backgroundColor: '#ffffff',
            isDrawingMode: false
        });
        
        var currentImage = null;
        var isDrawing = false;
        
        // تهيئة واجهة المستخدم
        $(document).ready(function() {
            // إعداد معالج الأحداث
            setupEventHandlers();
            
            // تحديث الفرشاة
            updateBrush();
            
            // إظهار حالة اللوحة الفارغة
            checkCanvasState();
        });
        
        function setupEventHandlers() {
            // أحداث تحميل الصورة
            $('#fileInput').on('change', handleFileSelect);
            $('#uploadBtn, #uploadBtn2').on('click', function() {
                $('#fileInput').click();
            });
            
            // أحداث عناصر التحكم في الرسم
            const drawingColorEl = document.getElementById('drawing-color');
            const drawingShadowColorEl = document.getElementById('drawing-shadow-color');
            const drawingLineWidthEl = document.getElementById('drawing-line-width');
            const drawingShadowWidth = document.getElementById('drawing-shadow-width');
            const drawingShadowOffset = document.getElementById('drawing-shadow-offset');
            const selectorEl = document.getElementById('drawing-mode-selector');
            
            drawingColorEl.addEventListener('change', updateBrush);
            drawingShadowColorEl.addEventListener('change', updateBrush);
            
            drawingLineWidthEl.addEventListener('input', () => {
                document.getElementById('line-width-info').textContent = drawingLineWidthEl.value + 'px';
                updateBrush();
            });
            
            drawingShadowWidth.addEventListener('input', () => {
                document.getElementById('shadow-width-info').textContent = drawingShadowWidth.value + 'px';
                updateBrush();
            });
            
            drawingShadowOffset.addEventListener('input', () => {
                document.getElementById('shadow-offset-info').textContent = drawingShadowOffset.value + 'px';
                updateBrush();
            });
            
            selectorEl.addEventListener('change', handleDrawingModeChange);
            
            // أحداث الأزرار
            $('#clear-drawings').on('click', clearDrawings);
            $('#save-image').on('click', saveImage);
        }
        
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file && file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    loadImageToCanvas(e.target.result);
                };
                reader.readAsDataURL(file);
            } else {
                alert('الرجاء تحديد ملف صورة صالح.');
            }
        }
        
        function loadImageToCanvas(imageSrc) {
            canvas.clear();
            canvas.isDrawingMode = true;
            
            fabric.Image.fromURL(imageSrc, function(img) {
                const canvasWidth = canvas.getWidth();
                const canvasHeight = canvas.getHeight();
                const imgWidth = img.width;
                const imgHeight = img.height;
                
                const scaleX = (canvasWidth - 40) / imgWidth;
                const scaleY = (canvasHeight - 40) / imgHeight;
                const scale = Math.min(scaleX, scaleY, 1);
                
                img.set({
                    scaleX: scale,
                    scaleY: scale,
                    left: (canvasWidth - imgWidth * scale) / 2,
                    top: (canvasHeight - imgHeight * scale) / 2,
                    selectable: false
                });
                
                canvas.add(img);
                canvas.renderAll();
                
                currentImage = img;
                canvas.isDrawingMode = true;
                isDrawing = true;
                
                // إخفاء حالة اللوحة الفارغة
                $('#emptyCanvas').hide();
            });
        }
        
        function updateBrush() {
            const brush = canvas.freeDrawingBrush;
            if (!brush) return;
            
            const drawingColorEl = document.getElementById('drawing-color');
            const drawingShadowColorEl = document.getElementById('drawing-shadow-color');
            const drawingLineWidthEl = document.getElementById('drawing-line-width');
            const drawingShadowWidth = document.getElementById('drawing-shadow-width');
            const drawingShadowOffset = document.getElementById('drawing-shadow-offset');
            
            brush.color = drawingColorEl.value;
            brush.width = parseInt(drawingLineWidthEl.value, 10) || 1;
            brush.shadow = new fabric.Shadow({
                blur: parseInt(drawingShadowWidth.value, 10) || 0,
                offsetX: parseInt(drawingShadowOffset.value, 10) || 0,
                offsetY: parseInt(drawingShadowOffset.value, 10) || 0,
                color: drawingShadowColorEl.value,
            });
        }
        
        function handleDrawingModeChange() {
            const selectorEl = document.getElementById('drawing-mode-selector');
            const val = selectorEl.value;
            
            // تحديث مؤشر الوضع
            $('#modeIndicator').text('وضع الرسم: ' + getModeName(val));
            
            if (val === 'hline') {
                const brush = new fabric.PatternBrush(canvas);
                brush.getPatternSrc = () => {
                    const patternCanvas = document.createElement('canvas');
                    patternCanvas.width = patternCanvas.height = 10;
                    const ctx = patternCanvas.getContext('2d');
                    ctx.strokeStyle = brush.color;
                    ctx.lineWidth = 5;
                    ctx.beginPath();
                    ctx.moveTo(0, 5);
                    ctx.lineTo(10, 5);
                    ctx.stroke();
                    return patternCanvas;
                };
                canvas.freeDrawingBrush = brush;
            }
            else if (val === 'vline') {
                const brush = new fabric.PatternBrush(canvas);
                brush.getPatternSrc = () => {
                    const patternCanvas = document.createElement('canvas');
                    patternCanvas.width = patternCanvas.height = 10;
                    const ctx = patternCanvas.getContext('2d');
                    ctx.strokeStyle = brush.color;
                    ctx.lineWidth = 5;
                    ctx.beginPath();
                    ctx.moveTo(5, 0);
                    ctx.lineTo(5, 10);
                    ctx.stroke();
                    return patternCanvas;
                };
                canvas.freeDrawingBrush = brush;
            }
            else if (val === 'square') {
                const brush = new fabric.PatternBrush(canvas);
                brush.getPatternSrc = () => {
                    const patternCanvas = document.createElement('canvas');
                    patternCanvas.width = patternCanvas.height = 10;
                    const ctx = patternCanvas.getContext('2d');
                    ctx.fillStyle = brush.color;
                    ctx.fillRect(0, 0, 5, 5);
                    return patternCanvas;
                };
                canvas.freeDrawingBrush = brush;
            }
            else if (val === 'diamond') {
                const brush = new fabric.PatternBrush(canvas);
                brush.getPatternSrc = () => {
                    const patternCanvas = document.createElement('canvas');
                    patternCanvas.width = patternCanvas.height = 10;
                    const ctx = patternCanvas.getContext('2d');
                    ctx.fillStyle = brush.color;
                    ctx.beginPath();
                    ctx.moveTo(5, 0);
                    ctx.lineTo(10, 5);
                    ctx.lineTo(5, 10);
                    ctx.lineTo(0, 5);
                    ctx.closePath();
                    ctx.fill();
                    return patternCanvas;
                };
                canvas.freeDrawingBrush = brush;
            }
            else {
                const brushClass = fabric[val + 'Brush'] || fabric.PencilBrush;
                canvas.freeDrawingBrush = new brushClass(canvas);
            }
            
            updateBrush();
        }
        
        function getModeName(mode) {
            const names = {
                'Pencil': 'قلم رصاص',
                'Circle': 'دائرة',
                'Spray': 'رذاذ',
                'hline': 'خطوط أفقية',
                'vline': 'خطوط رأسية',
                'square': 'مربعات',
                'diamond': 'ألماس'
            };
            return names[mode] || mode;
        }
        
        function clearDrawings() {
            if (!currentImage) {
                alert('الرجاء تحميل صورة أولاً.');
                return;
            }
            
            // إزالة جميع الكائنات باستثناء الصورة الخلفية
            const objects = canvas.getObjects();
            objects.forEach(obj => {
                if (obj !== currentImage) {
                    canvas.remove(obj);
                }
            });
            
            canvas.renderAll();
        }
        
        function saveImage() {
            if (!currentImage) {
                alert('الرجاء تحميل صورة أولاً.');
                return;
            }
            
            const dataURL = canvas.toDataURL({
                format: 'png',
                quality: 0.9
            });
            
            const link = document.createElement('a');
            link.download = 'رسمتي.png';
            link.href = dataURL;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
        
        function checkCanvasState() {
            if (canvas.getObjects().length === 0) {
                $('#emptyCanvas').show();
            } else {
                $('#emptyCanvas').hide();
            }
        }
    </script>
</body>
</html>