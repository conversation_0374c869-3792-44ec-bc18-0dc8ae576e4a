-- 001_create_tables.sql
-- سكريبت إنشاء قاعدة البيانات لموقع 4dads.pro

-- ملاحظة: يجب تعديل اسم قاعدة البيانات واسم المستخدم وكلمة المرور حسب إعدادات Hostinger الخاصة بك
-- [!] متغيرات يجب تعديلها: DB_NAME, DB_USER, DB_PASSWORD

-- إنشاء قاعدة البيانات إذا لم تكن موجودة
-- CREATE DATABASE IF NOT EXISTS `DB_NAME` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- USE `DB_NAME`;

-- جدول المستخدمين
CREATE TABLE IF NOT EXISTS `users` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(100) NOT NULL,
  `email` VARCHAR(100) NOT NULL UNIQUE,
  `phone` VARCHAR(20),
  `password` VARCHAR(255), -- سيت<PERSON> تخزين كلمة المرور مشفرة
  `google_id` VARCHAR(100), -- معرف جوجل للمستخدمين المسجلين عبر جوجل
  `profile_picture` VARCHAR(255), -- رابط الصورة الشخصية (قد يكون من جوجل)
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_login` TIMESTAMP NULL,
  `status` ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
  INDEX `idx_email` (`email`),
  INDEX `idx_google_id` (`google_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول خطط الاشتراك
CREATE TABLE IF NOT EXISTS `subscription_plans` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(100) NOT NULL,
  `description` TEXT,
  `price` DECIMAL(10, 2) NOT NULL,
  `duration_days` INT NOT NULL, -- مدة الاشتراك بالأيام
  `features` TEXT, -- ميزات الخطة بتنسيق JSON
  `is_active` BOOLEAN DEFAULT TRUE,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول اشتراكات المستخدمين
CREATE TABLE IF NOT EXISTS `user_subscriptions` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `user_id` INT NOT NULL,
  `plan_id` INT NOT NULL,
  `status` ENUM('active', 'expired', 'cancelled', 'pending') DEFAULT 'pending',
  `start_date` TIMESTAMP NULL,
  `end_date` TIMESTAMP NULL,
  `paypal_subscription_id` VARCHAR(100), -- معرف الاشتراك في PayPal
  `auto_renew` BOOLEAN DEFAULT FALSE,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`plan_id`) REFERENCES `subscription_plans` (`id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_status` (`status`),
  INDEX `idx_end_date` (`end_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول المدفوعات
CREATE TABLE IF NOT EXISTS `payments` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `user_id` INT NOT NULL,
  `subscription_id` INT NOT NULL,
  `amount` DECIMAL(10, 2) NOT NULL,
  `currency` VARCHAR(3) DEFAULT 'USD',
  `payment_method` ENUM('paypal', 'credit_card', 'other') DEFAULT 'paypal',
  `transaction_id` VARCHAR(100), -- معرف المعاملة من PayPal
  `status` ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
  `payment_date` TIMESTAMP NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`subscription_id`) REFERENCES `user_subscriptions` (`id`) ON DELETE CASCADE,
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_status` (`status`),
  INDEX `idx_transaction_id` (`transaction_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدخال بيانات أولية لخطط الاشتراك
INSERT INTO `subscription_plans` (`name`, `description`, `price`, `duration_days`, `features`, `is_active`) VALUES
('الخطة الشهرية', 'اشتراك شهري بميزات كاملة', 9.99, 30, '["ميزة 1", "ميزة 2", "ميزة 3"]', TRUE),
('الخطة السنوية', 'اشتراك سنوي بميزات كاملة مع خصم 20%', 99.99, 365, '["ميزة 1", "ميزة 2", "ميزة 3", "ميزة 4", "دعم فني مميز"]', TRUE),
('الخطة التجريبية', 'اشتراك تجريبي لمدة أسبوع', 0.00, 7, '["ميزة 1", "ميزة 2"]', TRUE);

-- جدول رموز إعادة تعيين كلمة المرور
CREATE TABLE IF NOT EXISTS `password_reset_tokens` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `user_id` INT NOT NULL,
  `token` VARCHAR(100) NOT NULL,
  `expires_at` TIMESTAMP NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `used` BOOLEAN DEFAULT FALSE,
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  INDEX `idx_token` (`token`),
  INDEX `idx_expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول سجل الأحداث
CREATE TABLE IF NOT EXISTS `activity_logs` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `user_id` INT,
  `action` VARCHAR(100) NOT NULL,
  `description` TEXT,
  `ip_address` VARCHAR(45),
  `user_agent` TEXT,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_action` (`action`),
  INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
