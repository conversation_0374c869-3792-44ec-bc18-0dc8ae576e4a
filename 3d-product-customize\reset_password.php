<?php
require_once 'config.php';

// Check if token is provided
if (!isset($_GET['token']) || empty($_GET['token'])) {
    header("Location: login.php");
    exit;
}

$token = $_GET['token'];
$tokenValid = false;
$userId = null;

// Check if token is valid
$sql = "SELECT pr.user_id, u.email 
       FROM password_resets pr 
       JOIN users u ON pr.user_id = u.user_id 
       WHERE pr.token = ? AND pr.expiry_date > NOW()";

if ($stmt = $conn->prepare($sql)) {
    $stmt->bind_param("s", $token);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $tokenValid = true;
        $userId = $row['user_id'];
        $userEmail = $row['email'];
    }
    
    $stmt->close();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - 3D Product Customizer</title>
    <link rel="stylesheet" href="styles/subscription.css">
</head>
<body>
    <div class="auth-container">
        <div class="auth-box">
            <h1>Reset Your Password</h1>
            
            <div id="message-container"></div>
            
            <?php if ($tokenValid): ?>
                <form id="reset-password-form" method="post" action="process_reset_password.php">
                    <input type="hidden" name="token" value="<?php echo htmlspecialchars($token); ?>">
                    <input type="hidden" name="user_id" value="<?php echo htmlspecialchars($userId); ?>">
                    
                    <div class="form-group">
                        <label for="password">New Password</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="confirm_password">Confirm New Password</label>
                        <input type="password" id="confirm_password" name="confirm_password" required>
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" class="btn-primary">Reset Password</button>
                    </div>
                </form>
            <?php else: ?>
                <div class="message error">
                    <p>Invalid or expired password reset token.</p>
                    <p>Please request a new password reset link.</p>
                </div>
                
                <div class="form-group" style="margin-top: 20px;">
                    <a href="forgot_password.php" class="btn-primary">Request New Reset Link</a>
                </div>
            <?php endif; ?>
            
            <div class="auth-links">
                <p>Remember your password? <a href="login.php">Login</a></p>
            </div>
        </div>
    </div>

    <script src="js/validation.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const resetPasswordForm = document.getElementById('reset-password-form');
            if (resetPasswordForm) {
                resetPasswordForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    // Get form values
                    const password = document.getElementById('password').value;
                    const confirmPassword = document.getElementById('confirm_password').value;
                    
                    // Validate password
                    if (password.length < 8) {
                        showMessage('message-container', 'Password must be at least 8 characters long', 'error');
                        return;
                    }
                    
                    // Check if passwords match
                    if (password !== confirmPassword) {
                        showMessage('message-container', 'Passwords do not match', 'error');
                        return;
                    }
                    
                    // If validation passes, submit the form
                    const formData = new FormData(resetPasswordForm);
                    
                    fetch('process_reset_password.php', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showMessage('message-container', data.message, 'success');
                            setTimeout(() => {
                                window.location.href = 'login.php';
                            }, 2000);
                        } else {
                            showMessage('message-container', data.message, 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showMessage('message-container', 'An error occurred. Please try again later.', 'error');
                    });
                });
            }
        });
    </script>
</body>
</html>
