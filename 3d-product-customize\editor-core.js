/**
 * EditorCore - Main controller for the Fabric.js canvas editor
 * Handles initialization, object management, and event coordination
 */

// Import Fabric.js (or declare it if it's globally available)
// For example, if using a module bundler:
// import { fabric } from 'fabric';
// Or, if Fabric.js is included via a script tag, you might declare:
// const fabric = window.fabric; // Assuming it's available on the window object

// Declare other dependencies if they are not globally available
class ControlPanel {} // Placeholder, replace with actual implementation/import
class CanvasSizeManager {} // Placeholder, replace with actual implementation/import
class ThreeDSync {} // Placeholder, replace with actual implementation/import
class History {
  constructor(editorCore) {
    this.editorCore = editorCore
    this.states = []
    this.index = -1
  }

  saveState() {
    const json = this.editorCore.exportAsJSON()
    this.states = this.states.slice(0, this.index + 1)
    this.states.push(json)
    this.index++
  }

  undo() {
    if (this.index > 0) {
      this.index--
      this.editorCore.importFromJSON(this.states[this.index])
    }
  }

  redo() {
    if (this.index < this.states.length - 1) {
      this.index++
      this.editorCore.importFromJSON(this.states[this.index])
    }
  }
}

class EditorCore {
  constructor(options = {}) {
    this.options = {
      canvasId: "fabric-canvas",
      width: 1024,
      height: 1024,
      backgroundColor: "#ffffff",
      ...options,
    }

    this.canvas = null
    this.history = null
    this.controlPanel = null
    this.canvasSizeManager = null
    this.threeDSync = null
    this.isInitialized = false

    // Bind methods to maintain context
    this.handleObjectSelected = this.handleObjectSelected.bind(this)
    this.handleSelectionCleared = this.handleSelectionCleared.bind(this)
    this.handleObjectModified = this.handleObjectModified.bind(this)
    this.handleCanvasModified = this.handleCanvasModified.bind(this)
    this.addText = this.addText.bind(this)
    this.addImage = this.addImage.bind(this)
    this.deleteSelected = this.deleteSelected.bind(this)
    this.duplicateSelected = this.duplicateSelected.bind(this)
    this.bringToFront = this.bringToFront.bind(this)
    this.sendToBack = this.sendToBack.bind(this)
    this.resetTransformation = this.resetTransformation.bind(this)
    this.toggleLock = this.toggleLock.bind(this)
  }

  /**
   * Initialize the Fabric.js canvas and all related components
   */
  init() {
    if (this.isInitialized) return

    // Create canvas instance
    this.canvas = new fabric.Canvas(this.options.canvasId, {
      width: this.options.width,
      height: this.options.height,
      backgroundColor: this.options.backgroundColor,
      preserveObjectStacking: true,
      stopContextMenu: true,
      selection: true,
    })

    // Initialize components
    this.history = new History(this)
    this.controlPanel = new ControlPanel(this)
    this.canvasSizeManager = new CanvasSizeManager(this)
    this.threeDSync = new ThreeDSync(this)

    // Set up event listeners
    this.setupEventListeners()

    // Set up keyboard shortcuts
    this.setupKeyboardShortcuts()

    this.isInitialized = true

    console.log("EditorCore initialized successfully")
    return this
  }

  /**
   * Set up event listeners for the canvas
   */
  setupEventListeners() {
    this.canvas.on("selection:created", this.handleObjectSelected)
    this.canvas.on("selection:updated", this.handleObjectSelected)
    this.canvas.on("selection:cleared", this.handleSelectionCleared)
    this.canvas.on("object:modified", this.handleObjectModified)
    this.canvas.on("object:added", this.handleCanvasModified)
    this.canvas.on("object:removed", this.handleCanvasModified)

    // Additional events for real-time 3D sync
    this.canvas.on("object:moving", () => this.threeDSync.syncWithThreeJs())
    this.canvas.on("object:scaling", () => this.threeDSync.syncWithThreeJs())
    this.canvas.on("object:rotating", () => this.threeDSync.syncWithThreeJs())
    this.canvas.on("text:changed", () => this.threeDSync.syncWithThreeJs())

    // Mouse events for better UX
    this.canvas.on("mouse:over", (e) => {
      if (e.target) {
        e.target.set("borderColor", "#2196F3")
        this.canvas.renderAll()
      }
    })

    this.canvas.on("mouse:out", (e) => {
      if (e.target) {
        e.target.set("borderColor", "rgba(102,153,255,0.75)")
        this.canvas.renderAll()
      }
    })
  }

  /**
   * Set up keyboard shortcuts
   */
  setupKeyboardShortcuts() {
    document.addEventListener("keydown", (e) => {
      // Skip if focus is on an input element
      if (e.target.tagName === "INPUT" || e.target.tagName === "TEXTAREA") return

      // Delete key - remove selected object
      if (e.key === "Delete" || e.key === "Backspace") {
        e.preventDefault()
        this.deleteSelected()
      }

      // Ctrl+Z - Undo
      if (e.key === "z" && (e.ctrlKey || e.metaKey)) {
        e.preventDefault()
        this.history.undo()
      }

      // Ctrl+Y or Ctrl+Shift+Z - Redo
      if ((e.key === "y" && (e.ctrlKey || e.metaKey)) || (e.key === "z" && (e.ctrlKey || e.metaKey) && e.shiftKey)) {
        e.preventDefault()
        this.history.redo()
      }

      // Ctrl+D - Duplicate
      if (e.key === "d" && (e.ctrlKey || e.metaKey)) {
        e.preventDefault()
        this.duplicateSelected()
      }

      // Ctrl+G - Group selected objects
      if (e.key === "g" && (e.ctrlKey || e.metaKey)) {
        e.preventDefault()
        this.groupSelected()
      }

      // Ctrl+Shift+G - Ungroup selected group
      if (e.key === "g" && (e.ctrlKey || e.metaKey) && e.shiftKey) {
        e.preventDefault()
        this.ungroupSelected()
      }
    })
  }

  /**
   * Handle object selection event
   */
  handleObjectSelected(e) {
    const selectedObject = e.selected[0]
    if (selectedObject) {
      this.controlPanel.showForObject(selectedObject)
    }
  }

  /**
   * Handle selection cleared event
   */
  handleSelectionCleared() {
    this.controlPanel.hide()
  }

  /**
   * Handle object modified event
   */
  handleObjectModified() {
    this.history.saveState()
    this.threeDSync.syncWithThreeJs()
  }

  /**
   * Handle canvas modified event (objects added/removed)
   */
  handleCanvasModified() {
    this.history.saveState()
    this.threeDSync.syncWithThreeJs()
  }

  /**
   * Add text to the canvas
   */
  addText(text = "New Text", options = {}) {
    const defaultOptions = {
      left: this.canvas.width / 2,
      top: this.canvas.height / 2,
      fontFamily: "Arial",
      fontSize: 40,
      fill: "#000000",
      editable: true,
    }

    const textObject = new fabric.IText(text, {
      ...defaultOptions,
      ...options,
    })

    this.canvas.add(textObject)
    this.canvas.setActiveObject(textObject)
    this.canvas.renderAll()

    this.history.saveState()
    this.threeDSync.syncWithThreeJs()

    return textObject
  }

  /**
   * Add image to the canvas
   */
  addImage(url, options = {}) {
    return new Promise((resolve, reject) => {
      fabric.Image.fromURL(
        url,
        (img) => {
          const defaultOptions = {
            left: this.canvas.width / 2,
            top: this.canvas.height / 2,
            scaleX: 0.5,
            scaleY: 0.5,
          }

          img.set({
            ...defaultOptions,
            ...options,
          })

          this.canvas.add(img)
          this.canvas.setActiveObject(img)
          this.canvas.renderAll()

          this.history.saveState()
          this.threeDSync.syncWithThreeJs()

          resolve(img)
        },
        { crossOrigin: "anonymous" },
      )
    })
  }

  /**
   * Delete selected object(s)
   */
  deleteSelected() {
    const activeObject = this.canvas.getActiveObject()
    if (!activeObject) return

    if (activeObject.type === "activeSelection") {
      // Delete multiple selected objects
      activeObject.forEachObject((obj) => {
        this.canvas.remove(obj)
      })
      this.canvas.discardActiveObject()
    } else {
      // Delete single object
      this.canvas.remove(activeObject)
    }

    this.canvas.renderAll()
    this.history.saveState()
    this.threeDSync.syncWithThreeJs()
  }

  /**
   * Duplicate selected object(s)
   */
  duplicateSelected() {
    const activeObject = this.canvas.getActiveObject()
    if (!activeObject) return

    // Clone the object(s)
    activeObject.clone((cloned) => {
      this.canvas.discardActiveObject()

      if (cloned.type === "activeSelection") {
        // Handle multiple objects
        cloned.canvas = this.canvas
        cloned.forEachObject((obj) => {
          obj.set({
            left: obj.left + 20,
            top: obj.top + 20,
          })
          this.canvas.add(obj)
        })

        // Group the cloned objects
        this.canvas.setActiveObject(cloned)
      } else {
        // Handle single object
        cloned.set({
          left: cloned.left + 20,
          top: cloned.top + 20,
        })
        this.canvas.add(cloned)
        this.canvas.setActiveObject(cloned)
      }

      this.canvas.renderAll()
      this.history.saveState()
      this.threeDSync.syncWithThreeJs()
    })
  }

  /**
   * Bring selected object to front
   */
  bringToFront() {
    const activeObject = this.canvas.getActiveObject()
    if (!activeObject) return

    activeObject.bringToFront()
    this.canvas.renderAll()
    this.history.saveState()
  }

  /**
   * Send selected object to back
   */
  sendToBack() {
    const activeObject = this.canvas.getActiveObject()
    if (!activeObject) return

    activeObject.sendToBack()
    this.canvas.renderAll()
    this.history.saveState()
  }

  /**
   * Reset transformation of selected object
   */
  resetTransformation() {
    const activeObject = this.canvas.getActiveObject()
    if (!activeObject) return

    activeObject.set({
      scaleX: 1,
      scaleY: 1,
      angle: 0,
    })

    this.canvas.renderAll()
    this.history.saveState()
    this.threeDSync.syncWithThreeJs()
  }

  /**
   * Toggle lock/unlock of selected object
   */
  toggleLock() {
    const activeObject = this.canvas.getActiveObject()
    if (!activeObject) return

    const isLocked = activeObject.lockMovementX && activeObject.lockMovementY

    activeObject.set({
      lockMovementX: !isLocked,
      lockMovementY: !isLocked,
      lockRotation: !isLocked,
      lockScalingX: !isLocked,
      lockScalingY: !isLocked,
    })

    this.canvas.renderAll()
    this.controlPanel.updateLockState(!isLocked)
    this.history.saveState()
  }

  /**
   * Group selected objects
   */
  groupSelected() {
    const activeObject = this.canvas.getActiveObject()
    if (!activeObject || activeObject.type !== "activeSelection") return

    const group = activeObject.toGroup()
    this.canvas.setActiveObject(group)
    this.canvas.renderAll()
    this.history.saveState()
    this.threeDSync.syncWithThreeJs()
  }

  /**
   * Ungroup selected group
   */
  ungroupSelected() {
    const activeObject = this.canvas.getActiveObject()
    if (!activeObject || activeObject.type !== "group") return

    const items = activeObject.getObjects()
    activeObject.destroy()
    this.canvas.remove(activeObject)

    items.forEach((item) => {
      this.canvas.add(item)
    })

    this.canvas.renderAll()
    this.history.saveState()
    this.threeDSync.syncWithThreeJs()
  }

  /**
   * Update text font
   */
  updateTextFont(fontFamily) {
    const activeObject = this.canvas.getActiveObject()
    if (!activeObject || (!activeObject.isType("i-text") && !activeObject.isType("text"))) return

    activeObject.set("fontFamily", fontFamily)
    this.canvas.renderAll()
    this.history.saveState()
    this.threeDSync.syncWithThreeJs()
  }
  
  /**
   * Update text styling with advanced options
   * @param {Object} styleOptions - Object containing style properties
   */
  updateTextStyling(styleOptions) {
    const activeObject = this.canvas.getActiveObject()
    if (!activeObject || (!activeObject.isType("i-text") && !activeObject.isType("text"))) return
    
    activeObject.set(styleOptions)
    this.canvas.renderAll()
    this.history.saveState()
    this.threeDSync.syncWithThreeJs()
  }
  
  /**
   * Apply text shadow
   * @param {Object} shadowOptions - Shadow options (color, blur, offsetX, offsetY)
   */
  applyTextShadow(shadowOptions) {
    const activeObject = this.canvas.getActiveObject()
    if (!activeObject || (!activeObject.isType("i-text") && !activeObject.isType("text"))) return
    
    const shadow = new fabric.Shadow(shadowOptions)
    activeObject.set('shadow', shadow)
    this.canvas.renderAll()
    this.history.saveState()
    this.threeDSync.syncWithThreeJs()
  }

  /**
   * Export canvas as JSON
   * @param {boolean} download - Whether to trigger a download of the JSON file
   * @returns {Object|undefined} - JSON object if download is false, undefined otherwise
   */
  exportAsJSON(download = false) {
    const json = this.canvas.toJSON(["id", "customProperty", "selectable", "borderColor"])
    
    if (download) {
      // Convert to a downloadable file
      const jsonStr = JSON.stringify(json, null, 2)
      const blob = new Blob([jsonStr], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      
      // Create download link
      const a = document.createElement('a')
      a.href = url
      a.download = `canvas_export_${new Date().toISOString().slice(0, 10)}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      
      // Clean up
      setTimeout(() => URL.revokeObjectURL(url), 100)
      return undefined
    }
    
    return json
  }

  /**
   * Import canvas from JSON
   * @param {Object|string} json - JSON object or string to load
   * @param {Function} callback - Optional callback after import
   */
  importFromJSON(json, callback) {
    // Handle string input (from file upload)
    if (typeof json === 'string') {
      try {
        json = JSON.parse(json)
      } catch (e) {
        console.error('Failed to parse JSON:', e)
        return
      }
    }
    
    this.canvas.loadFromJSON(json, () => {
      this.canvas.renderAll()
      this.history.saveState()
      this.threeDSync.syncWithThreeJs()
      
      if (callback && typeof callback === 'function') {
        callback()
      }
    })
  }
  
  /**
   * Import canvas from a JSON file
   * @param {File} file - JSON file to import
   */
  importFromJSONFile(file) {
    const reader = new FileReader()
    
    reader.onload = (e) => {
      try {
        const json = JSON.parse(e.target.result)
        this.importFromJSON(json)
      } catch (err) {
        console.error('Error importing JSON file:', err)
        alert('Failed to import JSON file. The file might be corrupted or in an invalid format.')
      }
    }
    
    reader.readAsText(file)
  }

  /**
   * Export canvas as image
   * @param {string} format - Image format (png, jpeg)
   * @param {number} quality - Image quality (0-1)
   * @param {number} multiplier - Resolution multiplier
   * @param {boolean} download - Whether to trigger a download
   * @returns {string|undefined} - DataURL if download is false, undefined otherwise
   */
  exportAsImage(format = "png", quality = 1, multiplier = 2, download = false) {
    // Higher resolution export for better quality
    const dataUrl = this.canvas.toDataURL({
      format: format,
      quality: quality,
      multiplier: multiplier, // Higher resolution for better quality
    })
    
    if (download) {
      // Create download link
      const a = document.createElement('a')
      a.href = dataUrl
      a.download = `canvas_export_${new Date().toISOString().slice(0, 10)}.${format}`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      return undefined
    }
    
    return dataUrl
  }
}
