<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forgot Password - 3D Product Customizer</title>
    <link rel="stylesheet" href="styles/subscription.css">
</head>
<body>
    <div class="auth-container">
        <div class="auth-box">
            <h1>Forgot Password</h1>
            
            <div id="message-container"></div>
            
            <form id="forgot-password-form" method="post" action="process_forgot_password.php">
                <div class="form-group">
                    <label for="email">Email Address</label>
                    <p class="form-help">Enter your email address and we'll send you a link to reset your password.</p>
                    <input type="email" id="email" name="email" required>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn-primary">Send Reset Link</button>
                </div>
                
                <div class="auth-links">
                    <p>Remember your password? <a href="login.php">Login</a></p>
                </div>
            </form>
        </div>
    </div>

    <script src="js/validation.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const forgotPasswordForm = document.getElementById('forgot-password-form');
            const messageContainer = document.getElementById('message-container');
            
            forgotPasswordForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                // Get form value
                const email = document.getElementById('email').value.trim();
                
                // Validate email
                if (!isValidEmail(email)) {
                    showMessage('message-container', 'Please enter a valid email address', 'error');
                    return;
                }
                
                // If validation passes, submit the form
                const formData = new FormData(forgotPasswordForm);
                
                fetch('process_forgot_password.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showMessage('message-container', data.message, 'success');
                        document.getElementById('email').value = '';
                    } else {
                        showMessage('message-container', data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showMessage('message-container', 'An error occurred. Please try again later.', 'error');
                });
            });
        });
    </script>
</body>
</html>
