<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta charset="UTF-8">
  <title>Interactive 3D Viewer – عارض نماذج ثلاثية الأبعاد تفاعلي | 4Dads.pro</title>
  
  <!-- وصف ميتا باللغتين -->
  <meta name="description" content="اكتشف عارض نماذج ثلاثية الأبعاد تفاعلي على 4Dads.pro – تحكم متقدم في الألوان والإضاءة، تخصيص خلفية، وتصدير صيغ متعددة | 3D model viewer with advanced controls, color & lighting customizer." />
  
  <!-- الكلمات الدلالية (اختياريّة الاستخدام) -->
  <meta name="keywords" content="
    3D model viewer, Interactive 3D viewer, 3D viewer online, 3D model customizer, 3D lighting editor,
    عارض نماذج ثلاثية الأبعاد, تخصيص الألوان في 3D, إعد<PERSON> خلفية 3D, تصدير تصميم ثلاثي الأبعاد, 3D viewer controls" />
  
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      name="description"
      content="عارض تفاعلي للنماذج ثلاثية الأبعاد مع أدوات تحكم متقدمة لتخصيص الألوان والخلفيات والإضاءة. يتيح إضافة النصوص والصور وتصدير التصاميم بتنسيقات متعددة. مثالي للمصممين ومحترفي النماذج ثلاثية الأبعاد."
    />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&family=Almarai:wght@300;400;700;81&family=Amiri:wght@400;700&family=Noto+Kufi+Arabic:wght@100;200;300;400;500;600;700;800;900&family=Changa:wght@200;300;400;500;600;700;800&family=Lalezar&family=Mada:wght@200;300;400;500;600;700;800;900&family=Rakkas&family=Reem+Kufi:wght@400;700&family=Lateef:wght@200;300;400;500;600;700;800&family=Ruwudu:wght@400;500;600;700&family=Badeen+Display&family=Cairo:wght@200;300;400;500;700;800;900&family=Kufam:wght@400;500;600;700;800;900&family=Aref+Ruqaa:wght@400;700&family=Qahiri&family=Marhey:wght@300;400;500;600;700&family=Amiri+Quran&family=Oi&family=Aref+Ruqaa+Ink:wght@400;700&family=Reem+Kufi+Fun:wght@400;700&family=Vibes&display=swap"
      rel="stylesheet"
    />
    <link rel="manifest" href="/manifest.json" />
    <link rel="service-worker" href="/service-worker.js" />
    <!--<link rel="stylesheet" href="/css/styles.css" />-->
    <!--<link rel="stylesheet" href="fonts.css" />-->
    <link rel="stylesheet" href="arabicFonts.css" />
    <link rel="icon" href="/content/img/ui/favicon.ico" type="image/x-icon" />
    <link rel="apple-touch-icon" sizes="57x57" href="img/icons/57.png" alt="أيقونة التطبيق 57x57" />
    <link rel="apple-touch-icon" sizes="60x60" href="img/icons/60.png" alt="أيقونة التطبيق 60x60" />
    <link rel="apple-touch-icon" sizes="72x72" href="img/icons/72.png" alt="أيقونة التطبيق 72x72" />
    <link rel="apple-touch-icon" sizes="76x76" href="img/icons/76.png" alt="أيقونة التطبيق 76x76" />
    <link rel="apple-touch-icon" sizes="114x114" href="img/icons/114.png" alt="أيقونة التطبيق 114x114" />
    <link rel="apple-touch-icon" sizes="120x120" href="img/icons/120.png" alt="أيقونة التطبيق 120x120" />
    <link rel="apple-touch-icon" sizes="144x144" href="img/icons/144.png" alt="أيقونة التطبيق 144x144" />
    <link rel="apple-touch-icon" sizes="152x152" href="img/icons/152.png" alt="أيقونة التطبيق 152x152" />
    <link rel="apple-touch-icon" sizes="152x152" href="img/icons/512.png" alt="أيقونة التطبيق 512x512" />
    <link rel="apple-touch-icon" sizes="180x180" href="img/icons/180.png" alt="أيقونة التطبيق 180x180" />
    <link
      rel="icon"
      type="image/png"
      sizes="192x192"
      href="img/icons/192.png"
      alt="أيقونة التطبيق 192x192"
    />
    <link rel="icon" type="image/png" sizes="32x32" href="img/icons/32.png" alt="أيقونة التطبيق 32x32" />
    <link rel="icon" type="image/png" sizes="96x96" href="img/icons/96.png" alt="أيقونة التطبيق 96x96" />
    <link rel="icon" type="image/png" sizes="16x16" href="img/icons/16.png" alt="أيقونة التطبيق 16x16" />
    <meta name="msapplication-TileColor" content="#d3a77b" />
    <meta name="msapplication-TileImage" content="img/icons/144.png" alt="أيقونة التطبيق 144x144" />
    <meta name="theme-color" content="#182949" />
    <link rel="apple-touch-icon" href="/img/icons/icon.svg" alt="أيقونة التطبيق" />
    <link rel="icon" type="image/x-icon" href="/img/ui/favicon.ico" alt="أيقونة التطبيق" />

    <script src="libs/hammer/hammer.min.js"></script>
    <script src="libs/threejs/build/three.min.js"></script>
    <script src="libs/threejs/modules/js/controls/OrbitControls.min.js"></script>
    <script src="libs/threejs/modules/js/loaders/GLTFLoader.js"></script>
    <script src="libs/fabric/fabric.min.js"></script>
<style>
    /* أنماط بطاقات التسعير */
    .pricing-cards {
        display: flex;
        justify-content: center;
        gap: 10px;
        max-width: 400px;
        margin: 0 auto;
    }

    .pricing-card {
        background-color: #182949;
        border: 2px solid #d3a77b;
        border-radius: 8px;
        padding: 12px;
        width: 45%;
        position: relative;
        transition: transform 0.3s;
        cursor: pointer;
    }

    .pricing-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    .pricing-card.featured {
        border-color: #efc776;
        box-shadow: 0 4px 12px rgba(239, 199, 118, 0.2);
    }

    .pricing-badge {
        position: absolute;
        top: -10px;
        right: 50%;
        transform: translateX(50%);
        background-color: #efc776;
        color: #182949;
        font-weight: bold;
        padding: 3px 8px;
        border-radius: 12px;
        font-size: 10px;
    }

    .pricing-header {
        margin-bottom: 10px;
        padding-bottom: 8px;
        border-bottom: 1px solid rgba(211, 167, 123, 0.3);
    }

    .price {
        display: flex;
        align-items: baseline;
        justify-content: center;
        color: #d3a77b;
    }

    .currency {
        font-size: 14px;
        margin-left: 2px;
    }

    .amount {
        font-size: 24px;
        font-weight: bold;
    }

    .period {
        font-size: 12px;
        margin-right: 3px;
        opacity: 0.8;
    }

    .savings {
        color: #efc776;
        font-size: 11px;
        margin-top: 4px;
        text-align: center;
    }

    .pricing-features {
        margin-bottom: 10px;
        text-align: right;
    }

    .pricing-features ul {
        list-style: none;
        padding: 0;
        margin: 0;
        font-size: 11px;
    }

    .pricing-features li {
        padding: 2px 0;
        color: #d3a77b;
    }

    .paypal-button-container {
        height: 30px;
        transform: scale(0.85);
        transform-origin: center top;
    }

    section.pricing h2 {
        text-align: center;
        color: #d3a77b;
        font-size: 15px;
        margin: 5px 0;
    }

           html, body {
          margin: 0;
          padding: 0;
          height: 100%;
          overflow: hidden;
          direction: rtl;
          }

          /* إخفاء خيارات المشتركين فقط للمستخدمين غير المشتركين */
          .subscriber-only.hidden {
              display: none;
          }
          #container {
  display: flex;
  flex-direction: row;
  height: 100%;
  transition: all 0.3s;
}

          #threejs-container {
          background: #182949;
          width: 100%;
          height: 70%;
          top: 0;
          transition: all 0.3s;
          }

          #image-editor-container {
          width: 100%;
          bottom: 140px;
          height: 30%;
          position: absolute;
          display: none;
          z-index: 10;
          transition: all 0.3s;
          }
          @media (min-width: 1024px) {
  #image-editor-container {
    display: block;
    width: 35%;
    height: 50%;
    bottom: 80px;
    right: 0;
  }
}

      .bar {
          position: absolute;
          z-index: 20;
          display: none;
      }

      .button-container {
          position: absolute;
          z-index: 30;
          width: 100%;
          display: flex;
          justify-content: center;
          gap: 10px;
      }



      .button {
          padding: 10px 20px;
          background-color: #344464;
          color: #d3a77b;
          border: none;
          cursor: pointer;
      }

      .controls button {
      background: 0x586c85;
      border: 0xd3a77b;
      color: 0xd3a77b;
      padding: 10px;
      cursor: pointer;
      width: 100%;
      text-align: right;
      font-size: 16px;
      }
      .controls button:hover {
      background: rgba(255, 255, 255, 0.2);
      }
      .controls {
      position: absolute;
      top: 10px;
      right: 10px;
      padding: 10px;
      border-radius: 10px;
      z-index: 1000;
      /*display: flex;*/
      display: none;
      flex-direction: row;
      gap: 1px;
      color: #d3a77b;
      font-size: 12px;
      height: fit-content;
      align-items: center;
      justify-content: center;
      text-align: center;
      }
      .controls.show {
      display: block;
      }
      #background-controls {
      top: 33%;
      left: 50px;
      }

      #background-controls {
      top: 34%;
      left: 200px;
      }

      #ground-controls {
      top: 33%;
      left: 350px;
      }

      .controls.active {
      display: block;
      }
      .controls button:hover {
      background-color: #586c85;
      border-color: #d3a77b;
      color: #d3a77b;
      transform: scale(1.05);
      }

      .controls button:hover svg {
      fill: #d3a77b;
      color: #d3a77b;
      }

      .controls-grid,
      .ground-controls,
      .background-controls,
      .model-controls {
      display: flex;
      border-radius: 5px;
      gap: 1px;
      justify-content: center;
      max-width: 600px;
      margin: auto;
      }

      .controls-grid button,
      .controls-grid .gradient input {
      width: 24px;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #344464;
      border: 2px solid #d3a77b;
      border-radius: 5px;
      cursor: pointer;
      transition: background-color 0.3s ease;
      }

      .gradient input {
          width: 24px;
          height: 48px;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #586c85;
          border: 1px solid #d3a77b;
          border-radius: 5px;
          cursor: pointer;
          transition: background-color 0.3s ease;
      }
      .gradient input:hover {
          background-color: #d3a77b;
          border-color: #182949;
          color: #d3a77b;
          transform: scale(1.05);
      }

      .controls-grid button:hover {
          flex-direction: row;
          background-color: #d3a77b;
          border-color: #586c85;
          color: #586c85;
          transform: scale(1.05);
      }

      .controls-grid button:hover svg {
          fill: #344464;
      }

      .controls-grid .gradient {
          display: flex;
          flex-direction: row;
          gap: 1px;
      }

      .controls {
          flex-direction: row;
          bottom: 10px;
          top: auto;
          left: 10px;
          right: 10px;
          justify-content: center;
      }

      .controls button {
          width: 48px;
          height: 48px;
      }

      .controls-grid button,
      .controls-grid .gradient input {
          width: 24p;
          height: 48px;
      }

      .gradient input {
          width: 24px;
          height: 48px;
      }

      /* تنسيق الأزرار */
      input[type="color"],
      input[type="file"],
      button {
          width: 48px;
          height: 48px;
          background-color: #344464;
          border: 2px solid #d3a77b;
          color: #d3a77b;
          border-radius: 5px;
          cursor: pointer;
      }

      /* تنسيق زر الأيقونة */
      .color-picker-container {
          position: relative;
          display: inline-block;
      }

      /* إخفاء منتقي اللون ولكنه يبقى تفاعليًا */
      .color-picker-container input[type="color"] {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          opacity: 0;
          cursor: pointer;
      }

      /* تنسيق زر الأيقونة */
      .color-picker-label {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 44px;
          height: 44px;
          background-color: #344464;
          border: 2px solid #d3a77b;
          border-radius: 5px;
      }

      /* تنسيق الأيقونة داخل الزر */
      .color-picker-label svg {
          fill: #d3a77b;
          width: 24px;
          height: 24px;
      }

      input[type="file"] {
          display: none;
      }

      .color-inputs {
          display: none;
          flex-direction: row;
          gap: 5px;
          margin-bottom: 10px;
      }
      #skybox-thumbnails {
          position: fixed;
          top: 0;
          right: -100px;
          width: 75px;
          height: 100vh;
          background-color: #344464;
          border-color: #d3a77b;
          color: rgb#d3a77b;
          border-radius: 5px;
          box-shadow: 1px 4px 8px rgba(0, 0, 0, 0.5);
          transition: right 0.3s ease;
          overflow-y: auto;
          z-index: 1000;
      }

      #skybox-thumbnails.active {
          right: 0;
      }

      .skybox-grid {
          display: flex;
          flex-direction: column;
          gap: 5px;
          padding: 5px;
      }
      #skybox-thumbnails img {
          width: 100%;
          height: auto;
          cursor: pointer;
          border: 2px solid transparent;
          border-radius: 5px;
          transition: border-color 0.3s ease;
      }

      #skybox-thumbnails img:hover {
          border-color: #d3a77b;
      }

      #model-skybox-picker {
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          gap: 8px;
      }
      #texture-thumbnails {
          position: fixed;
          top: 0;
          left: -100px;
          width: 75px;
          height: 100vh;
          background-color: #344464;
          border-color: #d3a77b;
          color: rgb#d3a77b;
          border-radius: 5px;
          box-shadow: 1px 4px 8px rgba(0, 0, 0, 0.5);
          transition: right 0.3s ease;
          overflow-y: auto;
          z-index: 1000;
      }

      #texture-thumbnails.active {
          left: 0;
      }

      .texture-grid {
          display: flex;
          flex-direction: column;
          gap: 5px;
          padding: 5px;
      }

      #texture-thumbnails img {
          width: 100%;
          height: auto;
          cursor: pointer;
          border: 2px solid transparent;
          border-radius: 5px;
          transition: border-color 0.3s ease;
      }

      #texture-thumbnails img:hover {
          border-color: #222222;
      }

      #model-texture-picker {
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          gap: 5px;
      }



      .range-input label {
          display: block;
          margin-bottom: 10px;
          color: #d3a77b;
          font-size: 12px;
          font-weight: bold;
          text-align: center; /* توسيط النص في المنتصف */
          width: 100%;
      }


      .range-input {
          width: 100px;
          height: 50px;
        position: relative;
        margin: 5px 10px;
        padding: 10px 10px;
        background: #182949;
        border-radius: 5px;
        display: flex;
        flex-direction: row;
        align-items: center;
      }







      .gradient {
          display: flex;
          flex-direction: column;
          gap: 1px;
          margin-right: 1px;
      }

      .gradient .rect {
          width: 10px;
          height: 10px;
          background-color: 0x586c85;
          border-radius: 2px;
      }

      .color-input {
          display: none;
      }

      /* .toggleSidbarButton {
          position: fixed;
          top: 2px;
          right: 20px;
          background-color: #182949;
          border-color: #d3a77b;
          color: #182949;
          border: 2px solid #d3a77b;
          border-radius: 10%;
          width: 48px;
          height: 48px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          z-index: 1000;
          transition:
              background-color 0.3s,
              transform 0.3s,
              border-color 0.3s;
      }
      .toggleSidbarButton:hover {
          background-color: #d3a77b;
          border-color: #586c85;
          color: #586c85;
          transform: scale(1.05);
      }

      .toggleSidbarButton:hover svg {
          fill: #344464;
      }

      .toggleSidbarButton svg {
          width: 24px;
          height: 24px;
          fill: #d3a77b;
          transition: fill 0.3s;
      } */
/*
      .bottom-slide {
          position: fixed;
          bottom: -200px;
          left: 0;
          right: 0;
          opacity: 75%;
          background-color: #344464;
          border-color: #d3a77b;
          color: #d3a77b;
          padding: 10px;
          box-shadow: 0 -2px 5px 0x34446442;
          z-index: 999;
          transition: bottom 0.5s ease-in-out;
          display: flex;
          flex-wrap: wrap;
          gap: 10px;
          overflow-x: auto;
          overflow-y: auto;
          max-height: 100px;
          max-width: 90%;
      } */
      .bottom-slide.active {
          bottom: 0;
      }

      .sidebar {
          width: 150px;
          padding: 1px;
          display: none;
          position: fixed;
          top: 0;
          left: -100px;
          height: 100vh;
          background-color: #586c85;
          overflow-y: auto;
          z-index: 999;
          transition: left 0.3s ease;
      }

      .sidebar.active {
          display: block;
          left: 0;
      }
      /********************************/

          input[type="range"] {
          width: 80%; /* تقليل عرض شريط التمرير ليكون أقل من الزر */
          height: 25px;
          background: transparent;
          border: none;
          outline: none;
          -webkit-appearance: none;
          margin: 0 auto; /* توسيط المنزلق */
          display: block; /* لتفعيل التوسيط */
      }

      /* تخصيص شكل شريط التمرير */
      input[type="range"]::-webkit-slider-runnable-track {
          height: 10px; /* تقليل سماكة الشريط */
          background: #586c85;
          width: 90%; /* تصغير عرض الشريط ليكون داخل المربع */
          border-radius: 5px;
          border: 2px solid #d3a77b;
      }

      /* تخصيص زر السحب */
      input[type="range"]::-webkit-slider-thumb {
          -webkit-appearance: none;
          appearance: none;
          width: 20px;
          height: 20px;
          background: #d3a77b;
          border-radius: 50%;
          cursor: pointer;
          margin-top: -3px;
      }

      input[type="range"]::-moz-range-track {
          height: 6px;
          background: #cfbaa5;
          width: 50px;
          border-radius: 5px;
          border: 1px solid #d3a77b;
      }

      input[type="range"]::-moz-range-thumb {
          width: 12px;
          height: 12px;
          background: #d3a77b;
          border-radius: 50%;
          cursor: pointer;
      }


      .icon-button {
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: center;
          background-color: #344464;
          color: #182949;
          border: 2px solid #d3a77b;
          border-radius: 5px;
          padding: 5px;
          margin-bottom: 5px;
          cursor: pointer;
          transition: all 0.3s ease;
          width: 80%;
          height: 40px;
          text-align: center;
      }
      /*
      .icon-button:hover {
          background-color: #d3a77b;
          color: #182949;
          border-color: #182949;
          transform: scale(1.01);
      }
      */

      .controls-grid button,
      .controls-grid {
          width: 1.5vw;
          height: 48px;
      }

      .gradient input {
          width: 24px;
          height: 48px;
      }

      .controls-grid button,
      .controls-grid {
          width: 48px;
          height: 48px;
      }

      .gradient input {
          width: 24px;
          height: 48px;
      }

      .Ground-grid button {
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: center;
          background-color: #182949;
          color: #d3a77b;
          border: 2px solid #d3a77b;
          border-radius: 5px;
          padding: 10px ;
          margin: 10px 10px 5px 5px;
          cursor: pointer;
          transition: all 0.3s ease;
          width: 120px;
          height: 50px;
          text-align: center;
      }
      .Ground-grid button:hover {
          background-color: #d3a77b;
          border-color: #586c85;
          color: #586c85;
          transform: scale(1.05);
      }
      /*

      #btn-toggle-filters {
          position: absolute;
          top: 10px;
          right: 0;
          z-index: 10;
          padding: 5px 11px;
          background: #344464;
          color: #d3a77b;
          border: none;
          border-radius: 3px;
          cursor: pointer;
      }
      /* حاوية أزرار التراجع والاستعادة (تظهر أسفل زر الفلاتر) */
      #undo-redo-container {
          position: absolute;
          top: 45px;
          right: 0;
          display: flex;
          flex-direction: row;
          z-index: 10;
      }
      #undo-redo-container button {
          margin: 2px 0;
          padding: 5px 10px;
          border: none;
          border-radius: 3px;
          cursor: pointer;
          background: #344464;
          color: #d3a77b;
          font-size: 12px;
      }
      /* قائمة أزرار الفلاتر العمودية (على الجانب الأيسر داخل القماش) */
      #filter-buttons-container {
          display: none;
          position: absolute;
          top: 50px;
          left: 0;
          height: calc(100% - 50px);
          background: #344464;
          overflow-y: auto;
          padding: 5px;
          box-sizing: border-box;
          z-index: 6;
          /*display: flex;*/
          flex-direction: column;
          align-items: flex-start;
      }
      #filter-buttons-container button {
      margin: 5px 0;
      padding: 5px 5px;
      border-radius: 3px solid #d3a77b;
      cursor: pointer;
      background: #344464;
      color: #d3a77b;
      font-size: 12px;
      }
      /* شريط الأسفل */
      #bottom-bar {
      position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      background-color: 0x344464;
      padding: 10px;
      text-align: center;
      /*box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1);*/
      z-index: 4;
      }

      #upload-btn,
      #video-upload-btn {
      display: none;
      }
      /* القائمة العائمة للنصوص */
      #floating-menu-text {
      display: none;
      position: absolute;
      top: 10px;
      left: 50%;
      transform: translateX(-50%);
      background: #344464;
      border: 1px solid #d3a77b;
      border-radius: 5px;
      padding: 5px;
      z-index: 5;
      white-space: nowrap;
      overflow-x: auto;
      }
      #floating-menu-text button {
      background: #182949;
      border: none;
      color: #d3a77b;
      width: 25px;
      height: 25px;
      padding: 2px 2px;
      margin: 2px;
      border-radius: 3px;
      cursor: pointer;
      flex-shrink: 0;
      }

      /* القائمة العائمة للصور */
      #floating-menu-image {
      display: none;
      position: absolute;
      top: 10px;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(255, 255, 255, 0.9);
      border: 1px solid #ccc;
      border-radius: 5px;
      padding: 5px;
      z-index: 5;
      white-space: nowrap;
      overflow-x: auto;
      }
      #floating-menu-image button {
      background: #182949;
      border: none;
      color: #d3a77b;
      width: 25px;
      height: 25px;
      padding: 2px 2px;
      margin: 2px;
      border-radius: 3px;
      cursor: pointer;
      flex-shrink: 0;
      }
      /* لوحة الستكرات */
      #stickers-panel {
      width: 100%;
      position: fixed;
      top: 90px;
      left: 50%;
      transform: translateX(-50%);
      background: #344464;
      border: 1px solid #d3a77b;
      color: #d3a77b;
      display: none;
      padding: 1px;
      z-index: 4;
      max-height: 180px;
      overflow-y: auto;
      }
      #stickers-panel .sticker-item {
      border: solid 1px #d3a77b;
      background: #586c85;
      color: #d3a77b;
      width: 30px;
      height: 30px;
      margin: 1px;
      cursor: pointer;
      padding: 1px;
      }


      #font-buttons-container {
      background-color: #182949;
      display: flex;
      gap: 10px;
      overflow-x: auto;
      padding: 5px;
      white-space: nowrap;
      justify-content: center;
      }

      /* زر الخط */
      .font-btn {
        flex: 0 0 auto;
        padding: 5px;
        margin: 2px;
        width: 60px;
        height: 30px;
        cursor: pointer;
        font-size: 14px;
        font-weight: bold;
        color: #efc776;
        border: 1px solid #efc776;
        background-color: #254667;
        transition: background-color 0.3s;
        display: flex;
        align-items: center;
        justify-content: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      /* تغيير لون الزر عند التحويم */
      .font-btn:hover {
      background-color: #3a5a7e;
      }

      /* منتقيات اللون */
      #color-picker,
      #bg-color-picker {
      display: none;
      position: absolute;
      z-index: 10;
      }
      /* تنسيق البار السفلي الذي يحتوي على قائمة الأزرار */
      .animate-buttons {
      position: fixed;
      bottom: 220px;
      left: 0;
      width: 100%;
      background-color: 0xf1f1f1;
      padding: 5px;
      margin: 5px;
      display: none; /* مخفية افتراضياً */
      z-index: 999;
      }
      .animate-buttons ul {
      list-style: none;
      left: 50%;
      margin: 5px;
      padding: 5px;
      display: flex;
      justify-content: center;
      align-items: center;
      }
      /* .animate-buttons li {
      margin: 5px;
      } */
      .animate-buttons button {
      background-color: #182949;
      color: #efc776;
      /*left: 50%;*/
      border: none;
      padding: 5px;
      border-radius: 5px;
      cursor: pointer;
      }

      /* تنسيقات قائمة عرض النماذج */
      #model-list-overlay {
      display: none;
      position: fixed;
      top: 100px;
      left: 10px;
      right: 10px;
      bottom: 100px;
      /*background: rgba(0,0,0,0.5);*/
      z-index: 10;
      overflow-y: auto;
      }
      #model-list-container {
      background: #efc776;
      margin: 5px;
      padding: 5px;
      border-radius: 5px;
      max-width: 80%;
      }
      #model-search {
      width: 90%;
      margin: 5px;
      padding: 5px;
      border-radius: 5px;
      font-size: 16px;
      }
      #model-grid {
      display: grid;
      grid-gap: 20px;
      }
      @media (max-width: 599px) {
      #model-grid {
      grid-template-columns: repeat(1, 1fr);
      }
      }
      @media (max-width: 600px) {
      #model-grid {
      grid-template-columns: repeat(2, 1fr);
      }
      }
      @media (min-width: 601px) and (max-width: 1024px) {
      #model-grid {
      grid-template-columns: repeat(3, 1fr);
      }
      }
      @media (min-width: 1025px) {
      #model-grid {
      grid-template-columns: repeat(4, 1fr);
      }
      }
      .model-card {
          opacity: 80%;
      top: 100px;
      cursor: pointer;
      text-align: center;
      }
      .model-card img {
      width: 80%;
      border-radius: 5px;
      }
.top-bar,
.controls-grid {
display: flex;
flex-direction: row;
align-items: center;
gap: 0.1rem;
flex-wrap: wrap;
}
/* تنسيق البار العلوي */
.top-bar {
position: fixed;
top: 20px;
left: 0;
width: 100%;
height: 50px;
background-color: 0x182949;
color: #efc776;
display: flex;
align-items: center;
justify-content: center;
z-index: 1000;
}
.top-bar button {
background-color: #182949;
color: #d3a77b;
border: solid 2px #d3a77b;
padding: 5px;
margin: 5px;
border-radius: 5px;
cursor: pointer;
}


/* القوائم السفلية المخفية بشكل افتراضي */
.bottom-menu {
position: fixed;
left: 50%;
transform: translateX(-50%);
padding: 5px;
margin: 5px;
margin: 5px;
width: 70%;
height: 50px;
display: none; /* مخفية افتراضيًا */
flex-direction: row;
justify-content: center;
align-items: center;
gap: 5px;
z-index: 99;
}
/* قائمة MediaRecorder تظهر في صف مستقل أعلى */
.bottom-menu.media {
bottom: 60px;
}
/* قائمة ccapture تظهر في صف مستقل أسفل */
.bottom-menu.ccapture {
bottom: 0;
}
button {
padding: 5px;
margin: 5px;
border: solid 2px #d3a77b;
background: #182949;
color: #182949;
cursor: pointer;
border-radius: 5px;
font-size: 14px;
transition: 0.3s;
}

select {
padding: 5px;
margin: 5px;
border: 2px solid #d3a77b;
border-radius: 3px;
font-size: 14px;
background: #182949;
color: #d3a77b;
}

/* التنسيقات الأساسية للحاويات */
#threejs-container, #canvas {
position: fixed;
top: 0;
left: 0;
width: 100%;
height: 100%;
transition: all 0.3s ease;
z-index: 1;
}


/* تنسيقات القائمة السفلية */
.bottom-controls {
position: fixed;
bottom: -100px;
left: 0;
width: 60%;
background: rgba(24, 41, 73, 0.9);
padding: 5px;
margin: 5px;
display: flex;
justify-content: center;
align-items: center;
gap: 10px;
transition: bottom 0.3s ease;
z-index: 1000;
flex-wrap: wrap;
}

.bottom-controls.show {
padding: 5px;
margin: 5px;
bottom: 0;
}

.bottom-controls button {
background: #586c85;
color: #d3a77b;
border: none;
padding: 5px;
margin: 5px;
border-radius: 5px;
cursor: pointer;
transition: all 0.3s ease;
white-space: nowrap;
}

.bottom-controls button:hover {
background: #586c85;
}

/* تنسيقات زر التحكم */
.toggle-controls-btn {
position: fixed;
top: 20px;
right: 70px;
z-index: 1001;
background: #586c85;
color: #d3a77b;
border: none;
padding: 5px;
margin: 5px;
border-radius: 5px;
cursor: pointer;
transition: all 0.3s ease;
}

.toggle-controls-btn:hover {
background: #d3a77b;
}

/* تعديل موضع زر التصدير */
#export-btn {
position: fixed;
top: 20px;
right: 20px;
z-index: 1001;
}
lang-switcher {
margin-bottom: 30px;
}
.lang-dropdown {
    padding: 10px 20px;
    border: 2px solid #d3a77b;
    border-radius: 4px;
    background-color: #182949;
    cursor: pointer;
    font-size: 16px;
}

.lang-dropdown:hover {
    background-color: #182949;
  color: #efc776;
}

.action-btn {
    padding: 12px 24px;
    margin: 10px 5px;
    cursor: pointer;
    background-color: #344464;
    color: #d3a77b;
    border: 2px solid #d3a77b;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.action-btn:hover {
    background-color: #d3a77b;
  color: #182949;
}

[data-tooltip] {
    position: relative;
}

[data-tooltip]:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #182949;
    color: #d3a77b;
    padding: 6px 12px;
    border: 2px solid #d3a77b;
    border-radius: 4px;
    font-size: 14px;
    white-space: nowrap;
    pointer-events: none;
}

         .tooltip {
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            padding: 5px 10px;
            background-color: #182949;
            color: #d3a77b;
             border: 1px solid #d3a77b;
            border-radius: 5px;
            white-space: nowrap;
            font-size: 12px;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .title {
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            padding: 5px 10px;
            background-color: #182949;
            color: #d3a77b;
            border: 1px solid #d3a77b;
            border-radius: 5px;
            white-space: nowrap;
            font-size: 12px;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .button:hover .tooltip {
            opacity: 1;
        }

        .button:hover .title {
            opacity: 1;
        }

/* RTL التنسيقات الخاصة باللغة العربية */
/*
[lang="ar"] body {
    direction: rtl;
    font-family: 'Arial', sans-serif;
}*/

.modal {
  display: none;
  position: fixed;
  z-index: 2000;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  overflow: auto;
  background: rgba(0,0,0,0.5);
  justify-content: center;
  align-items: center;
}
.modal-content {
  background: #fff;
  margin: 10vh auto;
  padding: 20px;
  border-radius: 10px;
  max-width: 400px;
  box-shadow: 0 2px 10px #0002;
  position: relative;
  direction: rtl;
}
.close-modal {
  position: absolute;
  top: 10px;
  left: 10px;
  font-size: 28px;
  color: #d3a77b;
  cursor: pointer;
}

</style>
<script src="js/main.js"></script>
<script src="js/validation.js"></script>
<script src="js/paypal-integration.js"></script>
<script src="js/auth.js"></script>
<script src="arabic-support.js"></script>
<script src="https://www.paypal.com/sdk/js?client-id=AXgPoRNaqSHSwGjkABv89PBIkQxVwz-7ZCX5EoBkkG2UYqTYDhZ9W_3ajWWr17ij30QW7QLLBZIYbYve&vault=true&intent=subscription" data-sdk-integration-source="button-factory"></script>
<style>
/* Registration Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 9999;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.8);
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal-content {
  background-color: #344464;
  margin: 20px;
  max-width: 450px; /* تصغير الحجم */
  width: 70%;
  height: auto;
  border-radius: 10px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
  position: relative;
  padding: 20px;
  color: #fff;
  border: 2px solid #d3a77b;
  animation: slideDown 0.4s ease;
}

@keyframes slideDown {
  from { transform: translateY(-30px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.close-modal {
  position: absolute;
  right: 15px;
  top: 10px;
  color: #d3a77b;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}

.form-group {
  margin-bottom: 20px;
}

label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #d3a77b;
}

input[type="text"],
input[type="email"],
input[type="password"] {
  width: 90%;
  padding: 12px;
  border: 1px solid #d3a77b;
  border-radius: 5px;
  font-size: 16px;
  background-color: #182949;
  color: #fff;
}

button[type="submit"] {
  width: 100%;
  padding: 14px;
  background-color: #d3a77b;
  color: #182949;
  border: none;
  border-radius: 5px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
  font-weight: bold;
}

button[type="submit"]:hover {
  background-color: #c39669;
}

.message {
  margin-top: 15px;
  padding: 10px;
  border-radius: 5px;
  display: none;
}

.success {
  background-color: rgba(46, 204, 113, 0.2);
  color: #2ecc71;
  border: 1px solid #2ecc71;
  display: block;
}

.error {
  background-color: rgba(231, 76, 60, 0.2);
  color: #e74c3c;
  border: 1px solid #e74c3c;
  display: block;
}

.modal-footer {
  margin-top: 20px;
  text-align: center;
  color: #d3a77b;
}

.modal-footer a {
  color: #fff;
  text-decoration: underline;
  cursor: pointer;
}

.subscription-info {
  margin-top: 15px;
  padding: 15px;
  background-color: rgba(52, 152, 219, 0.2);
  border-radius: 5px;
  border: 1px solid #3498db;
  color: #fff;
}

.subscription-options {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
}

.subscription-options label {
  display: inline-flex;
  align-items: center;
  padding: 10px 15px;
  background-color: #344464;
  border: 1px solid #d3a77b;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s;
  min-width: 150px;
}

.subscription-options label:hover {
  background-color: #455576;
}

.subscription-options input[type="radio"] {
  margin-right: 8px;
}

/* تنسيق استجابي للشاشات الصغيرة */
@media (max-width: 768px) {
  .subscription-options > div {
    flex-direction: column;
  }

  .subscription-options label,
  .subscription-options > div > label {
    width: 100% !important;
    margin-bottom: 10px;
  }
}

/* تحسين ظهور صف الاشتراكات */
.subscription-row {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

/* تمييز الخيار المختار */
.subscription-options input[type="radio"]:checked + span {
  font-weight: bold;
  color: #efc776;
}
</style>
</head>
  <body>
   <div id="container"></div>
    <div id="threejs-container"></div>
    <canvas id="canvas"></canvas>

    <!-- أزرار تسجيل الدخول وإنشاء حساب في الهيدر -->
    <div class="auth-buttons" style="position: fixed; top: 20px; left: 20px; z-index: 1000; display: flex; gap: 10px;">
      <!-- زر تسجيل الدخول كأيقونة SVG -->
      <!-- <button id="login-btn" style="background-color: #344464; border: none; border-radius: 50%; width: 40px; height: 40px; display: flex; justify-content: center; align-items: center; cursor: pointer; box-shadow: 0 2px 5px rgba(0,0,0,0.3);">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#d3a77b" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
          <circle cx="12" cy="7" r="4"></circle>
        </svg>
      </button> -->
    </div>

    <!-- Registration Modal with PayPal Integration -->
    <div id="registration-modal" class="modal" style="display: none;">
      <div class="modal-content">
        <span class="close-modal">&times;</span>
        <h2 style="text-align: center; color: #d3a77b;" data-translate="register-title">تسجيل مستخدم جديد</h2>
        <div id="formMessage" class="message"></div>

        <form id="registrationForm" action="register.php" method="post">
          <div class="form-group">
            <label for="fullname" data-translate="fullname-label">الاسم الكامل</label>
            <input type="text" id="fullname" name="fullname" required>
          </div>

          <div class="form-group">
            <label for="email" data-translate="email-label">البريد الإلكتروني</label>
            <input type="email" id="email" name="email" required>
          </div>

          <div class="form-group">
            <label for="mobile" data-translate="mobile-label">رقم الهاتف</label>
            <input type="text" id="mobile" name="mobile" required>
          </div>

          <div class="form-group">
            <label for="password" data-translate="password-label">كلمة المرور</label>
            <input type="password" id="password" name="password" required>
          </div>

          <div class="form-group">
            <label for="confirmPassword" data-translate="confirm-password-label">تأكيد كلمة المرور</label>
            <input type="password" id="confirmPassword" name="confirmPassword" required>
          </div>

          <div class="form-group">
            <label for="subscriptionType" data-translate="subscription-type-label">نوع الاشتراك</label>
            <div class="subscription-options">
              <div class="subscription-row">
                <label style="width: 48%;">
                  <input type="radio" name="plan" value="monthly" checked>
                  <span data-translate="monthly-subscription">اشتراك شهري</span> (5$)
                </label>
                <label style="width: 48%;">
                  <input type="radio" name="plan" value="yearly">
                  <span data-translate="yearly-subscription">اشتراك سنوي</span> (50$)
                </label>
              </div>
              <div class="subscription-row">
                <label style="width: 48%;">
                  <input type="radio" name="plan" value="arab">
                  <span data-translate="arab-subscription">اشتراك العرب</span>
                </label>
                <label style="width: 48%;">
                  <input type="radio" name="plan" value="free">
                  <span data-translate="free-subscription">اشتراك مجاني</span>
                </label>
              </div>
            </div>
          </div>

          <div class="form-group">
            <h3 style="color: #d3a77b; font-size: 16px;" data-translate="complete-subscription">أكمل الاشتراك لمتابعة التسجيل</h3>

            <!-- حاويات أزرار PayPal للاشتراكات -->
            <div id="subscription-containers">
                <!-- الحاوية الأساسية التي ستحتوي على زر PayPal -->
                <div id="subscription-payment-container">
                    <!-- حاوية زر PayPal الموحدة للاشتراك الشهري والسنوي -->
                    <div id="standard-subscription-container" style="display: block;">
                        <!-- الحاوية الشهرية - المعلومات فقط -->
                        <div id="monthly-info" class="subscription-info" style="display: block; background-color: #e6f7ff; padding: 10px; margin-bottom: 10px; border-radius: 5px; text-align: center; border: 1px solid #91d5ff;">
                            <h4 style="margin: 0; color: #096dd9;">الاشتراك الشهري - 5$</h4>
                            <p style="margin: 5px 0 0 0; font-size: 14px; color: #555;">ادفع شهريًا واستمتع بجميع الميزات</p>
                        </div>

                        <!-- الحاوية السنوية - المعلومات فقط -->
                        <div id="yearly-info" class="subscription-info" style="display: none; background-color: #fff1e6; padding: 10px; margin-bottom: 10px; border-radius: 5px; text-align: center; border: 1px solid #ffd591;">
                            <h4 style="margin: 0; color: #d4800a;">الاشتراك السنوي - 50$</h4>
                            <p style="margin: 5px 0 0 0; font-size: 14px; color: #555;">عرض خاص - وفر 17% من قيمة الاشتراك مقارنة بالاشتراك الشهري</p>
                            <div class="subscription-note" style="margin-top: 10px; padding: 5px; text-align: center; color: #d46b08; font-weight: bold;">
                                ★ اشترك لمدة سنة واحصل على مزايا حصرية ★
                            </div>
                        </div>

                        <div style="text-align: center; margin-top: 10px;">
                            <div id="paypal-button-container"></div>
                        </div>
                    </div>

                    <!-- الحاوية العرب - المعلومات فقط -->
                    <div id="arab-info" class="subscription-info" style="display: none; background-color: #f6ffed; padding: 10px; margin-bottom: 10px; border-radius: 5px; text-align: center; border: 1px solid #b7eb8f;">
                        <h4 style="margin: 0; color: #52c41a;">اشتراك العرب - عرض خاص</h4>
                        <p style="margin: 5px 0 0 0; font-size: 14px; color: #555;">اشتراك خاص للمستخدمين العرب بمزايا حصرية وأسعار مناسبة</p>
                        <div class="subscription-note" style="margin-top: 10px; padding: 5px; text-align: center; color: #389e0d; font-weight: bold;">
                            ★ مزايا خاصة للمستخدمين العرب ★
                        </div>
                        <ul style="margin: 10px 0; padding: 0 20px; text-align: right; font-size: 13px; color: #555; list-style-type: disc;">
                            <li>وصول كامل إلى جميع الميزات المتقدمة</li>
                            <li>دعم فني على مدار الساعة باللغة العربية</li>
                            <li>محتوى عربي حصري ومحدث بشكل مستمر</li>
                            <li>تصاميم جاهزة خاصة بالمستخدمين العرب</li>
                        </ul>
                    </div>

                    <!-- الحاوية المجانية - المعلومات فقط -->
                    <div id="free-info" class="subscription-info" style="display: none; background-color: #f9f0ff; padding: 10px; margin-bottom: 10px; border-radius: 5px; text-align: center; border: 1px solid #d3adf7;">
                        <h4 style="margin: 0; color: #722ed1;">الاشتراك المجاني</h4>
                        <p style="margin: 5px 0 0 0; font-size: 14px; color: #555;">تجربة مجانية مع ميزات أساسية</p>
                        <div class="subscription-note" style="margin-top: 10px; padding: 5px; text-align: center; color: #531dab; font-weight: bold;">
                            ★ الاشتراك المجاني المثالي للبدء ★
                        </div>
                        <ul style="margin: 10px 0; padding: 0 20px; text-align: right; font-size: 13px; color: #555; list-style-type: disc;">
                            <li>وصول محدود إلى الميزات الأساسية</li>
                            <li>عدد محدود من المشاريع</li>
                            <li>دعم فني عبر البريد الإلكتروني</li>
                            <li>قم بالترقية في أي وقت للاستمتاع بالميزات الكاملة</li>
                        </ul>
                    </div>

                    <!-- حاوية زر PayPal الموحدة للاشتراك الشهري والسنوي -->
                    <div style="text-align: center; margin-top: 10px;" id="standard-subscription-container">
                        <div id="paypal-button-container"></div>
                    </div>

                    <!-- حاوية زر PayPal لاشتراك العرب -->
                    <div style="text-align: center; margin-top: 10px; display: none;" id="arab-subscription-container">
                        <div id="paypal-button-container-P-59T55805XP961023HNAOIPYI"></div>
                        <script src="https://www.paypal.com/sdk/js?client-id=AXgPoRNaqSHSwGjkABv89PBIkQxVwz-7ZCX5EoBkkG2UYqTYDhZ9W_3ajWWr17ij30QW7QLLBZIYbYve&vault=true&intent=subscription" data-sdk-integration-source="button-factory"></script>
                        <script>
                          paypal.Buttons({
                              style: {
                                  shape: 'rect',
                                  color: 'gold',
                                  layout: 'vertical',
                                  label: 'subscribe'
                              },
                              createSubscription: function(data, actions) {
                                return actions.subscription.create({
                                  /* Creates the subscription */
                                  plan_id: 'P-59T55805XP961023HNAOIPYI'
                                });
                              },
                              onApprove: function(data, actions) {
                                document.getElementById('subscription_id').value = data.subscriptionID;
                                document.getElementById('registerButton').style.display = 'block';
                                alert('تم الاشتراك بنجاح! يمكنك الآن إكمال التسجيل.');
                              }
                          }).render('#paypal-button-container-P-59T55805XP961023HNAOIPYI');
                        </script>
                    </div>

                    <!-- حاوية زر PayPal للاشتراك المجاني -->
                    <div style="text-align: center; margin-top: 10px; display: none;" id="free-subscription-container">
                        <div id="paypal-button-container-P-707238101Y904923HNAOIWGY"></div>
                        <script src="https://www.paypal.com/sdk/js?client-id=AXgPoRNaqSHSwGjkABv89PBIkQxVwz-7ZCX5EoBkkG2UYqTYDhZ9W_3ajWWr17ij30QW7QLLBZIYbYve&vault=true&intent=subscription" data-sdk-integration-source="button-factory"></script>
                        <script>
                          paypal.Buttons({
                              style: {
                                  shape: 'rect',
                                  color: 'gold',
                                  layout: 'vertical',
                                  label: 'subscribe'
                              },
                              createSubscription: function(data, actions) {
                                return actions.subscription.create({
                                  /* Creates the subscription */
                                  plan_id: 'P-707238101Y904923HNAOIWGY'
                                });
                              },
                              onApprove: function(data, actions) {
                                document.getElementById('subscription_id').value = data.subscriptionID;
                                document.getElementById('registerButton').style.display = 'block';
                                alert('تم الاشتراك بنجاح! يمكنك الآن إكمال التسجيل.');
                              }
                          }).render('#paypal-button-container-P-707238101Y904923HNAOIWGY');
                        </script>
                    </div>
                </div>
            </div>

            <!-- زر للاختبار فقط، سيتم إزالته في الإصدار النهائي -->
            <div style="margin-top: 15px; text-align: center;">
              <button type="button" id="testPaymentButton" style="background-color: #28a745; color: white; padding: 10px 15px; border: none; border-radius: 5px; cursor: pointer;" data-translate="payment-test-button">
                اختبار الدفع (للتطوير فقط)
              </button>
            </div>
          </div>

          <!-- Hidden field for subscription ID -->
          <input type="hidden" id="subscription_id" name="subscription_id">

          <div class="form-group">
            <!-- Register button - hidden by default, shown after PayPal subscription -->
            <button type="submit" id="registerButton" style="display: none;" data-translate="register-submit-button">تسجيل</button>
          </div>

          <script>
          // كود لمعالجة نوع الاشتراك وإظهار الأزرار المناسبة
          document.addEventListener('DOMContentLoaded', function() {
            // الحصول على جميع خيارات الاشتراك
            const planRadios = document.querySelectorAll('input[name="plan"]');

            // حاويات معلومات ودفع الاشتراك
            const monthlyInfo = document.getElementById('monthly-info');
            const yearlyInfo = document.getElementById('yearly-info');
            const arabInfo = document.getElementById('arab-info');
            const freeInfo = document.getElementById('free-info');

            const standardContainer = document.getElementById('standard-subscription-container');
            const arabContainer = document.getElementById('arab-subscription-container');
            const freeContainer = document.getElementById('free-subscription-container');

            // وظيفة لإخفاء جميع الكروت والحاويات
            function hideAllContainers() {
              // إخفاء كل كروت المعلومات
              monthlyInfo.style.display = 'none';
              yearlyInfo.style.display = 'none';
              arabInfo.style.display = 'none';
              freeInfo.style.display = 'none';

              // إخفاء كل حاويات الدفع
              standardContainer.style.display = 'none';
              arabContainer.style.display = 'none';
              freeContainer.style.display = 'none';
            }

            // إضافة مستمع حدث لكل خيار اشتراك
            planRadios.forEach(radio => {
              radio.addEventListener('change', function() {
                // إخفاء جميع الحاويات أولاً
                hideAllContainers();

                // إظهار الحاوية المناسبة حسب الخيار المحدد
                switch(this.value) {
                  case 'monthly':
                    monthlyInfo.style.display = 'block';
                    standardContainer.style.display = 'block';
                    break;
                  case 'yearly':
                    yearlyInfo.style.display = 'block';
                    standardContainer.style.display = 'block';
                    break;
                  case 'arab':
                    // إظهار فقط معلومات اشتراك العرب
                    arabInfo.style.display = 'block';
                    arabContainer.style.display = 'block';
                    // التأكد من إخفاء معلومات الاشتراكات الأخرى
                    yearlyInfo.style.display = 'none';
                    monthlyInfo.style.display = 'none';
                    standardContainer.style.display = 'none';
                    break;
                  case 'free':
                    // إظهار فقط معلومات الاشتراك المجاني
                    freeInfo.style.display = 'block';
                    freeContainer.style.display = 'block';
                    // التأكد من إخفاء معلومات الاشتراكات الأخرى
                    yearlyInfo.style.display = 'none';
                    monthlyInfo.style.display = 'none';
                    standardContainer.style.display = 'none';
                    break;
                }
              });
            });

            // التحقق من الاشتراك المحدد عند تحميل الصفحة
            const selectedPlan = document.querySelector('input[name="plan"]:checked');
            if (selectedPlan) {
              // إخفاء كل الحاويات أولاً
              hideAllContainers();

              // إظهار الحاوية المناسبة
              switch(selectedPlan.value) {
                case 'monthly':
                  monthlyInfo.style.display = 'block';
                  standardContainer.style.display = 'block';
                  break;
                case 'yearly':
                  yearlyInfo.style.display = 'block';
                  standardContainer.style.display = 'block';
                  break;
                case 'arab':
                  // إظهار فقط معلومات اشتراك العرب
                  arabInfo.style.display = 'block';
                  arabContainer.style.display = 'block';
                  // التأكد من إخفاء معلومات الاشتراكات الأخرى
                  yearlyInfo.style.display = 'none';
                  monthlyInfo.style.display = 'none';
                  standardContainer.style.display = 'none';
                  break;
                case 'free':
                  // إظهار فقط معلومات الاشتراك المجاني
                  freeInfo.style.display = 'block';
                  freeContainer.style.display = 'block';
                  // التأكد من إخفاء معلومات الاشتراكات الأخرى
                  yearlyInfo.style.display = 'none';
                  monthlyInfo.style.display = 'none';
                  standardContainer.style.display = 'none';
                  break;
              }
            } else {
              // إذا لم يكن هناك اختيار محدد، يتم عرض الاشتراك الشهري كافتراضي
              monthlyInfo.style.display = 'block';
              standardContainer.style.display = 'block';
            }

            // تهيئة زر PayPal الخاص بالاختبار (للتطوير فقط)
            document.getElementById('testPaymentButton').addEventListener('click', function() {
              document.getElementById('registerButton').style.display = 'block';
            });
          });
          </script>
        </form>

        <div class="modal-footer">
          <span data-translate="have-account">لديك حساب بالفعل؟</span>
          <a id="login-link" data-translate="login-link" style="color: #d3a77b; text-decoration: underline; cursor: pointer; margin-right: 5px;">تسجيل الدخول</a>
        </div>
      </div>
    </div>

    <!-- Login Modal (Main form displayed by default) -->
    <div id="login-modal" class="modal" style="display: none; flex-direction: column; justify-content: center; align-items: center;">
      <div class="modal-content">
        <span class="close-modal">&times;</span>
        <h2 style="text-align: center; color: #d3a77b;" data-translate="login-title">تسجيل الدخول</h2>
        <div id="loginFormMessage" class="message"></div>

        <form id="loginForm">
          <div class="form-group">
            <label for="loginEmail" data-translate="email-label">البريد الإلكتروني</label>
            <input type="email" id="loginEmail" name="loginEmail" required>
          </div>

          <div class="form-group">
            <label for="loginPassword" data-translate="password-label">كلمة المرور</label>
            <input type="password" id="loginPassword" name="loginPassword" required>
          </div>

          <div class="form-group">
            <button type="submit" data-translate="login-submit-button">دخول</button>
          </div>
        </form>
        <div style="text-align: left; margin-top: 10px;">
          <a href="reset-password.html" style="color: #d3a77b; text-decoration: none; font-size: 14px;">نسيت كلمة المرور؟</a>
        </div>

        <div class="form-group" style="margin-top: 20px; text-align: center">
          <!-- <p style="margin-bottom: 10px; color: #d3a77b">
            أو تسجيل الدخول باستخدام
          </p> -->
          <button
            id="google-signin"
            type="button"
            style="
              display: flex;
              align-items: center;
              justify-content: center;
              width: 100%;
              padding: 10px;
              background-color: white;
              color: #444;
              border: 1px solid #ddd;
              border-radius: 5px;
              font-size: 16px;
              cursor: pointer;
              margin: 0 auto;
            "
          >
            <svg
              style="margin-right: 10px"
              width="18"
              height="18"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 48 48"
            >
              <path
                fill="#EA4335"
                d="M24 9.5c3.54 0 6.71 1.22 9.21 3.6l6.85-6.85C35.9 2.38 30.47 0 24 0 14.62 0 6.51 5.38 2.56 13.22l7.98 6.19C12.43 13.72 17.74 9.5 24 9.5z"
              />
              <path
                fill="#4285F4"
                d="M46.98 24.55c0-1.57-.15-3.09-.38-4.55H24v9.02h12.94c-.58 2.96-2.26 5.48-4.78 7.18l7.73 6c4.51-4.18 7.09-10.36 7.09-17.65z"
              />
              <path
                fill="#FBBC05"
                d="M10.53 28.59c-.48-1.45-.76-2.99-.76-4.59s.27-3.14.76-4.59l-7.98-6.19C.92 16.46 0 20.12 0 24c0 3.88.92 7.54 2.56 10.78l7.97-6.19z"
              />
              <path
                fill="#34A853"
                d="M24 48c6.48 0 11.93-2.13 15.89-5.81l-7.73-6c-2.15 1.45-4.92 2.3-8.16 2.3-6.26 0-11.57-4.22-13.47-9.91l-7.98 6.19C6.51 42.62 14.62 48 24 48z"
              />
              <path fill="none" d="M0 0h48v48H0z" />
            </svg>
            تسجيل الدخول باستخدام Google
          </button>
        </div>

        <div class="modal-footer" style="text-align: center; margin-top: 20px;">
          <button id="register-button" class="register-btn" style="background-color: #d3a77b; color: #182949; border: none; border-radius: 5px; padding: 10px 20px; font-size: 14px; cursor: pointer; width: 100%; box-shadow: 0 2px 5px rgba(0,0,0,0.3);" data-translate="register-button">التسجيل وإنشاء حساب جديد</button>
        </div>
      </div>
    </div>

    <!-- التاب العلوي -->
    <div class="top-bar">

      <a class="lang-switcher">
        <select id="languageSelector" class="lang-dropdown">
        <option value="auto"  data-i18n-tooltip="lang-auto">En</option>
        <option value="ar"  data-i18n-tooltip="lang-ar">Ar</option>
        <option value="en"  data-i18n-tooltip="lang-en">En</option>
        <option value="es"  data-i18n-tooltip="lang-es">Es</option>
        <option value="fr"  data-i18n-tooltip="lang-fr">Fr</option>
        <option value="zh"  data-i18n-tooltip="lang-zh">Zh</option>
        <option value="ru"  data-i18n-tooltip="lang-ru">Ru</option>
        <option value="hi"  data-i18n-tooltip="lang-hi">Hi</option>
      </select>
      </a>
      <button id="login-btn" class="action-btn" style="margin-right: 10px;" onclick="showRegistrationModal();"><svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#d3a77b"><path d="M480-480q-66 0-113-47t-47-113q0-66 47-113t113-47q66 0 113 47t47 113q0 66-47 113t-113 47ZM160-160v-112q0-34 17.5-62.5T224-378q62-31 126-46.5T480-440q66 0 130 15.5T736-378q29 15 46.5 43.5T800-272v112H160Zm80-80h480v-32q0-11-5.5-20T700-306q-54-27-109-40.5T480-360q-56 0-111 13.5T260-306q-9 5-14.5 14t-5.5 20v32Zm240-320q33 0 56.5-23.5T560-640q0-33-23.5-56.5T480-720q-33 0-56.5 23.5T400-640q0 33 23.5 56.5T480-560Zm0-80Zm0 400Z"/></svg> </button>

      <button id="toggle-sidebar-button" class="toggleSidbarButton" data-i18n-tooltip="toggle-sidebar-button">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          height="24px"
          viewBox="0 -960 960 960"
          width="24px"
          fill="#d3a77b"
        >
          <path
            d="m422-232 207-248H469l29-227-185 267h139l-30 208ZM320-80l40-280H160l360-520h80l-40 320h240L400-80h-80Zm151-390Z"
          />
        </svg>
      </button>

      <button onclick="toggleExportMenus()" data-i18n-tooltip="export-button">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          height="30px"
          viewBox="0 -960 960 960"
          width="30px"
          fill="#d3a77b"
        >
          <path
            d="M146.67-160q-27 0-46.84-19.83Q80-199.67 80-226.67v-506.66q0-27 19.83-46.84Q119.67-800 146.67-800h506.66q27 0 46.84 19.83Q720-760.33 720-733.33V-530l160-160v420L720-430v203.33q0 27-19.83 46.84Q680.33-160 653.33-160H146.67Zm0-66.67h506.66v-506.66H146.67v506.66Zm0 0v-506.66 506.66Z"
          />
        </svg>
      </button>
      <button id="toggleAnimateButton" data-i18n-tooltip="animate-button">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          height="30px"
          viewBox="0 -960 960 960"
          width="30px"
          fill="#d3a77b"
        >
          <path
            d="M315.33-289.33q-27.5 0-47.08-19.59-19.58-19.58-19.58-47.08v-248q0-27.5 19.58-47.08 19.58-19.59 47.08-19.59h43.34L392-704h178l33.33 33.33h43.34q27.5 0 47.08 19.59 19.58 19.58 19.58 47.08v248q0 27.5-19.58 47.08-19.58 19.59-47.08 19.59H315.33Zm0-66.67h331.34v-248H315.33v248Zm165.45-56.67q27.55 0 47.05-19.61 19.5-19.62 19.5-47.17t-19.61-47.05Q508.1-546 480.55-546t-47.05 19.62Q414-506.76 414-479.22q0 27.55 19.62 47.05 19.62 19.5 47.16 19.5Zm-120.11-532q29.56-8.43 59.56-11.88 30-3.45 60.44-3.45 93.33 0 177.16 33.5 83.84 33.5 148.34 92.79 64.5 59.28 105.5 139.5 41 80.21 49 172.88H894q-6.33-76-39.33-141.84-33-65.83-84.84-115.33Q718-828 650.33-858.33q-67.66-30.34-143.66-35L578-822l-47.33 47.33-170-170ZM600-15.33Q570.6-6.9 540.77-3.45 510.93 0 480.67 0q-94 0-177.5-33.5t-148.21-92.72q-64.7-59.21-105.83-139.33Q8-345.67-.67-438.67h67.28q6.72 76 39.56 141.84Q139-231 190.83-181.5q51.84 49.5 119.5 79.83Q378-71.33 454-66.67L382.67-138 430-185.33l170 170ZM481.33-480Z"
          />
        </svg>
      </button>
    </div>

    <!-- قائمة MediaRecorder (صف مستقل) -->
    <div class="bottom-menu media" id="mediaMenu">
      <select id="export-format">
        <option value="png">PNG</option>
        <option value="jpg">JPG</option>
        <option value="webm" class="subscriber-only">WEBM</option>
        <option value="mp4" class="subscriber-only">MP4</option>
      </select>
      <select id="export-dimensions">
        <!-- أبعاد عامة -->
        <option value="1920x1080">1920x1080 (HD)</option>
        <option value="1280x720">1280x720 (HD)</option>
        <!-- أبعاد الإنستغرام -->
        <option value="1080x1920">1080x1920</option>
        <option value="1080x1080">1080x1080</option>
        <option value="1080x1350">1080x1350</option>
        <option value="1080x566">1080x566</option>
        <!-- أبعاد فيسبوك -->
        <option value="1200x630">1200x630</option>
        <!-- أبعاد تويتر -->
        <option value="1600x900">1600x900</option>
        <!-- أبعاد لينكدإن -->
        <option value="1200x627">1200x627</option>
        <!-- أبعاد يوتيوب -->
        <option value="1280x720">1280x720</option>
      </select>
      <button onclick="exportMedia()">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          height="24px"
          viewBox="0 -960 960 960"
          width="24px"
          fill="#d3a77b"
        >
          <path
            d="M480-320 280-520l56-58 104 104v-326h80v326l104-104 56 58-200 200ZM240-160q-33 0-56.5-23.5T160-240v-120h80v120h480v-120h80v120q0 33-23.5 56.5T720-160H240Z"
          />
        </svg>
      </button>
    </div>
    <div class="animate-buttons" id="animateButtons" data-i18n-tooltip="animate-button">
      <ul>
        <li>
          <button id="btn-model-rotate" class="animate-bar" data-i18n-tooltip="btn-model-rotate">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              height="24px"
              viewBox="0 -960 960 960"
              width="24px"
              fill="#d3a77b"
            >
              <path
                d="M440-80q-50-5-96-24.5T256-156l56-58q29 21 61.5 34t66.5 18v82Zm80 0v-82q104-15 172-93.5T760-438q0-117-81.5-198.5T480-718h-8l64 64-56 56-160-160 160-160 56 58-62 62h6q75 0 140.5 28.5t114 77q48.5 48.5 77 114T840-438q0 137-91 238.5T520-80ZM198-214q-32-42-51.5-88T122-398h82q5 34 18 66.5t34 61.5l-58 56Zm-76-264q6-51 25-98t51-86l58 56q-21 29-34 61.5T204-478h-82Z"
              />
            </svg>
            <span></span>
          </button>
        </li>
        <li>
          <button id="btn-model-reverse" class="animate-bar" data-i18n-tooltip="btn-model-reverse">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              height="24px"
              viewBox="0 -960 960 960"
              width="24px"
              fill="#d3a77b"
            >
              <path
                d="M522-80v-82q34-5 66.5-18t61.5-34l56 58q-42 32-88 51.5T522-80Zm-80 0Q304-98 213-199.5T122-438q0-75 28.5-140.5t77-114q48.5-48.5 114-77T482-798h6l-62-62 56-56 160 160-160 160-56-56 64-64h-8q-117 0-198.5 81.5T202-438q0 104 68 182.5T442-162v82Zm322-134-58-56q21-29 34-61.5t18-66.5h82q-5 50-24.5 96T764-214Zm76-264h-82q-5-34-18-66.5T706-606l58-56q32 39 51 86t25 98Z"
              />
            </svg>
            <span></span>
          </button>
        </li>
        <li>
          <button id="btn-scene-rotate" class="animate-bar" data-i18n-tooltip="btn-scene-rotate">
            <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#d3a77b"><path d="m360-160-56-56 70-72q-128-17-211-70T80-480q0-83 115.5-141.5T480-680q169 0 284.5 58.5T880-480q0 62-66.5 111T640-296v-82q77-20 118.5-49.5T800-480q0-32-85.5-76T480-600q-149 0-234.5 44T160-480q0 24 51 57.5T356-372l-52-52 56-56 160 160-160 160 56 56 62-62h6q75 0 140.5 28.5t114 77q48.5 48.5 77 114T840-438q0 137-91 238.5T520-80Z"/></svg>
            <span></span>
          </button>
        </li>
        <li>
          <button id="btn-model-swing" class="animate-bar" data-i18n-tooltip="btn-model-swing">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              height="24px"
              viewBox="0 -960 960 960"
              width="24px"
              fill="#d3a77b"
            >
              <path
                d="M160-160v-80h110l-16-14q-52-46-73-105t-21-119q0-111 66.5-197.5T400-790v84q-72 26-116 88.5T240-478q0 45 17 87.5t53 78.5l10 10v-98h80v240H160Zm400-10v-84q72-26 116-88.5T720-482q0-45-17-87.5T650-648l-10-10v98h-80v-240h240v80H690l16 14q49 49 71.5 106.5T800-482q0 111-66.5 197.5T560-170Z"
              />
            </svg>
            <span></span>
          </button>
        </li>
        <li>
          <button id="btn-model-hover" class="animate-bar" data-i18n-tooltip="btn-model-hover">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              height="24px"
              viewBox="0 -960 960 960"
              width="24px"
              fill="#d3a77b"
            >
              <path
                d="M320-160q-117 0-198.5-81.5T40-440q0-107 70.5-186.5T287-718l-63-66 56-56 160 160-160 160-56-57 59-59q-71 14-117 69t-46 127q0 83 58.5 141.5T320-240h120v80H320Zm200-360v-280h360v280H520Zm0 360v-280h360v280H520Zm80-80h200v-120H600v120Z"
              />
            </svg>
            <span></span>
          </button>
        </li>
        <li>
          <button id="btn-model-jump" class="animate-bar" data-i18n-tooltip="btn-model-jump">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              height="24px"
              viewBox="0 -960 960 960"
              width="24px"
              fill="#d3a77b"
            >
              <path
                d="m280-120-56-56 63-66q-106-12-176.5-91.5T40-520q0-117 81.5-198.5T320-800h120v80H320q-83 0-141.5 58.5T120-520q0 72 46 127t117 69l-59-59 56-57 160 160-160 160Zm240-40v-280h360v280H520Zm0-360v-280h360v280H520Zm80-80h200v-120H600v120Z"
              />
            </svg>
            <span></span>
          </button>
        </li>
      </ul>
    </div>

    <div id="image-editor-container">
      <div id="floating-menu-text">
        <button id="btn-bring-front"  data-i18n-tooltip="btn-bring-front"> <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#d3a77b"><path d="M360-280q-33 0-56.5-23.5T280-360v-400q0-33 23.5-56.5T360-840h400q33 0 56.5 23.5T840-760v400q0 33-23.5 56.5T760-280H360Zm0-80h400v-400H360v400ZM200-200v80q-33 0-56.5-23.5T120-200h80Zm-80-80v-80h80v80h-80Zm0-160v-80h80v80h-80Zm0-160v-80h80v80h-80Zm160 480v-80h80v80h-80Zm160 0v-80h80v80h-80Zm160 0v-80h80v80h-80Z"/></svg></button>
        <button id="btn-send-back"  data-i18n-tooltip="btn-send-back"> <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#d3a77b"><path d="M200-120q-33 0-56.5-23.5T120-200v-480h80v480h480v80H200Zm160-240v80q-33 0-56.5-23.5T280-360h80Zm-80-80v-80h80v80h-80Zm0-160v-80h80v80h-80Zm80-160h-80q0-33 23.5-56.5T360-840v80Zm80 480v-80h80v80h-80Zm0-480v-80h80v80h-80Zm160 0v-80h80v80h-80Zm0 480v-80h80v80h-80Zm160-480v-80q33 0 56.5 23.5T840-760h-80Zm0 400h80q0 33-23.5 56.5T760-280v-80Zm0-80v-80h80v80h-80Zm0-160v-80h80v80h-80Z"/></svg></button>
        <button id="btn-copy"  data-i18n-tooltip="btn-copy"><svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#d3a77b"><path d="M360-240q-33 0-56.5-23.5T280-320v-480q0-33 23.5-56.5T360-880h360q33 0 56.5 23.5T800-800v480q0 33-23.5 56.5T720-240H360Zm0-80h360v-480H360v480ZM200-80q-33 0-56.5-23.5T120-160v-560h80v560h440v80H200Zm160-240v-480 480Z"/></svg></button>
        <button id="btn-delete"  data-i18n-tooltip="btn-delete"><svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#d3a77b"><path d="M280-120q-33 0-56.5-23.5T200-200v-520h-40v-80h200v-40h240v40h200v80h-40v520q0 33-23.5 56.5T680-120H280Zm400-600H280v520h400v-520ZM360-280h80v-360h-80v360Zm160 0h80v-360h-80v360ZM280-720v520-520Z"/></svg></button>
        <button id="btn-text-dir" data-i18n-tooltip="btn-text-dir"><svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#d3a77b"><path d="M120-120v-80h720v80H120Zm0-160v-80h480v80H120Zm0-160v-80h720v80H120Zm0-160v-80h480v80H120Zm0-160v-80h720v80H120Z"/></svg></button>
        <button id="btn-text-color" data-i18n-tooltip="btn-text-color"><svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#d3a77b"><path d="m247-904 57-56 343 343q23 23 23 57t-23 57L457-313q-23 23-57 23t-57-23L153-503q-23-23-23-57t23-57l190-191-96-96Zm153 153L209-560h382L400-751Zm360 471q-33 0-56.5-23.5T680-360q0-21 12.5-45t27.5-45q9-12 19-25t21-25q11 12 21 25t19 25q15 21 27.5 45t12.5 45q0 33-23.5 56.5T760-280ZM80 0v-160h800V0H80Z"/></svg></button>
        <button id="btn-font" data-i18n-tooltip="btn-font"><svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#d3a77b"><path d="M280-160v-520H80v-120h520v120H400v520H280Zm360 0v-320H520v-120h360v120H760v320H640Z"/></svg></button>
      </div>
      <!-- القائمة العائمة للصور -->
      <div id="floating-menu-image">
        <button id="btn-img-bring-front" data-i18n-tooltip="btn-bring-front"> <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#d3a77b"><path d="M360-280q-33 0-56.5-23.5T280-360v-400q0-33 23.5-56.5T360-840h400q33 0 56.5 23.5T840-760v400q0 33-23.5 56.5T760-280H360Zm0-80h400v-400H360v400ZM200-200v80q-33 0-56.5-23.5T120-200h80Zm-80-80v-80h80v80h-80Zm0-160v-80h80v80h-80Zm0-160v-80h80v80h-80Zm160 480v-80h80v80h-80Zm160 0v-80h80v80h-80Zm160 0v-80h80v80h-80Z"/></svg></button>
        <button id="btn-img-send-back" data-i18n-tooltip="btn-send-back"> <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#d3a77b"><path d="M200-120q-33 0-56.5-23.5T120-200v-480h80v480h480v80H200Zm160-240v80q-33 0-56.5-23.5T280-360h80Zm-80-80v-80h80v80h-80Zm0-160v-80h80v80h-80Zm80-160h-80q0-33 23.5-56.5T360-840v80Zm80 480v-80h80v80h-80Zm0-480v-80h80v80h-80Zm160 0v-80h80v80h-80Zm0 480v-80h80v80h-80Zm160-480v-80q33 0 56.5 23.5T840-760h-80Zm0 400h80q0 33-23.5 56.5T760-280v-80Zm0-80v-80h80v80h-80Zm0-160v-80h80v80h-80Z"/></svg></button>
        <button id="btn-img-copy" data-i18n-tooltip="btn-copy"> <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#d3a77b"><path d="M360-240q-33 0-56.5-23.5T280-320v-480q0-33 23.5-56.5T360-880h360q33 0 56.5 23.5T800-800v480q0 33-23.5 56.5T720-240H360Zm0-80h360v-480H360v480ZM200-80q-33 0-56.5-23.5T120-160v-560h80v560h440v80H200Zm160-240v-480 480Z"/></svg> نسخ</button>
        <button id="btn-delete-image" data-i18n-tooltip=" btn-delete">  <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#d3a77b"><path d="M280-120q-33 0-56.5-23.5T200-200v-520h-40v-80h200v-40h240v40h200v80h-40v520q0 33-23.5 56.5T680-120H280Zm400-600H280v520h400v-520ZM360-280h80v-360h-80v360Zm160 0h80v-360h-80v360ZM280-720v520-520Z"/></svg> </button>
      </div>

      <canvas id="3dcanvas"></canvas>
      <div id="font-buttons-container">
        <button
          class="font-btn tajawal-regular"
          data-font="Tajawal"
          style="font-family: 'Tajawal', sans-serif"
        >
          تجوال
        </button>
        <button
          class="font-btn almarai-regular"
          data-font="Almarai"
          style="font-family: 'Almarai', sans-serif"
        >
          مرايا
        </button>
        <button
          class="font-btn amiri-regular"
          data-font="Amiri"
          style="font-family: 'Amiri', serif"
        >
          أميري
        </button>
        <button
          class="font-btn noto-kufi-arabic-regular"
          data-font="Noto Kufi Arabic"
          style="font-family: 'Noto Kufi Arabic', sans-serif"
        >
          الكوفة
        </button>
        <button
          class="font-btn changa-regular"
          data-font="Changa"
          style="font-family: 'Changa', sans-serif"
        >
          تغيير
        </button>
        <button
          class="font-btn lalezar-regular"
          data-font="Lalezar"
          style="font-family: 'Lalezar', system-ui"
        >
          ليزر
        </button>
        <button
          class="font-btn mada-regular"
          data-font="Mada"
          style="font-family: 'Mada', sans-serif"
        >
          مدى
        </button>
        <button
          class="font-btn rakkas-regular"
          data-font="Rakkas"
          style="font-family: 'Rakkas', serif"
        >
          رقاص
        </button>
        <button
          class="font-btn reem-kufi-regular"
          data-font="Reem Kufi"
          style="font-family: 'Reem Kufi', sans-serif"
        >
          ريم
        </button>
        <button
          class="font-btn lateef-regular"
          data-font="Lateef"
          style="font-family: 'Lateef', serif"
        >
          لطيف
        </button>
        <button
          class="font-btn ruwudu-regular"
          data-font="Ruwudu"
          style="font-family: 'Ruwudu', serif"
        >
          روضة
        </button>
        <button
          class="font-btn badeen-display-regular"
          data-font="Badeen Display"
          style="font-family: 'Badeen Display', system-ui"
        >
          بدين
        </button>
        <button
          class="font-btn cairo-regular"
          data-font="Cairo"
          style="font-family: 'Cairo', sans-serif"
        >
          القاهرة
        </button>
        <button
          class="font-btn kufam-regular"
          data-font="Kufam"
          style="font-family: 'Kufam', sans-serif"
        >
          كوفام
        </button>
        <button
          class="font-btn aref-ruqaa-regular"
          data-font="Aref Ruqaa"
          style="font-family: 'Aref Ruqaa', serif"
        >
          رقعة
        </button>
        <button
          class="font-btn fustat-regular"
          data-font="Fustat-Regular"
          style="font-family: 'Fustat-Regular', sans-serif"
        >
          فتات
        </button>
        <button
          class="font-btn zain-regular"
          data-font="Zain"
          style="font-family: 'Zain', sans-serif"
        >
          زين
        </button>
        <button
          class="font-btn alkalami-regular"
          data-font="Alkalami"
          style="font-family: 'Alkalami', serif"
        >
          الكلمي
        </button>
        <button
          class="font-btn katibeh-regular"
          data-font="Katibeh"
          style="font-family: 'Katibeh', serif"
        >
          كتيبة
        </button>
        <button
          class="font-btn cairo-play-regular"
          data-font="Cairo Play"
          style="font-family: 'Cairo Play', sans-serif"
        >
          كايرو
        </button>
        <button
          class="font-btn beiruti-regular"
          data-font="Beiruti"
          style="font-family: 'Beiruti', sans-serif"
        >
          بيروت
        </button>
        <button
          class="font-btn qahiri-regular"
          data-font="Qahiri"
          style="font-family: 'Qahiri', sans-serif"
        >
          قاهري
        </button>
        <button
          class="font-btn marhey-regular"
          data-font="Marhey"
          style="font-family: 'Marhey', sans-serif"
        >
          مرح
        </button>
        <button
          class="font-btn amiri-quran-regular"
          data-font="Amiri Quran"
          style="font-family: 'Amiri Quran', serif"
        >
          اميري
        </button>
        <button
          class="font-btn oi-regular"
          data-font="Oi"
          style="font-family: 'Oi', serif"
        >
          مرحي
        </button>
        <button
          class="font-btn aref-ruqaa-ink-regular"
          data-font="Aref Ruqaa Ink"
          style="font-family: 'Aref Ruqaa Ink', serif"
        >
          رقعه
        </button>
        <button
          class="font-btn reem-kufi-fun-regular"
          data-font="Reem Kufi Fun"
          style="font-family: 'Reem Kufi Fun', sans-serif"
        >
          كوفي
        </button>
        <button
          class="font-btn vibes-regular"
          data-font="Vibes"
          style="font-family: 'Vibes', system-ui"
        >
          فايب
        </button>
      </div>
    </div>


    <div id="bottom-bar">
      <button id="add-images-btn" data-i18n-tooltip="add-images-btn">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          height="24px"
          viewBox="0 -960 960 960"
          width="24px"
          fill="#d3a77b"
        >
          <path
            d="M480-260q75 0 127.5-52.5T660-440q0-75-52.5-127.5T480-620q-75 0-127.5 52.5T300-440q0 75 52.5 127.5T480-260Zm0-80q-42 0-71-29t-29-71q0-42 29-71t71-29q42 0 71 29t29 71q0 42-29 71t-71 29ZM160-120q-33 0-56.5-23.5T80-200v-480q0-33 23.5-56.5T160-760h126l74-80h240l74 80h126q33 0 56.5 23.5T880-680v480q0 33-23.5 56.5T800-120H160Zm0-80h640v-480H638l-73-80H395l-73 80H160v480Zm320-240Z"
          /></svg
        ><span class="bottomBar"></span>
      </button>
      <button id="add-text-btn" data-i18n-tooltip="add-text-btn">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          height="24px"
          viewBox="0 -960 960 960"
          width="24px"
          fill="#d3a77b"
        >
          <path d="M420-160v-520H200v-120h560v120H540v520H420Z" /></svg
        ><span class="bottomBar"></span>
      </button>
      <button id="add-stickers-btn" data-i18n-tooltip="add-stickers-btn">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          height="24px"
          viewBox="0 -960 960 960"
          width="24px"
          fill="#d3a77b"
        >
          <path
            d="M40-40v-240h80v-400H40v-240h240v80h400v-80h240v240h-80v400h80v240H680v-80H280v80H40Zm240-160h400v-80h80v-400h-80v-80H280v80h-80v400h80v80Zm32-120 136-360h64l136 360h-62l-32-92H408l-32 92h-64Zm114-144h108l-52-150h-4l-52 150ZM120-760h80v-80h-80v80Zm640 0h80v-80h-80v80Zm0 640h80v-80h-80v80Zm-640 0h80v-80h-80v80Zm80-640Zm560 0Zm0 560Zm-560 0Z"
          /></svg
        ><span class="bottomBar"></span>
      </button>
      <button id="toggle-canvas-btn" data-i18n-tooltip="toggle-canvas-btn">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          height="24px"
          viewBox="0 -960 960 960"
          width="24px"
          fill="#d3a77b"
        >
          <path
            d="M200-120q-33 0-56.5-23.5T120-200v-560q0-33 23.5-56.5T200-840h560q33 0 56.5 23.5T840-760v560q0 33-23.5 56.5T760-120H200Zm0-80h560v-480H200v480Zm280-80q-82 0-146.5-44.5T240-440q29-71 93.5-115.5T480-600q82 0 146.5 44.5T720-440q-29 71-93.5 115.5T480-280Zm0-60q56 0 102-26.5t72-73.5q-26-47-72-73.5T480-540q-56 0-102 26.5T306-440q26 47 72 73.5T480-340Zm0-100Zm0 60q25 0 42.5-17.5T540-440q0-25-17.5-42.5T480-500q-25 0-42.5 17.5T420-440q0 25 17.5 42.5T480-380Z"
          /></svg
        ><span class="bottomBar"></span>
      </button>
      <button id="btn-show-models" data-i18n-tooltip="btn-show-models">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          height="24px"
          viewBox="0 -960 960 960"
          width="24px"
          fill="#d3a77b"
        >
          <path
            d="M80-140v-320h320v320H80Zm80-80h160v-160H160v160Zm60-340 220-360 220 360H220Zm142-80h156l-78-126-78 126ZM863-42 757-148q-21 14-45.5 21t-51.5 7q-75 0-127.5-52.5T480-300q0-75 52.5-127.5T660-480q75 0 127.5 52.5T840-300q0 26-7 50.5T813-204L919-98l-56 56ZM660-200q42 0 71-29t29-71q0-42-29-71t-71-29q-42 0-71 29t-29 71q0 42 29 71t71 29ZM320-380Zm120-260Z"
          /></svg
        ><span class="bottomBar"></span>
      </button>

      <input type="file" id="upload-btn" multiple accept="image/*" />
    </div>
    <!-- لوحة عرض النماذج -->
    <div id="model-list-overlay">
      <div id="model-list-container">
        <input type="text" id="model-search" data-i18n-key="model-search" placeholder="Search for a model..." />
        <div id="model-grid"></div>
      </div>
    </div>
    <!-- لوحة الستكرات -->
    <div id="stickers-panel"></div>

    <!-- منتقيات اللون -->
    <input type="color" id="color-picker" />
    <input type="color" id="bg-color-picker" />

    <div class="sidebar">
      <div class="Ground-grid">
        <span class="range-input" data-i18n-key="Ground-grid">Ground-grid</span>
        <input type="checkbox" id="toggleVisibility" checked />
        <button id="toggler" data-i18n-key="toggler">Ground</button>
      </div>
      <!-- استبدال جميع عناصر button بـ div مع تصحيح الهيكل -->
      <div class="range-input">
        <label for="light-vertical" class="range-label"
        data-i18n-key="light-vertical">light-vertical</label
        >
        <input
          type="range"
          id="light-vertical"
          min="-10"
          max="10"
          step="0.1"
          value="0"
        />
      </div>

      <div class="range-input">
        <label for="table-light" class="range-label"data-i18n-key="table-light">table-light</label
            >
        <input
          type="range"
          id="table-light"
          min="0"
          max="5"
          step="0.1"
          value="3"
        />
      </div>

      <div class="range-input">
        <label for="fov" class="range-label" data-i18n-key="fov">fov</label>
        <input
          type="range"
          id="fov"
          min="0.1"
          max="1.5"
          step="0.1"
          value="0.7"
        />
      </div>

      <div class="range-input">
        <label for="color" class="range-label" data-i18n-key="color">color</label>
        <input type="color" id="color" value="#d3a77b" />
      </div>

      <div class="range-input">
        <label for="metalness" class="range-label" data-i18n-key="metalness">metalness</label>
        <input
          type="range"
          id="metalness"
          min="0"
          max="1"
          step="0.1"
          value="0.1"
        />
      </div>

      <div class="range-input">
        <label for="roughness" class="range-label" data-i18n-key="roughness">roughness</label>
        <input
          type="range"
          id="roughness"
          min="0"
          max="1"
          step="0.1"
          value="0.1"
        />
      </div>

      <div class="range-input">
        <label for="env-intensity" class="range-label" data-i18n-key="env-intensity">env-intensity</label>
        <input
          type="range"
          id="env-intensity"
          min="-50"
          max="50"
          step="5"
          value="0"
        />
      </div>

      <div class="range-input">
        <label for="model-x" class="range-label" data-i18n-key="model-x">model-x</label>
        <input
          type="range"
          id="model-x"
          min="-10"
          max="10"
          step="2"
          value="0"
        />
      </div>

      <div class="range-input">
        <label for="model-y" class="range-label" data-i18n-key="model-y">model-y</label>
        <input
          type="range"
          id="model-y"
          min="-10"
          max="10"
          step="1"
          value="0"
        />
      </div>

      <div class="range-input">
        <label for="model-z" class="range-label" data-i18n-key="model-z">model-z</label>
        <input
          type="range"
          id="model-z"
          min="-10"
          max="10"
          step="1"
          value="-2"
        />
      </div>

      <div class="range-input">
        <label for="ground-y" class="range-label" data-i18n-key="ground-y">ground-y</label>
        <input
          type="range"
          id="ground-y"
          min="-10"
          max="10"
          step="1"
          value="0"
        />
      </div>

      <div class="range-input">
        <label for="camera-x" class="range-label" data-i18n-key="camera-x">camera-x</label>
        <input
          type="range"
          id="camera-x"
          min="-10"
          max="10"
          step="1"
          value="-1"
        />
      </div>

      <div class="range-input">
        <label for="camera-y" class="range-label" data-i18n-key="camera-y">camera-y</label>
        <input
          type="range"
          id="camera-y"
          min="-10"
          max="10"
          step="1"
          value="2"
        />
      </div>

      <div class="range-input">
        <label for="camera-z" class="range-label" data-i18n-key="camera-z">camera-z</label>
        <input
          type="range"
          id="camera-z"
          min="-10"
          max="10"
          step="1"
          value="4"
        />
      </div>
    </div>
    <div id="model-controls" class="controls"
      */<h3>النموذج</h3>*/
      <div id="model-controls" class="controls">
        <div class="controls-grid">
          <button onclick="model-color-picker()" data-i18n-tooltip="model-color">model-color</button>
          <button onclick="model-texture-input()"data-i18n-tooltip="model-texture">model-texture</button>
          <button onclick="resetModel()"data-i18n-tooltip="model-reset">model-reset</button>
        </div>
      </div>

      <div class="controls-grid">
        <!-- زر اختيار اللون -->
        <div class="color-picker-container">
          <input
            type="color"
            id="model-color-picker" />
          <label for="model-color-picker" class="color-picker-label" data-i18n-tooltip="model-color-picker">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 -960 960 960">
              <path
                d="M252.33-912.67 300-960l346.12 346.12q23.21 23.21 23.21 56.96 0 33.76-23 56.59l-182 182q-23 23-55 23t-55-23l-182-182q-23-22.85-23-56.63t23-56.63l189.34-189.74-109.34-109.34Zm157 157L213-559.33h392.67L409.33-755.67Zm345.19 475q-30.85 0-52.69-21.93Q680-324.53 680-355.33q0-18.57 9.5-39.79 9.5-21.21 23.83-42.21 8.34-12.67 19.34-27 11-14.34 22-27 11 12.66 22 27 11 14.33 19.33 27 14.33 21 23.83 42.21 9.5 21.22 9.5 39.79 0 30.8-21.97 52.73-21.98 21.93-52.84 21.93ZM80 .67v-134h800v134H80Z"
              />
            </svg>
          </label>
        </div>

        <div>
          <button id="model-texture-picker" data-i18n-tooltip="model-texture-picker">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              height="24px"
              viewBox="0 -960 960 960"
              width="24px"
              fill="#d3a77b"
            >
              <path
                d="M480-260q75 0 127.5-52.5T660-440q0-75-52.5-127.5T480-620q-75 0-127.5 52.5T300-440q0 75 52.5 127.5T480-260Zm0-80q-42 0-71-29t-29-71q0-42 29-71t71-29q42 0 71 29t29 71q0 42-29 71t-71 29ZM160-120q-33 0-56.5-23.5T80-200v-480q0-33 23.5-56.5T160-760h126l74-80h240l74 80h126q33 0 56.5 23.5T880-680v480q0 33-23.5 56.5T800-120H160Zm0-80h640v-480H638l-73-80H395l-73 80H160v480Zm320-240Z"
              />
            </svg>
          </button>
          <input type="file" id="model-texture-input" accept="image/*" />
        </div>
        <div class="gradient">
          <input type="color" id="gradient-model-1" data-i18n-tooltip="model-gradient" />
          <input type="color" id="gradient-model-2" data-i18n-tooltip="model-gradient" />
          <input type="color" id="gradient-model-3" data-i18n-tooltip="model-gradient" />
        </div>
        <div>
          <button id="reset-model" data-i18n-tooltip="reset-model">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              height="24px"
              viewBox="0 -960 960 960"
              width="24px"
              fill="#d3a77b"
            >
              <path
                d="M520-330v-60h160v60H520Zm60 210v-50h-60v-60h60v-50h60v160h-60Zm100-50v-60h160v60H680Zm40-110v-160h60v50h60v60h-60v50h-60Zm111-280h-83q-26-88-99-144t-169-56q-117 0-198.5 81.5T200-480q0 72 32.5 132t87.5 98v-110h80v240H160v-80h94q-62-50-98-122.5T120-480q0-75 28.5-140.5t77-114q48.5-48.5 114-77T480-840q129 0 226.5 79.5T831-560Z"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- قوائم التحكم -->
    <div id="model-controls" class="controls">
      <div class="controls-grid">
        <button id="model-color-picker" data-i18n-tooltip="model-color-picker">
        </button>
        <button id="model-texture-input" data-i18n-tooltip="model-texture-picker"></button>
        <button id="reset-model" data-i18n-tooltip="reset-model"></button>
      </div>
    </div>

    <div id="background-controls" class="controls">
      <div class="controls-grid">
        <div class="color-picker-container">
          <input
            type="color"
            id="background-color-picker"
            data-i18n-tooltip="background-color-picker"
          />
          <label for="background-color-picker" data-i18n-tooltip="background-color-picker" class="color-picker-label">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 -960 960 960">
              <path
                d="M252.33-912.67 300-960l346.12 346.12q23.21 23.21 23.21 56.96 0 33.76-23 56.59l-182 182q-23 23-55 23t-55-23l-182-182q-23-22.85-23-56.63t23-56.63l189.34-189.74-109.34-109.34Zm157 157L213-559.33h392.67L409.33-755.67Zm345.19 475q-30.85 0-52.69-21.93Q680-324.53 680-355.33q0-18.57 9.5-39.79 9.5-21.21 23.83-42.21 8.34-12.67 19.34-27 11-14.34 22-27 11 12.66 22 27 11 14.33 19.33 27 14.33 21 23.83 42.21 9.5 21.22 9.5 39.79 0 30.8-21.97 52.73-21.98 21.93-52.84 21.93ZM80 .67v-134h800v134H80Z"
              />
            </svg>
          </label>
        </div>

        <div>
          <button id="background-image-picker" data-i18n-tooltip="background-image-picker">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              height="24px"
              viewBox="0 -960 960 960"
              width="24px"
              fill="#d3a77b"
            >
              <path
                d="M480-260q75 0 127.5-52.5T660-440q0-75-52.5-127.5T480-620q-75 0-127.5 52.5T300-440q0 75 52.5 127.5T480-260Zm0-80q-42 0-71-29t-29-71q0-42 29-71t71-29q42 0 71 29t29 71q0 42-29 71t-71 29ZM160-120q-33 0-56.5-23.5T80-200v-480q0-33 23.5-56.5T160-760h126l74-80h240l74 80h126q33 0 56.5 23.5T880-680v480q0 33-23.5 56.5T800-120H160Zm0-80h640v-480H638l-73-80H395l-73 80H160v480Zm320-240Z"
              />
            </svg>
          </button>
          <input type="file" id="background-image-input" accept="image/*" />
        </div>
        <div>
          <button id="show-skybox-thumbnails" data-i18n-tooltip="show-skybox-thumbnails">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              height="24px"
              viewBox="0 -960 960 960"
              width="24px"
              fill="#d3a77b"
            >
              <path
                d="M480-80q-121 0-216.5-63.5T120-308q-11-5-20-9t-18-9q-20-11-31-29t-11-41v-168q0-23 11-41t31-29q9-5 18-9t20-9q48-101 143.5-164.5T480-880q121 0 216.5 63.5T840-652q11 5 20 9t18 9q20 11 31 29.5t11 40.5v168q0 22-11 40.5T878-326q-9 5-18 9t-20 9q-48 101-143.5 164.5T480-80Zm0-80q69 0 131-28.5T718-268q-59 14-118.5 21T480-240q-60 0-119.5-7T242-268q45 51 107 79.5T480-160Zm0-320Zm0-320q-69 0-131 28.5T242-692q59-14 118.5-21t119.5-7q60 0 119.5 7T718-692q-45-51-107-79.5T480-800Zm0 480q107 0 200.5-20T840-396v-168q-66-36-159.5-56T480-640q-107 0-200.5 20T120-564v168q66 36 159.5 56T480-320Z"
              />
            </svg>
          </button>
          <div id="skybox-thumbnails">
            <div class="skybox-grid">
              <img src="skybox/skybox1.jpg" alt="Skybox 1" />
              <img src="skybox/skybox2.jpg" alt="Skybox 2" />
              <img src="skybox/skybox3.jpg" alt="Skybox 3" />
            </div>
          </div>
        </div>
        <div class="gradient">
          <input
            type="color"
            id="gradient-background-1"
            data-i18n-tooltip="background-gradient"
          />
          <input
            type="color"
            id="gradient-background-2"
            data-i18n-tooltip="background-gradient"
          />
          <input
            type="color"
            id="gradient-background-3"
            data-i18n-tooltip="background-gradient"
          />
        </div>
        <div>
          <button id="reset-background" data-i18n-tooltip="reset-background">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              height="24px"
              viewBox="0 -960 960 960"
              width="24px"
              fill="#d3a77b"
            >
              <path
                d="M520-330v-60h160v60H520Zm60 210v-50h-60v-60h60v-50h60v160h-60Zm100-50v-60h160v60H680Zm40-110v-160h60v50h60v60h-60v50h-60Zm111-280h-83q-26-88-99-144t-169-56q-117 0-198.5 81.5T200-480q0 72 32.5 132t87.5 98v-110h80v240H160v-80h94q-62-50-98-122.5T120-480q0-75 28.5-140.5t77-114q48.5-48.5 114-77T480-840q129 0 226.5 79.5T831-560Z"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>

    <div id="ground-controls" class="controls">
      <div class="controls-grid">
        <div>
          <div class="color-picker-container">
            <input
              type="color"
              id="ground-color-picker"
              data-i18n-tooltip="ground-color-picker"
            />
            <label for="ground-color-picker"  data-i18n-tooltip="ground-color-picker" class="color-picker-label">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 -960 960 960">
                <path
                  d="M252.33-912.67 300-960l346.12 346.12q23.21 23.21 23.21 56.96 0 33.76-23 56.59l-182 182q-23 23-55 23t-55-23l-182-182q-23-22.85-23-56.63t23-56.63l189.34-189.74-109.34-109.34Zm157 157L213-559.33h392.67L409.33-755.67Zm345.19 475q-30.85 0-52.69-21.93Q680-324.53 680-355.33q0-18.57 9.5-39.79 9.5-21.21 23.83-42.21 8.34-12.67 19.34-27 11-14.34 22-27 11 12.66 22 27 11 14.33 19.33 27 14.33 21 23.83 42.21 9.5 21.22 9.5 39.79 0 30.8-21.97 52.73-21.98 21.93-52.84 21.93ZM80 .67v-134h800v134H80Z"
                />
              </svg>
            </label>
          </div>
        </div>
        <div>
          <button id="show-texture-thumbnails" data-i18n-tooltip="show-texture-thumbnails">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              height="24px"
              viewBox="0 -960 960 960"
              width="24px"
              fill="#d3a77b"
            >
              <path
                d="M176-120q-19-4-35.5-20.5T120-176l664-664q21 5 36 20.5t21 35.5L176-120Zm-56-252v-112l356-356h112L120-372Zm0-308v-80q0-33 23.5-56.5T200-840h80L120-680Zm560 560 160-160v80q0 33-23.5 56.5T760-120h-80Zm-308 0 468-468v112L484-120H372Z"
              />
            </svg>
          </button>
        </div>
        <div>
          <button id="ground-texture-picker" data-i18n-tooltip="ground-texture-picker">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              height="24px"
              viewBox="0 -960 960 960"
              width="24px"
              fill="#d3a77b"
            >
              <path
                d="M480-260q75 0 127.5-52.5T660-440q0-75-52.5-127.5T480-620q-75 0-127.5 52.5T300-440q0 75 52.5 127.5T480-260Zm0-80q-42 0-71-29t-29-71q0-42 29-71t71-29q42 0 71 29t29 71q0 42-29 71t-71 29ZM160-120q-33 0-56.5-23.5T80-200v-480q0-33 23.5-56.5T160-760h126l74-80h240l74 80h126q33 0 56.5 23.5T880-680v480q0 33-23.5 56.5T800-120H160Zm0-80h640v-480H638l-73-80H395l-73 80H160v480Zm320-240Z"
              />
            </svg>
          </button>
          <input type="file" id="ground-texture-input" accept="image/*" />
        </div>
        <div>
          <button id="reset-ground" data-i18n-tooltip="reset-ground">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              height="24px"
              viewBox="0 -960 960 960"
              width="24px"
              fill="#d3a77b"
            >
              <path
                d="M520-330v-60h160v60H520Zm60 210v-50h-60v-60h60v-50h60v160h-60Zm100-50v-60h160v60H680Zm40-110v-160h60v50h60v60h-60v50h-60Zm111-280h-83q-26-88-99-144t-169-56q-117 0-198.5 81.5T200-480q0 72 32.5 132t87.5 98v-110h80v240H160v-80h94q-62-50-98-122.5T120-480q0-75 28.5-140.5t77-114q48.5-48.5 114-77T480-840q129 0 226.5 79.5T831-560Z"
              />
            </svg>
          </button>
        </div>
        <div>
          <div class="gradient">
            <input
              type="color"
              id="gradient-ground-1"
              data-i18n-tooltip="ground-gradient"
            />
            <input
              type="color"
              id="gradient-ground-2"
              data-i18n-tooltip="ground-gradient"
            />
            <input
              type="color"
              id="gradient-ground-3"
              data-i18n-tooltip="ground-gradient"
            />
          </div>
        </div>
      </div>
      <div id="texture-thumbnails">
        <div class="texture-grid">
          <img src="textures/black.jpg" alt="Texture 1" />
          <img src="textures/blue.jpg" alt="Texture 2" />
          <img src="textures/gold.jpg" alt="Texture 3" />
        </div>
      </div>
    </div>
    <div id="model-controls" class="controls" style="display: none">
      <input type="color" id="model-color-picker" data-i18n-tooltip="model-color-picker" />
      <button id="model-texture-picker" data-i18n-tooltip="model-texture-picker"></button>
      <input
        type="file"
        id="model-texture-input"
        accept="image/*"
        style="display: none"
      />
      <button id="reset-model" data-i18n-tooltip="reset-model"></button>
    </div>
    <div id="ground-controls" class="controls" style="display: none">
      <input type="color" id="ground-color-picker" data-i18n-tooltip="ground-color-picker" />
      <button id="ground-texture-picker" data-i18n-tooltip="ground-texture-picker"></button>
      <input
        type="file"
        id="ground-texture-input"
        accept="image/*"
        style="display: none"
      />
      <button id="reset-ground" data-i18n-tooltip="reset-ground"></button>
    </div>
    <div id="background-controls" class="controls" style="display: none">
      <input
        type="color"
        id="background-color-picker"
        data-i18n-tooltip="background-color-picker"
      />
      <button id="background-image-picker" data-i18n-tooltip="background-image-picker"></button>
      <input
        type="file"
        id="background-image-input"
        accept="image/*"
        style="display: none"
      />
      <button id="reset-background" data-i18n-tooltip="reset-background"></button>
    </div>


    <script src="/js/translations.js"></script>
<script>
   let stickersPanel = document.getElementById("stickers-panel");
for (let i = 1; i <= 265; i++) {
  let img = document.createElement("img");
  img.src = `img/stickers/${i}.png`;
  img.className = "sticker-item";
  img.alt = `ستيكر ${i}`;
  stickersPanel.appendChild(img);
}
/* --- وظائف عرض النماذج --- */
function renderModelGrid(filter = "") {
  const grid = document.getElementById("model-grid");
  grid.innerHTML = "";
  models
    .filter((m) => m.name.indexOf(filter) !== -1)
    .forEach((modelItem) => {
      const card = document.createElement("div");
      card.className = "model-card";
      card.innerHTML = `<img src="models/${modelItem.folder}/preview.webp" alt="${modelItem.name}" />
                    <p>${modelItem.name}</p>`;
      card.addEventListener("click", function (e) {
        e.stopPropagation();
        loadModel(modelItem.folder);
        document.getElementById("model-list-overlay").style.display = "none";
      });
      grid.appendChild(card);
    });
}

document
  .getElementById("btn-show-models")
  .addEventListener("click", function (e) {
    e.stopPropagation();
    renderModelGrid();
    document.getElementById("model-list-overlay").style.display = "block";
  });

document.getElementById("model-search").addEventListener("input", function (e) {
  const filter = e.target.value;
  renderModelGrid(filter);
});

//   ==============================

// عند اختيار لون جديد، يتم تطبيقه على مادة الأرضية
document
  .getElementById("model-color-picker")
  .addEventListener("change", function (event) {
    const color = event.target.value;
    model.material.color.set(color);
  });

// =========================

// كود الوظائف المطلوبة
function modelColorPicker() {
  // تنفيذ تغيير لون النموذج
  console.log("تغيير لون النموذج");
}

function modelTextureInput() {
  // تنفيذ إضافة نسيج للنموذج
  console.log("إضافة نسيج للنموذج");
}

function resetModel() {
  // تنفيذ إعادة تعيين النموذج
  console.log("إعادة تعيين النموذج");
}

function addTouchListeners(buttonId, func) {
  const button = document.getElementById(buttonId);
  button.addEventListener("touchstart", func);
  button.addEventListener("click", func); // للحفاظ على وظيفة النقر العادية على الكمبيوتر
}

// إضافة مستمعي الأحداث للمس للأزرار
addTouchListeners("model-color-picker", modelColorPicker);
addTouchListeners("model-texture-input", modelTextureInput);
addTouchListeners("reset-model", resetModel);

// =================================

// كود الوظائف المطلوبة
function modelColorPicker() {
  // تنفيذ تغيير لون النموذج
  console.log("تغيير لون النموذج");
}

function modelTextureInput() {
  // تنفيذ إضافة نسيج للنموذج
  console.log("إضافة نسيج للنموذج");
}

function resetModel() {
  // تنفيذ إعادة تعيين النموذج
  console.log("إعادة تعيين النموذج");
}

function addTouchListeners(buttonId, func) {
  const button = document.getElementById(buttonId);
  button.addEventListener("touchstart", func);
  button.addEventListener("click", func); // للحفاظ على وظيفة النقر العادية على الكمبيوتر
}

// إضافة مستمعي الأحداث للمس للأزرار
addTouchListeners("model-color-picker", modelColorPicker);
addTouchListeners("model-texture-input", modelTextureInput);
addTouchListeners("reset-model", resetModel);

// -----------------------------

// إغلاق القوائم عند النقر خارجها
document.addEventListener("click", (event) => {
  const clickedElement = event.target;

  // التحقق مما إذا كان النقر خارج القوائم وأزرار الفتح
  if (
    !animateButtons.contains(clickedElement) &&
    !toggleAnimateButton.contains(clickedElement)
  ) {
    animateButtons.style.display = "none";
  }

  if (
    !mediaMenu.contains(clickedElement) &&
    !toggleExportButton.contains(clickedElement)
  ) {
    mediaMenu.style.display = "none";
  }

  // التحقق مما إذا كان ccaptureMenu موجودًا قبل محاولة إخفائه
  if (
    ccaptureMenu &&
    !ccaptureMenu.contains(clickedElement) &&
    !toggleExportButton.contains(clickedElement)
  ) {
    ccaptureMenu.style.display = "none";
  }
});
const toggleAnimateButton = document.getElementById("toggleAnimateButton");
const animateButtons = document.getElementById("animateButtons");

toggleAnimateButton.addEventListener("click", () => {
  // تبديل الحالة: إذا كانت القائمة مخفية يتم عرضها، وإذا كانت معروضة يتم إخفاؤها
  if (
    animateButtons.style.display === "none" ||
    animateButtons.style.display === ""
  ) {
    animateButtons.style.display = "block";
  } else {
    animateButtons.style.display = "none";
  }
});

const sidebar = document.querySelector(".sidebar");
const toggleButton = document.getElementById("toggle-sidebar-button");
toggleButton.addEventListener("click", function (event) {
  event.stopPropagation();
  sidebar.classList.toggle("active");
});
document.addEventListener("click", function (event) {
  const isClickInsideSidebar = sidebar.contains(event.target);
  const isClickOnToggleButton = toggleButton.contains(event.target);
  if (!isClickInsideSidebar && !isClickOnToggleButton) {
    sidebar.classList.remove("active");
  }
});
sidebar.addEventListener("click", function (event) {
  event.stopPropagation();
});
document
  .getElementById("model-color-picker")
  .addEventListener("change", function (event) {
    const color = event.target.value;
    model.material.color.set(color);
  });
function modelColorPicker() {
  console.log("تغيير لون النموذج");
}
function modelTextureInput() {
  console.log("إضافة نسيج للنموذج");
}
function resetModel() {
  console.log("إعادة تعيين النموذج");
}
function addTouchListeners(buttonId, func) {
  const button = document.getElementById(buttonId);
  button.addEventListener("touchstart", func);
  button.addEventListener("click", func);
}

addTouchListeners("model-color-picker", modelColorPicker);
addTouchListeners("model-texture-input", modelTextureInput);
addTouchListeners("reset-model", resetModel);

document
  .getElementById("background-color-picker")
  .addEventListener("change", function (event) {
    const color = event.target.value;
    background.material.color.set(color);
  });

document
  .getElementById("ground-color-picker")
  .addEventListener("change", function (event) {
    const color = event.target.value;
    ground.material.color.set(color);
  });
function createGradientTexture(color1, color2, color3) {
  const canvas = document.createElement("canvas");
  canvas.width = 1024;
  canvas.height = 1024;
  const ctx = canvas.getContext("2d");

  const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
  gradient.addColorStop(0, color1);
  gradient.addColorStop(0.5, color2);
  if (color3) gradient.addColorStop(1, color3);

  ctx.fillStyle = gradient;
  ctx.fillRect(0, 0, canvas.width, canvas.height);

  return new THREE.CanvasTexture(canvas);
}

function applyModelGradient() {
  const color1 = document.getElementById("gradient-model-1").value;
  const color2 = document.getElementById("gradient-model-2").value;
  const color3 = document.getElementById("gradient-model-3").value;

  const gradientTexture = createGradientTexture(color1, color2, color3);
  if (selectedPart) {
    if (!selectedPart.material.isCloned) {
      selectedPart.material = selectedPart.material.clone();
      selectedPart.material.isCloned = true;
    }
    selectedPart.material.map = gradientTexture;
    selectedPart.material.needsUpdate = true;
  }
}

function applyBackgroundGradient() {
  const color1 = document.getElementById("gradient-background-1").value;
  const color2 = document.getElementById("gradient-background-2").value;
  const color3 = document.getElementById("gradient-background-3").value;

  const gradientTexture = createGradientTexture(color1, color2, color3);
  scene.background = gradientTexture;
}

function applyGroundGradient() {
  const color1 = document.getElementById("gradient-ground-1").value;
  const color2 = document.getElementById("gradient-ground-2").value;
  const color3 = document.getElementById("gradient-ground-3").value;

  const gradientTexture = createGradientTexture(color1, color2, color3);
  ground.material.map = gradientTexture;
  ground.material.needsUpdate = true;
}

document
  .getElementById("gradient-model-1")
  .addEventListener("input", applyModelGradient);
document
  .getElementById("gradient-model-2")
  .addEventListener("input", applyModelGradient);
document
  .getElementById("gradient-model-3")
  .addEventListener("input", applyModelGradient);

document
  .getElementById("gradient-background-1")
  .addEventListener("input", applyBackgroundGradient);
document
  .getElementById("gradient-background-2")
  .addEventListener("input", applyBackgroundGradient);
document
  .getElementById("gradient-background-3")
  .addEventListener("input", applyBackgroundGradient);

document
  .getElementById("gradient-ground-1")
  .addEventListener("input", applyGroundGradient);
document
  .getElementById("gradient-ground-2")
  .addEventListener("input", applyGroundGradient);
document
  .getElementById("gradient-ground-3")
  .addEventListener("input", applyGroundGradient);

function enableGestureControls(elementId) {
  const element = document.getElementById(elementId);
  const hammer = new Hammer(element);

  hammer.on("doubletap", function () {
    element.classList.toggle("show");
    console.log("تم تفعيل اللمس المزدوج على: " + elementId);
  });

  hammer.on("press", function () {
    element.classList.toggle("show");
    console.log("تم تفعيل اللمس المطول على: " + elementId);
  });
}

enableGestureControls("model-controls");
enableGestureControls("background-controls");
enableGestureControls("ground-controls");

let scene,
  camera,
  renderer,
  controls,
  raycaster,
  directionalLight,
  ambientLight;
let model,
  ground,
  grid,
  selectedPart = null,
  defaultModel = null;
let currentLoader; // GLTFLoader المستخدم لتحميل النماذج
const textureLoader = new THREE.TextureLoader(); // لتحميل القوام (الكود الأول)
const loader = new THREE.TextureLoader(); // لتحميل القوام (الكود الثاني)
const container = document.getElementById("threejs-container");
let fabricTexture;
let printMesh = null;
let rotationSpeed = 0;
let mouse = new THREE.Vector2();
let capturerCCapture = null;

// متغيرات الحركة
let swing = false,
  hover = false,
  jump = false;
// مصفوفة النماذج – قم بتعديلها لتطابق أسماء المجلدات الفرعية لديك
const models = [
  { folder: "crewneck-sweatshirt-mockup", name: "crewneck-sweatshirt-mockup" },
  { folder: "full-zip-hoodie-mockup", name: "full-zip-hoodie-mockup" },
  { folder: "mens-crewneck-sweatshirt-mockup", name: "mens-crewneck-sweatshirt-mockup" },
  { folder: "long-sleeve-v-neck-shirt-mockup", name: "long-sleeve-v-neck-shirt-mockup" },
  { folder: "mens-round-neck-sweatshirt-mockup", name: "mens-round-neck-sweatshirt-mockup" },
  { folder: "Oversized-hoodie", name: "Oversized-hoodie" },
  { folder: "Oversized-hoodie-mockup", name: "Oversized-hoodie-mockup" },
  { folder: "Raglan-hoodie-mockup", name: "Raglan-hoodie-mockup" },
  { folder: "Raglan-t-shirt-mockup", name: "Raglan-t-shirt-mockup" },
  { folder: "Round-neck-long-sleeve-raglan-t-shirt", name: "Round-neck-long-sleeve-raglan-t-shirt" },
  { folder: "round-neck-long-sleeve-raglan-t-shirt-mockup", name: "round-neck-long-sleeve-raglan-t-shirt-mockup" },
  { folder: "Short-sleeve-cropped-hoodie", name: "Short-sleeve-cropped-hoodie" },
  { folder: "Sleeveless-hoodie-mockup", name: "Sleeveless-hoodie-mockup" },
  { folder: "Sleeveless-zip-hoodie-mockup", name: "Sleeveless-zip-hoodie-mockup" },
  { folder: "Tank-top-mockup", name: "Tank-top-mockup" },
  { folder: "Tshirt-mockup", name: "Tshirt-mockup" },
  { folder: "Women‘s-crop-tank-top", name: "Women‘s-crop-tank-top" },
  { folder: "Women‘s-deep-v-neck-tshirt", name: "Women‘s-deep-v-neck-tshirt" },
  { folder: "Women's-hoodie-mockup", name: "Women's-hoodie-mockup" },
  { folder: "Women's-slim-fit-v-neck-tshirt", name: "Women's-slim-fit-v-neck-tshirt" },
  { folder: "Women's-tank-top", name: "Women's-tank-top" },
  { folder: "Women's-tank-top-mockup", name: "Women's-tank-top-mockup" },
  { folder: "Women's-tshirt", name: "Women's-tshirt" },
  { folder: "womens-t-shirt-mockup", name: "womens-t-shirt-mockup" },
  { folder: "Women-tank-top-mockup", name: "Women-tank-top-mockup" },
  { folder: "0", name: "0" },
    { folder: "Modelled-boxy-hoodie-mockup", name: "Modelled-boxy-hoodie-mockup" },
{ folder: "Womens-tshirt", name: "Womens-tshirt" },
{ folder: "sweatshirt-womens-mockup", name: "sweatshirt-womens-mockup" },
{ folder: "womens-tshirt-mockup", name: "womens-tshirt-mockup" },




];

// تهيئة currentLoader لاستخدام GLTFLoader (لتفعيل التحميل الديناميكي)
currentLoader = new THREE.GLTFLoader();

/**
 * تحميل النموذج ثلاثي الأبعاد من مجلد محدد وإضافته إلى المشهد.
 * - يقوم بتعيين المواد، توسيط النموذج، وضبط الإعدادات الافتراضية.
 * @param {string} folderName اسم مجلد النموذج
 */
function loadModel(folderName) {
  if (model) {
    scene.remove(model);
  }
  currentLoader.load(
    `models/${folderName}/model.gltf`,
    function (gltf) {
      model = gltf.scene;

      // مسح التدوير والموضع الأولي للنموذج
      model.rotation.set(0, 0, 0);
      model.position.set(0, 0, 0);

      // حساب صندوق الإحاطة (Box3) لضبط حجم النموذج بشكل مناسب
      const box = new THREE.Box3().setFromObject(model);
      const size = new THREE.Vector3();
      box.getSize(size);
      const maxDim = Math.max(size.x, size.y, size.z);
      const scaleFactor = 2 / maxDim;
      model.scale.set(scaleFactor, scaleFactor, scaleFactor);

      // توسيط النموذج بشكل دقيق في منتصف المشهد
      box.setFromObject(model);
      const center = new THREE.Vector3();
      box.getCenter(center);
      model.position.copy(center).multiplyScalar(-1);

      // رفع النموذج للأعلى لتجنب التداخل مع حاوية image-editor-container
      model.position.y += 1.0; // زيادة قيمة الارتفاع للأعلى

      // ضبط المواد لكل جزء من النموذج
      model.traverse((child) => {
        if (child.isMesh) {
          child.castShadow = true;
          if (child.name.toLowerCase().includes("print")) {
            printMesh = child;
            child.material = new THREE.MeshStandardMaterial({
              color: 0xffffff,
              metalness: 0.01,
              roughness: 0.1,
              envMapIntensity: 0.1,
              map: fabricTexture || null,
            });
          } else {
            child.material = new THREE.MeshStandardMaterial({
              color: 0xffffff,
              metalness: 0.01,
              roughness: 0.1,
              envMapIntensity: 0.1,
            });
          }
        }
      });

      // إضافة النموذج للمشهد
      scene.add(model);

      // تعيين عناصر التحكم لتستهدف مركز النموذج
      controls.target.set(0, model.position.y, 0); // تعديل هدف الكاميرا ليكون مركز النموذج
      controls.update();

      defaultModel = model.clone();
    },
    undefined,
    (error) => {
      console.error("خطأ في تحميل النموذج من المجلد", folderName, error);
    }
  );
}

// دالة لتوسيط النموذج في المشهد
function centerModelInScene() {
  if (!model) return;

  // مسح التدوير والموضع الأولي
  model.position.set(0, 0, 0);

  // حساب حجم وأبعاد النموذج
  const box = new THREE.Box3().setFromObject(model);
  const size = new THREE.Vector3();
  const center = new THREE.Vector3();

  box.getSize(size);
  box.getCenter(center);

  // توسيط النموذج وفقًا للمركز المحسوب
  model.position.copy(center).multiplyScalar(-1);

  // رفع النموذج للأعلى لتجنب التداخل مع حاوية image-editor-container
  // نظرًا لأن ارتفاع threejs-container هو 70% والنموذج يجب أن يظهر في منتصفه
  model.position.y += 1.0; // زيادة قيمة الارتفاع للأعلى

  // تحديث عناصر التحكم
  controls.target.set(0, model.position.y, 0); // تعديل هدف الكاميرا ليكون مركز النموذج
  controls.update();
}

// تحميل النموذج الافتراضي من أول عنصر في المصفوفة
loadModel("0");

const skyboxFolders = [
  "skybox/bologni/",
  "skybox/Cloudy/",
  "skybox/DallasW/",
  "skybox/entrance/",
  "skybox/FullMoon/",
  "skybox/farm/",
  "skybox/hotel/",
  "skybox/lebombo/",
  "skybox/Marriott/",
  "skybox/photostudio/",
  "skybox/darker/",
  "skybox/skyboxsun/",
  "skybox/spruit/",
  "skybox/sun/",
  "skybox/SunSet/",
  "skybox/CloudsWater/",
  "skybox/Tropical/",
  "skybox/Vasa/",
  "skybox/veranda/",
  "skybox/Stormy/",
];
const texturePaths = [
  "textures/black.jpg",
  "textures/black1.jpg",
  "textures/black2.jpg",
  "textures/wood1.jpg",
  "textures/wood2.jpg",
  "textures/wood3.jpg",
  "textures/wood4.jpg",
  "textures/wood5.jpg",
  "textures/silver.jpg",
  "textures/rock.jpg",
  "textures/paper.jpg",
  "textures/textil.jpg",
  "textures/wood6.jpg",
  "textures/color.jpg",
  "textures/marble.jpg",
  "textures/marble2.jpg",
  "textures/marble3.jpg",
  "textures/marble4.jpg",
  "textures/marble5.jpg",
  "textures/color.jpg",
  "textures/marble.jpg",
  "textures/marble2.jpg",
  "textures/marble3.jpg",
  "textures/marble4.jpg",
  "textures/gold.jpg",
  "textures/gold1.jpg",
  "textures/blue.jpg",
  "textures/blue1.jpg",
  "textures/blue2.jpg",
  "textures/blue3.jpg",
  "textures/gold2.jpg",
  "textures/gold3.jpg",
  "textures/gold4.jpg",
  "textures/gold5.jpg",
  "textures/gold6.jpg",
  "textures/blue3.jpg",
  "textures/gold1.jpg",
  "textures/marble5.jpg",
  "textures/marble1.jpg",
  "textures/marble6.jpg",
  "textures/reflectiv.jpg",
  "textures/silver1.jpg",
  "textures/silver2.jpg",
  "textures/silver3.jpg",
  "textures/silver4.jpg",
  "textures/silver5.jpg",
  "textures/white1.jpg",
  "textures/wood.jpg",
  "textures/wood6.jpg",
  "textures/silver4.jpg",
];
function loadSkybox(folder) {
  const cubeTextureLoader = new THREE.CubeTextureLoader();
  cubeTextureLoader.setPath(folder);

  cubeTextureLoader.load(
    ["px.jpg", "nx.jpg", "py.jpg", "ny.jpg", "pz.jpg", "nz.jpg"],
    (texture) => {
      scene.background = texture;
    },
    undefined,
    (error) => {
      console.error("خطأ في تحميل خلفية السماء:", error);
      scene.background = new THREE.Color(0x344464);
    }
  );
}

function showSkyboxThumbnails() {
  const thumbnailsContainer = document.querySelector(
    "#skybox-thumbnails .skybox-grid"
  );
  thumbnailsContainer.innerHTML = "";

  skyboxFolders.forEach((folder, index) => {
    const img = document.createElement("img");
    img.src = folder + "px.jpg";
    img.alt = `خلفية السماء ${index + 1}`;
    img.onclick = () => {
      loadSkybox(folder);
      document.getElementById("skybox-thumbnails").classList.remove("active");
    };
    thumbnailsContainer.appendChild(img);
  });

  document.getElementById("skybox-thumbnails").classList.add("active");
}

function showTextureThumbnails() {
  const thumbnailsContainer = document.querySelector(
    "#texture-thumbnails .texture-grid"
  );
  thumbnailsContainer.innerHTML = "";

  texturePaths.forEach((path, index) => {
    const img = document.createElement("img");
    img.src = path;
    img.alt = `القوام ${index + 1}`;
    img.onclick = () => {
      applyTexture(path);
      document.getElementById("texture-thumbnails").classList.remove("active");
    };
    thumbnailsContainer.appendChild(img);
  });

  document.getElementById("texture-thumbnails").classList.add("active");
}

function applyTexture(texturePath) {
  const texture = loader.load(texturePath);
  if (selectedPart) {
    if (!selectedPart.material.isCloned) {
      selectedPart.material = selectedPart.material.clone();
      selectedPart.material.isCloned = true;
    }
    selectedPart.material.map = texture;
    selectedPart.material.needsUpdate = true;
  } else {
    scene.background = texture;
  }
}

document
  .getElementById("show-skybox-thumbnails")
  .addEventListener("click", showSkyboxThumbnails);

document
  .getElementById("show-texture-thumbnails")
  .addEventListener("click", showTextureThumbnails);

function init() {
  scene = new THREE.Scene();
  scene.background = new THREE.Color(0x1f2f52);

  // الحصول على أبعاد حاوية threejs-container
  const container = document.getElementById('threejs-container');
  const containerWidth = container.clientWidth;
  const containerHeight = container.clientHeight;

  camera = new THREE.PerspectiveCamera(
    50,
    containerWidth / containerHeight,
    1.2,
    1000
  );
  // تعديل موضع الكاميرا لتكون أعلى وتنظر للأسفل قليلاً
  camera.position.set(-1, 3, 4);

  renderer = new THREE.WebGLRenderer({
    antialias: true,
    canvas: document.getElementById("canvas"),
  });
  renderer.setSize(containerWidth, containerHeight);
  renderer.shadowMap.enabled = true;
  document.body.appendChild(renderer.domElement);

  controls = new THREE.OrbitControls(camera, renderer.domElement);
  controls.enableDamping = true;
  controls.dampingFactor = 0.05;
  controls.minDistance = 5;
  controls.maxDistance = 15;

  controls.minPolarAngle = Math.PI / 8;
  controls.maxPolarAngle = Math.PI / 2.5;
  // زاوية قصوى
  raycaster = new THREE.Raycaster();
  mouse = new THREE.Vector2();

  const groundGeometry = new THREE.BoxGeometry(10, 10, 0.5);
  const groundMaterial = new THREE.MeshStandardMaterial({
    color: 0xe0e5eb,
  });
  ground = new THREE.Mesh(groundGeometry, groundMaterial);
  ground.rotation.x = -Math.PI / 2;
  // رفع الأرضية لتتناسب مع موضع النموذج الجديد
  ground.position.y = 0; // تعديل موضع الأرضية لتكون أعلى
  ground.receiveShadow = true;
  scene.add(ground);

  const gridColor = new THREE.Color("#d3a77b");
  const grid = new THREE.GridHelper(9, 9, gridColor, gridColor);
  // رفع الشبكة لتتناسب مع موضع النموذج الجديد
  grid.position.y = 0.3; // تعديل موضع الشبكة لتكون أعلى
  grid.rotation.y = THREE.MathUtils.degToRad(90);
  scene.add(grid);

  const toggler = document.getElementById("toggler");
  const toggleVisibility = document.getElementById("toggleVisibility");

  let isGridSelected = true;

  toggler.addEventListener("click", () => {
    isGridSelected = !isGridSelected;
    if (isGridSelected) {
      toggler.textContent = "ارضية";
      grid.visible = toggleVisibility.checked;
      ground.visible = false;
    } else {
      toggler.textContent = "شبكة";
      grid.visible = false;
      ground.visible = toggleVisibility.checked;
    }
  });

  toggleVisibility.addEventListener("change", () => {
    if (isGridSelected) {
      grid.visible = toggleVisibility.checked;
    } else {
      ground.visible = toggleVisibility.checked;
    }
  });

  directionalLight = new THREE.DirectionalLight(0xffffff, 0.45);
  directionalLight.position.set(5, 5, 5);
  directionalLight.castShadow = true;
  scene.add(directionalLight);

  ambientLight = new THREE.AmbientLight(0x969696, 1);
  scene.add(ambientLight);

  const backLight = new THREE.DirectionalLight(0xcecece, 0.5);
  backLight.position.set(-1, 1, -1);
  scene.add(backLight);

  window.addEventListener("resize", onWindowResize);
  window.addEventListener("dblclick", onDoubleClick);

  setupSidebar();
  function onWindowResize() {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);

    // إعادة توسيط النموذج بعد تغيير الحجم
    if (model) {
      centerModelInScene();
    }
  }

  document.addEventListener("click", function (event) {
    const controls = document.querySelectorAll(".controls");
    controls.forEach((control) => {
      if (!control.contains(event.target)) {
        control.style.display = "none";
      }
    });
  });

  document.getElementById("reset-model").addEventListener("click", function () {
    const originalPosition = model.position.clone();
    const originalRotation = model.rotation.clone();
    const originalScale = model.scale.clone();

    scene.remove(model);

    model = defaultModel.clone();

    model.position.copy(originalPosition);
    model.rotation.copy(originalRotation);
    model.scale.copy(originalScale);

    model.traverse(function (child) {
      if (child.isMesh) {
        child.material = new THREE.MeshStandardMaterial({
          color: 0xffffff,
          metalness: 0.01,
          roughness: 0.1,
          envMapIntensity: 0.1,
        });
      }
    });

    scene.add(model);

    controls.update();
  });

  document
    .getElementById("reset-ground")
    .addEventListener("click", function () {
      ground.material.color.set(0xe7ecf3);
      ground.material.map = null;
      ground.material.needsUpdate = true;
    });

  document
    .getElementById("reset-background")
    .addEventListener("click", function () {
      scene.background = new THREE.Color(0x182949);
    });
}

// تحديث حجم المُعرض عند تغيير حجم النافذة
function onWindowResize() {
  const container = document.getElementById('threejs-container');
  const width = container.clientWidth;
  const height = container.clientHeight;

  camera.aspect = width / height;
  camera.updateProjectionMatrix();
  renderer.setSize(width, height);

  // إعادة توسيط النموذج بعد تغيير الحجم
  if (model) {
    centerModelInScene();
  }
}
window.addEventListener("resize", onWindowResize, false);

function onDoubleClick(event) {
  mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
  mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;

  raycaster.setFromCamera(mouse, camera);

  const intersects = raycaster.intersectObjects([model, ground], true);

  if (intersects.length > 0) {
    const selectedObject = intersects[0].object;

    document.querySelectorAll(".controls").forEach((control) => {
      control.style.display = "none";
    });

    if (selectedObject === ground) {
      selectedPart = ground;
      document.getElementById("ground-controls").style.display = "block";
      document.getElementById(
        "ground-controls"
      ).style.left = `${event.clientX}px`;
      document.getElementById(
        "ground-controls"
      ).style.top = `${event.clientY}px`;
    } else if (selectedObject === model || isPartOfModel(selectedObject)) {
      selectedPart = selectedObject;
      document.getElementById("model-controls").style.display = "block";
      document.getElementById(
        "model-controls"
      ).style.left = `${event.clientX}px`;
      document.getElementById(
        "model-controls"
      ).style.top = `${event.clientY}px`;
    }
  } else {
    document.getElementById("background-controls").style.display = "block";
    document.getElementById(
      "background-controls"
    ).style.left = `${event.clientX}px`;
    document.getElementById(
      "background-controls"
    ).style.top = `${event.clientY}px`;
  }
}

function isPartOfModel(object) {
  let parent = object.parent;
  while (parent !== null) {
    if (parent === model) {
      return true;
    }
    parent = parent.parent;
  }
  return false;
}

function animate() {
  requestAnimationFrame(animate);
  controls.update();
  if (rotationSpeed !== 0 && model) {
    model.rotation.y += rotationSpeed;
  }

  if (fabricTexture) {
    fabricTexture.needsUpdate = true;
    if (printMesh) {
      printMesh.material.map = fabricTexture;
      printMesh.material.needsUpdate = true;
    }
  }

  // تطبيق تأثيرات الحركة
  if (swing && model) {
    model.rotation.z = 0.1 * Math.sin(Date.now() * 0.005);
  }
  if (hover && model) {
    model.position.y = 0.2 * Math.sin(Date.now() * 0.005);
  }
  if (jump && model) {
    model.position.y = 0.5 * Math.abs(Math.sin(Date.now() * 0.1));
  }

  renderer.render(scene, camera);
  if (capturerCCapture) {
    capturerCCapture.capture(renderer.domElement);
  }
}

function toggleSidebar() {
  const sidebar = document.getElementById("sidebar");
  sidebar.classList.toggle("active");
}

function setupSidebar() {
  document.getElementById("light-vertical").addEventListener("input", (e) => {
    directionalLight.position.y = parseFloat(e.target.value);
  });

  document.getElementById("table-light").addEventListener("input", (e) => {
    directionalLight.intensity = parseFloat(e.target.value);
  });

  document.getElementById("fov").addEventListener("input", (e) => {
    camera.fov = parseFloat(e.target.value) * 100;
    camera.updateProjectionMatrix();
  });

  document.getElementById("color").addEventListener("input", (e) => {
    const color = new THREE.Color(e.target.value);
    model.traverse((child) => {
      if (child.isMesh) {
        child.material.color = color;
      }
    });
  });

  document.getElementById("metalness").addEventListener("input", (e) => {
    model.traverse((child) => {
      if (child.isMesh) {
        child.material.metalness = parseFloat(e.target.value);
      }
    });
  });

  document.getElementById("roughness").addEventListener("input", (e) => {
    model.traverse((child) => {
      if (child.isMesh) {
        child.material.roughness = parseFloat(e.target.value);
      }
    });
  });

  document.getElementById("env-intensity").addEventListener("input", (e) => {
    model.traverse((child) => {
      if (child.isMesh) {
        child.material.envMapIntensity = parseFloat(e.target.value);
      }
    });
  });

  document.getElementById("model-x").addEventListener("input", (e) => {
    model.position.x = parseFloat(e.target.value);
  });

  document.getElementById("model-y").addEventListener("input", (e) => {
    model.position.y = parseFloat(e.target.value);
  });

  document.getElementById("model-z").addEventListener("input", (e) => {
    model.position.z = parseFloat(e.target.value);
  });

  document.getElementById("ground-y").addEventListener("input", (e) => {
    ground.position.y = parseFloat(e.target.value);
  });

  document.getElementById("camera-x").addEventListener("input", (e) => {
    camera.position.x = parseFloat(e.target.value);
  });

  document.getElementById("camera-y").addEventListener("input", (e) => {
    camera.position.y = parseFloat(e.target.value);
  });

  document.getElementById("camera-z").addEventListener("input", (e) => {
    camera.position.z = parseFloat(e.target.value);
  });
}

init();
animate();

// استدعاء دالة تغيير الحجم وتوسيط النموذج بعد تحميل الصفحة
window.addEventListener('load', function() {
  onWindowResize();

  // ضمان تحديث النموذج بعد تحميل الصفحة بالكامل
  setTimeout(function() {
    if (model) {
      centerModelInScene();
    }
  }, 500);
});

function isPartOfModel(object) {
  let parent = object.parent;
  while (parent !== null) {
    if (parent === model) {
      return true;
    }
    parent = parent.parent;
  }
  return false;
}

function hideAllControls() {
  document.querySelectorAll(".controls").forEach((control) => {
    control.style.display = "none";
  });
}

function showControl(controlId, x, y) {
  hideAllControls();
  const control = document.getElementById(controlId);
  control.style.left = x + "px";
  control.style.top = y + "px";
  control.style.display = "block";
}

function handleInteraction(clientX, clientY) {
  mouse.x = (clientX / window.innerWidth) * 2 - 1;
  mouse.y = -(clientY / window.innerHeight) * 2 + 1;
  raycaster.setFromCamera(mouse, camera);

  const intersects = raycaster.intersectObjects([model, ground], true);
  if (intersects.length > 0) {
    const selectedObject = intersects[0].object;
    hideAllControls();
    if (selectedObject === ground) {
      selectedPart = ground;
      showControl("ground-controls", clientX, clientY);
    } else if (selectedObject === model || isPartOfModel(selectedObject)) {
      selectedPart = selectedObject;
      showControl("model-controls", clientX, clientY);
    }
  } else {
    hideAllControls();
    showControl("background-controls", clientX, clientY);
  }
}

function onDoubleClick(event) {
  handleInteraction(event.clientX, event.clientY);
}
document.addEventListener("dblclick", onDoubleClick, false);

const hammer = new Hammer(document.body);
hammer.get("doubletap").set({ taps: 2 });
hammer.get("press").set({ time: 500 });
hammer.on("doubletap press", function (ev) {
  handleInteraction(ev.center.x, ev.center.y);
});

document
  .getElementById("model-color-picker")
  .addEventListener("input", function (event) {
    const color = event.target.value;
    if (selectedPart) {
      if (!selectedPart.material.isCloned) {
        selectedPart.material = selectedPart.material.clone();
        selectedPart.material.isCloned = true;
      }
      selectedPart.material.color.set(color);
    }
  });

document
  .getElementById("model-texture-picker")
  .addEventListener("click", function () {
    document.getElementById("model-texture-input").click();
  });

document
  .getElementById("model-texture-input")
  .addEventListener("change", function (event) {
    const file = event.target.files[0];
    if (file && selectedPart) {
      const reader = new FileReader();
      reader.onload = function (e) {
        loader.load(e.target.result, function (texture) {
          texture.flipY = false;
          if (!selectedPart.material.isCloned) {
            selectedPart.material = selectedPart.material.clone();
            selectedPart.material.isCloned = true;
          }
          selectedPart.material.map = texture;
          selectedPart.material.needsUpdate = true;
        });
      };
      reader.readAsDataURL(file);
    }
  });

const groundColorPicker = document.getElementById("ground-color-picker");

function updateGroundColor() {
  const color = groundColorPicker.value;
  ground.material.color.set(color);
}

groundColorPicker.addEventListener("input", updateGroundColor);
groundColorPicker.addEventListener("change", updateGroundColor);
groundColorPicker.addEventListener("touchend", function () {
  setTimeout(updateGroundColor, 100);
});

document
  .getElementById("ground-texture-picker")
  .addEventListener("click", function () {
    document.getElementById("ground-texture-input").click();
  });

document
  .getElementById("ground-texture-input")
  .addEventListener("change", function (event) {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = function (e) {
        loader.load(e.target.result, function (texture) {
          texture.flipY = true;
          ground.material.map = texture;
          ground.material.needsUpdate = true;
        });
      };
      reader.readAsDataURL(file);
    }
  });

document
  .getElementById("background-color-picker")
  .addEventListener("input", function (event) {
    const color = event.target.value;
    scene.background = new THREE.Color(color);
  });

document
  .getElementById("background-image-picker")
  .addEventListener("click", function () {
    document.getElementById("background-image-input").click();
  });

document
  .getElementById("background-image-input")
  .addEventListener("change", function (event) {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = function (e) {
        loader.load(e.target.result, function (texture) {
          texture.flipY = true;
          scene.background = texture;
        });
      };
      reader.readAsDataURL(file);
    }
  });

document.getElementById("reset-model").addEventListener("click", function () {
  const originalPosition = model.position.clone();
  const originalRotation = model.rotation.clone();
  const originalScale = model.scale.clone();

  scene.remove(model);

  model = defaultModel.clone();

  model.position.copy(originalPosition);
  model.rotation.copy(originalRotation);
  model.scale.copy(originalScale);

  model.traverse(function (child) {
    if (child.isMesh) {
      child.material = new THREE.MeshStandardMaterial({
        color: 0xffffff,
        metalness: 0.01,
        roughness: 0.1,
        envMapIntensity: 0.2,
      });
    }
  });

  scene.add(model);
});

document.getElementById("reset-ground").addEventListener("click", function () {
  ground.material.color.set(0xffffff);
  ground.material.map = null;
  ground.material.needsUpdate = true;
});

document
  .getElementById("reset-background")
  .addEventListener("click", function () {
    scene.background = new THREE.Color(0x182949);
  });

function animate() {
  requestAnimationFrame(animate);
  if (model && rotationSpeed !== 0) {
    model.rotation.y += rotationSpeed;
  }
  controls.update();
  renderer.render(scene, camera);
  if (capturerCCapture) {
    capturerCCapture.capture(renderer.domElement);
  }

  if (fabricTexture) {
    fabricTexture.needsUpdate = true;
    if (printMesh) {
      printMesh.material.map = fabricTexture;
      printMesh.material.needsUpdate = true;
    }
  }

  // تطبيق تأثيرات الحركة
  if (swing && model) {
    model.rotation.z = 0.1 * Math.sin(Date.now() * 0.005);
  }
  if (hover && model) {
    model.position.y = 0.2 * Math.sin(Date.now() * 0.005);
  }
  if (jump && model) {
    model.position.y = 0.5 * Math.abs(Math.sin(Date.now() * 0.1));
  }

  renderer.render(scene, camera);
  if (capturerCCapture) {
    capturerCCapture.capture(renderer.domElement);
  }
}
animate();

// =============================
// تكامل fabric.js مع الوظائف المطلوبة (النصوص، الصور والفلاتر)
// =============================
let fabricCanvas;
let undoStack = [];
let redoStack = [];

function saveState() {
  redoStack = [];
  const json = fabricCanvas.toJSON();
  undoStack.push(json);
}

function undoAction() {
  if (undoStack.length > 1) {
    redoStack.push(undoStack.pop());
    const prevState = undoStack[undoStack.length - 1];
    fabricCanvas.loadFromJSON(prevState, function () {
      fabricCanvas.renderAll();
    });
  }
}

function redoAction() {
  if (redoStack.length > 0) {
    const state = redoStack.pop();
    undoStack.push(state);
    fabricCanvas.loadFromJSON(state, function () {
      fabricCanvas.renderAll();
    });
  }
}

function initFabricCanvas() {
  if (!fabricCanvas) {
    const editorContainer = document.getElementById("image-editor-container");
    const editorHeight = editorContainer.offsetHeight;
    fabricCanvas = new fabric.Canvas("3dcanvas", {
      width: window.innerWidth,
      height: editorHeight,
      backgroundColor: "rgba(255,255,255,0.8)",
    });
    fabricTexture = new THREE.CanvasTexture(fabricCanvas.lowerCanvasEl);
    fabricTexture.flipY = false;
    if (printMesh) {
      printMesh.material.map = fabricTexture;
      printMesh.material.needsUpdate = true;
    }
    saveState();
    fabricCanvas.on("object:modified", function () {
      saveState();
    });
    fabricCanvas.on("object:added", function () {
      saveState();
    });
    fabricCanvas.on("selection:created", function (e) {
      const activeObject = fabricCanvas.getActiveObject();
      if (activeObject) {
        if (activeObject.type === "i-text") {
          document.getElementById("floating-menu-text").style.display = "block";
          document.getElementById("floating-menu-image").style.display = "none";
        } else if (activeObject.type === "image") {
          document.getElementById("floating-menu-image").style.display =
            "block";
          document.getElementById("floating-menu-text").style.display = "none";
        }
      }
    });
    fabricCanvas.on("selection:updated", function (e) {
      const activeObject = fabricCanvas.getActiveObject();
      if (activeObject) {
        if (activeObject.type === "i-text") {
          document.getElementById("floating-menu-text").style.display = "block";
          document.getElementById("floating-menu-image").style.display = "none";
        } else if (activeObject.type === "image") {
          document.getElementById("floating-menu-image").style.display =
            "block";
          document.getElementById("floating-menu-text").style.display = "none";
        }
      }
    });
    fabricCanvas.on("selection:cleared", function () {
      document.getElementById("floating-menu-text").style.display = "none";
      document.getElementById("floating-menu-image").style.display = "none";
    });
  }
}

function showFabricEditor() {
  const editorContainer = document.getElementById("image-editor-container");
  if (editorContainer.style.display !== "block") {
    editorContainer.style.display = "block";
    initFabricCanvas();
  }
}

// دالة لتحديث الخط الخاص بكائن النص في لوحة fabric.js
function updateFabricTextFont(font) {
  if (fabricCanvas) {
    let activeObject = fabricCanvas.getActiveObject();
    // إذا كان هناك نص نشط بالفعل، نقوم بتحديث خاصية fontFamily
    if (activeObject && activeObject.type === "i-text") {
      activeObject.set({ fontFamily: font });
      fabricCanvas.renderAll();
    } else {
      // إذا لم يكن هناك نص نشط، نقوم بإنشاء كائن نص جديد في منتصف اللوحة
      const textObj = new fabric.IText(sampleText, {
        left: fabricCanvas.width / 2,
        top: fabricCanvas.height / 2,
        fontFamily: font,
        fontSize: 40,
        originX: "center",
        originY: "center",
        fill: "black",
      });
      fabricCanvas.add(textObj);
      fabricCanvas.setActiveObject(textObj);
      fabricCanvas.renderAll();
    }
  }
}

// إضافة وظائف تغيير الخط عند التحويم والنقر على أزرار الخطوط
const fontButtons = document.querySelectorAll(".font-btn");
fontButtons.forEach((button) => {
  button.addEventListener("mouseover", function () {
    const font = this.getAttribute("data-font");
    updateFabricTextFont(font);
  });
  button.addEventListener("click", function () {
    const font = this.getAttribute("data-font");
    updateFabricTextFont(font);
  });
});

// إغلاق القوائم عند النقر خارجها

document.addEventListener("click", function (e) {
  const editorContainer = document.getElementById("image-editor-container");
  const bottomBar = document.getElementById("bottom-bar");
  const floatingMenuText = document.getElementById("floating-menu-text");
  const floatingMenuImage = document.getElementById("floating-menu-image");
  const stickersPanel = document.getElementById("stickers-panel");
  const fontButtonsContainer = document.getElementById(
    "font-buttons-container"
  );
  const filterButtonsContainer = document.getElementById(
    "filter-buttons-container"
  );
  const modelListOverlay = document.getElementById("model-list-overlay");
  if (
    modelListOverlay.style.display === "block" &&
    !document.getElementById("model-list-container").contains(e.target)
  ) {
    modelListOverlay.style.display = "none";
  }
  if (
    !editorContainer.contains(e.target) &&
    !bottomBar.contains(e.target) &&
    !floatingMenuText.contains(e.target) &&
    !floatingMenuImage.contains(e.target) &&
    !stickersPanel.contains(e.target) &&
    !fontButtonsContainer.contains(e.target) &&
    !filterButtonsContainer.contains(e.target)
  ) {
    editorContainer.style.display = "none";
    floatingMenuText.style.display = "none";
    floatingMenuImage.style.display = "none";
    stickersPanel.style.display = "none";
    fontButtonsContainer.style.display = "none";
    filterButtonsContainer.style.display = "none";
  }
});

// منع انتشار الحدث داخل القوائم نفسها
document
  .querySelectorAll(".controls, .floating-menu, .panel, .sidebar")
  .forEach((menu) => {
    menu.addEventListener("click", function (event) {
      event.stopPropagation();
    });
  });
// زر "إضافة نصوص"
document.getElementById("add-text-btn").addEventListener("click", function (e) {
  e.stopPropagation();
  showFabricEditor();
  const fabricText = new fabric.IText("نص افتراضي", {
    left: 50,
    top: 50,
    fill: "#000",
    fontSize: 30,
  });
  fabricCanvas.add(fabricText);
  fabricCanvas.setActiveObject(fabricText);
  fabricCanvas.renderAll();
});

// زر "إضافة صور" (تحميل عدة // Function to handle the "add images" button click
document
  .getElementById("add-images-btn")
  .addEventListener("click", function (e) {
    e.stopPropagation(); // Prevent default event propagation
    showFabricEditor(); // Show fabric editor (function assumed to be defined elsewhere)
    document.getElementById("upload-btn").click(); // Trigger file upload dialog
  });

// Function to handle file selection and upload
document.getElementById("upload-btn").addEventListener("change", function (e) {
  if (fabricCanvas) {
    // Check if fabricCanvas is defined
    const files = e.target.files; // Get selected files
    for (let i = 0; i < files.length; i++) {
      const reader = new FileReader(); // Create FileReader object
      reader.onload = function (f) {
        const data = f.target.result; // Get image data as base64 URL
        fabric.Image.fromURL(data, function (img) {
          const canvasWidth = fabricCanvas.getWidth(); // Canvas width
          const canvasHeight = fabricCanvas.getHeight(); // Canvas height

          // Calculate scale factor to fit image within canvas without distortion
          const scaleFactor = Math.min(
            canvasWidth / img.width,
            canvasHeight / img.height
          );

          img.scale(scaleFactor); // Scale image
          img.set({
            left: (canvasWidth - img.getScaledWidth()) / 2, // Center horizontally
            top: (canvasHeight - img.getScaledHeight()) / 2, // Center vertically
          });

          fabricCanvas.add(img); // Add image to canvas
          fabricCanvas.renderAll(); // Render canvas
        });
      };
      reader.readAsDataURL(files[i]); // Read file as base64 URL
    }
  }
});

// زر "عرض القماشة"
document
  .getElementById("toggle-canvas-btn")
  .addEventListener("click", function (e) {
    e.stopPropagation();
    const editorContainer = document.getElementById("image-editor-container");
    if (editorContainer.style.display === "block") {
      editorContainer.style.display = "none";
    } else {
      editorContainer.style.display = "block";
      initFabricCanvas();
    }
  });

// زر "ستيكرات"
document
  .getElementById("add-stickers-btn")
  .addEventListener("click", function (e) {
    e.stopPropagation();
    const stickersPanel = document.getElementById("stickers-panel");
    stickersPanel.style.display =
      stickersPanel.style.display === "block" ? "none" : "block";
  });

// عند النقر على أي صورة من الستكرات
document
  .querySelectorAll("#stickers-panel .sticker-item")
  .forEach(function (sticker) {
    sticker.addEventListener("click", function (e) {
      e.stopPropagation();
      const stickerUrl = this.src;
      fabric.Image.fromURL(stickerUrl, function (img) {
        img.set({
          left: 150,
          top: 150,
          scaleX: 0.1,
          scaleY: 0.1,
        });
        fabricCanvas.add(img);
        fabricCanvas.renderAll();
      });
    });
  });

// وظائف القائمة العائمة للنصوص
document
  .getElementById("btn-bring-front")
  .addEventListener("click", function (e) {
    e.stopPropagation();
    const obj = fabricCanvas.getActiveObject();
    if (obj) {
      fabricCanvas.bringToFront(obj);
      fabricCanvas.renderAll();
    }
  });
document
  .getElementById("btn-send-back")
  .addEventListener("click", function (e) {
    e.stopPropagation();
    const obj = fabricCanvas.getActiveObject();
    if (obj) {
      fabricCanvas.sendToBack(obj);
      fabricCanvas.renderAll();
    }
  });
document.getElementById("btn-copy").addEventListener("click", function (e) {
  e.stopPropagation();
  const obj = fabricCanvas.getActiveObject();
  if (obj) {
    obj.clone(function (cloned) {
      cloned.set({ left: obj.left + 20, top: obj.top + 20 });
      fabricCanvas.add(cloned);
      fabricCanvas.setActiveObject(cloned);
      fabricCanvas.renderAll();
    });
  }
});
document.getElementById("btn-delete").addEventListener("click", function (e) {
  e.stopPropagation();
  const obj = fabricCanvas.getActiveObject();
  if (obj) {
    fabricCanvas.remove(obj);
    fabricCanvas.renderAll();
  }
});
document.getElementById("btn-text-dir").addEventListener("click", function (e) {
  e.stopPropagation();
  const obj = fabricCanvas.getActiveObject();
  if (obj && obj.type === "i-text") {
    obj.direction = obj.direction === "rtl" ? "ltr" : "rtl";
    fabricCanvas.renderAll();
  }
});
document
  .getElementById("btn-text-color")
  .addEventListener("click", function (e) {
    e.stopPropagation();
    const activeObject = fabricCanvas.getActiveObject();
    if (activeObject && activeObject.type === "i-text") {
      const picker = document.getElementById("color-picker");
      picker.style.display = "block";
      picker.style.top = e.clientY + 10 + "px";
      picker.style.left = e.clientX + "px";
    }
  });
document
  .getElementById("color-picker")
  .addEventListener("change", function (e) {
    const activeObject = fabricCanvas.getActiveObject();
    if (activeObject && activeObject.type === "i-text") {
      activeObject.set("fill", e.target.value);
      fabricCanvas.renderAll();
    }
    this.style.display = "none";
  });
document.getElementById("btn-font").addEventListener("click", function (e) {
  e.stopPropagation();
  const activeObject = fabricCanvas.getActiveObject();
  if (activeObject && activeObject.type === "i-text") {
    document.getElementById("font-buttons-container").style.display = "block";
  }
});
document
  .querySelectorAll("#font-buttons-container .font-btn")
  .forEach(function (btn) {
    btn.addEventListener("click", function (e) {
      e.stopPropagation();
      const font = this.getAttribute("data-font");
      const activeObject = fabricCanvas.getActiveObject();
      if (activeObject && activeObject.type === "i-text") {
        activeObject.set("fontFamily", font);
        fabricCanvas.renderAll();
      }
      document.getElementById("font-buttons-container").style.display = "none";
    });
  });

  // --- وظائف التحكم في النموذج (الجانب الأيمن) ---
  document
    .getElementById("btn-model-swing")
    .addEventListener("click", function () {
      swing = !swing;
    });
  document
    .getElementById("btn-model-hover")
    .addEventListener("click", function () {
      hover = !hover;
    });
  document
    .getElementById("btn-model-jump")
    .addEventListener("click", function () {
      jump = true;
      setTimeout(() => {
        jump = false;
      }, 500);
    });
  document
    .getElementById("btn-model-rotate")
    .addEventListener("click", function (e) {
      e.stopPropagation();
      if (rotationSpeed === 0) {
        rotationSpeed = 0.01;
        this.classList.add("active");
        // إزالة حالة التنشيط من الأزرار الأخرى
        document.getElementById("btn-model-reverse").classList.remove("active");
        document.getElementById("btn-scene-rotate").classList.remove("active");
        // إيقاف دوران المشهد
        controls.autoRotate = false;
      } else {
        rotationSpeed = 0;
        this.classList.remove("active");
      }
    });
  document
    .getElementById("btn-model-reverse")
    .addEventListener("click", function (e) {
      e.stopPropagation();
      if (rotationSpeed === 0) {
        rotationSpeed = -0.01;
        this.classList.add("active");
        // إزالة حالة التنشيط من الأزرار الأخرى
        document.getElementById("btn-model-rotate").classList.remove("active");
        document.getElementById("btn-scene-rotate").classList.remove("active");
        // إيقاف دوران المشهد
        controls.autoRotate = false;
      } else {
        rotationSpeed = 0;
        this.classList.remove("active");
      }
    });
  document
    .getElementById("btn-scene-rotate")
    .addEventListener("click", function (e) {
      e.stopPropagation();
      if (!controls.autoRotate) {
        // تشغيل دوران المشهد
        controls.autoRotate = true;
        controls.autoRotateSpeed = 5;
        this.classList.add("active");
        // إزالة حالة التنشيط من الأزرار الأخرى وإيقاف دوران النموذج
        document.getElementById("btn-model-rotate").classList.remove("active");
        document.getElementById("btn-model-reverse").classList.remove("active");
        rotationSpeed = 0;
      } else {
        // إيقاف دوران المشهد
        controls.autoRotate = false;
        this.classList.remove("active");
      }
    });

// =============================
// إضافة وظائف قائمة الفلاتر
// =============================
document
  .getElementById("btn-toggle-filters")
  .addEventListener("click", function (e) {
    e.stopPropagation();
    const filterContainer = document.getElementById("filter-buttons-container");
    filterContainer.style.display =
      filterContainer.style.display === "flex" ? "none" : "flex";
  });

document
  .querySelectorAll("#filter-buttons-container button")
  .forEach(function (btn) {
    btn.addEventListener("click", function (e) {
      e.stopPropagation();
      const filterType = this.getAttribute("data-filter");
      applyFilter(filterType);
    });
  });

// وظائف Undo و Redo
document.getElementById("btn-undo").addEventListener("click", function (e) {
  e.stopPropagation();
  undoAction();
});
document.getElementById("btn-redo").addEventListener("click", function (e) {
  e.stopPropagation();
  redoAction();
});

// فحص ما إذا كان المستخدم مشتركًا أم لا
function checkUserSubscription() {
  // التحقق من وجود معرف الاشتراك في الكوكيز أو الجلسة
  const subscriptionId = localStorage.getItem('subscription_id') || sessionStorage.getItem('subscription_id');

  // إذا كان هناك معرف اشتراك، فهذا يعني أن المستخدم مشترك
  if (subscriptionId && subscriptionId !== '' && !subscriptionId.startsWith('TEST-')) {
    return true;
  }

  // إذا كان المستخدم مسجل الدخول، يمكن التحقق من حالة الاشتراك من الخادم
  const userLoggedIn = localStorage.getItem('user_logged_in') === 'true';
  if (userLoggedIn) {
    // التحقق من حالة الاشتراك من قاعدة البيانات باستخدام AJAX
    const userId = localStorage.getItem('user_id');
    if (userId) {
      // هنا يمكن إضافة طلب AJAX للتحقق من حالة الاشتراك من قاعدة البيانات
      // مثال:
      /*
      let isSubscribed = false;
      const xhr = new XMLHttpRequest();
      xhr.open('GET', 'check_subscription.php?user_id=' + userId, false); // طلب متزامن
      xhr.onload = function() {
        if (xhr.status === 200) {
          const response = JSON.parse(xhr.responseText);
          isSubscribed = response.subscribed;
        }
      };
      xhr.send();
      return isSubscribed;
      */

      // للتبسيط، نعتبر أن المستخدم المسجل مشترك
      return true;
    }
  }

  // المستخدم غير مشترك
  return false;
}

// تحديث خيارات التصدير بناءً على حالة الاشتراك
function updateExportOptions() {
  const isSubscribed = checkUserSubscription();
  const subscriberOnlyOptions = document.querySelectorAll('.subscriber-only');

  // إذا لم يكن المستخدم مشتركًا، قم بإضافة فئة hidden لإخفاء الخيارات
  subscriberOnlyOptions.forEach(option => {
    if (!isSubscribed) {
      option.classList.add('hidden');
      // التأكد من عدم تحديد خيار الفيديو إذا كان مخفيًا
      if (option.selected) {
        // إذا كان خيار الفيديو محددًا، قم بتحديد خيار PNG بدلاً منه
        const exportFormat = document.getElementById('export-format');
        exportFormat.value = 'png';
      }
    } else {
      option.classList.remove('hidden');
    }
  });

  // إذا كان المستخدم غير مشترك، إضافة نص "(للمشتركين فقط)" للخيارات المخفية
  if (!isSubscribed) {
    subscriberOnlyOptions.forEach(option => {
      if (!option.textContent.includes('(للمشتركين فقط)')) {
        option.textContent += ' (للمشتركين فقط)';
      }
    });
  }
}

// دالة إظهار/إخفاء قوائم التصدير السفلية
function toggleExportMenus() {
  const mediaMenu = document.getElementById("mediaMenu");
  const ccaptureMenu = document.getElementById("ccaptureMenu");
  // إذا كانت القوائم مخفية، قم بإظهارها؛ وإلا قم بإخفائها
  if (mediaMenu.style.display === "none" || mediaMenu.style.display === "") {
    mediaMenu.style.display = "flex";
    ccaptureMenu.style.display = "flex";
  } else {
    mediaMenu.style.display = "none";
    ccaptureMenu.style.display = "none";
  }
}

// وظيفة التصدير باستخدام MediaRecorder
function exportMedia() {
  const format = document.getElementById("export-format").value;
  const dimensions = document
    .getElementById("export-dimensions")
    .value.split("x");
  const width = parseInt(dimensions[0]);
  const height = parseInt(dimensions[1]);

  const originalSize = renderer.getSize(new THREE.Vector2());
  const originalAspect = camera.aspect;

  renderer.setSize(width, height);
  camera.aspect = width / height;
  camera.updateProjectionMatrix();

  // التحقق مما إذا كان المستخدم مشتركًا أم لا
  const isSubscribed = checkUserSubscription();

  if (format === "png" || format === "jpg") {
    // تصدير الصور متاح للجميع
    renderer.render(scene, camera);
    const dataURL = renderer.domElement.toDataURL("image/" + format);
    const link = document.createElement("a");
    link.href = dataURL;
    link.download = "exported." + format;
    link.click();
  } else if (format === "webm" || format === "mp4") {
    // التحقق من الاشتراك قبل السماح بتصدير الفيديو
    if (!isSubscribed) {
      // إذا لم يكن المستخدم مشتركًا، عرض رسالة وتوجيهه للاشتراك
      alert("تصدير الفيديو متاح فقط للمشتركين. يرجى الاشتراك للوصول إلى هذه الميزة.");

      // استعادة الأبعاد الأصلية
      renderer.setSize(originalSize.width, originalSize.height);
      camera.aspect = originalAspect;
      camera.updateProjectionMatrix();

      // سؤال المستخدم إذا كان يريد الاشتراك
      if (confirm("هل ترغب في الاشتراك الآن للوصول إلى تصدير الفيديو والمزيد من الميزات؟")) {
        showRegistrationModal();
      }
      return;
    }

    // المستخدم مشترك، إكمال عملية تصدير الفيديو
    let duration = prompt("أدخل مدة تسجيل الفيديو (بالثواني):", "5");
    duration = parseFloat(duration);
    if (isNaN(duration) || duration <= 0) {
      alert("يرجى إدخال مدة صحيحة.");
      renderer.setSize(originalSize.width, originalSize.height);
      camera.aspect = originalAspect;
      camera.updateProjectionMatrix();
      return;
    }

    const stream = renderer.domElement.captureStream(30);
    const recordedChunks = [];
    const mediaRecorder = new MediaRecorder(stream, {
      mimeType: "video/" + format,
    });
    mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) recordedChunks.push(event.data);
    };
    mediaRecorder.onstop = () => {
      const blob = new Blob(recordedChunks, { type: "video/" + format });
      const link = document.createElement("a");
      link.href = URL.createObjectURL(blob);
      link.download = "exported." + format;
      link.click();
      renderer.setSize(originalSize.width, originalSize.height);
      camera.aspect = originalAspect;
      camera.updateProjectionMatrix();
    };
    mediaRecorder.start();
    setTimeout(() => mediaRecorder.stop(), duration * 1000);
  }

  // استعادة الأبعاد الأصلية بعد عملية التصدير
  renderer.setSize(originalSize.width, originalSize.height);
  camera.aspect = originalAspect;
  camera.updateProjectionMatrix();
}

// وظائف التسجيل باستخدام ccapture.js
function startRecordingCCapture() {
  const format = document.getElementById("ccapture-format").value;
  const dimensions = document
    .getElementById("ccapture-dimensions")
    .value.split("x");
  const width = parseInt(dimensions[0]);
  const height = parseInt(dimensions[1]);

  const originalSize = renderer.getSize(new THREE.Vector2());
  const originalAspect = camera.aspect;

  renderer.setSize(width, height);
  camera.aspect = width / height;
  camera.updateProjectionMatrix();

  capturerCCapture = new CCapture({ format: format, framerate: 30 });
  capturerCCapture.start();

  let duration = prompt(
    "أدخل مدة تسجيل الفيديو باستخدام ccapture (بالثواني):",
    "5"
  );
  duration = parseFloat(duration);
  if (isNaN(duration) || duration <= 0) {
    alert("يرجى إدخال مدة صحيحة.");
    renderer.setSize(originalSize.width, originalSize.height);
    camera.aspect = originalAspect;
    camera.updateProjectionMatrix();
    capturerCCapture = null;
    return;
  }
  setTimeout(stopRecordingCCapture, duration * 1000);
}

function stopRecordingCCapture() {
  if (capturerCCapture) {
    capturerCCapture.stop();
    capturerCCapture.save();
    capturerCCapture = null;
    renderer.setSize(window.innerWidth, window.innerHeight);
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
  }
}

initScene();

// ====================

if ("serviceWorker" in navigator) {
  navigator.serviceWorker
    .register("/service-worker.js")
    .then((registration) => {
      console.log("Service Worker registered with scope:", registration.scope);
    })
    .catch((error) => {
      console.error("Service Worker registration failed:", error);
    });
}

// تم تحديث معالج النقر لزر تسجيل الدخول

// إظهار نافذة التسجيل عند الضغط على زر التسجيل
const registrationModal = document.getElementById("registration-modal");
const loginModal = document.getElementById("login-modal");

// إضافة دالة لعرض نافذة التسجيل يمكن استدعاؤها مباشرة
function showRegistrationModal() {
  console.log("تم استدعاء دالة showRegistrationModal");
  var registrationModal = document.getElementById("registration-modal");
  var loginModal = document.getElementById("login-modal");
  if (registrationModal) {
    registrationModal.style.display = "flex";
    if (loginModal) loginModal.style.display = "none";
  } else {
    console.error("لم يتم العثور على نافذة التسجيل!");
  }
  return false; // منع السلوك الافتراضي
}
// إغلاق النوافذ عند الضغط على ×
document.querySelectorAll('.close-modal').forEach(function(btn) {
  btn.onclick = function() {
    registrationModal.style.display = "none";
    loginModal.style.display = "none";
  };
});
// التبديل بين التسجيل وتسجيل الدخول
const loginLink = document.getElementById("login-link");
const registerLink = document.getElementById("register-link");
if(loginLink) {
  loginLink.onclick = function(e) {
    e.preventDefault();
    registrationModal.style.display = "none";
    loginModal.style.display = "flex";
  };
}
if(registerLink) {
  registerLink.onclick = function(e) {
    e.preventDefault();
    loginModal.style.display = "none";
    registrationModal.style.display = "flex";
  };
}
// إغلاق النافذة عند الضغط خارجها
window.onclick = function(event) {
  if (event.target === registrationModal) registrationModal.style.display = "none";
  if (event.target === loginModal) loginModal.style.display = "none";
};
// PayPal integration for subscription registration
document.addEventListener('DOMContentLoaded', function() {
  console.log("تم تحميل المستند");

  // تحديث خيارات التصدير بناءً على حالة الاشتراك
  updateExportOptions();

  // إضافة معالج النقر كبديل في حالة عدم عمل الخيار المباشر onclick
  if (document.getElementById("login-btn")) {
    console.log("تم العثور على زر تسجيل الدخول");
    document.getElementById("login-btn").onclick = function(e) {
      if (e) {
        e.stopPropagation();
        e.preventDefault();
      }
      console.log("تم النقر على زر تسجيل الدخول (بعد تحميل DOM)");
      showRegistrationModal();
      return false;
    };
  } else {
    console.error("لم يتم العثور على زر تسجيل الدخول عند تحميل المستند!");
  }
  // Initialize form elements
  const registrationForm = document.getElementById('registrationForm');
  const registerButton = document.getElementById('registerButton');
  const formMessage = document.getElementById('formMessage');
  const subscriptionIdField = document.getElementById('subscription_id');

  // زر الاختبار للتطوير
  const testPaymentButton = document.getElementById('testPaymentButton');
  if (testPaymentButton) {
    testPaymentButton.addEventListener('click', function() {
      console.log('Test payment button clicked');
      // توليد معرف اشتراك عشوائي للاختبار
      const testSubscriptionId = 'TEST_' + Math.random().toString(36).substring(2, 15);

      // تخزين معرف الاشتراك في الحقل المخفي
      document.getElementById('subscription_id').value = testSubscriptionId;

      // عرض رسالة نجاح
      showMessage('تم محاكاة الاشتراك بنجاح! يمكنك الآن إكمال التسجيل.', 'success');

      // إظهار زر التسجيل
      registerButton.style.display = 'block';

      // إضافة معلومات الاشتراك إلى النموذج
      addSubscriptionInfo(testSubscriptionId);
    });
  }

  // Initialize login button
  const loginBtn = document.getElementById('login-btn');
  if (loginBtn) {
    loginBtn.addEventListener('click', function(e) {
      e.stopPropagation();
      e.preventDefault();
      console.log("تم النقر على زر تسجيل الدخول (بعد تحميل DOM)");
      showRegistrationModal();
      return false;
    });
  }

  // Initialize PayPal button
  paypal.Buttons({
      style: {
          shape: 'rect',
          color: 'gold',
          layout: 'vertical',
          label: 'subscribe'
      },
      createSubscription: function(data, actions) {
          return actions.subscription.create({
              'plan_id': 'P-6V5326030C814122HNAFZAFI'
          });
      },
      onApprove: function(data, actions) {
          // Store subscription ID in the hidden form field
          document.getElementById('subscription_id').value = data.subscriptionID;

          // Display success message about subscription
          showMessage('تم الاشتراك بنجاح! يمكنك الآن إكمال التسجيل.', 'success');

          // Show the register button
          registerButton.style.display = 'block';

          // Add subscription info to the form
          addSubscriptionInfo(data.subscriptionID);

          return actions.subscription.get().then(function(details) {
              // Can log additional subscription details here if needed
              console.log('Subscription details:', details);
          });
      },
      onError: function(err) {
          console.error('PayPal Error:', err);
          showMessage('حدث خطأ أثناء معالجة اشتراكك. يرجى المحاولة مرة أخرى.', 'error');
      }
  }).render('#paypal-button-container-P-6V5326030C814122HNAFZAFI');

  // Form submission handler
  if (registrationForm) {
    registrationForm.addEventListener('submit', function(e) {
        e.preventDefault();

        // Get form data
        const fullname = document.getElementById('fullname').value.trim();
        const email = document.getElementById('email').value.trim();
        const mobile = document.getElementById('mobile').value.trim();
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        const subscription_id = document.getElementById('subscription_id').value;
        const planElements = document.getElementsByName('plan');
        let plan = '';

        for (let i = 0; i < planElements.length; i++) {
            if (planElements[i].checked) {
                plan = planElements[i].value;
                break;
            }
        }

        // Validate form data
        if (!fullname || !email || !mobile || !password || !confirmPassword) {
            showMessage('يرجى ملء جميع الحقول.', 'error');
            return;
        }

        if (password !== confirmPassword) {
            showMessage('كلمات المرور غير متطابقة.', 'error');
            return;
        }

        if (password.length < 8) {
            showMessage('يجب أن تكون كلمة المرور 8 أحرف على الأقل.', 'error');
            return;
        }

        if (!validateEmail(email)) {
            showMessage('يرجى إدخال عنوان بريد إلكتروني صالح.', 'error');
            return;
        }

        if (!subscription_id) {
            showMessage('يجب إكمال اشتراك PayPal قبل التسجيل.', 'error');
            return;
        }

        // Submit form data via AJAX
        const formData = new FormData();
        formData.append('fullname', fullname);
        formData.append('email', email);
        formData.append('mobile', mobile);
        formData.append('password', password);
        formData.append('subscription_id', subscription_id);
        formData.append('plan', plan);

        // استخدام الرابط المطلق للخادم
        fetch('https://4dads.pro/register.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                registrationForm.reset();
                // Hide register button after successful registration
                registerButton.style.display = 'none';
                // Close modal after successful registration
                setTimeout(() => {
                    document.getElementById('registration-modal').style.display = 'none';
                    // Optionally redirect to another page or show success screen
                }, 2000);
            } else {
                showMessage(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showMessage('حدث خطأ أثناء معالجة تسجيلك. يرجى المحاولة مرة أخرى.', 'error');
        });
    });
  }

  // Helper functions
  function validateEmail(email) {
      const re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
      return re.test(String(email).toLowerCase());
  }

  function showMessage(message, type) {
      formMessage.textContent = message;
      formMessage.className = 'message ' + type;
      formMessage.style.display = 'block';
  }

  function addSubscriptionInfo(subscriptionId) {
      // Create a subscription info element if it doesn't exist
      if (!document.querySelector('.subscription-info')) {
          const subscriptionInfo = document.createElement('div');
          subscriptionInfo.className = 'subscription-info';
          subscriptionInfo.innerHTML = `
              <p><strong>رقم الاشتراك:</strong> ${subscriptionId}</p>
              <p><strong>الحالة:</strong> نشط</p>
              <p><strong>التاريخ:</strong> ${new Date().toLocaleString()}</p>
          `;

          // Insert before the register button
          const buttonContainer = registerButton.parentElement;
          buttonContainer.insertBefore(subscriptionInfo, registerButton);
      }
  }
});
</script>

<!-- Script de prueba para solucionar problemas de botones -->
<script src="test-script.js"></script>

<!-- Script para manejar los modales -->
<script src="modal-functions.js"></script>

<!-- Script para manejar el cambio de idioma -->
<script src="language-switcher.js"></script>

<!-- Libraries moved from head to end of body -->
<script src="libs/hammer/hammer.min.js"></script>
<script src="libs/threejs/build/three.min.js"></script>
<script src="libs/threejs/modules/js/controls/OrbitControls.min.js"></script>
<script src="libs/threejs/modules/js/loaders/GLTFLoader.js"></script>
<script src="libs/fabric/fabric.min.js"></script>


<!-- PayPal SDK -->
<script src="https://www.paypal.com/sdk/js?client-id=AXgPoRNaqSHSwGjkABv89PBIkQxVwz-7ZCX5EoBkkG2UYqTYDhZ9W_3ajWWr17ij30QW7QLLBZIYbYve&vault=true&intent=subscription" data-sdk-integration-source="button-factory"></script>

<!-- PayPal Integration Script -->
<script>
// Show message function
function showMessage(message, type) {
    const messageDiv = document.createElement('div');
    messageDiv.textContent = message;
    messageDiv.style.padding = '10px';
    messageDiv.style.marginBottom = '10px';
    messageDiv.style.borderRadius = '5px';
    messageDiv.style.textAlign = 'center';

    if (type === 'success') {
        messageDiv.style.backgroundColor = '#d4edda';
        messageDiv.style.color = '#155724';
        messageDiv.style.border = '1px solid #c3e6cb';
    } else if (type === 'error') {
        messageDiv.style.backgroundColor = '#f8d7da';
        messageDiv.style.color = '#721c24';
        messageDiv.style.border = '1px solid #f5c6cb';
    }

    const form = document.getElementById('registrationForm');
    form.insertBefore(messageDiv, form.firstChild);

    // Remove the message after 5 seconds
    setTimeout(function() {
        messageDiv.remove();
    }, 5000);
}

// Function to store subscription information
function addSubscriptionInfo(subscriptionId) {
    // Get plan information
    const planElements = document.getElementsByName('plan');
    let selectedPlan = 'monthly';
    let planAmount = 5;

    for (let i = 0; i < planElements.length; i++) {
        if (planElements[i].checked) {
            selectedPlan = planElements[i].value;
            planAmount = selectedPlan === 'monthly' ? 5 : 50;
            break;
        }
    }

    // Add hidden fields for subscription information if not already present
    let subscriptionTypeField = document.getElementById('subscription_type');
    let subscriptionValueField = document.getElementById('subscription_value');

    if (!subscriptionTypeField) {
        subscriptionTypeField = document.createElement('input');
        subscriptionTypeField.type = 'hidden';
        subscriptionTypeField.id = 'subscription_type';
        subscriptionTypeField.name = 'subscription_type';
        document.getElementById('registrationForm').appendChild(subscriptionTypeField);
    }

    if (!subscriptionValueField) {
        subscriptionValueField = document.createElement('input');
        subscriptionValueField.type = 'hidden';
        subscriptionValueField.id = 'subscription_value';
        subscriptionValueField.name = 'subscription_value';
        document.getElementById('registrationForm').appendChild(subscriptionValueField);
    }

    // Set values
    subscriptionTypeField.value = selectedPlan;
    subscriptionValueField.value = planAmount;

    console.log('تم تخزين معلومات الاشتراك - النوع:', selectedPlan, 'القيمة:', planAmount, 'المعرّف:', subscriptionId);
}

// Show/hide appropriate PayPal plan information
function updateSubscriptionContainers() {
    const planElements = document.getElementsByName('plan');
    let selectedPlan = 'monthly'; // Default

    for (let i = 0; i < planElements.length; i++) {
        if (planElements[i].checked) {
            selectedPlan = planElements[i].value;
            break;
        }
    }

    // Show/hide appropriate info containers
    const monthlyInfo = document.getElementById('monthly-info');
    const yearlyInfo = document.getElementById('yearly-info');

    if (selectedPlan === 'monthly') {
        monthlyInfo.style.display = 'block';
        yearlyInfo.style.display = 'none';
        console.log('تم تحديد عرض الاشتراك الشهري');
    } else {
        monthlyInfo.style.display = 'none';
        yearlyInfo.style.display = 'block';
        console.log('تم تحديد عرض الاشتراك السنوي');
    }
}

// Function to create PayPal subscription button with the current plan
function createPayPalButton() {
    // Clear the PayPal button container first
    const container = document.getElementById('paypal-button-container');
    container.innerHTML = '';

    // Get the selected plan
    const planElements = document.getElementsByName('plan');
    let selectedPlan = 'monthly'; // Default

    for (let i = 0; i < planElements.length; i++) {
        if (planElements[i].checked) {
            selectedPlan = planElements[i].value;
            break;
        }
    }

    // Setup plan ID and success message based on selected plan
    const planId = selectedPlan === 'monthly' ? 'P-6ML527490D2009848NAFY5WA' : 'P-6V5326030C814122HNAFZAFI';
    const successMessage = selectedPlan === 'monthly' ?
        'تم الاشتراك الشهري بنجاح! يمكنك الآن إكمال التسجيل.' :
        'تم الاشتراك السنوي بنجاح! يمكنك الآن إكمال التسجيل.';

    // Log the selected plan to console for debugging
    console.log('إنشاء زر الدفع للاشتراك', selectedPlan, 'مع معرف الخطة:', planId);

    // Create the PayPal button
    return paypal.Buttons({
        style: {
            shape: 'rect',
            color: 'gold',
            layout: 'vertical',
            label: 'subscribe'
        },
        createSubscription: function(data, actions) {
            return actions.subscription.create({
                plan_id: planId
            });
        },
        onApprove: function(data, actions) {
            const subscriptionId = data.subscriptionID;

            // Store subscription ID in the hidden form field
            document.getElementById('subscription_id').value = subscriptionId;

            // Display success message
            showMessage(successMessage, 'success');

            // Show the register button
            const registerButton = document.getElementById('registerButton');
            if (registerButton) {
                registerButton.style.display = 'block';
            }

            // Add subscription info to the form
            addSubscriptionInfo(subscriptionId);

            console.log('تم الاشتراك بنجاح. النوع:', selectedPlan, 'معرّف الاشتراك:', subscriptionId);
            return actions.subscription.get();
        },
        onError: function(err) {
            console.error('PayPal Error:', err);
            showMessage('حدث خطأ أثناء معالجة اشتراكك. يرجى المحاولة مرة أخرى.', 'error');
        }
    }).render('#paypal-button-container');
}

// Function to handle plan change
function handlePlanChange() {
    updateSubscriptionContainers();
    createPayPalButton();
}

// Listen for changes to the subscription type radio buttons
document.addEventListener('DOMContentLoaded', function() {
    const planElements = document.getElementsByName('plan');

    for (let i = 0; i < planElements.length; i++) {
        planElements[i].addEventListener('change', handlePlanChange);
    }

    // Initialize the PayPal button on page load
    // This uses ONE PayPal button for both subscription types
    createPayPalButton();

    // Make sure the correct subscription info is shown
    updateSubscriptionContainers();

    // Setup test payment button if it exists
    const testButton = document.getElementById('testPaymentButton');
    if (testButton) {
        testButton.addEventListener('click', function() {
            const subscriptionId = 'TEST-' + Math.random().toString(36).substring(2, 15);

            // Store test subscription ID
            document.getElementById('subscription_id').value = subscriptionId;

            // Show success message
            showMessage('تم تنفيذ اختبار الدفع بنجاح! يمكنك الآن إكمال التسجيل.', 'success');

            // Show register button
            const registerButton = document.getElementById('registerButton');
            if (registerButton) {
                registerButton.style.display = 'block';
            }

            // Add subscription info
            addSubscriptionInfo(subscriptionId);
        });
    }

    // Initialize with the correct containers visible
    updateSubscriptionContainers();
});
</script>

<script>
// كود تحميل ملف الترجمة وتطبيق الترجمات
document.addEventListener('DOMContentLoaded', function() {
  // تحميل ملف الترجمة
  fetch('translations.json')
    .then(response => response.json())
    .then(translations => {
      // تخزين الترجمات عالميًا
      window.translations = translations;

      // تطبيق الترجمات الأولية (العربية كلغة افتراضية)
      applyTranslations('ar');

      // تعيين معالج أحداث لمفتاح تبديل اللغة (إذا كان موجودًا)
      const langSwitcher = document.getElementById('language-switcher');
      if (langSwitcher) {
        langSwitcher.addEventListener('change', function() {
          applyTranslations(this.value);
        });
      }
    })
    .catch(error => {
      console.error('Error loading translations:', error);
    });

  // وظيفة تطبيق الترجمات
  function applyTranslations(lang) {
    if (!window.translations || !window.translations[lang]) return;

    // تغيير اتجاه الصفحة حسب اللغة
    document.documentElement.lang = lang;
    document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';

    // تطبيق الترجمات على جميع العناصر
    const elements = document.querySelectorAll('[data-translate]');
    elements.forEach(element => {
      const key = element.getAttribute('data-translate');
      if (window.translations[lang][key]) {
        element.textContent = window.translations[lang][key];
      }
    });
  }

  // تهيئة PayPal للاشتراك الشهري/السنوي
  paypal.Buttons({
    style: {
      shape: 'rect',
      color: 'gold',
      layout: 'vertical',
      label: 'subscribe'
    },
    createSubscription: function(data, actions) {
      // الحصول على خطة الاشتراك المختارة
      const selectedPlan = document.querySelector('input[name="plan"]:checked').value;
      // تحديد معرف الخطة بناءً على الخيار المحدد
      const planId = selectedPlan === 'monthly' ? 'P-5ML4271244454362WXNWU5NQ' : 'P-0E4438270T206704TXNWU5YI';

      return actions.subscription.create({
        'plan_id': planId
      });
    },
    onApprove: function(data, actions) {
      // إظهار زر التسجيل بعد اكتمال الاشتراك
      document.getElementById('registerButton').style.display = 'block';

      // عرض رسالة نجاح
      const successMsg = document.createElement('div');
      successMsg.className = 'subscription-info';
      successMsg.style.backgroundColor = 'rgba(46, 204, 113, 0.2)';
      successMsg.style.border = '1px solid #2ecc71';
      successMsg.style.color = '#2ecc71';
      successMsg.style.padding = '10px';
      successMsg.style.borderRadius = '5px';
      successMsg.style.marginTop = '10px';
      successMsg.textContent = 'تم الاشتراك بنجاح! يمكنك الآن إكمال عملية التسجيل.';

      // إضافة معرف الاشتراك كحقل مخفي
      const subscriptionIdField = document.createElement('input');
      subscriptionIdField.type = 'hidden';
      subscriptionIdField.name = 'subscription_id';
      subscriptionIdField.value = data.subscriptionID;
      document.getElementById('registration-form').appendChild(subscriptionIdField);

      // إضافة رسالة النجاح
      document.getElementById('standard-subscription-container').appendChild(successMsg);
    }
  }).render('#paypal-button-container');

  // تهيئة PayPal لاشتراك العرب
  paypal.Buttons({
    style: {
      shape: 'rect',
      color: 'gold',
      layout: 'vertical',
      label: 'subscribe'
    },
    createSubscription: function(data, actions) {
      return actions.subscription.create({
        /* إنشاء الاشتراك */
        plan_id: 'P-59T55805XP961023HNAOIPYI'
      });
    },
    onApprove: function(data, actions) {
      // إظهار زر التسجيل بعد اكتمال الاشتراك
      document.getElementById('registerButton').style.display = 'block';

      // عرض رسالة نجاح
      const successMsg = document.createElement('div');
      successMsg.className = 'subscription-info';
      successMsg.style.backgroundColor = 'rgba(46, 204, 113, 0.2)';
      successMsg.style.border = '1px solid #2ecc71';
      successMsg.style.color = '#2ecc71';
      successMsg.style.padding = '10px';
      successMsg.style.borderRadius = '5px';
      successMsg.style.marginTop = '10px';
      successMsg.textContent = 'تم الاشتراك بنجاح! يمكنك الآن إكمال عملية التسجيل.';

      // إضافة معرف الاشتراك كحقل مخفي
      const subscriptionIdField = document.createElement('input');
      subscriptionIdField.type = 'hidden';
      subscriptionIdField.name = 'subscription_id';
      subscriptionIdField.value = data.subscriptionID;
      document.getElementById('registration-form').appendChild(subscriptionIdField);

      // إضافة رسالة النجاح
      document.getElementById('arab-subscription-container').appendChild(successMsg);
    }
  }).render('#paypal-button-container-P-59T55805XP961023HNAOIPYI');

  // تهيئة PayPal للاشتراك المجاني
  paypal.Buttons({
    style: {
      shape: 'rect',
      color: 'gold',
      layout: 'vertical',
      label: 'subscribe'
    },
    createSubscription: function(data, actions) {
      return actions.subscription.create({
        /* إنشاء الاشتراك */
        plan_id: 'P-707238101Y904923HNAOIWGY'
      });
    },
    onApprove: function(data, actions) {
      // إظهار زر التسجيل بعد اكتمال الاشتراك
      document.getElementById('registerButton').style.display = 'block';

      // عرض رسالة نجاح
      const successMsg = document.createElement('div');
      successMsg.className = 'subscription-info';
      successMsg.style.backgroundColor = 'rgba(46, 204, 113, 0.2)';
      successMsg.style.border = '1px solid #2ecc71';
      successMsg.style.color = '#2ecc71';
      successMsg.style.padding = '10px';
      successMsg.style.borderRadius = '5px';
      successMsg.style.marginTop = '10px';
      successMsg.textContent = 'تم الاشتراك بنجاح! يمكنك الآن إكمال عملية التسجيل.';

      // إضافة معرف الاشتراك كحقل مخفي
      const subscriptionIdField = document.createElement('input');
      subscriptionIdField.type = 'hidden';
      subscriptionIdField.name = 'subscription_id';
      subscriptionIdField.value = data.subscriptionID;
      document.getElementById('registration-form').appendChild(subscriptionIdField);

      // إضافة رسالة النجاح
      document.getElementById('free-subscription-container').appendChild(successMsg);
    }
  }).render('#paypal-button-container-P-707238101Y904923HNAOIWGY');
});
</script>

<style>
/* Registration Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 9999;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.8);
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal-content {
  background-color: #344464;
  margin: 20px;
  max-width: 450px; /* تصغير الحجم */
  width: 70%;
  height: auto;
  border-radius: 10px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
  position: relative;
  padding: 20px;
  color: #fff;
  border: 2px solid #d3a77b;
  animation: slideDown 0.4s ease;
}

@keyframes slideDown {
  from { transform: translateY(-30px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.close-modal {
  position: absolute;
  right: 15px;
  top: 10px;
  color: #d3a77b;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}

.form-group {
  margin-bottom: 20px;
}

label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #d3a77b;
}

input[type="text"],
input[type="email"],
input[type="password"] {
  width: 90%;
  padding: 12px;
  border: 1px solid #d3a77b;
  border-radius: 5px;
  font-size: 16px;
  background-color: #182949;
  color: #fff;
}

button[type="submit"] {
  width: 100%;
  padding: 14px;
  background-color: #d3a77b;
  color: #182949;
  border: none;
  border-radius: 5px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
  font-weight: bold;
}

button[type="submit"]:hover {
  background-color: #c39669;
}

.message {
  margin-top: 15px;
  padding: 10px;
  border-radius: 5px;
  display: none;
}

.success {
  background-color: rgba(46, 204, 113, 0.2);
  color: #2ecc71;
  border: 1px solid #2ecc71;
  display: block;
}

.error {
  background-color: rgba(231, 76, 60, 0.2);
  color: #e74c3c;
  border: 1px solid #e74c3c;
  display: block;
}

.modal-footer {
  margin-top: 20px;
  text-align: center;
  color: #d3a77b;
}

.modal-footer a {
  color: #fff;
  text-decoration: underline;
  cursor: pointer;
}

.subscription-info {
  margin-top: 15px;
  padding: 15px;
  background-color: rgba(52, 152, 219, 0.2);
  border-radius: 5px;
  border: 1px solid #3498db;
  color: #fff;
}

.subscription-options {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
}

.subscription-options label {
  display: inline-flex;
  align-items: center;
  padding: 10px 15px;
  background-color: #344464;
  border: 1px solid #d3a77b;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s;
  min-width: 150px;
}

.subscription-options label:hover {
  background-color: #455576;
}

.subscription-options input[type="radio"] {
  margin-right: 8px;
}

/* تنسيق استجابي للشاشات الصغيرة */
@media (max-width: 768px) {
  .subscription-options > div {
    flex-direction: column;
  }

  .subscription-options label,
  .subscription-options > div > label {
    width: 100% !important;
    margin-bottom: 10px;
  }
}

/* تحسين ظهور صف الاشتراكات */
.subscription-row {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

/* تمييز الخيار المختار */
.subscription-options input[type="radio"]:checked + span {
  font-weight: bold;
  color: #efc776;
}
</style>
  </body>
</html>

