<!-- سيتم الآن إدراج تحديث أدوات الفرش والرسم من 2.html إلى نسخة 1.html الأصلية. الملف محدث أدناه-->
<!-- تم إصلاح الخطأ: تحميل مكتبة Fabric.js + تعريف canvas قبل استدعاء setupDrawingEventHandlers -->

<!-- تضمين مكتبة Fabric.js -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.1/fabric.min.js"></script>

<!-- Drawing Controls HTML -->
<<div class="drawing-controls">
    <label for="drawing-color">
      Color:
      <input type="color" id="drawing-color" value="#000000">
    </label>
    <label for="drawing-line-width">
      Line Width:
      <input type="range" min="1" max="100" id="drawing-line-width" value="5">
      <span id="line-width-info">5</span>
    </label>
    <label for="drawing-mode-selector">
      Drawing Mode:
      <select id="drawing-mode-selector">
            <option>Pencil</option>
            <option>Circle</option>
            <option>Spray</option>
            <option>Pattern</option>
            <option>hline</option>
            <option>vline</option>
            <option>square</option>
            <option>diamond</option>
            <option>texture</option>
        </select>
    </label>
</div>

    <label for="drawing-line-width">
        Line width:
        <span class="info" id="line-width-info">30</span>
        <input type="range" min="1" max="150" id="drawing-line-width" value="30">
    </label>

    <label for="drawing-color">
        Line color:
        <input type="color" id="drawing-color" value="#76cef4">
    </label>

    <label for="drawing-shadow-color">
        Shadow color:
        <input type="color" id="drawing-shadow-color" value="#5a7896">
    </label>

    <label for="drawing-shadow-width">
        Shadow width:
        <span class="info" id="shadow-width-info">0</span>
        <input type="range" min="0" max="50" id="drawing-shadow-width" value="0">
    </label>

    <label for="drawing-shadow-offset">
        Shadow offset:
        <span class="info" id="shadow-offset-info">0</span>
        <input type="range" min="0" max="50" id="drawing-shadow-offset" value="0">
    </label>

    <button id="clear-drawings">Clear Drawings</button>
</div>

<!-- Drawing Script: ensure canvas is defined before handlers -->
<script>
    // تأكد من تحميل مكتبة fabric.js قبل تنفيذ هذا السكريبت
    document.addEventListener("DOMContentLoaded", function () {
        if (typeof fabric === "undefined") {
            alert("Fabric.js library is not loaded. Please check the script link.");
            return;
        }

        const canvas = new fabric.Canvas('c', {
            width: 800,
            height: 600,
            backgroundColor: '#ffffff'
        });

        function setupDrawingEventHandlers() {
            const drawingColorEl = document.getElementById('drawing-color');
            const drawingShadowColorEl = document.getElementById('drawing-shadow-color');
            const drawingLineWidthEl = document.getElementById('drawing-line-width');
            const drawingShadowWidth = document.getElementById('drawing-shadow-width');
            const drawingShadowOffset = document.getElementById('drawing-shadow-offset');
            const selectorEl = document.getElementById('drawing-mode-selector');

            drawingColorEl.addEventListener('change', updateBrush);
            drawingShadowColorEl.addEventListener('change', updateBrush);

            drawingLineWidthEl.addEventListener('input', () => {
                document.getElementById('line-width-info').textContent = drawingLineWidthEl.value;
                updateBrush();
            });

            drawingShadowWidth.addEventListener('input', () => {
                document.getElementById('shadow-width-info').textContent = drawingShadowWidth.value;
                updateBrush();
            });

            drawingShadowOffset.addEventListener('input', () => {
                document.getElementById('shadow-offset-info').textContent = drawingShadowOffset.value;
                updateBrush();
            });

            selectorEl.addEventListener('change', handleDrawingModeChange);
        }

        function updateBrush() {
            const brush = canvas.freeDrawingBrush;
            if (!brush) return;

            const drawingColorEl = document.getElementById('drawing-color');
            const drawingShadowColorEl = document.getElementById('drawing-shadow-color');
            const drawingLineWidthEl = document.getElementById('drawing-line-width');
            const drawingShadowWidth = document.getElementById('drawing-shadow-width');
            const drawingShadowOffset = document.getElementById('drawing-shadow-offset');

            brush.color = drawingColorEl.value;
            brush.width = parseInt(drawingLineWidthEl.value, 10) || 1;
            brush.shadow = new fabric.Shadow({
                blur: parseInt(drawingShadowWidth.value, 10) || 0,
                offsetX: parseInt(drawingShadowOffset.value, 10) || 0,
                offsetY: parseInt(drawingShadowOffset.value, 10) || 0,
                color: drawingShadowColorEl.value,
            });
        }

        function handleDrawingModeChange() {
            const selectorEl = document.getElementById('drawing-mode-selector');
            const val = selectorEl.value;

            if (val === 'hline') {
                const brush = new fabric.PatternBrush(canvas);
                brush.getPatternSrc = () => {
                    const patternCanvas = document.createElement('canvas');
                    patternCanvas.width = patternCanvas.height = 10;
                    const ctx = patternCanvas.getContext('2d');
                    ctx.strokeStyle = brush.color;
                    ctx.lineWidth = 5;
                    ctx.beginPath();
                    ctx.moveTo(0, 5);
                    ctx.lineTo(10, 5);
                    ctx.stroke();
                    return patternCanvas;
                };
                canvas.freeDrawingBrush = brush;
            } else if (val === 'vline') {
                const brush = new fabric.PatternBrush(canvas);
                brush.getPatternSrc = () => {
                    const patternCanvas = document.createElement('canvas');
                    patternCanvas.width = patternCanvas.height = 10;
                    const ctx = patternCanvas.getContext('2d');
                    ctx.strokeStyle = brush.color;
                    ctx.lineWidth = 5;
                    ctx.beginPath();
                    ctx.moveTo(5, 0);
                    ctx.lineTo(5, 10);
                    ctx.stroke();
                    return patternCanvas;
                };
                canvas.freeDrawingBrush = brush;
            } else {
                canvas.freeDrawingBrush = new fabric[val + 'Brush'](canvas) || new fabric.PencilBrush(canvas);
            }

            updateBrush();
            canvas.isDrawingMode = true;
        }

        document.getElementById('clear-drawings').addEventListener('click', function() {
            const objects = canvas.getObjects().filter(obj => obj.type === 'path');
            objects.forEach(obj => canvas.remove(obj));
            canvas.renderAll();
        });

        setupDrawingEventHandlers();
    });
</script>
