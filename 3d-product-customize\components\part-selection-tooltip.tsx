"use client"

import { useState, useEffect } from "react"

interface PartSelectionTooltipProps {
  show: boolean
  position: { x: number; y: number }
}

export function PartSelectionTooltip({ show, position }: PartSelectionTooltipProps) {
  const [visible, setVisible] = useState(false)

  useEffect(() => {
    if (show) {
      setVisible(true)
      const timer = setTimeout(() => {
        setVisible(false)
      }, 2000)
      return () => clearTimeout(timer)
    }
    setVisible(false)
  }, [show])

  if (!visible) return null

  return (
    <div
      className="absolute z-50 rounded-md bg-black/80 px-3 py-1.5 text-xs text-white"
      style={{
        top: position.y + 20,
        left: position.x,
        transform: "translateX(-50%)",
      }}
    >
      انقر نقرًا مزدوجًا لتخصيص هذا الجزء
    </div>
  )
}
