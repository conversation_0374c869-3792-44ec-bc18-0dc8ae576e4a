// src/app/api/auth/google/route.js
// [!] هذا الملف يتعامل مع طلبات المصادقة باستخدام Google
// [!] يجب تعديل متغيرات Google OAuth حسب إعدادات مشروع Google Cloud الخاص بك

import { NextResponse } from 'next/server';
import { OAuth2Client } from 'google-auth-library';

// [!] متغيرات يجب تعديلها: GOOGLE_CLIENT_ID
const GOOGLE_CLIENT_ID = 'YOUR_GOOGLE_CLIENT_ID'; // يجب استبدالها بمعرف العميل الخاص بك من Google Cloud Console

// إنشاء عميل OAuth2
const client = new OAuth2Client(GOOGLE_CLIENT_ID);

export async function POST(request) {
  try {
    const { token } = await request.json();

    if (!token) {
      return NextResponse.json(
        { success: false, message: 'ر<PERSON><PERSON> المصادقة مفقود' },
        { status: 400 }
      );
    }

    // التحقق من صحة رمز المصادقة من Google
    const ticket = await client.verifyIdToken({
      idToken: token,
      audience: GOOGLE_CLIENT_ID,
    });

    // الحصول على بيانات المستخدم من رمز المصادقة
    const payload = ticket.getPayload();
    
    // استخراج المعلومات المطلوبة
    const { sub: googleId, name, email, picture } = payload;

    // [!] في بيئة الإنتاج، يجب استخدام اتصال حقيقي بقاعدة البيانات
    // هذا مثال لكيفية تنفيذ ذلك باستخدام mysql2
    
    /*
    // استيراد مكتبة mysql2
    import mysql from 'mysql2/promise';
    
    // إنشاء اتصال بقاعدة البيانات
    const connection = await mysql.createConnection({
      host: 'DB_HOST',      // مثال: 'mysql-4dads.hostinger.com'
      user: 'DB_USER',      // مثال: 'u123456789_4dads'
      password: 'DB_PASSWORD', // كلمة المرور الخاصة بقاعدة البيانات
      database: 'DB_NAME',  // مثال: 'u123456789_4dads'
    });
    
    // التحقق مما إذا كان المستخدم موجودًا بالفعل
    const [existingUsers] = await connection.execute(
      'SELECT * FROM users WHERE google_id = ? OR email = ?',
      [googleId, email]
    );
    
    let userId;
    
    if (existingUsers.length > 0) {
      // تحديث بيانات المستخدم الموجود
      userId = existingUsers[0].id;
      await connection.execute(
        'UPDATE users SET google_id = ?, name = ?, profile_picture = ?, last_login = NOW() WHERE id = ?',
        [googleId, name, picture, userId]
      );
    } else {
      // إنشاء مستخدم جديد
      const [result] = await connection.execute(
        'INSERT INTO users (name, email, google_id, profile_picture, created_at, last_login) VALUES (?, ?, ?, ?, NOW(), NOW())',
        [name, email, googleId, picture]
      );
      userId = result.insertId;
    }
    
    // الحصول على بيانات المستخدم الكاملة
    const [userRows] = await connection.execute(
      'SELECT id, name, email, profile_picture FROM users WHERE id = ?',
      [userId]
    );
    
    const user = userRows[0];
    
    // إغلاق الاتصال بقاعدة البيانات
    await connection.end();
    */
    
    // للاختبار، نعيد استجابة نجاح مع بيانات المستخدم من Google
    return NextResponse.json(
      { 
        success: true, 
        message: 'تم تسجيل الدخول بنجاح باستخدام Google',
        user: {
          id: 1, // قيمة وهمية للاختبار
          name,
          email,
          profile_picture: picture
        }
      },
      { status: 200 }
    );
    
  } catch (error) {
    console.error('Google authentication error:', error);
    return NextResponse.json(
      { success: false, message: 'فشل المصادقة باستخدام Google' },
      { status: 500 }
    );
  }
}
