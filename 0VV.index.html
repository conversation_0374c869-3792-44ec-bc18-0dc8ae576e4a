<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Fabric.js Canvas Editor</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/hammer.js/2.0.8/hammer.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            overflow: hidden;
            height: 100vh;
        }

        .app-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        /* Top Toolbar */
        .top-toolbar {
            background: #2c3e50;
            color: white;
            padding: 8px 16px;
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: wrap;
            min-height: 60px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .toolbar-group {
            display: flex;
            gap: 4px;
            align-items: center;
        }

        .toolbar-btn {
            background: transparent;
            border: 1px solid rgba(255,255,255,0.2);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
            position: relative;
            min-width: 44px;
            min-height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .toolbar-btn:hover {
            background: rgba(255,255,255,0.1);
            border-color: rgba(255,255,255,0.3);
        }

        .toolbar-btn.active {
            background: #3498db;
            border-color: #3498db;
        }

        .toolbar-btn .tooltip {
            position: absolute;
            bottom: -35px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.2s;
            z-index: 1000;
        }

        .toolbar-btn:hover .tooltip {
            opacity: 1;
        }

        /* Canvas Container */
        .canvas-container {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #ecf0f1;
            padding: 20px;
            overflow: hidden;
        }

        #canvas-wrapper {
            background: white;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }

        /* Bottom Sub-toolbar */
        .sub-toolbar {
            background: #34495e;
            color: white;
            padding: 12px 16px;
            display: flex;
            align-items: center;
            gap: 12px;
            flex-wrap: wrap;
            min-height: 70px;
            box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
        }

        .sub-toolbar-group {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 4px 8px;
            border-radius: 6px;
            background: rgba(255,255,255,0.05);
        }

        .sub-toolbar label {
            font-size: 12px;
            color: #bdc3c7;
            margin-right: 4px;
        }

        .sub-toolbar input, .sub-toolbar select {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            color: white;
            padding: 6px 8px;
            border-radius: 4px;
            font-size: 12px;
            min-width: 60px;
        }

        .sub-toolbar input[type="color"] {
            width: 40px;
            height: 32px;
            padding: 2px;
            cursor: pointer;
        }

        .sub-toolbar input[type="range"] {
            width: 80px;
        }

        /* Color Picker */
        .color-picker {
            display: flex;
            gap: 4px;
            align-items: center;
        }

        .color-swatch {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            border: 2px solid rgba(255,255,255,0.3);
            cursor: pointer;
            transition: transform 0.2s;
        }

        .color-swatch:hover {
            transform: scale(1.1);
        }

        .color-swatch.active {
            border-color: #3498db;
            transform: scale(1.1);
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 10000;
            justify-content: center;
            align-items: center;
        }

        .modal.active {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            padding: 24px;
            max-width: 90vw;
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #eee;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
            padding: 4px;
        }

        .png-library-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
            gap: 12px;
            margin-top: 16px;
        }

        .png-item {
            aspect-ratio: 1;
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
            font-size: 24px;
        }

        .png-item:hover {
            border-color: #3498db;
            transform: scale(1.05);
        }

        /* Crop Overlay */
        .crop-overlay {
            position: absolute;
            border: 2px dashed #3498db;
            background: rgba(52, 152, 219, 0.1);
            cursor: move;
            display: none;
        }

        .crop-handle {
            position: absolute;
            width: 10px;
            height: 10px;
            background: #3498db;
            border: 2px solid white;
            border-radius: 50%;
        }

        .crop-handle.nw { top: -5px; left: -5px; cursor: nw-resize; }
        .crop-handle.ne { top: -5px; right: -5px; cursor: ne-resize; }
        .crop-handle.sw { bottom: -5px; left: -5px; cursor: sw-resize; }
        .crop-handle.se { bottom: -5px; right: -5px; cursor: se-resize; }

        /* Responsive Design */
        @media (max-width: 768px) {
            .top-toolbar, .sub-toolbar {
                padding: 8px 12px;
                gap: 6px;
            }
            
            .toolbar-btn {
                padding: 6px 8px;
                font-size: 12px;
                min-width: 40px;
                min-height: 40px;
            }
            
            .canvas-container {
                padding: 10px;
            }
            
            .sub-toolbar-group {
                flex-wrap: wrap;
                gap: 4px;
            }
            
            .modal-content {
                margin: 20px;
                padding: 16px;
            }
        }

        /* Loading Spinner */
        .loading {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 9999;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Hidden file input */
        .hidden-input {
            display: none;
        }

        /* Layer Panel */
        .layer-panel {
            position: fixed;
            right: -300px;
            top: 60px;
            width: 280px;
            height: calc(100vh - 130px);
            background: white;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
            transition: right 0.3s ease;
            z-index: 1000;
            border-radius: 8px 0 0 8px;
        }

        .layer-panel.active {
            right: 0;
        }

        .layer-panel-header {
            padding: 16px;
            border-bottom: 1px solid #eee;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .layer-list {
            padding: 8px;
            overflow-y: auto;
            height: calc(100% - 60px);
        }

        .layer-item {
            padding: 8px 12px;
            border: 1px solid #eee;
            border-radius: 6px;
            margin-bottom: 4px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.2s;
        }

        .layer-item:hover {
            background: #f8f9fa;
            border-color: #3498db;
        }

        .layer-item.active {
            background: #e3f2fd;
            border-color: #3498db;
        }

        .layer-actions {
            display: flex;
            gap: 4px;
        }

        .layer-btn {
            background: none;
            border: none;
            padding: 4px;
            cursor: pointer;
            border-radius: 4px;
            font-size: 12px;
        }

        .layer-btn:hover {
            background: rgba(52, 152, 219, 0.1);
        }

        /* Add this to the existing CSS */
        .crop-active {
            cursor: crosshair !important;
        }

        .crop-active .upper-canvas {
            cursor: crosshair !important;
        }

        /* Crop controls styling */
        .crop-controls {
            position: absolute;
            pointer-events: none;
        }

        .crop-handle {
            position: absolute;
            width: 12px;
            height: 12px;
            background: #3498db;
            border: 2px solid white;
            border-radius: 50%;
            cursor: pointer;
            pointer-events: all;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .crop-handle:hover {
            background: #2980b9;
            transform: scale(1.2);
        }

        .crop-edge {
            position: absolute;
            background: transparent;
            pointer-events: all;
        }

        .crop-edge.top, .crop-edge.bottom {
            height: 8px;
            cursor: ns-resize;
        }

        .crop-edge.left, .crop-edge.right {
            width: 8px;
            cursor: ew-resize;
        }

        .crop-edge.top { top: -4px; left: 0; right: 0; }
        .crop-edge.bottom { bottom: -4px; left: 0; right: 0; }
        .crop-edge.left { left: -4px; top: 0; bottom: 0; }
        .crop-edge.right { right: -4px; top: 0; bottom: 0; }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Top Toolbar -->
        <div class="top-toolbar">
            <div class="toolbar-group">
                <button class="toolbar-btn active" data-category="draw">
                    ✏️
                    <span class="tooltip">Draw</span>
                </button>
                <button class="toolbar-btn" data-category="shapes">
                    ⬜
                    <span class="tooltip">Shapes</span>
                </button>
                <button class="toolbar-btn" data-category="text">
                    📝
                    <span class="tooltip">Text</span>
                </button>
                <button class="toolbar-btn" data-category="images">
                    🖼️
                    <span class="tooltip">Images</span>
                </button>
                <button class="toolbar-btn" data-category="crop">
                    ✂️
                    <span class="tooltip">Crop</span>
                </button>
                <button class="toolbar-btn" data-category="effects">
                    ✨
                    <span class="tooltip">Effects</span>
                </button>
                <button class="toolbar-btn" data-category="library">
                    📚
                    <span class="tooltip">PNG Library</span>
                </button>
            </div>
            
            <div class="toolbar-group">
                <button class="toolbar-btn" onclick="undo()">
                    ↶
                    <span class="tooltip">Undo</span>
                </button>
                <button class="toolbar-btn" onclick="redo()">
                    ↷
                    <span class="tooltip">Redo</span>
                </button>
                <button class="toolbar-btn" onclick="toggleLayers()">
                    📋
                    <span class="tooltip">Layers</span>
                </button>
                <button class="toolbar-btn" onclick="exportCanvas()">
                    💾
                    <span class="tooltip">Export</span>
                </button>
            </div>
        </div>

        <!-- Canvas Container -->
        <div class="canvas-container">
            <div id="canvas-wrapper">
                <canvas id="canvas"></canvas>
            </div>
        </div>

        <!-- Sub Toolbar -->
        <div class="sub-toolbar" id="sub-toolbar">
            <!-- Dynamic content based on selected category -->
        </div>
    </div>

    <!-- Layer Panel -->
    <div class="layer-panel" id="layer-panel">
        <div class="layer-panel-header">
            <span>Layers</span>
            <button class="layer-btn" onclick="toggleLayers()">✕</button>
        </div>
        <div class="layer-list" id="layer-list">
            <!-- Dynamic layer items -->
        </div>
    </div>

    <!-- PNG Library Modal -->
    <div class="modal" id="png-library-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>PNG Library</h3>
                <button class="modal-close" onclick="closePngLibrary()">✕</button>
            </div>
            <div class="png-library-categories">
                <button class="toolbar-btn" onclick="loadPngCategory('icons')">Icons</button>
                <button class="toolbar-btn" onclick="loadPngCategory('shapes')">Shapes</button>
                <button class="toolbar-btn" onclick="loadPngCategory('ui')">UI Elements</button>
                <button class="toolbar-btn" onclick="loadPngCategory('arrows')">Arrows</button>
            </div>
            <div class="png-library-grid" id="png-library-grid">
                <!-- Dynamic PNG items -->
            </div>
        </div>
    </div>

    <!-- Hidden file inputs -->
    <input type="file" id="image-upload" class="hidden-input" accept="image/*" multiple>
    <input type="file" id="single-image-upload" class="hidden-input" accept="image/*">

    <!-- Loading Spinner -->
    <div class="loading" id="loading">
        <div class="spinner"></div>
    </div>

    <script>
        // Global variables
        let canvas;
        let currentCategory = 'draw';
        let isDrawingMode = false;
        let cropMode = false;
        let cropBox = null;
        let history = [];
        let historyIndex = -1;
        let hammer;

        // Color presets
        const colorPresets = [
            '#000000', '#FFFFFF', '#FF0000', '#00FF00', '#0000FF',
            '#FFFF00', '#FF00FF', '#00FFFF', '#FFA500', '#800080',
            '#FFC0CB', '#A52A2A', '#808080', '#000080', '#008000'
        ];

        // Initialize the application
        function init() {
            // Initialize Fabric.js canvas
            canvas = new fabric.Canvas('canvas', {
                width: 800,
                height: 600,
                backgroundColor: 'white'
            });

            // Initialize touch support
            initTouchSupport();

            // Set up event listeners
            setupEventListeners();

            // Initialize default category
            switchCategory('draw');

            // Save initial state
            saveState();

            // Make canvas responsive
            makeCanvasResponsive();
        }

        // Initialize touch support with Hammer.js
        function initTouchSupport() {
            const canvasElement = document.getElementById('canvas');
            hammer = new Hammer(canvasElement);
            
            // Enable pinch and rotate
            hammer.get('pinch').set({ enable: true });
            hammer.get('rotate').set({ enable: true });
            
            // Pinch to zoom
            hammer.on('pinch', function(e) {
                const zoom = canvas.getZoom() * e.scale;
                canvas.setZoom(Math.max(0.1, Math.min(5, zoom)));
            });
            
            // Double tap to reset zoom
            hammer.on('doubletap', function(e) {
                canvas.setZoom(1);
                canvas.viewportTransform = [1, 0, 0, 1, 0, 0];
                canvas.renderAll();
            });
        }

        // Make canvas responsive
        function makeCanvasResponsive() {
            function resizeCanvas() {
                const container = document.querySelector('.canvas-container');
                const containerWidth = container.clientWidth - 40;
                const containerHeight = container.clientHeight - 40;
                
                const canvasRatio = canvas.width / canvas.height;
                const containerRatio = containerWidth / containerHeight;
                
                let newWidth, newHeight;
                
                if (canvasRatio > containerRatio) {
                    newWidth = Math.min(containerWidth, canvas.width);
                    newHeight = newWidth / canvasRatio;
                } else {
                    newHeight = Math.min(containerHeight, canvas.height);
                    newWidth = newHeight * canvasRatio;
                }
                
                const canvasWrapper = document.getElementById('canvas-wrapper');
                canvasWrapper.style.width = newWidth + 'px';
                canvasWrapper.style.height = newHeight + 'px';
                
                canvas.setDimensions({
                    width: newWidth,
                    height: newHeight
                });
            }
            
            window.addEventListener('resize', resizeCanvas);
            resizeCanvas();
        }

        // Set up event listeners
        function setupEventListeners() {
            // Category buttons
            document.querySelectorAll('[data-category]').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    switchCategory(e.target.dataset.category);
                });
            });

            // Canvas events
            canvas.on('object:added', saveState);
            canvas.on('object:removed', saveState);
            canvas.on('object:modified', saveState);
            canvas.on('selection:created', updateLayerPanel);
            canvas.on('selection:updated', updateLayerPanel);
            canvas.on('selection:cleared', updateLayerPanel);

            // File upload events
            document.getElementById('image-upload').addEventListener('change', handleMultipleImageUpload);
            document.getElementById('single-image-upload').addEventListener('change', handleSingleImageUpload);

            // Keyboard shortcuts
            document.addEventListener('keydown', handleKeyboardShortcuts);
        }

        // Handle keyboard shortcuts
        function handleKeyboardShortcuts(e) {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'z':
                        e.preventDefault();
                        if (e.shiftKey) {
                            redo();
                        } else {
                            undo();
                        }
                        break;
                    case 'y':
                        e.preventDefault();
                        redo();
                        break;
                    case 'c':
                        e.preventDefault();
                        copyObject();
                        break;
                    case 'v':
                        e.preventDefault();
                        pasteObject();
                        break;
                    case 'd':
                        e.preventDefault();
                        duplicateObject();
                        break;
                }
            }
            
            // Delete key
            if (e.key === 'Delete' || e.key === 'Backspace') {
                deleteSelected();
            }
        }

        // Switch between categories
        function switchCategory(category) {
            currentCategory = category;
            
            // Update active button
            document.querySelectorAll('[data-category]').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-category="${category}"]`).classList.add('active');
            
            // Update sub-toolbar
            updateSubToolbar(category);
            
            // Disable drawing mode for non-draw categories
            if (category !== 'draw') {
                canvas.isDrawingMode = false;
                isDrawingMode = false;
            }
        }

        // Update sub-toolbar based on category
        function updateSubToolbar(category) {
            const subToolbar = document.getElementById('sub-toolbar');
            
            switch(category) {
                case 'draw':
                    subToolbar.innerHTML = `
                        <div class="sub-toolbar-group">
                            <label>Tool:</label>
                            <select onchange="setDrawingTool(this.value)">
                                <option value="pencil">Pencil</option>
                                <option value="brush">Brush</option>
                                <option value="marker">Marker</option>
                                <option value="spray">Spray</option>
                                <option value="pattern">Pattern</option>
                            </select>
                        </div>
                        <div class="sub-toolbar-group">
                            <label>Size:</label>
                            <input type="range" min="1" max="50" value="5" onchange="setBrushSize(this.value)">
                            <span id="brush-size-display">5</span>
                        </div>
                        <div class="sub-toolbar-group">
                            <label>Color:</label>
                            <input type="color" value="#000000" onchange="setBrushColor(this.value)">
                            <div class="color-picker">
                                ${colorPresets.map(color => 
                                    `<div class="color-swatch" style="background: ${color}" onclick="setBrushColor('${color}')"></div>`
                                ).join('')}
                            </div>
                        </div>
                        <div class="sub-toolbar-group">
                            <label>Opacity:</label>
                            <input type="range" min="0" max="1" step="0.1" value="1" onchange="setBrushOpacity(this.value)">
                        </div>
                        <div class="sub-toolbar-group">
                            <button class="toolbar-btn" onclick="toggleDrawingMode()">✏️ Draw</button>
                            <button class="toolbar-btn" onclick="setEraser()">🧽 Eraser</button>
                        </div>
                    `;
                    break;
                    
                case 'shapes':
                    subToolbar.innerHTML = `
                        <div class="sub-toolbar-group">
                            <button class="toolbar-btn" onclick="addShape('rectangle')">⬜ Rectangle</button>
                            <button class="toolbar-btn" onclick="addShape('circle')">⭕ Circle</button>
                            <button class="toolbar-btn" onclick="addShape('triangle')">🔺 Triangle</button>
                            <button class="toolbar-btn" onclick="addShape('line')">📏 Line</button>
                            <button class="toolbar-btn" onclick="addShape('arrow')">➡️ Arrow</button>
                        </div>
                        <div class="sub-toolbar-group">
                            <label>Fill:</label>
                            <input type="color" value="#3498db" onchange="setShapeFill(this.value)">
                        </div>
                        <div class="sub-toolbar-group">
                            <label>Stroke:</label>
                            <input type="color" value="#2c3e50" onchange="setShapeStroke(this.value)">
                            <input type="range" min="0" max="20" value="2" onchange="setShapeStrokeWidth(this.value)">
                        </div>
                    `;
                    break;
                    
                case 'text':
                    subToolbar.innerHTML = `
                        <div class="sub-toolbar-group">
                            <button class="toolbar-btn" onclick="addText()">📝 Add Text</button>
                            <button class="toolbar-btn" onclick="addTextbox()">📄 Text Box</button>
                        </div>
                        <div class="sub-toolbar-group">
                            <label>Font:</label>
                            <select onchange="setTextFont(this.value)">
                                <option value="Arial">Arial</option>
                                <option value="Helvetica">Helvetica</option>
                                <option value="Times New Roman">Times New Roman</option>
                                <option value="Georgia">Georgia</option>
                                <option value="Verdana">Verdana</option>
                                <option value="Tahoma">Tahoma</option>
                                <option value="Courier New">Courier New</option>
                                <option value="Impact">Impact</option>
                                <option value="Comic Sans MS">Comic Sans MS</option>
                                <option value="Trebuchet MS">Trebuchet MS</option>
                                <option value="Amiri">Amiri (Arabic)</option>
                                <option value="Cairo">Cairo (Arabic)</option>
                            </select>
                        </div>
                        <div class="sub-toolbar-group">
                            <label>Size:</label>
                            <input type="number" min="8" max="200" value="24" onchange="setTextSize(this.value)">
                        </div>
                        <div class="sub-toolbar-group">
                            <label>Color:</label>
                            <input type="color" value="#000000" onchange="setTextColor(this.value)">
                        </div>
                        <div class="sub-toolbar-group">
                            <button class="toolbar-btn" onclick="setTextAlign('left')">⬅️</button>
                            <button class="toolbar-btn" onclick="setTextAlign('center')">↔️</button>
                            <button class="toolbar-btn" onclick="setTextAlign('right')">➡️</button>
                        </div>
                        <div class="sub-toolbar-group">
                            <button class="toolbar-btn" onclick="toggleTextBold()">B</button>
                            <button class="toolbar-btn" onclick="toggleTextItalic()">I</button>
                            <button class="toolbar-btn" onclick="toggleTextUnderline()">U</button>
                        </div>
                        <div class="sub-toolbar-group">
                            <label>Stroke:</label>
                            <input type="color" value="#000000" onchange="setTextStrokeColor(this.value)">
                            <input type="range" min="0" max="10" value="0" onchange="setTextStrokeWidth(this.value)">
                        </div>
                        <div class="sub-toolbar-group">
                            <label>Shadow:</label>
                            <input type="color" value="#000000" onchange="setTextShadowColor(this.value)">
                            <input type="range" min="0" max="20" value="0" onchange="setTextShadowBlur(this.value)">
                            <input type="range" min="-20" max="20" value="2" onchange="setTextShadowOffsetX(this.value)">
                            <input type="range" min="-20" max="20" value="2" onchange="setTextShadowOffsetY(this.value)">
                        </div>
                    `;
                    break;
                    
                case 'images':
                    subToolbar.innerHTML = `
                        <div class="sub-toolbar-group">
                            <button class="toolbar-btn" onclick="uploadSingleImage()">🖼️ Upload Image</button>
                            <button class="toolbar-btn" onclick="uploadMultipleImages()">📁 Upload Multiple</button>
                        </div>
                        <div class="sub-toolbar-group">
                            <label>Border:</label>
                            <input type="color" value="#000000" onchange="setImageBorder(this.value)">
                            <input type="range" min="0" max="20" value="0" onchange="setImageBorderWidth(this.value)">
                        </div>
                        <div class="sub-toolbar-group">
                            <label>Shadow:</label>
                            <input type="color" value="#000000" onchange="setImageShadowColor(this.value)">
                            <input type="range" min="0" max="50" value="0" onchange="setImageShadowBlur(this.value)">
                        </div>
                    `;
                    break;
                    
                case 'crop':
                    subToolbar.innerHTML = `
                        <div class="sub-toolbar-group">
                            <button class="toolbar-btn" onclick="enableCropMode()">✂️ Crop Mode</button>
                            <button class="toolbar-btn" onclick="applyCrop()">✅ Apply</button>
                            <button class="toolbar-btn" onclick="cancelCrop()">❌ Cancel</button>
                        </div>
                        <div class="sub-toolbar-group">
                            <label>Preset:</label>
                            <select onchange="setCropRatio(this.value)">
                                <option value="free">Free</option>
                                <option value="1:1">Square (1:1)</option>
                                <option value="4:3">4:3</option>
                                <option value="16:9">16:9</option>
                                <option value="3:2">3:2</option>
                                <option value="a4">A4</option>
                                <option value="instagram">Instagram</option>
                                <option value="facebook">Facebook</option>
                                <option value="youtube">YouTube</option>
                                <option value="tiktok">TikTok</option>
                            </select>
                        </div>
                    `;
                    break;
                    
                case 'effects':
                    subToolbar.innerHTML = `
                        <div class="sub-toolbar-group">
                            <button class="toolbar-btn" onclick="applyFilter('brightness')">☀️ Brightness</button>
                            <button class="toolbar-btn" onclick="applyFilter('contrast')">🔆 Contrast</button>
                            <button class="toolbar-btn" onclick="applyFilter('blur')">🌫️ Blur</button>
                            <button class="toolbar-btn" onclick="applyFilter('grayscale')">⚫ Grayscale</button>
                        </div>
                        <div class="sub-toolbar-group">
                            <button class="toolbar-btn" onclick="applyFilter('sepia')">🟤 Sepia</button>
                            <button class="toolbar-btn" onclick="applyFilter('invert')">🔄 Invert</button>
                            <button class="toolbar-btn" onclick="applyFilter('saturate')">🌈 Saturate</button>
                            <button class="toolbar-btn" onclick="clearFilters()">🧹 Clear</button>
                        </div>
                        <div class="sub-toolbar-group">
                            <label>Intensity:</label>
                            <input type="range" min="0" max="2" step="0.1" value="1" onchange="setFilterIntensity(this.value)">
                        </div>
                    `;
                    break;
                    
                case 'library':
                    subToolbar.innerHTML = `
                        <div class="sub-toolbar-group">
                            <button class="toolbar-btn" onclick="openPngLibrary()">📚 Open Library</button>
                        </div>
                    `;
                    break;
            }
        }

        // Drawing functions
        function toggleDrawingMode() {
            isDrawingMode = !isDrawingMode;
            canvas.isDrawingMode = isDrawingMode;
            
            if (isDrawingMode) {
                canvas.freeDrawingBrush = new fabric.PencilBrush(canvas);
                canvas.freeDrawingBrush.width = 5;
                canvas.freeDrawingBrush.color = '#000000';
            }
        }

        function setDrawingTool(tool) {
            if (!isDrawingMode) toggleDrawingMode();
            
            switch(tool) {
                case 'pencil':
                    canvas.freeDrawingBrush = new fabric.PencilBrush(canvas);
                    break;
                case 'brush':
                    canvas.freeDrawingBrush = new fabric.PencilBrush(canvas);
                    canvas.freeDrawingBrush.width = 10;
                    break;
                case 'marker':
                    canvas.freeDrawingBrush = new fabric.PencilBrush(canvas);
                    canvas.freeDrawingBrush.width = 15;
                    break;
                case 'spray':
                    canvas.freeDrawingBrush = new fabric.SprayBrush(canvas);
                    break;
                case 'pattern':
                    canvas.freeDrawingBrush = new fabric.PatternBrush(canvas);
                    break;
            }
        }

        function setBrushSize(size) {
            if (canvas.freeDrawingBrush) {
                canvas.freeDrawingBrush.width = parseInt(size);
            }
            document.getElementById('brush-size-display').textContent = size;
        }

        function setBrushColor(color) {
            if (canvas.freeDrawingBrush) {
                canvas.freeDrawingBrush.color = color;
            }
            
            // Update color swatches
            document.querySelectorAll('.color-swatch').forEach(swatch => {
                swatch.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        function setBrushOpacity(opacity) {
            if (canvas.freeDrawingBrush) {
                canvas.freeDrawingBrush.color = canvas.freeDrawingBrush.color + Math.round(opacity * 255).toString(16).padStart(2, '0');
            }
        }

        function setEraser() {
            canvas.freeDrawingBrush = new fabric.EraserBrush(canvas);
            canvas.freeDrawingBrush.width = 10;
            canvas.isDrawingMode = true;
            isDrawingMode = true;
        }

        // Shape functions
        let currentShapeFill = '#3498db';
        let currentShapeStroke = '#2c3e50';
        let currentShapeStrokeWidth = 2;

        function setShapeFill(color) {
            currentShapeFill = color;
        }

        function setShapeStroke(color) {
            currentShapeStroke = color;
        }

        function setShapeStrokeWidth(width) {
            currentShapeStrokeWidth = parseInt(width);
        }

        function addShape(type) {
            let shape;
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            switch(type) {
                case 'rectangle':
                    shape = new fabric.Rect({
                        left: centerX - 50,
                        top: centerY - 25,
                        width: 100,
                        height: 50,
                        fill: currentShapeFill,
                        stroke: currentShapeStroke,
                        strokeWidth: currentShapeStrokeWidth
                    });
                    break;
                    
                case 'circle':
                    shape = new fabric.Circle({
                        left: centerX - 25,
                        top: centerY - 25,
                        radius: 25,
                        fill: currentShapeFill,
                        stroke: currentShapeStroke,
                        strokeWidth: currentShapeStrokeWidth
                    });
                    break;
                    
                case 'triangle':
                    shape = new fabric.Triangle({
                        left: centerX - 25,
                        top: centerY - 25,
                        width: 50,
                        height: 50,
                        fill: currentShapeFill,
                        stroke: currentShapeStroke,
                        strokeWidth: currentShapeStrokeWidth
                    });
                    break;
                    
                case 'line':
                    shape = new fabric.Line([centerX - 50, centerY, centerX + 50, centerY], {
                        stroke: currentShapeStroke,
                        strokeWidth: currentShapeStrokeWidth
                    });
                    break;
                    
                case 'arrow':
                    // Create arrow using path
                    const arrowPath = 'M 0 0 L 50 0 L 40 -10 M 50 0 L 40 10';
                    shape = new fabric.Path(arrowPath, {
                        left: centerX - 25,
                        top: centerY,
                        stroke: currentShapeStroke,
                        strokeWidth: currentShapeStrokeWidth,
                        fill: ''
                    });
                    break;
            }
            
            if (shape) {
                canvas.add(shape);
                canvas.setActiveObject(shape);
                canvas.renderAll();
            }
        }

        // Text functions
        let currentTextFont = 'Arial';
        let currentTextSize = 24;
        let currentTextColor = '#000000';

        function setTextFont(font) {
            currentTextFont = font;
            const activeObject = canvas.getActiveObject();
            if (activeObject && (activeObject.type === 'text' || activeObject.type === 'textbox')) {
                activeObject.set('fontFamily', font);
                canvas.renderAll();
            }
        }

        function setTextSize(size) {
            currentTextSize = parseInt(size);
            const activeObject = canvas.getActiveObject();
            if (activeObject && (activeObject.type === 'text' || activeObject.type === 'textbox')) {
                activeObject.set('fontSize', currentTextSize);
                canvas.renderAll();
            }
        }

        function setTextColor(color) {
            currentTextColor = color;
            const activeObject = canvas.getActiveObject();
            if (activeObject && (activeObject.type === 'text' || activeObject.type === 'textbox')) {
                activeObject.set('fill', color);
                canvas.renderAll();
            }
        }

        function addText() {
            const text = new fabric.Text('Click to edit text', {
                left: canvas.width / 2 - 50,
                top: canvas.height / 2,
                fontFamily: currentTextFont,
                fontSize: currentTextSize,
                fill: currentTextColor
            });
            
            canvas.add(text);
            canvas.setActiveObject(text);
            canvas.renderAll();
        }

        function addTextbox() {
            const textbox = new fabric.Textbox('Click to edit text', {
                left: canvas.width / 2 - 100,
                top: canvas.height / 2,
                width: 200,
                fontFamily: currentTextFont,
                fontSize: currentTextSize,
                fill: currentTextColor
            });
            
            canvas.add(textbox);
            canvas.setActiveObject(textbox);
            canvas.renderAll();
        }

        function setTextAlign(align) {
            const activeObject = canvas.getActiveObject();
            if (activeObject && (activeObject.type === 'text' || activeObject.type === 'textbox')) {
                activeObject.set('textAlign', align);
                canvas.renderAll();
            }
        }

        function toggleTextBold() {
            const activeObject = canvas.getActiveObject();
            if (activeObject && (activeObject.type === 'text' || activeObject.type === 'textbox')) {
                const currentWeight = activeObject.fontWeight || 'normal';
                activeObject.set('fontWeight', currentWeight === 'bold' ? 'normal' : 'bold');
                canvas.renderAll();
            }
        }

        function toggleTextItalic() {
            const activeObject = canvas.getActiveObject();
            if (activeObject && (activeObject.type === 'text' || activeObject.type === 'textbox')) {
                const currentStyle = activeObject.fontStyle || 'normal';
                activeObject.set('fontStyle', currentStyle === 'italic' ? 'normal' : 'italic');
                canvas.renderAll();
            }
        }

        function toggleTextUnderline() {
            const activeObject = canvas.getActiveObject();
            if (activeObject && (activeObject.type === 'text' || activeObject.type === 'textbox')) {
                const currentUnderline = activeObject.underline || false;
                activeObject.set('underline', !currentUnderline);
                canvas.renderAll();
            }
        }

        // Image functions
        function uploadSingleImage() {
            document.getElementById('single-image-upload').click();
        }

        function uploadMultipleImages() {
            document.getElementById('image-upload').click();
        }

        function handleSingleImageUpload(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(event) {
                    fabric.Image.fromURL(event.target.result, function(img) {
                        // حساب الحجم المناسب للصورة
                        const maxWidth = canvas.width * 0.8;
                        const maxHeight = canvas.height * 0.8;
                        const scale = Math.min(maxWidth / img.width, maxHeight / img.height, 1);
                        
                        img.set({
                            left: canvas.width / 2,
                            top: canvas.height / 2,
                            originX: 'center',
                            originY: 'center',
                            scaleX: scale,
                            scaleY: scale
                        });
                        
                        canvas.add(img);
                        canvas.setActiveObject(img);
                        canvas.renderAll();
                    });
                };
                reader.readAsDataURL(file);
            }
            // إعادة تعيين قيمة الإدخال للسماح برفع نفس الصورة مرة أخرى
            e.target.value = '';
        }

        function handleMultipleImageUpload(e) {
            const files = Array.from(e.target.files);
            let loadedCount = 0;
            const totalFiles = files.length;
            
            showLoading();
            
            files.forEach((file, index) => {
                const reader = new FileReader();
                reader.onload = function(event) {
                    fabric.Image.fromURL(event.target.result, function(img) {
                        // ترتيب الصور في شبكة مع توسيط أفضل
                        const cols = Math.ceil(Math.sqrt(totalFiles));
                        const row = Math.floor(index / cols);
                        const col = index % cols;
                        
                        const spacing = 150;
                        const gridWidth = cols * spacing;
                        const gridHeight = Math.ceil(totalFiles / cols) * spacing;
                        const startX = (canvas.width - gridWidth) / 2 + spacing / 2;
                        const startY = (canvas.height - gridHeight) / 2 + spacing / 2;
                        
                        const scale = Math.min(100 / img.width, 100 / img.height, 0.5);
                        
                        img.set({
                            left: startX + (col * spacing),
                            top: startY + (row * spacing),
                            originX: 'center',
                            originY: 'center',
                            scaleX: scale,
                            scaleY: scale
                        });
                        
                        canvas.add(img);
                        loadedCount++;
                        
                        if (loadedCount === totalFiles) {
                            hideLoading();
                            canvas.renderAll();
                        }
                    });
                };
                reader.readAsDataURL(file);
            });
            
            // إعادة تعيين قيمة الإدخال
            e.target.value = '';
        }

        function setImageBorder(color) {
            const activeObject = canvas.getActiveObject();
            if (activeObject && activeObject.type === 'image') {
                activeObject.set('stroke', color);
                canvas.renderAll();
            }
        }

        function setImageBorderWidth(width) {
            const activeObject = canvas.getActiveObject();
            if (activeObject && activeObject.type === 'image') {
                activeObject.set('strokeWidth', parseInt(width));
                canvas.renderAll();
            }
        }

        function setImageShadowColor(color) {
            const activeObject = canvas.getActiveObject();
            if (activeObject && activeObject.type === 'image') {
                activeObject.set('shadow', new fabric.Shadow({
                    color: color,
                    blur: activeObject.shadow ? activeObject.shadow.blur : 10,
                    offsetX: activeObject.shadow ? activeObject.shadow.offsetX : 5,
                    offsetY: activeObject.shadow ? activeObject.shadow.offsetY : 5
                }));
                canvas.renderAll();
            }
        }

        function setImageShadowBlur(blur) {
            const activeObject = canvas.getActiveObject();
            if (activeObject && activeObject.type === 'image') {
                const currentShadow = activeObject.shadow || { color: '#000000', offsetX: 5, offsetY: 5 };
                activeObject.set('shadow', new fabric.Shadow({
                    color: currentShadow.color,
                    blur: parseInt(blur),
                    offsetX: currentShadow.offsetX,
                    offsetY: currentShadow.offsetY
                }));
                canvas.renderAll();
            }
        }

        // Filter functions
        let currentFilterIntensity = 1;

        function setFilterIntensity(intensity) {
            currentFilterIntensity = parseFloat(intensity);
        }

        function applyFilter(filterType) {
            const activeObject = canvas.getActiveObject();
            if (!activeObject || activeObject.type !== 'image') {
                alert('Please select an image to apply filters');
                return;
            }

            let filter;
            switch(filterType) {
                case 'brightness':
                    filter = new fabric.Image.filters.Brightness({
                        brightness: (currentFilterIntensity - 1) * 0.5
                    });
                    break;
                case 'contrast':
                    filter = new fabric.Image.filters.Contrast({
                        contrast: (currentFilterIntensity - 1) * 0.5
                    });
                    break;
                case 'blur':
                    filter = new fabric.Image.filters.Blur({
                        blur: currentFilterIntensity * 0.1
                    });
                    break;
                case 'grayscale':
                    filter = new fabric.Image.filters.Grayscale();
                    break;
                case 'sepia':
                    filter = new fabric.Image.filters.Sepia();
                    break;
                case 'invert':
                    filter = new fabric.Image.filters.Invert();
                    break;
                case 'saturate':
                    filter = new fabric.Image.filters.Saturation({
                        saturation: (currentFilterIntensity - 1) * 0.5
                    });
                    break;
            }

            if (filter) {
                activeObject.filters.push(filter);
                activeObject.applyFilters();
                canvas.renderAll();
            }
        }

        function clearFilters() {
            const activeObject = canvas.getActiveObject();
            if (activeObject && activeObject.type === 'image') {
                activeObject.filters = [];
                activeObject.applyFilters();
                canvas.renderAll();
            }
        }

        // Crop functions
        let originalImage = null;

        function enableCropMode() {
            cropMode = true;
            const activeObject = canvas.getActiveObject();
            if (!activeObject || activeObject.type !== 'image') {
                alert('Please select an image to crop');
                cropMode = false;
                return;
            }
            
            // Store original image reference
            originalImage = activeObject;
            
            // Create crop overlay
            createCropOverlay(activeObject);
        }

        function createCropOverlay(imageObj) {
            // إزالة صندوق القص الموجود إن وجد
            if (cropBox) {
                canvas.remove(cropBox);
            }
            
            const bounds = imageObj.getBoundingRect();
            
            cropBox = new fabric.Rect({
                left: bounds.left + 20,
                top: bounds.top + 20,
                width: Math.max(50, bounds.width - 40),
                height: Math.max(50, bounds.height - 40),
                fill: 'rgba(52, 152, 219, 0.1)',
                stroke: '#3498db',
                strokeWidth: 2,
                strokeDashArray: [5, 5],
                selectable: true,
                evented: true,
                hasControls: true,
                hasBorders: true,
                transparentCorners: false,
                cornerColor: '#3498db',
                cornerStyle: 'circle',
                cornerSize: 12,
                borderColor: '#3498db',
                borderScaleFactor: 2,
                // تمكين التحكم في جميع الجهات
                lockMovementX: false,
                lockMovementY: false,
                lockScalingX: false,
                lockScalingY: false,
                lockRotation: true, // منع الدوران
                // تخصيص المقابض
                setControlsVisibility: {
                    mt: true, // أعلى وسط
                    mb: true, // أسفل وسط  
                    ml: true, // يسار وسط
                    mr: true, // يمين وسط
                    tl: true, // أعلى يسار
                    tr: true, // أعلى يمين
                    bl: true, // أسفل يسار
                    br: true, // أسفل يمين
                    mtr: false // إزالة مقبض الدوران
                }
            });
            
            // تخصيص شكل المقابض
            cropBox.controls.tl.cornerSize = 12;
            cropBox.controls.tr.cornerSize = 12;
            cropBox.controls.bl.cornerSize = 12;
            cropBox.controls.br.cornerSize = 12;
            cropBox.controls.mt.cornerSize = 12;
            cropBox.controls.mb.cornerSize = 12;
            cropBox.controls.ml.cornerSize = 12;
            cropBox.controls.mr.cornerSize = 12;
            
            canvas.add(cropBox);
            canvas.setActiveObject(cropBox);
            canvas.renderAll();
            
            // إضافة مستمعي الأحداث للتحكم المحسن
            canvas.on('object:moving', function(e) {
                if (e.target === cropBox && originalImage) {
                    constrainCropBox();
                }
            });
            
            canvas.on('object:scaling', function(e) {
                if (e.target === cropBox && originalImage) {
                    constrainCropBox();
                }
            });
            
            canvas.on('object:modified', function(e) {
                if (e.target === cropBox && originalImage) {
                    constrainCropBox();
                }
            });
        }

        function constrainCropBox() {
            if (!cropBox || !originalImage) return;
            
            const imageBounds = originalImage.getBoundingRect();
            const cropBounds = cropBox.getBoundingRect();
            
            // التأكد من أن صندوق القص داخل حدود الصورة
            let newLeft = cropBox.left;
            let newTop = cropBox.top;
            let newWidth = cropBox.width * cropBox.scaleX;
            let newHeight = cropBox.height * cropBox.scaleY;
            
            // تقييد الموضع
            if (cropBounds.left < imageBounds.left) {
                newLeft = imageBounds.left + (cropBox.width * cropBox.scaleX) / 2;
            }
            if (cropBounds.top < imageBounds.top) {
                newTop = imageBounds.top + (cropBox.height * cropBox.scaleY) / 2;
            }
            if (cropBounds.left + cropBounds.width > imageBounds.left + imageBounds.width) {
                newLeft = imageBounds.left + imageBounds.width - (cropBox.width * cropBox.scaleX) / 2;
            }
            if (cropBounds.top + cropBounds.height > imageBounds.top + imageBounds.height) {
                newTop = imageBounds.top + imageBounds.height - (cropBox.height * cropBox.scaleY) / 2;
            }
            
            // تقييد الحجم
            const maxWidth = imageBounds.width;
            const maxHeight = imageBounds.height;
            
            if (newWidth > maxWidth) {
                cropBox.set('scaleX', maxWidth / cropBox.width);
            }
            if (newHeight > maxHeight) {
                cropBox.set('scaleY', maxHeight / cropBox.height);
            }
            
            cropBox.set({
                left: newLeft,
                top: newTop
            });
            
            cropBox.setCoords();
            canvas.renderAll();
        }

        // Add a message display function
        function showMessage(message, type = 'info') {
            // Create or get message element
            let messageEl = document.getElementById('message-display');
            if (!messageEl) {
                messageEl = document.createElement('div');
                messageEl.id = 'message-display';
                messageEl.style.cssText = `
                    position: fixed;
                    top: 80px;
                    right: 20px;
                    padding: 12px 20px;
                    border-radius: 6px;
                    color: white;
                    font-weight: 500;
                    z-index: 10001;
                    opacity: 0;
                    transition: opacity 0.3s ease;
                    pointer-events: none;
                `;
                document.body.appendChild(messageEl);
            }
            
            // Set message and style based on type
            messageEl.textContent = message;
            const colors = {
                success: '#27ae60',
                error: '#e74c3c',
                info: '#3498db',
                warning: '#f39c12'
            };
            messageEl.style.backgroundColor = colors[type] || colors.info;
            
            // Show message
            messageEl.style.opacity = '1';
            
            // Hide after 3 seconds
            setTimeout(() => {
                messageEl.style.opacity = '0';
            }, 3000);
        }

        // PNG Library functions
        function openPngLibrary() {
            document.getElementById('png-library-modal').classList.add('active');
            loadPngCategory('icons');
        }

        function closePngLibrary() {
            document.getElementById('png-library-modal').classList.remove('active');
        }

        function loadPngCategory(category) {
            const grid = document.getElementById('png-library-grid');
            
            // Sample PNG items - in a real app, these would be actual PNG files
            const pngItems = {
                icons: ['🏠', '⚙️', '📧', '📱', '💡', '🔒', '🔍', '❤️', '⭐', '🔔'],
                shapes: ['⬜', '⭕', '🔺', '🔶', '🔷', '🔸', '🔹', '🔺', '🔻', '◀️'],
                ui: ['▶️', '⏸️', '⏹️', '⏭️', '⏮️', '🔄', '🔀', '🔁', '🔂', '📶'],
                arrows: ['➡️', '⬅️', '⬆️', '⬇️', '↗️', '↖️', '↘️', '↙️', '🔄', '🔃']
            };
            
            grid.innerHTML = '';
            
            pngItems[category].forEach(item => {
                const pngItem = document.createElement('div');
                pngItem.className = 'png-item';
                pngItem.textContent = item;
                pngItem.onclick = () => addPngToCanvas(item);
                grid.appendChild(pngItem);
            });
        }

        function addPngToCanvas(pngData) {
            // In a real implementation, this would load actual PNG files
            // For demo purposes, we'll add text objects
            const text = new fabric.Text(pngData, {
                left: canvas.width / 2,
                top: canvas.height / 2,
                fontSize: 48,
                originX: 'center',
                originY: 'center'
            });
            
            canvas.add(text);
            canvas.setActiveObject(text);
            canvas.renderAll();
            
            closePngLibrary();
        }

        // History functions
        function saveState() {
            const state = JSON.stringify(canvas.toJSON());
            history = history.slice(0, historyIndex + 1);
            history.push(state);
            historyIndex++;
            
            // Limit history size
            if (history.length > 50) {
                history.shift();
                historyIndex--;
            }
        }

        function undo() {
            if (historyIndex > 0) {
                historyIndex--;
                const state = history[historyIndex];
                canvas.loadFromJSON(state, function() {
                    canvas.renderAll();
                    updateLayerPanel();
                });
            }
        }

        function redo() {
            if (historyIndex < history.length - 1) {
                historyIndex++;
                const state = history[historyIndex];
                canvas.loadFromJSON(state, function() {
                    canvas.renderAll();
                    updateLayerPanel();
                });
            }
        }

        // Object manipulation functions
        let clipboard = null;

        function copyObject() {
            const activeObject = canvas.getActiveObject();
            if (activeObject) {
                activeObject.clone(function(cloned) {
                    clipboard = cloned;
                });
            }
        }

        function pasteObject() {
            if (clipboard) {
                clipboard.clone(function(clonedObj) {
                    canvas.discardActiveObject();
                    clonedObj.set({
                        left: clonedObj.left + 10,
                        top: clonedObj.top + 10,
                        evented: true,
                    });
                    if (clonedObj.type === 'activeSelection') {
                        clonedObj.canvas = canvas;
                        clonedObj.forEachObject(function(obj) {
                            canvas.add(obj);
                        });
                        clonedObj.setCoords();
                    } else {
                        canvas.add(clonedObj);
                    }
                    canvas.setActiveObject(clonedObj);
                    canvas.requestRenderAll();
                });
            }
        }

        function duplicateObject() {
            const activeObject = canvas.getActiveObject();
            if (activeObject) {
                activeObject.clone(function(cloned) {
                    cloned.set({
                        left: cloned.left + 10,
                        top: cloned.top + 10
                    });
                    canvas.add(cloned);
                    canvas.setActiveObject(cloned);
                    canvas.renderAll();
                });
            }
        }

        function deleteSelected() {
            const activeObjects = canvas.getActiveObjects();
            if (activeObjects.length) {
                activeObjects.forEach(obj => canvas.remove(obj));
                canvas.discardActiveObject();
                canvas.renderAll();
            }
        }

        // Layer functions
        function toggleLayers() {
            const layerPanel = document.getElementById('layer-panel');
            layerPanel.classList.toggle('active');
            updateLayerPanel();
        }

        function updateLayerPanel() {
            const layerList = document.getElementById('layer-list');
            const objects = canvas.getObjects();
            
            layerList.innerHTML = '';
            
            objects.forEach((obj, index) => {
                const layerItem = document.createElement('div');
                layerItem.className = 'layer-item';
                if (canvas.getActiveObject() === obj) {
                    layerItem.classList.add('active');
                }
                
                const layerName = obj.type.charAt(0).toUpperCase() + obj.type.slice(1);
                
                layerItem.innerHTML = `
                    <span>${layerName} ${index + 1}</span>
                    <div class="layer-actions">
                        <button class="layer-btn" onclick="moveLayerUp(${index})">↑</button>
                        <button class="layer-btn" onclick="moveLayerDown(${index})">↓</button>
                        <button class="layer-btn" onclick="toggleLayerVisibility(${index})">${obj.visible !== false ? '👁️' : '🚫'}</button>
                        <button class="layer-btn" onclick="deleteLayer(${index})">🗑️</button>
                    </div>
                `;
                
                layerItem.onclick = (e) => {
                    if (!e.target.classList.contains('layer-btn')) {
                        canvas.setActiveObject(obj);
                        canvas.renderAll();
                        updateLayerPanel();
                    }
                };
                
                layerList.appendChild(layerItem);
            });
        }

        function moveLayerUp(index) {
            const objects = canvas.getObjects();
            if (index < objects.length - 1) {
                const obj = objects[index];
                canvas.bringForward(obj);
                canvas.renderAll();
                updateLayerPanel();
            }
        }

        function moveLayerDown(index) {
            const objects = canvas.getObjects();
            if (index > 0) {
                const obj = objects[index];
                canvas.sendBackwards(obj);
                canvas.renderAll();
                updateLayerPanel();
            }
        }

        function toggleLayerVisibility(index) {
            const objects = canvas.getObjects();
            const obj = objects[index];
            obj.set('visible', obj.visible === false);
            canvas.renderAll();
            updateLayerPanel();
        }

        function deleteLayer(index) {
            const objects = canvas.getObjects();
            const obj = objects[index];
            canvas.remove(obj);
            canvas.renderAll();
            updateLayerPanel();
        }

        // Export functions
        function exportCanvas() {
            const format = prompt('Export format (png, jpg, svg, json):', 'png');
            if (!format) return;
            
            switch(format.toLowerCase()) {
                case 'png':
                    downloadCanvas('png');
                    break;
                case 'jpg':
                case 'jpeg':
                    downloadCanvas('jpeg');
                    break;
                case 'svg':
                    downloadSVG();
                    break;
                case 'json':
                    downloadJSON();
                    break;
                default:
                    alert('Unsupported format');
            }
        }

        function downloadCanvas(format) {
            const dataURL = canvas.toDataURL(`image/${format}`, 1.0);
            const link = document.createElement('a');
            link.download = `canvas.${format}`;
            link.href = dataURL;
            link.click();
        }

        function downloadSVG() {
            const svg = canvas.toSVG();
            const blob = new Blob([svg], { type: 'image/svg+xml' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.download = 'canvas.svg';
            link.href = url;
            link.click();
            URL.revokeObjectURL(url);
        }

        function downloadJSON() {
            const json = JSON.stringify(canvas.toJSON(), null, 2);
            const blob = new Blob([json], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.download = 'canvas.json';
            link.href = url;
            link.click();
            URL.revokeObjectURL(url);
        }

        // Utility functions
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
        }

        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }

        // دوال التحكم في حدود النص
        function setTextStrokeColor(color) {
            const activeObject = canvas.getActiveObject();
            if (activeObject && (activeObject.type === 'text' || activeObject.type === 'textbox')) {
                activeObject.set('stroke', color);
                canvas.renderAll();
            }
        }

        function setTextStrokeWidth(width) {
            const activeObject = canvas.getActiveObject();
            if (activeObject && (activeObject.type === 'text' || activeObject.type === 'textbox')) {
                activeObject.set('strokeWidth', parseInt(width));
                canvas.renderAll();
            }
        }

        // دوال التحكم في ظل النص
        function setTextShadowColor(color) {
            const activeObject = canvas.getActiveObject();
            if (activeObject && (activeObject.type === 'text' || activeObject.type === 'textbox')) {
                const currentShadow = activeObject.shadow || { blur: 5, offsetX: 2, offsetY: 2 };
                activeObject.set('shadow', new fabric.Shadow({
                    color: color,
                    blur: currentShadow.blur,
                    offsetX: currentShadow.offsetX,
                    offsetY: currentShadow.offsetY
        }));
        canvas.renderAll();
    }
}

function setTextShadowBlur(blur) {
    const activeObject = canvas.getActiveObject();
    if (activeObject && (activeObject.type === 'text' || activeObject.type === 'textbox')) {
        const currentShadow = activeObject.shadow || { color: '#000000', offsetX: 2, offsetY: 2 };
        activeObject.set('shadow', new fabric.Shadow({
            color: currentShadow.color,
            blur: parseInt(blur),
            offsetX: currentShadow.offsetX,
            offsetY: currentShadow.offsetY
        }));
        canvas.renderAll();
    }
}

function setTextShadowOffsetX(offsetX) {
    const activeObject = canvas.getActiveObject();
    if (activeObject && (activeObject.type === 'text' || activeObject.type === 'textbox')) {
        const currentShadow = activeObject.shadow || { color: '#000000', blur: 5, offsetY: 2 };
        activeObject.set('shadow', new fabric.Shadow({
            color: currentShadow.color,
            blur: currentShadow.blur,
            offsetX: parseInt(offsetX),
            offsetY: currentShadow.offsetY
        }));
        canvas.renderAll();
    }
}

function setTextShadowOffsetY(offsetY) {
    const activeObject = canvas.getActiveObject();
    if (activeObject && (activeObject.type === 'text' || activeObject.type === 'textbox')) {
        const currentShadow = activeObject.shadow || { color: '#000000', blur: 5, offsetX: 2 };
        activeObject.set('shadow', new fabric.Shadow({
            color: currentShadow.color,
            blur: currentShadow.blur,
            offsetX: currentShadow.offsetX,
            offsetY: parseInt(offsetY)
        }));
        canvas.renderAll();
    }
}

        // Utility functions
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
        }

        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }

        // Initialize the application when the page loads
        window.addEventListener('load', init);
    </script>
</body>
</html>