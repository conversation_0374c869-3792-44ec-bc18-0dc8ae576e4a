/**
 * fabric-tools.js
 * يحتوي على تعريفات وإعدادات أدوات Fabric.js
 */

// تعريف الأدوات والأزرار
const fabricTools = {
    // مجموعات الأدوات الرئيسية
    toolGroups: {
        shapes: {
            name: "الأشكال",
            icon: "◻️",
            tools: {
                rectangle: { name: "مستطيل", icon: "◻️" },
                circle: { name: "دائرة", icon: "○" },
                triangle: { name: "مثلث", icon: "△" },
                line: { name: "خط", icon: "╱" },
                arrow: { name: "سهم", icon: "↗" }
            }
        },
        text: {
            name: "النص",
            icon: "أ",
            tools: {
                text: { name: "نص", icon: "أ" },
                itext: { name: "نص تفاعلي", icon: "أب" },
                textbox: { name: "مربع نص", icon: "▭أ" }
            }
        },
        images: {
            name: "الصور",
            icon: "🖼️",
            tools: {
                uploadImage: { name: "تحميل من جهازك", icon: "📤" },
                imageFromURL: { name: "من رابط", icon: "🔗" }
            }
        },
        drawing: {
            name: "الرسم",
            icon: "✏️",
            tools: {
                pencil: { name: "قلم رصاص", icon: "✏️" },
                brush: { name: "فرشاة", icon: "🖌️" },
                eraser: { name: "ممحاة", icon: "🧽" }
            }
        },
        filters: {
            name: "الفلاتر",
            icon: "🎨",
            tools: {
                blur: { name: "تمويه", icon: "🌫️" },
                grayscale: { name: "تدرج الرمادي", icon: "⚪" },
                invert: { name: "عكس", icon: "🔄" },
                sepia: { name: "بني داكن", icon: "🟤" },
                sharpen: { name: "شحذ", icon: "✨" },
                brightness: { name: "سطوع", icon: "☀️" },
                contrast: { name: "تباين", icon: "◐" },
                vintage: { name: "عتيق", icon: "📜" }
            }
        },
        controls: {
            name: "التحكم",
            icon: "🔧",
            tools: {
                move: { name: "نقل", icon: "↕️" },
                resize: { name: "تغيير الحجم", icon: "⤡" },
                rotate: { name: "تدوير", icon: "🔄" },
                skew: { name: "إمالة", icon: "⟿" },
                zIndex: { name: "مؤشر Z", icon: "⧠" },
                snapToGrid: { name: "لصق على الشبكة", icon: "📏" },
                lockUnlock: { name: "قفل/إلغاء قفل", icon: "🔒" }
            }
        },
        background: {
            name: "الخلفية",
            icon: "🖼️",
            tools: {
                backgroundColor: { name: "لون الخلفية", icon: "🎨" },
                grid: { name: "شبكة", icon: "⊞" },
                alignmentGuides: { name: "مساعدات المحاذاة", icon: "📐" }
            }
        },
        textEditing: {
            name: "تحرير النص",
            icon: "T",
            tools: {
                fontFamily: { name: "الخط", icon: "فـ" },
                fontSize: { name: "الحجم", icon: "أ+" },
                alignment: { name: "المحاذاة", icon: "⫏" },
                textColor: { name: "اللون", icon: "🎨" },
                textShadow: { name: "الظل", icon: "⬝" }
            }
        },
        exportImport: {
            name: "تصدير/استيراد",
            icon: "💾",
            tools: {
                exportJSON: { name: "تصدير JSON", icon: "📤" },
                importJSON: { name: "استيراد JSON", icon: "📥" },
                exportSVG: { name: "تصدير SVG", icon: "📤" },
                importSVG: { name: "استيراد SVG", icon: "📥" },
                exportPNG: { name: "تصدير PNG", icon: "📤" },
                exportJPEG: { name: "تصدير JPEG", icon: "📤" }
            }
        },
        history: {
            name: "التاريخ",
            icon: "⏱️",
            tools: {
                undo: { name: "تراجع", icon: "↩️" },
                redo: { name: "إعادة", icon: "↪️" },
                copy: { name: "نسخ", icon: "📋" },
                paste: { name: "لصق", icon: "📌" },
                delete: { name: "حذف", icon: "🗑️" }
            }
        },
        layers: {
            name: "الطبقات",
            icon: "⧠",
            tools: {
                layerPanel: { name: "لوحة الطبقات", icon: "⧠" }
            }
        }
    },

    // تهيئة لوحة Fabric.js
    initFabricCanvas: function(canvasId) {
        // إنشاء كائن لوحة Fabric.js
        const canvas = new fabric.Canvas(canvasId, {
            backgroundColor: '#ffffff',
            preserveObjectStacking: true,
            width: 800,
            height: 600
        });

        // إضافة تاريخ للتراجع والإعادة
        canvas.historyStack = [];
        canvas.historyIndex = -1;
        canvas.historyProcessing = false;

        // إضافة مستمع للتغييرات لتحديث التاريخ
        canvas.on('object:modified', function() {
            if (!canvas.historyProcessing) {
                fabricTools.saveCanvasState(canvas);
            }
        });

        canvas.on('object:added', function() {
            if (!canvas.historyProcessing) {
                fabricTools.saveCanvasState(canvas);
            }
        });

        canvas.on('object:removed', function() {
            if (!canvas.historyProcessing) {
                fabricTools.saveCanvasState(canvas);
            }
        });

        return canvas;
    },

    // حفظ حالة اللوحة للتراجع/الإعادة
    saveCanvasState: function(canvas) {
        // إذا كنا في منتصف التاريخ، قم بإزالة الحالات المستقبلية
        if (canvas.historyIndex < canvas.historyStack.length - 1) {
            canvas.historyStack = canvas.historyStack.slice(0, canvas.historyIndex + 1);
        }

        // حفظ الحالة الحالية
        const json = JSON.stringify(canvas.toJSON(['id', 'selectable']));
        canvas.historyStack.push(json);
        canvas.historyIndex = canvas.historyStack.length - 1;

        // الحد من حجم التاريخ
        if (canvas.historyStack.length > 30) {
            canvas.historyStack.shift();
            canvas.historyIndex--;
        }
    },

    // التراجع عن آخر إجراء
    undo: function(canvas) {
        if (canvas.historyIndex > 0) {
            canvas.historyProcessing = true;
            canvas.historyIndex--;
            canvas.loadFromJSON(JSON.parse(canvas.historyStack[canvas.historyIndex]), function() {
                canvas.renderAll();
                canvas.historyProcessing = false;
                canvas.fire('history:undo');
            });
        }
    },

    // إعادة الإجراء الذي تم التراجع عنه
    redo: function(canvas) {
        if (canvas.historyIndex < canvas.historyStack.length - 1) {
            canvas.historyProcessing = true;
            canvas.historyIndex++;
            canvas.loadFromJSON(JSON.parse(canvas.historyStack[canvas.historyIndex]), function() {
                canvas.renderAll();
                canvas.historyProcessing = false;
                canvas.fire('history:redo');
            });
        }
    },

    // إضافة مستطيل إلى اللوحة
    addRectangle: function(canvas) {
        const rect = new fabric.Rect({
            left: 100,
            top: 100,
            fill: '#' + Math.floor(Math.random() * 16777215).toString(16),
            width: 100,
            height: 100,
            strokeWidth: 2,
            stroke: '#000000',
            rx: 0,
            ry: 0,
            angle: 0,
            scaleX: 1,
            scaleY: 1,
            hasControls: true
        });
        canvas.add(rect);
        canvas.setActiveObject(rect);
        canvas.renderAll();
        return rect;
    },

    // إضافة دائرة إلى اللوحة
    addCircle: function(canvas) {
        const circle = new fabric.Circle({
            left: 100,
            top: 100,
            fill: '#' + Math.floor(Math.random() * 16777215).toString(16),
            radius: 50,
            strokeWidth: 2,
            stroke: '#000000'
        });
        canvas.add(circle);
        canvas.setActiveObject(circle);
        canvas.renderAll();
        return circle;
    },

    // إضافة مثلث إلى اللوحة
    addTriangle: function(canvas) {
        const triangle = new fabric.Triangle({
            left: 100,
            top: 100,
            fill: '#' + Math.floor(Math.random() * 16777215).toString(16),
            width: 100,
            height: 100,
            strokeWidth: 2,
            stroke: '#000000'
        });
        canvas.add(triangle);
        canvas.setActiveObject(triangle);
        canvas.renderAll();
        return triangle;
    },

    // إضافة خط إلى اللوحة
    addLine: function(canvas) {
        const line = new fabric.Line([50, 50, 200, 200], {
            stroke: '#000000',
            strokeWidth: 2
        });
        canvas.add(line);
        canvas.setActiveObject(line);
        canvas.renderAll();
        return line;
    },

    // إضافة سهم إلى اللوحة
    addArrow: function(canvas) {
        const line = new fabric.Line([50, 50, 200, 200], {
            stroke: '#000000',
            strokeWidth: 2
        });
        
        const triangle = new fabric.Triangle({
            width: 20,
            height: 20,
            fill: '#000000',
            left: 200,
            top: 200,
            angle: 45
        });
        
        const arrow = new fabric.Group([line, triangle]);
        canvas.add(arrow);
        canvas.setActiveObject(arrow);
        canvas.renderAll();
        return arrow;
    },

    // إضافة نص إلى اللوحة
    addText: function(canvas) {
        const text = new fabric.Text('أدخل النص هنا', {
            left: 100,
            top: 100,
            fontFamily: 'Arial',
            fontSize: 30,
            fill: '#000000'
        });
        canvas.add(text);
        canvas.setActiveObject(text);
        canvas.renderAll();
        return text;
    },

    // إضافة نص تفاعلي إلى اللوحة
    addIText: function(canvas) {
        const itext = new fabric.IText('أدخل النص التفاعلي هنا', {
            left: 100,
            top: 100,
            fontFamily: 'Arial',
            fontSize: 30,
            fill: '#000000'
        });
        canvas.add(itext);
        canvas.setActiveObject(itext);
        canvas.renderAll();
        return itext;
    },

    // إضافة مربع نص إلى اللوحة
    addTextbox: function(canvas) {
        const textbox = new fabric.Textbox('أدخل النص متعدد الأسطر هنا. يمكنك كتابة نص طويل وسيتم تقسيمه تلقائيًا.', {
            left: 100,
            top: 100,
            fontFamily: 'Arial',
            fontSize: 20,
            fill: '#000000',
            width: 300
        });
        canvas.add(textbox);
        canvas.setActiveObject(textbox);
        canvas.renderAll();
        return textbox;
    },

    // تحميل صورة من ملف
    uploadImage: function(canvas, file) {
        const reader = new FileReader();
        reader.onload = function(event) {
            const imgObj = new Image();
            imgObj.src = event.target.result;
            imgObj.onload = function() {
                const image = new fabric.Image(imgObj);
                
                // تغيير حجم الصورة إذا كانت كبيرة جدًا
                if (image.width > canvas.width || image.height > canvas.height) {
                    const scale = Math.min(
                        canvas.width / image.width, 
                        canvas.height / image.height
                    ) * 0.8;
                    image.scale(scale);
                }
                
                canvas.add(image);
                canvas.setActiveObject(image);
                canvas.renderAll();
            };
        };
        reader.readAsDataURL(file);
    },

    // إضافة صورة من URL
    addImageFromURL: function(canvas, url) {
        fabric.Image.fromURL(url, function(image) {
            // تغيير حجم الصورة إذا كانت كبيرة جدًا
            if (image.width > canvas.width || image.height > canvas.height) {
                const scale = Math.min(
                    canvas.width / image.width, 
                    canvas.height / image.height
                ) * 0.8;
                image.scale(scale);
            }
            
            canvas.add(image);
            canvas.setActiveObject(image);
            canvas.renderAll();
        }, { crossOrigin: 'anonymous' });
    },

    // تفعيل أداة القلم الرصاص
    enablePencilBrush: function(canvas) {
        canvas.isDrawingMode = true;
        canvas.freeDrawingBrush = new fabric.PencilBrush(canvas);
        canvas.freeDrawingBrush.width = 2;
        canvas.freeDrawingBrush.color = '#000000';
    },

    // تفعيل أداة الفرشاة
    enableBrush: function(canvas) {
        canvas.isDrawingMode = true;
        canvas.freeDrawingBrush = new fabric.PencilBrush(canvas);
        canvas.freeDrawingBrush.width = 10;
        canvas.freeDrawingBrush.color = '#000000';
    },

    // تفعيل أداة الممحاة
    enableEraser: function(canvas) {
        canvas.isDrawingMode = true;
        canvas.freeDrawingBrush = new fabric.PencilBrush(canvas);
        canvas.freeDrawingBrush.width = 20;
        canvas.freeDrawingBrush.color = '#ffffff';
    },

    // تعطيل وضع الرسم
    disableDrawingMode: function(canvas) {
        canvas.isDrawingMode = false;
    },

    // تطبيق فلتر على الكائن المحدد
    applyFilter: function(canvas, filterType) {
        const activeObject = canvas.getActiveObject();
        if (!activeObject || !activeObject.filters) return;

        // إزالة جميع الفلاتر الحالية
        activeObject.filters = [];

        // إضافة الفلتر المطلوب
        switch (filterType) {
            case 'grayscale':
                activeObject.filters.push(new fabric.Image.filters.Grayscale());
                break;
            case 'invert':
                activeObject.filters.push(new fabric.Image.filters.Invert());
                break;
            case 'sepia':
                activeObject.filters.push(new fabric.Image.filters.Sepia());
                break;
            case 'blur':
                activeObject.filters.push(new fabric.Image.filters.Blur({
                    blur: 0.5
                }));
                break;
            case 'sharpen':
                // تنفيذ فلتر الشحذ باستخدام Convolve
                activeObject.filters.push(new fabric.Image.filters.Convolve({
                    matrix: [0, -1, 0, -1, 5, -1, 0, -1, 0]
                }));
                break;
            case 'brightness':
                activeObject.filters.push(new fabric.Image.filters.Brightness({
                    brightness: 0.1
                }));
                break;
            case 'contrast':
                activeObject.filters.push(new fabric.Image.filters.Contrast({
                    contrast: 0.1
                }));
                break;
            case 'vintage':
                // مزيج من الفلاتر لإنشاء تأثير عتيق
                activeObject.filters.push(new fabric.Image.filters.Sepia());
                activeObject.filters.push(new fabric.Image.filters.Contrast({
                    contrast: -0.1
                }));
                activeObject.filters.push(new fabric.Image.filters.Brightness({
                    brightness: -0.05
                }));
                break;
        }

        // تطبيق الفلاتر
        activeObject.applyFilters();
        canvas.renderAll();
    },

    // تغيير لون خلفية اللوحة
    setBackgroundColor: function(canvas, color) {
        canvas.setBackgroundColor(color, canvas.renderAll.bind(canvas));
    },

    // تصدير اللوحة كـ JSON
    exportToJSON: function(canvas) {
        return JSON.stringify(canvas.toJSON());
    },

    // استيراد اللوحة من JSON
    importFromJSON: function(canvas, json) {
        canvas.loadFromJSON(json, function() {
            canvas.renderAll();
            fabricTools.saveCanvasState(canvas);
        });
    },

    // تصدير اللوحة كـ SVG
    exportToSVG: function(canvas) {
        return canvas.toSVG();
    },

    // استيراد SVG إلى اللوحة
    importSVG: function(canvas, svgString) {
        fabric.loadSVGFromString(svgString, function(objects, options) {
            const svgObject = fabric.util.groupSVGElements(objects, options);
            canvas.add(svgObject);
            canvas.renderAll();
            fabricTools.saveCanvasState(canvas);
        });
    },

    // تصدير اللوحة كـ PNG
    exportToPNG: function(canvas) {
        return canvas.toDataURL({
            format: 'png',
            quality: 1
        });
    },

    // تصدير اللوحة كـ JPEG
    exportToJPEG: function(canvas) {
        return canvas.toDataURL({
            format: 'jpeg',
            quality: 0.8
        });
    },

    // نسخ الكائن المحدد
    copySelectedObject: function(canvas) {
        if (!canvas.getActiveObject()) return;
        
        canvas.getActiveObject().clone(function(cloned) {
            canvas.clipboard = cloned;
        });
    },

    // لصق الكائن المنسوخ
    pasteObject: function(canvas) {
        if (!canvas.clipboard) return;
        
        canvas.clipboard.clone(function(clonedObj) {
            canvas.discardActiveObject();
            
            // تعديل موضع الكائن الملصق قليلاً
            clonedObj.set({
                left: clonedObj.left + 10,
                top: clonedObj.top + 10,
                evented: true
            });
            
            if (clonedObj.type === 'activeSelection') {
                clonedObj.canvas = canvas;
                clonedObj.forEachObject(function(obj) {
                    canvas.add(obj);
                });
                clonedObj.setCoords();
            } else {
                canvas.add(clonedObj);
            }
            
            canvas.setActiveObject(clonedObj);
            canvas.renderAll();
        });
    },

    // حذف الكائن المحدد
    deleteSelectedObject: function(canvas) {
        const activeObject = canvas.getActiveObject();
        if (activeObject) {
            if (activeObject.type === 'activeSelection') {
                activeObject.forEachObject(function(obj) {
                    canvas.remove(obj);
                });
            } else {
                canvas.remove(activeObject);
            }
            canvas.discardActiveObject();
            canvas.renderAll();
        }
    },

    // تغيير حجم اللوحة
    resizeCanvas: function(canvas, width, height) {
        canvas.setWidth(width);
        canvas.setHeight(height);
        canvas.renderAll();
    },

    // تعيين حجم اللوحة من الإعدادات المسبقة
    setCanvasPreset: function(canvas, preset) {
        let width, height;
        
        switch (preset) {
            case 'instagram':
                width = 1080;
                height = 1080;
                break;
            case 'youtube':
                width = 1280;
                height = 720;
                break;
            case 'facebook':
                width = 820;
                height = 312;
                break;
            case 'a4':
                width = 2480;
                height = 3508;
                break;
            case 'custom':
                // يمكن تنفيذ نافذة منبثقة لإدخال الأبعاد المخصصة
                width = prompt('أدخل العرض (بالبكسل):', '800');
                height = prompt('أدخل الارتفاع (بالبكسل):', '600');
                width = parseInt(width);
                height = parseInt(height);
                break;
            default:
                width = 800;
                height = 600;
        }
        
        // التأكد من أن الأبعاد صالحة
        if (isNaN(width) || isNaN(height) || width <= 0 || height <= 0) {
            width = 800;
            height = 600;
        }
        
        // تغيير حجم اللوحة
        this.resizeCanvas(canvas, width, height);
        
        // تعديل حجم حاوية اللوحة للتناسب مع الشاشة
        const container = document.querySelector('.fabric-section');
        const containerWidth = container.clientWidth;
        const containerHeight = container.clientHeight - 100; // طرح ارتفاع شريط الأدوات
        
        // حساب النسبة للتكبير/التصغير
        const scaleX = containerWidth / width;
        const scaleY = containerHeight / height;
        const scale = Math.min(scaleX, scaleY);
        
        // تطبيق التكبير/التصغير على اللوحة
        canvas.setZoom(scale);
        
        // توسيط اللوحة في الحاوية
        const canvasElement = document.getElementById('fabric-canvas');
        canvasElement.style.marginTop = ((containerHeight - (height * scale)) / 2) + 'px';
        
        return { width, height, scale };
    }
};
