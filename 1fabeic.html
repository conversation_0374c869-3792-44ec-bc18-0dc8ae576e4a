<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محرر الرسم على الصور</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #1a2a6c, #b21f1f, #fdbb2d);
            color: #fff;
            min-height: 100vh;
            padding-bottom: 40px;
        }

        .container {
            background-color: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.3);
            max-width: 900px;
            margin: 0 auto;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        header {
            text-align: center;
            margin-bottom: 30px;
        }

        h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.5);
        }

        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .controls {
            margin-bottom: 20px;
            padding: 20px;
            background-color: rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
            justify-content: center;
        }

        button {
            padding: 12px 25px;
            background: linear-gradient(to right, #ff416c, #ff4b2b);
            color: white;
            border: none;
            border-radius: 50px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        button:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.3);
            background: linear-gradient(to right, #ff4b2b, #ff416c);
        }

        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        #uploadBtn {
            background: linear-gradient(to right, #00b09b, #96c93d);
        }

        #uploadBtn:hover {
            background: linear-gradient(to right, #96c93d, #00b09b);
        }

        #startDrawing {
            background: linear-gradient(to right, #2193b0, #6dd5ed);
            padding: 12px 30px;
        }

        #startDrawing:hover {
            background: linear-gradient(to right, #6dd5ed, #2193b0);
        }

        .file-input-wrapper {
            position: relative;
            overflow: hidden;
            display: inline-block;
            border-radius: 50px;
        }

        .file-input-wrapper input[type=file] {
            position: absolute;
            left: -9999px;
        }

        .canvas-container {
            text-align: center;
            margin-top: 20px;
            position: relative;
            border-radius: 10px;
            overflow: hidden;
            border: 3px solid rgba(255, 255, 255, 0.2);
            background-color: rgba(0, 0, 0, 0.2);
            min-height: 500px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        #canvas {
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }

        .tool-submenu {
            background-color: rgba(255, 255, 255, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            backdrop-filter: blur(5px);
        }

        .drawing-controls {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            align-items: center;
            justify-content: center;
        }

        .drawing-controls label {
            display: flex;
            flex-direction: column;
            align-items: center;
            font-size: 14px;
            background: rgba(0, 0, 0, 0.2);
            padding: 15px;
            border-radius: 10px;
            min-width: 150px;
        }

        .drawing-controls .info {
            font-size: 14px;
            font-weight: bold;
            margin-top: 5px;
            color: #fff;
        }

        .drawing-controls input[type="range"] {
            width: 100%;
            height: 8px;
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.2);
            outline: none;
        }

        .drawing-controls select {
            padding: 8px 15px;
            border-radius: 50px;
            border: none;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 14px;
            width: 100%;
            text-align: center;
        }

        .drawing-controls select option {
            background: rgba(0, 0, 0, 0.7);
        }

        .instructions {
            margin-top: 25px;
            padding: 20px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            font-size: 16px;
            line-height: 1.6;
        }

        .instructions h3 {
            margin-bottom: 10px;
            color: #6dd5ed;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .preview-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 1.8rem;
            font-weight: bold;
            color: rgba(255, 255, 255, 0.3);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            pointer-events: none;
            text-align: center;
            max-width: 90%;
        }

        .tool-title {
            text-align: center;
            margin-bottom: 15px;
            font-size: 1.5rem;
            color: #6dd5ed;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .mode-badge {
            display: inline-block;
            background: linear-gradient(to right, #2193b0, #6dd5ed);
            padding: 3px 10px;
            border-radius: 20px;
            font-size: 0.9rem;
            margin-left: 10px;
        }

        .color-preview {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 10px;
            border: 2px solid white;
        }

        footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
            }
            
            .drawing-controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .drawing-controls label {
                width: 100%;
            }
            
            button {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-paint-brush"></i> محرر الرسم على الصور</h1>
            <div class="subtitle">أضف لمساتك الإبداعية على صورك باستخدام أدوات الرسم المتعددة</div>
        </header>

        <div class="controls">
            <div class="file-input-wrapper">
                <button id="uploadBtn">
                    <i class="fas fa-upload"></i> رفع صورة
                </button>
                <input type="file" id="fileInput" accept="image/*">
            </div>
            
            <button id="startDrawing">
                <i class="fas fa-paint-brush"></i> بدء الرسم
            </button>
        </div>

        <div class="canvas-container">
            <canvas id="canvas" width="700" height="500"></canvas>
            <div class="preview-text">الرجاء رفع صورة لبدء الرسم</div>
        </div>

        <!-- Drawing Tools Submenu -->
        <div class="tool-submenu" id="drawingSubmenu">
            <div class="tool-title">
                <i class="fas fa-sliders-h"></i> أدوات الرسم
                <span class="mode-badge" id="currentMode">قلم رصاص</span>
            </div>
            
            <div class="drawing-controls">
                <label for="drawing-mode-selector">
                    نمط الرسم:
                    <select id="drawing-mode-selector">
                        <option>قلم رصاص</option>
                        <option>دائرة</option>
                        <option>رشاش</option>
                        <option>نمط</option>
                        <option>خط أفقي</option>
                        <option>خط عمودي</option>
                        <option>مربع</option>
                        <option>معين</option>
                    </select>
                </label>

                <label for="drawing-line-width">
                    عرض الخط:
                    <span class="info" id="line-width-info">30</span>
                    <input type="range" min="1" max="150" id="drawing-line-width" value="30">
                </label>

                <label for="drawing-color">
                    لون الخط:
                    <div style="display: flex; align-items: center; margin-top: 8px;">
                        <input type="color" id="drawing-color" value="#76cef4">
                        <span class="color-preview" id="colorPreview" style="background-color: #76cef4;"></span>
                    </div>
                </label>

                <label for="drawing-shadow-color">
                    لون الظل:
                    <div style="display: flex; align-items: center; margin-top: 8px;">
                        <input type="color" id="drawing-shadow-color" value="#5a7896">
                        <span class="color-preview" id="shadowPreview" style="background-color: #5a7896;"></span>
                    </div>
                </label>

                <label for="drawing-shadow-width">
                    عرض الظل:
                    <span class="info" id="shadow-width-info">0</span>
                    <input type="range" min="0" max="50" id="drawing-shadow-width" value="0">
                </label>

                <button id="clear-drawings" style="margin-top: 15px;">
                    <i class="fas fa-trash-alt"></i> مسح الرسومات
                </button>
            </div>
        </div>

        <div class="instructions">
            <h3><i class="fas fa-info-circle"></i> التعليمات:</h3>
            <p>1. ابدأ برفع صورة باستخدام زر "رفع صورة"</p>
            <p>2. اضغط على زر "بدء الرسم" لتفعيل أدوات الرسم</p>
            <p>3. اختر نمط الرسم الذي تريده من القائمة المنسدلة</p>
            <p>4. اضبط خصائص الرسم مثل عرض الخط، اللون، والظل</p>
            <p>5. ارسم على الصورة باستخدام الفأرة أو الشاشة اللمسية</p>
            <p>6. استخدم زر "مسح الرسومات" لإزالة كل ما رسمته</p>
        </div>
        
        <footer>
            <p>تم التطوير باستخدام مكتبة Fabric.js | أدوات الرسم المتقدمة</p>
        </footer>
    </div>

    <script>
        // تهيئة لوحة الرسم
        var canvas = new fabric.Canvas('canvas', {
            backgroundColor: 'rgba(255, 255, 255, 0.1)'
        });
        
        var currentImage = null;
        var isDrawing = false;

        // تهيئة الصفحة عند التحميل
        $(document).ready(function() {
            initializeCanvas();
            setupEventHandlers();
            setupDrawingEventHandlers();
            
            // إخفاء أدوات الرسم في البداية
            $('#drawingSubmenu').hide();
        });

        function initializeCanvas() {
            canvas.selection = false;
            canvas.renderAll();
        }

        // إعداد معالجات الأحداث
        function setupEventHandlers() {
            $('#fileInput').on('change', handleFileSelect);
            $('#uploadBtn').on('click', function() {
                $('#fileInput').click();
            });

            $('#startDrawing').on('click', toggleDrawingMode);
            $('#clear-drawings').on('click', clearDrawings);
        }

        // إعداد معالجات أحداث الرسم
        function setupDrawingEventHandlers() {
            const drawingColorEl = $('#drawing-color');
            const drawingShadowColorEl = $('#drawing-shadow-color');
            const drawingLineWidthEl = $('#drawing-line-width');
            const drawingShadowWidth = $('#drawing-shadow-width');
            const selectorEl = $('#drawing-mode-selector');

            drawingColorEl.on('change', function() {
                $('#colorPreview').css('background-color', $(this).val());
                updateBrush();
            });

            drawingShadowColorEl.on('change', function() {
                $('#shadowPreview').css('background-color', $(this).val());
                updateBrush();
            });

            drawingLineWidthEl.on('input', function() {
                $('#line-width-info').text($(this).val());
                updateBrush();
            });

            drawingShadowWidth.on('input', function() {
                $('#shadow-width-info').text($(this).val());
                updateBrush();
            });

            selectorEl.on('change', function() {
                $('#currentMode').text($(this).val());
                handleDrawingModeChange();
            });
        }

        // تحديث الفرشاة
        function updateBrush() {
            const brush = canvas.freeDrawingBrush;
            if (!brush) return;

            brush.color = $('#drawing-color').val();
            brush.width = parseInt($('#drawing-line-width').val(), 10) || 1;
            brush.shadow = new fabric.Shadow({
                blur: parseInt($('#drawing-shadow-width').val(), 10) || 0,
                offsetX: 0,
                offsetY: 0,
                color: $('#drawing-shadow-color').val(),
            });
        }

        // تغيير نمط الرسم
        function handleDrawingModeChange() {
            const val = $('#drawing-mode-selector').val();
            let brushClass;

            switch(val) {
                case 'قلم رصاص':
                    brushClass = fabric.PencilBrush;
                    break;
                case 'دائرة':
                    brushClass = fabric.CircleBrush;
                    break;
                case 'رشاش':
                    brushClass = fabric.SprayBrush;
                    break;
                case 'نمط':
                    brushClass = fabric.PatternBrush;
                    break;
                case 'خط أفقي':
                    brushClass = fabric.HorizontalLineBrush;
                    break;
                case 'خط عمودي':
                    brushClass = fabric.VerticalLineBrush;
                    break;
                case 'مربع':
                    brushClass = fabric.SquareBrush;
                    break;
                case 'معين':
                    brushClass = fabric.DiamondBrush;
                    break;
                default:
                    brushClass = fabric.PencilBrush;
            }

            canvas.freeDrawingBrush = new brushClass(canvas);
            updateBrush();
        }

        // مسح الرسومات
        function clearDrawings() {
            if (!currentImage) {
                alert('الرجاء رفع صورة أولاً');
                return;
            }

            // إزالة كل العناصر ما عدا الصورة الأساسية
            const objects = canvas.getObjects();
            objects.forEach(obj => {
                if (obj !== currentImage) {
                    canvas.remove(obj);
                }
            });

            canvas.renderAll();
        }

        // تبديل وضع الرسم
        function toggleDrawingMode() {
            if (!currentImage) {
                alert('الرجاء رفع صورة أولاً');
                return;
            }

            isDrawing = !isDrawing;
            const submenu = $('#drawingSubmenu');

            if (isDrawing) {
                submenu.slideDown(400);
                $('#startDrawing').html('<i class="fas fa-paint-brush"></i> إيقاف الرسم');
                canvas.isDrawingMode = true;
                updateBrush();
                
                // تحديث الوضع الحالي
                $('#currentMode').text($('#drawing-mode-selector').val());
            } else {
                submenu.slideUp(400);
                $('#startDrawing').html('<i class="fas fa-paint-brush"></i> بدء الرسم');
                canvas.isDrawingMode = false;
            }
        }

        // معالجة اختيار الملف
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file && file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    loadImageToCanvas(e.target.result);
                    $('.preview-text').hide();
                };
                reader.readAsDataURL(file);
            } else {
                alert('الرجاء اختيار ملف صورة صالح');
            }
        }

        // تحميل الصورة إلى اللوحة
        function loadImageToCanvas(imageSrc) {
            canvas.clear();
            canvas.isDrawingMode = false;

            fabric.Image.fromURL(imageSrc, function(img) {
                const canvasWidth = canvas.getWidth();
                const canvasHeight = canvas.getHeight();
                const imgWidth = img.width;
                const imgHeight = img.height;

                const scaleX = (canvasWidth - 40) / imgWidth;
                const scaleY = (canvasHeight - 40) / imgHeight;
                const scale = Math.min(scaleX, scaleY, 1);

                img.set({
                    scaleX: scale,
                    scaleY: scale,
                    selectable: false,
                    left: (canvasWidth - imgWidth * scale) / 2,
                    top: (canvasHeight - imgHeight * scale) / 2
                });

                canvas.add(img);
                canvas.renderAll();

                currentImage = img;
                $('#startDrawing').prop('disabled', false);
            });
        }
    </script>
</body>
</html>