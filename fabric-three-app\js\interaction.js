/**
 * interaction.js
 * يحتوي على وظائف التفاعل بين Fabric.js و Three.js
 */

const interaction = {
    // المتغيرات الرئيسية
    fabricCanvas: null,
    threeScene: null,
    
    // تهيئة التفاعل بين Fabric.js و Three.js
    init: function(fabricCanvas, threeScene) {
        this.fabricCanvas = fabricCanvas;
        this.threeScene = threeScene;
        
        // إضافة مستمعات الأحداث للتفاعل
        this.setupEventListeners();
        
        return this;
    },
    
    // إعداد مستمعات الأحداث
    setupEventListeners: function() {
        // مستمع لإضافة كائن جديد إلى لوحة Fabric.js
        this.fabricCanvas.on('object:added', (e) => {
            this.onFabricObjectAdded(e.target);
        });
        
        // مستمع لتعديل كائن في لوحة Fabric.js
        this.fabricCanvas.on('object:modified', (e) => {
            this.onFabricObjectModified(e.target);
        });
        
        // مستمع لحذف كائن من لوحة Fabric.js
        this.fabricCanvas.on('object:removed', (e) => {
            this.onFabricObjectRemoved(e.target);
        });
        
        // مستمع لتحديد كائن في لوحة Fabric.js
        this.fabricCanvas.on('selection:created', (e) => {
            this.onFabricSelectionCreated(e.selected);
        });
        
        // مستمع لإلغاء تحديد كائن في لوحة Fabric.js
        this.fabricCanvas.on('selection:cleared', () => {
            this.onFabricSelectionCleared();
        });
    },
    
    // عند إضافة كائن جديد إلى لوحة Fabric.js
    onFabricObjectAdded: function(fabricObject) {
        // تجاهل الكائنات التي تمت إضافتها أثناء تحميل JSON
        if (this.fabricCanvas.historyProcessing) return;
        
        // إنشاء كائن ثلاثي الأبعاد مقابل في مشهد Three.js
        const threeObject = this.createThreeObjectFromFabric(fabricObject);
        
        // ربط الكائن ثلاثي الأبعاد بكائن Fabric.js
        if (threeObject) {
            // تخزين معرف الكائن ثلاثي الأبعاد في كائن Fabric.js
            fabricObject.threeObjectId = threeObject.id;
        }
    },
    
    // عند تعديل كائن في لوحة Fabric.js
    onFabricObjectModified: function(fabricObject) {
        // تحديث الكائن ثلاثي الأبعاد المقابل
        this.updateThreeObjectFromFabric(fabricObject);
    },
    
    // عند حذف كائن من لوحة Fabric.js
    onFabricObjectRemoved: function(fabricObject) {
        // حذف الكائن ثلاثي الأبعاد المقابل
        if (fabricObject.threeObjectId) {
            const threeObject = this.threeScene.scene.getObjectById(fabricObject.threeObjectId);
            if (threeObject) {
                this.threeScene.scene.remove(threeObject);
            }
        }
    },
    
    // عند تحديد كائن في لوحة Fabric.js
    onFabricSelectionCreated: function(selectedObjects) {
        // يمكن إضافة تأثيرات تمييز على الكائنات ثلاثية الأبعاد المقابلة
        selectedObjects.forEach(fabricObject => {
            if (fabricObject.threeObjectId) {
                const threeObject = this.threeScene.scene.getObjectById(fabricObject.threeObjectId);
                if (threeObject && threeObject.material) {
                    // تخزين اللون الأصلي
                    if (!threeObject.originalColor) {
                        threeObject.originalColor = threeObject.material.color.clone();
                    }
                    
                    // تغيير لون الكائن لتمييزه
                    threeObject.material.color.set(0x00ff00); // لون أخضر للتمييز
                    threeObject.material.needsUpdate = true;
                }
            }
        });
    },
    
    // عند إلغاء تحديد كائن في لوحة Fabric.js
    onFabricSelectionCleared: function() {
        // إعادة الألوان الأصلية للكائنات ثلاثية الأبعاد
        this.threeScene.scene.traverse(object => {
            if (object.isMesh && object.originalColor) {
                object.material.color.copy(object.originalColor);
                object.material.needsUpdate = true;
            }
        });
    },
    
    // إنشاء كائن ثلاثي الأبعاد من كائن Fabric.js
    createThreeObjectFromFabric: function(fabricObject) {
        let geometry, material, mesh;
        
        // إنشاء هندسة بناءً على نوع كائن Fabric.js
        switch (fabricObject.type) {
            case 'rect':
                geometry = new THREE.BoxGeometry(
                    fabricObject.width * 0.01,
                    fabricObject.height * 0.01,
                    0.1
                );
                break;
            case 'circle':
                geometry = new THREE.SphereGeometry(
                    fabricObject.radius * 0.01,
                    32,
                    32
                );
                break;
            case 'triangle':
                geometry = new THREE.ConeGeometry(
                    fabricObject.width * 0.005,
                    fabricObject.height * 0.01,
                    3
                );
                break;
            case 'line':
                // إنشاء خط ثلاثي الأبعاد
                const points = [
                    new THREE.Vector3(0, 0, 0),
                    new THREE.Vector3(
                        (fabricObject.x2 - fabricObject.x1) * 0.01,
                        (fabricObject.y2 - fabricObject.y1) * 0.01,
                        0
                    )
                ];
                geometry = new THREE.BufferGeometry().setFromPoints(points);
                material = new THREE.LineBasicMaterial({
                    color: fabricObject.stroke || 0x000000,
                    linewidth: fabricObject.strokeWidth || 1
                });
                mesh = new THREE.Line(geometry, material);
                
                // تعيين موضع الخط
                mesh.position.x = fabricObject.x1 * 0.01;
                mesh.position.y = fabricObject.y1 * 0.01;
                mesh.position.z = 0;
                
                this.threeScene.scene.add(mesh);
                return mesh;
            case 'path':
                // تحويل مسار Fabric.js إلى شكل ثلاثي الأبعاد
                // هذا تنفيذ مبسط، يمكن تحسينه لدعم المسارات المعقدة
                geometry = new THREE.PlaneGeometry(
                    fabricObject.width * 0.01,
                    fabricObject.height * 0.01
                );
                break;
            case 'text':
            case 'i-text':
            case 'textbox':
                // إنشاء نص ثلاثي الأبعاد
                const loader = new THREE.FontLoader();
                
                // استخدام نص مؤقت حتى يتم تحميل الخط
                geometry = new THREE.PlaneGeometry(
                    fabricObject.width * 0.01,
                    fabricObject.height * 0.01
                );
                
                // إنشاء نسيج من النص
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.width = fabricObject.width * 2;
                canvas.height = fabricObject.height * 2;
                
                ctx.fillStyle = fabricObject.fill || '#000000';
                ctx.font = `${fabricObject.fontSize}px ${fabricObject.fontFamily}`;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(
                    fabricObject.text,
                    canvas.width / 2,
                    canvas.height / 2
                );
                
                const texture = new THREE.CanvasTexture(canvas);
                
                material = new THREE.MeshBasicMaterial({
                    map: texture,
                    transparent: true,
                    side: THREE.DoubleSide
                });
                
                break;
            case 'image':
                // إنشاء صورة ثلاثية الأبعاد
                geometry = new THREE.PlaneGeometry(
                    fabricObject.width * 0.01,
                    fabricObject.height * 0.01
                );
                
                // إنشاء نسيج من الصورة
                const imgTexture = new THREE.Texture(fabricObject.getElement());
                imgTexture.needsUpdate = true;
                
                material = new THREE.MeshBasicMaterial({
                    map: imgTexture,
                    transparent: true,
                    side: THREE.DoubleSide
                });
                
                break;
            default:
                // كائن افتراضي للأنواع غير المدعومة
                geometry = new THREE.BoxGeometry(0.5, 0.5, 0.5);
                material = new THREE.MeshStandardMaterial({
                    color: 0xff0000,
                    transparent: true,
                    opacity: 0.7
                });
        }
        
        // إذا لم يتم إنشاء المادة بعد (للأنواع غير الخط)
        if (!material) {
            material = new THREE.MeshStandardMaterial({
                color: fabricObject.fill || 0xff0000,
                transparent: true,
                opacity: fabricObject.opacity || 1,
                roughness: 0.7,
                metalness: 0.3
            });
        }
        
        // إنشاء الشبكة إذا لم يتم إنشاؤها بعد (للأنواع غير الخط)
        if (!mesh) {
            mesh = new THREE.Mesh(geometry, material);
        }
        
        // تعيين موضع الشبكة بناءً على موضع كائن Fabric.js
        // تحويل إحداثيات Fabric.js إلى إحداثيات Three.js
        const canvasWidth = this.fabricCanvas.width;
        const canvasHeight = this.fabricCanvas.height;
        
        if (fabricObject.type !== 'line') {
            mesh.position.x = (fabricObject.left - canvasWidth / 2) * 0.01;
            mesh.position.y = (canvasHeight / 2 - fabricObject.top) * 0.01;
            mesh.position.z = 0;
            
            // تطبيق الدوران
            if (fabricObject.angle) {
                mesh.rotation.z = -fabricObject.angle * Math.PI / 180;
            }
        }
        
        // إضافة الشبكة إلى المشهد
        this.threeScene.scene.add(mesh);
        
        return mesh;
    },
    
    // تحديث كائن ثلاثي الأبعاد من كائن Fabric.js
    updateThreeObjectFromFabric: function(fabricObject) {
        if (!fabricObject.threeObjectId) return;
        
        const threeObject = this.threeScene.scene.getObjectById(fabricObject.threeObjectId);
        if (!threeObject) return;
        
        // تحديث موضع الكائن ثلاثي الأبعاد
        const canvasWidth = this.fabricCanvas.width;
        const canvasHeight = this.fabricCanvas.height;
        
        if (fabricObject.type !== 'line') {
            threeObject.position.x = (fabricObject.left - canvasWidth / 2) * 0.01;
            threeObject.position.y = (canvasHeight / 2 - fabricObject.top) * 0.01;
            
            // تحديث الدوران
            if (fabricObject.angle !== undefined) {
                threeObject.rotation.z = -fabricObject.angle * Math.PI / 180;
            }
            
            // تحديث الحجم (للأشكال البسيطة)
            if (fabricObject.type === 'rect' && threeObject.geometry instanceof THREE.BoxGeometry) {
                threeObject.scale.x = fabricObject.width * 0.01 / threeObject.geometry.parameters.width;
                threeObject.scale.y = fabricObject.height * 0.01 / threeObject.geometry.parameters.height;
            } else if (fabricObject.type === 'circle' && threeObject.geometry instanceof THREE.SphereGeometry) {
                const scale = fabricObject.radius * 0.01 / threeObject.geometry.parameters.radius;
                threeObject.scale.set(scale, scale, scale);
            }
        } else {
            // تحديث الخط
            threeObject.position.x = fabricObject.x1 * 0.01;
            threeObject.position.y = fabricObject.y1 * 0.01;
            
            // تحديث نقاط الخط
            const points = [
                new THREE.Vector3(0, 0, 0),
                new THREE.Vector3(
                    (fabricObject.x2 - fabricObject.x1) * 0.01,
                    (fabricObject.y2 - fabricObject.y1) * 0.01,
                    0
                )
            ];
            threeObject.geometry.setFromPoints(points);
        }
        
        // تحديث المادة
        if (threeObject.material) {
            if (fabricObject.fill) {
                threeObject.material.color.set(fabricObject.fill);
            }
            
            if (fabricObject.opacity !== undefined) {
                threeObject.material.opacity = fabricObject.opacity;
                threeObject.material.transparent = fabricObject.opacity < 1;
            }
            
            threeObject.material.needsUpdate = true;
        }
    },
    
    // تطبيق نسيج من لوحة Fabric.js على النموذج ثلاثي الأبعاد
    applyCanvasTextureToModel: function() {
        // الحصول على صورة من لوحة Fabric.js
        const dataURL = this.fabricCanvas.toDataURL({
            format: 'png',
            quality: 1
        });
        
        // إنشاء صورة جديدة
        const image = new Image();
        image.onload = () => {
            // إنشاء نسيج من الصورة
            const texture = new THREE.Texture(image);
            texture.needsUpdate = true;
            
            // تطبيق النسيج على النموذج
            if (this.threeScene.model) {
                this.threeScene.model.traverse((node) => {
                    if (node.isMesh && node.material) {
                        if (Array.isArray(node.material)) {
                            node.material.forEach(material => {
                                material.map = texture;
                                material.needsUpdate = true;
                            });
                        } else {
                            node.material.map = texture;
                            node.material.needsUpdate = true;
                        }
                    }
                });
            }
        };
        image.src = dataURL;
    },
    
    // تحديث التفاعل بين Fabric.js و Three.js
    update: function() {
        // تحديث النسيج على النموذج ثلاثي الأبعاد
        this.applyCanvasTextureToModel();
    }
};
