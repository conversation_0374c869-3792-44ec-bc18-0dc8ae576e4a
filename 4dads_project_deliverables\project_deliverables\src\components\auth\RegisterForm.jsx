import React, { useState } from 'react';
import GoogleAuth from './GoogleAuth';

// مكون نموذج التسجيل
// [!] هذا المكون يتضمن نموذج التسجيل البسيط مع التحقق من صحة المدخلات

const RegisterForm = () => {
  // حالة النموذج
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: ''
  });

  // حالة أخطاء التحقق
  const [errors, setErrors] = useState({
    name: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: ''
  });

  // حالة تقديم النموذج
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState({ type: '', message: '' });

  // معالجة تغيير المدخلات
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
    
    // مسح رسالة الخطأ عند تغيير القيمة
    setErrors({
      ...errors,
      [name]: ''
    });
  };

  // التحقق من صحة المدخلات
  const validateForm = () => {
    let tempErrors = {
      name: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: ''
    };
    let isValid = true;

    // التحقق من الاسم
    if (!formData.name.trim()) {
      tempErrors.name = 'الاسم مطلوب';
      isValid = false;
    } else if (formData.name.trim().length < 3) {
      tempErrors.name = 'يجب أن يكون الاسم 3 أحرف على الأقل';
      isValid = false;
    }

    // التحقق من البريد الإلكتروني
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!formData.email.trim()) {
      tempErrors.email = 'البريد الإلكتروني مطلوب';
      isValid = false;
    } else if (!emailRegex.test(formData.email)) {
      tempErrors.email = 'البريد الإلكتروني غير صالح';
      isValid = false;
    }

    // التحقق من رقم الهاتف
    const phoneRegex = /^\+?[0-9]{8,15}$/;
    if (formData.phone && !phoneRegex.test(formData.phone)) {
      tempErrors.phone = 'رقم الهاتف غير صالح';
      isValid = false;
    }

    // التحقق من كلمة المرور
    if (!formData.password) {
      tempErrors.password = 'كلمة المرور مطلوبة';
      isValid = false;
    } else if (formData.password.length < 8) {
      tempErrors.password = 'يجب أن تكون كلمة المرور 8 أحرف على الأقل';
      isValid = false;
    }

    // التحقق من تطابق كلمة المرور
    if (!formData.confirmPassword) {
      tempErrors.confirmPassword = 'تأكيد كلمة المرور مطلوب';
      isValid = false;
    } else if (formData.password !== formData.confirmPassword) {
      tempErrors.confirmPassword = 'كلمات المرور غير متطابقة';
      isValid = false;
    }

    setErrors(tempErrors);
    return isValid;
  };

  // معالجة تقديم النموذج
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (validateForm()) {
      setIsSubmitting(true);
      setSubmitMessage({ type: '', message: '' });
      
      try {
        // [!] هنا يتم إرسال البيانات إلى الخادم
        // يجب تعديل عنوان API حسب إعدادات الخادم الخاص بك
        const response = await fetch('/api/auth/register', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: formData.name,
            email: formData.email,
            phone: formData.phone,
            password: formData.password
          }),
        });

        const data = await response.json();
        
        if (response.ok) {
          // تم التسجيل بنجاح
          setSubmitMessage({
            type: 'success',
            message: 'تم التسجيل بنجاح! سيتم توجيهك إلى صفحة تسجيل الدخول...'
          });
          
          // إعادة تعيين النموذج
          setFormData({
            name: '',
            email: '',
            phone: '',
            password: '',
            confirmPassword: ''
          });
          
          // [!] يمكن إضافة توجيه إلى صفحة تسجيل الدخول هنا
          // setTimeout(() => router.push('/login'), 2000);
        } else {
          // فشل التسجيل
          setSubmitMessage({
            type: 'error',
            message: data.message || 'حدث خطأ أثناء التسجيل. يرجى المحاولة مرة أخرى.'
          });
        }
      } catch (error) {
        console.error('Registration error:', error);
        setSubmitMessage({
          type: 'error',
          message: 'حدث خطأ في الاتصال بالخادم. يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى.'
        });
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  return (
    <div className="max-w-md mx-auto bg-white p-8 rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6 text-center text-gray-800">إنشاء حساب جديد</h2>
      
      {submitMessage.message && (
        <div className={`p-4 mb-4 rounded ${
          submitMessage.type === 'success' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
        }`}>
          {submitMessage.message}
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="space-y-4" dir="rtl">
        {/* حقل الاسم */}
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
            الاسم الكامل <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            className={`w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 ${
              errors.name ? 'border-red-500 focus:ring-red-200' : 'border-gray-300 focus:ring-blue-200'
            }`}
            placeholder="أدخل اسمك الكامل"
            dir="rtl"
          />
          {errors.name && <p className="mt-1 text-sm text-red-500">{errors.name}</p>}
        </div>
        
        {/* حقل البريد الإلكتروني */}
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
            البريد الإلكتروني <span className="text-red-500">*</span>
          </label>
          <input
            type="email"
            id="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            className={`w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 ${
              errors.email ? 'border-red-500 focus:ring-red-200' : 'border-gray-300 focus:ring-blue-200'
            }`}
            placeholder="<EMAIL>"
            dir="ltr"
          />
          {errors.email && <p className="mt-1 text-sm text-red-500">{errors.email}</p>}
        </div>
        
        {/* حقل رقم الهاتف */}
        <div>
          <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
            رقم الهاتف
          </label>
          <input
            type="tel"
            id="phone"
            name="phone"
            value={formData.phone}
            onChange={handleChange}
            className={`w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 ${
              errors.phone ? 'border-red-500 focus:ring-red-200' : 'border-gray-300 focus:ring-blue-200'
            }`}
            placeholder="+1234567890"
            dir="ltr"
          />
          {errors.phone && <p className="mt-1 text-sm text-red-500">{errors.phone}</p>}
        </div>
        
        {/* حقل كلمة المرور */}
        <div>
          <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
            كلمة المرور <span className="text-red-500">*</span>
          </label>
          <input
            type="password"
            id="password"
            name="password"
            value={formData.password}
            onChange={handleChange}
            className={`w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 ${
              errors.password ? 'border-red-500 focus:ring-red-200' : 'border-gray-300 focus:ring-blue-200'
            }`}
            placeholder="********"
          />
          {errors.password && <p className="mt-1 text-sm text-red-500">{errors.password}</p>}
        </div>
        
        {/* حقل تأكيد كلمة المرور */}
        <div>
          <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
            تأكيد كلمة المرور <span className="text-red-500">*</span>
          </label>
          <input
            type="password"
            id="confirmPassword"
            name="confirmPassword"
            value={formData.confirmPassword}
            onChange={handleChange}
            className={`w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 ${
              errors.confirmPassword ? 'border-red-500 focus:ring-red-200' : 'border-gray-300 focus:ring-blue-200'
            }`}
            placeholder="********"
          />
          {errors.confirmPassword && <p className="mt-1 text-sm text-red-500">{errors.confirmPassword}</p>}
        </div>
        
        {/* زر التسجيل */}
        <div>
          <button
            type="submit"
            disabled={isSubmitting}
            className={`w-full py-2 px-4 border border-transparent rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
              isSubmitting ? 'opacity-70 cursor-not-allowed' : ''
            }`}
          >
            {isSubmitting ? 'جاري التسجيل...' : 'تسجيل'}
          </button>
        </div>
        
        {/* رابط تسجيل الدخول */}
        <div className="text-center mt-4">
          <p className="text-sm text-gray-600">
            لديك حساب بالفعل؟{' '}
            <a href="/login" className="font-medium text-blue-600 hover:text-blue-500">
              تسجيل الدخول
            </a>
          </p>
        </div>
        
        {/* زر التسجيل باستخدام Google */}
        <div className="mt-6">
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-white text-gray-500">أو التسجيل باستخدام</span>
            </div>
          </div>
          
          <div className="mt-6">
            {/* استخدام مكون GoogleAuth */}
            <GoogleAuth 
              onSuccess={(user) => {
                // معالجة نجاح المصادقة
                setSubmitMessage({
                  type: 'success',
                  message: `تم تسجيل الدخول بنجاح باستخدام Google. مرحبًا ${user.name}!`
                });
                // يمكن إضافة توجيه إلى صفحة لوحة التحكم هنا
                // setTimeout(() => router.push('/dashboard'), 2000);
              }}
              onError={(errorMessage) => {
                // معالجة فشل المصادقة
                setSubmitMessage({
                  type: 'error',
                  message: errorMessage
                });
              }}
            />
          </div>
        </div>
      </form>
    </div>
  );
};

export default RegisterForm;
