<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 900px;
            margin: 0 auto;
        }

        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }

        button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        button:hover {
            background-color: #0056b3;
        }

        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }

        #uploadBtn {
            background-color: #28a745;
        }

        #uploadBtn:hover {
            background-color: #1e7e34;
        }

        #startCrop {
            background-color: #ffc107;
            color: #212529;
        }

        #startCrop:hover {
            background-color: #e0a800;
        }

        #startDrawing {
            background-color: #6f42c1;
        }

        #startDrawing:hover {
            background-color: #5a32a3;
        }

        .file-input-wrapper {
            position: relative;
            overflow: hidden;
            display: inline-block;
        }

        .file-input-wrapper input[type=file] {
            position: absolute;
            left: -9999px;
        }

        .canvas-container {
            text-align: center;
            margin-top: 20px;
            position: relative;
        }

        #canvas {
            border: 2px solid #dee2e6;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .tool-submenu {
            display: none;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .aspect-ratio-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 15px;
        }

        .aspect-ratio-buttons button {
            padding: 8px 12px;
            font-size: 12px;
            background-color: #6c757d;
            min-width: 60px;
        }

        .aspect-ratio-buttons button:hover {
            background-color: #5a6268;
        }

        .aspect-ratio-buttons button.active {
            background-color: #007bff;
        }

        .tool-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            align-items: center;
            border-top: 1px solid #dee2e6;
            padding-top: 15px;
        }

        .tool-actions button {
            font-size: 13px;
        }

        #cropImage {
            background-color: #dc3545;
        }

        #cropImage:hover {
            background-color: #c82333;
        }

        #rotateCrop {
            background-color: #17a2b8;
        }

        #rotateCrop:hover {
            background-color: #138496;
        }

        #deleteCrop {
            background-color: #6c757d;
        }

        #deleteCrop:hover {
            background-color: #5a6268;
        }

        #resetCrop {
            background-color: #fd7e14;
        }

        #resetCrop:hover {
            background-color: #e8690b;
        }

        .instructions {
            margin-top: 15px;
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 4px;
            font-size: 14px;
            color: #495057;
        }

        .size-info {
            font-size: 12px;
            color: #6c757d;
            margin-left: 10px;
        }

        .drawing-controls {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            align-items: center;
        }

        .drawing-controls label {
            display: flex;
            flex-direction: column;
            align-items: center;
            font-size: 12px;
        }

        .drawing-controls .info {
            font-size: 11px;
        }

        .drawing-controls input[type="range"] {
            width: 80px;
        }

        .drawing-controls select {
            padding: 5px;
            border-radius: 4px;
            border: 1px solid #ced4da;
        }

        /* Object Manipulation Toolbar */
        .object-toolbar {
            display: none;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .object-toolbar .tool-actions {
            border-top: none;
            padding-top: 0;
        }

        .object-toolbar button {
            min-width: 100px;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>Advanced Canvas Crop & Drawing Editor</h1>

        <div class="controls">
            <div class="file-input-wrapper">
                <button id="uploadBtn">Upload Photo</button>
                <input type="file" id="fileInput" accept="image/*">
            </div>
            <button id="startCrop">Start Crop</button>
            <button id="startDrawing">Start Drawing</button>
        </div>

        <div class="canvas-container">
            <canvas id="canvas"></canvas>

            <!-- Crop Tools Submenu -->
            <div class="tool-submenu" id="cropSubmenu">
                <div class="aspect-ratio-buttons">
                    <button data-ratio="1:1">1:1</button>
                    <button data-ratio="3:2">3:2</button>
                    <button data-ratio="4:3">4:3</button>
                    <button data-ratio="5:4">5:4</button>
                    <button data-ratio="16:9">16:9</button>
                    <button data-ratio="21:9">21:9</button>
                    <button data-ratio="4:5">4:5</button>
                    <button data-ratio="2.63:1">2.63:1</button>
                    <button data-ratio="3:1">3:1</button>
                    <button data-ratio="2:1">2:1</button>
                    <button data-ratio="free">Free</button>
                </div>

                <div class="tool-actions">
                    <button id="cropImage">Crop Image</button>
                    <button id="rotateCrop">Rotate Crop</button>
                    <button id="deleteCrop">Delete Crop</button>
                    <button id="resetCrop">Reset Crop</button>
                    <span class="size-info" id="sizeInfo">Select crop area</span>
                </div>
            </div>

            <!-- Drawing Tools Submenu -->
            <div class="tool-submenu" id="drawingSubmenu">
                <div class="drawing-controls">
                    <label for="drawing-mode-selector">
                        Mode:
                        <select id="drawing-mode-selector">
                            <option>Pencil</option>
                            <option>Circle</option>
                            <option>Spray</option>
                            <option>Pattern</option>
                            <option>hline</option>
                            <option>vline</option>
                            <option>square</option>
                            <option>diamond</option>
                            <option>texture</option>
                        </select>
                    </label>

                    <label for="drawing-line-width">
                        Line width:
                        <span class="info" id="line-width-info">30</span>
                        <input type="range" min="1" max="150" id="drawing-line-width" value="30">
                    </label>

                    <label for="drawing-color">
                        Line color:
                        <input type="color" id="drawing-color" value="#76cef4">
                    </label>

                    <label for="drawing-shadow-color">
                        Shadow color:
                        <input type="color" id="drawing-shadow-color" value="#5a7896">
                    </label>

                    <label for="drawing-shadow-width">
                        Shadow width:
                        <span class="info" id="shadow-width-info">0</span>
                        <input type="range" min="0" max="50" id="drawing-shadow-width" value="0">
                    </label>

                    <label for="drawing-shadow-offset">
                        Shadow offset:
                        <span class="info" id="shadow-offset-info">0</span>
                        <input type="range" min="0" max="50" id="drawing-shadow-offset" value="0">
                    </label>

                    <button id="clear-drawings">Clear Drawings</button>
                    <button id="undoAction">Undo</button>
        <button id="redoAction">Redo</button>
                </div>
            </div>

            <!-- Object Manipulation Toolbar -->
            <div class="object-toolbar" id="objectToolbar">
                <div class="tool-actions">
                    <button id="copyObject">Copy</button>
                    <button id="bringForward">Send Forward</button>
                    <button id="sendBackward">Send Backward</button>
                    <button id="deleteObject">Delete</button>
                    <button id="undoAction">Undo</button>
                    <button id="redoAction">Redo</button>
                </div>
            </div>
        </div>

        <div class="instructions">
            <strong>Instructions:</strong>
            Upload an image, then choose to crop or draw on it. For cropping: Select aspect ratios or use free mode.
            For drawing: Choose drawing mode and customize line properties. Drag to draw or adjust crop area.
            After uploading, use the object toolbar to manipulate objects.
        </div>
    </div>

    <canvas style="display: none;" id="canvas_crop"></canvas>
</body>

<script>
    var canvas = new fabric.Canvas('canvas', {
        width: 700,
        height: 500,
        backgroundColor: '#ffffff'
    });

    var cropRect = null;
    var currentImage = null;
    var isCropping = false;
    var isDrawing = false;
    var currentAspectRatio = 'free';
    var cropRotation = 0;
    var originalImageData = null;
    var history = [];
    var historyIndex = -1;

    // Initialize on document ready
    $(document).ready(function() {
        if (typeof fabric === 'undefined') {
            alert('Fabric.js library failed to load. Please refresh the page.');
            return;
        }

        initializeCanvas();
        setupEventHandlers();
        setupDrawingEventHandlers();
        setupObjectToolbarHandlers();
    });

    function initializeCanvas() {
        canvas.selection = false;
        canvas.renderAll();
    }

    function setupEventHandlers() {
        $('#fileInput').on('change', handleFileSelect);
        $('#uploadBtn').on('click', function() {
            $('#fileInput').click();
        });

        $('#startCrop').on('click', toggleCropMode);
        $('#startDrawing').on('click', toggleDrawingMode);

        $('.aspect-ratio-buttons button').on('click', function() {
            $('.aspect-ratio-buttons button').removeClass('active');
            $(this).addClass('active');
            currentAspectRatio = $(this).data('ratio');
            if (cropRect && currentImage) {
                updateCropRect();
            }
        });

        $('#cropImage').on('click', applyCrop);
        $('#rotateCrop').on('click', rotateCropArea);
        $('#deleteCrop').on('click', deleteCropArea);
        $('#resetCrop').on('click', resetCropArea);
        $('#clear-drawings').on('click', clearDrawings);
    }

    function setupDrawingEventHandlers() {
        const drawingColorEl = document.getElementById('drawing-color');
        const drawingShadowColorEl = document.getElementById('drawing-shadow-color');
        const drawingLineWidthEl = document.getElementById('drawing-line-width');
        const drawingShadowWidth = document.getElementById('drawing-shadow-width');
        const drawingShadowOffset = document.getElementById('drawing-shadow-offset');
        const selectorEl = document.getElementById('drawing-mode-selector');

        drawingColorEl.addEventListener('change', updateBrush);
        drawingShadowColorEl.addEventListener('change', updateBrush);

        drawingLineWidthEl.addEventListener('input', () => {
            document.getElementById('line-width-info').textContent = drawingLineWidthEl.value;
            updateBrush();
        });

        drawingShadowWidth.addEventListener('input', () => {
            document.getElementById('shadow-width-info').textContent = drawingShadowWidth.value;
            updateBrush();
        });

        drawingShadowOffset.addEventListener('input', () => {
            document.getElementById('shadow-offset-info').textContent = drawingShadowOffset.value;
            updateBrush();
        });

        selectorEl.addEventListener('change', handleDrawingModeChange);
    }

    function setupObjectToolbarHandlers() {
        $('#copyObject').on('click', copySelectedObject);
        $('#bringForward').on('click', bringSelectedForward);
        $('#sendBackward').on('click', sendSelectedBackward);
        $('#deleteObject').on('click', deleteSelectedObject);
        $('#undoAction').on('click', performUndo);
        $('#redoAction').on('click', performRedo);

        canvas.on('selection:created', function() {
            updateObjectToolbarButtons();
        });

        canvas.on('selection:updated', function() {
            updateObjectToolbarButtons();
        });

        canvas.on('selection:cleared', function() {
            updateObjectToolbarButtons();
        });
    }

    function updateBrush() {
        const brush = canvas.freeDrawingBrush;
        if (!brush) return;

        const drawingColorEl = document.getElementById('drawing-color');
        const drawingShadowColorEl = document.getElementById('drawing-shadow-color');
        const drawingLineWidthEl = document.getElementById('drawing-line-width');
        const drawingShadowWidth = document.getElementById('drawing-shadow-width');
        const drawingShadowOffset = document.getElementById('drawing-shadow-offset');

        brush.color = drawingColorEl.value;
        brush.width = parseInt(drawingLineWidthEl.value, 10) || 1;
        brush.shadow = new fabric.Shadow({
            blur: parseInt(drawingShadowWidth.value, 10) || 0,
            offsetX: parseInt(drawingShadowOffset.value, 10) || 0,
            offsetY: parseInt(drawingShadowOffset.value, 10) || 0,
            color: drawingShadowColorEl.value,
        });
    }

    function handleDrawingModeChange() {
        const selectorEl = document.getElementById('drawing-mode-selector');
        const val = selectorEl.value;

        if (val === 'hline') {
            const brush = new fabric.PatternBrush(canvas);
            brush.getPatternSrc = () => {
                const patternCanvas = document.createElement('canvas');
                patternCanvas.width = patternCanvas.height = 10;
                const ctx = patternCanvas.getContext('2d');
                ctx.strokeStyle = brush.color;
                ctx.lineWidth = 5;
                ctx.beginPath();
                ctx.moveTo(0, 5);
                ctx.lineTo(10, 5);
                ctx.stroke();
                return patternCanvas;
            };
            canvas.freeDrawingBrush = brush;
        }
        else if (val === 'vline') {
            const brush = new fabric.PatternBrush(canvas);
            brush.getPatternSrc = () => {
                const patternCanvas = document.createElement('canvas');
                patternCanvas.width = patternCanvas.height = 10;
                const ctx = patternCanvas.getContext('2d');
                ctx.strokeStyle = brush.color;
                ctx.lineWidth = 5;
                ctx.beginPath();
                ctx.moveTo(5, 0);
                ctx.lineTo(5, 10);
                ctx.stroke();
                return patternCanvas;
            };
            canvas.freeDrawingBrush = brush;
        }
        else {
            const brushClass = fabric[val + 'Brush'] || fabric.PencilBrush;
            canvas.freeDrawingBrush = new brushClass(canvas);
        }

        updateBrush();
    }

    function clearDrawings() {
        if (!currentImage) {
            alert('Please upload an image first.');
            return;
        }

        // Remove all objects except the background image
        const objects = canvas.getObjects();
        objects.forEach(obj => {
            if (obj !== currentImage) {
                canvas.remove(obj);
            }
        });

        saveState();
        canvas.renderAll();
    }

    function toggleDrawingMode() {
        if (!currentImage) {
            alert('Please upload an image first.');
            return;
        }

        isDrawing = !isDrawing;
        const submenu = $('#drawingSubmenu');

        if (isDrawing) {
            // If crop mode is active, turn it off
            if (isCropping) {
                $('#cropSubmenu').slideUp(300);
                $('#startCrop').text('Start Crop');
                isCropping = false;
                if (cropRect) {
                    canvas.remove(cropRect);
                    cropRect = null;
                }
            }

            submenu.slideDown(300);
            $('#startDrawing').text('Hide Drawing Tools');
            canvas.isDrawingMode = true;
            updateBrush();
        } else {
            submenu.slideUp(300);
            $('#startDrawing').text('Start Drawing');
            canvas.isDrawingMode = false;
        }
    }

    function handleFileSelect(event) {
        const file = event.target.files[0];
        if (file && file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = function(e) {
                loadImageToCanvas(e.target.result);
                // Show object toolbar after upload
                $('#objectToolbar').slideDown(300);
            };
            reader.readAsDataURL(file);
        } else {
            alert('Please select a valid image file.');
        }
    }

    function loadImageToCanvas(imageSrc) {
        canvas.clear();
        canvas.isDrawingMode = false;

        fabric.Image.fromURL(imageSrc, function(img) {
            const canvasWidth = canvas.getWidth();
            const canvasHeight = canvas.getHeight();
            const imgWidth = img.width;
            const imgHeight = img.height;

            const scaleX = (canvasWidth - 40) / imgWidth;
            const scaleY = (canvasHeight - 40) / imgHeight;
            const scale = Math.min(scaleX, scaleY, 1);

            img.set({
                scaleX: scale,
                scaleY: scale,
                selectable: true,
                left: (canvasWidth - imgWidth * scale) / 2,
                top: (canvasHeight - imgHeight * scale) / 2
            });

            canvas.add(img);
            canvas.renderAll();

            currentImage = img;
            originalImageData = {
                src: imageSrc,
                left: img.left,
                top: img.top,
                scaleX: img.scaleX,
                scaleY: img.scaleY
            };

            if (isCropping) {
                createCropRect();
            }

            // Save initial state
            saveState();
        });
    }

    function toggleCropMode() {
        if (!currentImage) {
            alert('Please upload an image first.');
            return;
        }

        isCropping = !isCropping;
        const submenu = $('#cropSubmenu');

        if (isCropping) {
            // If drawing mode is active, turn it off
            if (isDrawing) {
                $('#drawingSubmenu').slideUp(300);
                $('#startDrawing').text('Start Drawing');
                isDrawing = false;
                canvas.isDrawingMode = false;
            }

            submenu.slideDown(300);
            $('#startCrop').text('Hide Crop Tools');
            createCropRect();
        } else {
            submenu.slideUp(300);
            $('#startCrop').text('Start Crop');
            if (cropRect) {
                canvas.remove(cropRect);
                cropRect = null;
            }
        }
    }

    function createCropRect() {
        if (cropRect) {
            canvas.remove(cropRect);
        }

        const img = currentImage;
        const rectWidth = Math.min(200, img.width * img.scaleX * 0.8);
        const rectHeight = currentAspectRatio === 'free' ? rectWidth * 0.75 :
                          calculateHeightFromRatio(rectWidth, currentAspectRatio);

        cropRect = new fabric.Rect({
            left: img.left + (img.width * img.scaleX - rectWidth) / 2,
            top: img.top + (img.height * img.scaleY - rectHeight) / 2,
            width: rectWidth,
            height: rectHeight,
            fill: 'rgba(0,0,0,0.3)',
            stroke: '#00ff00',
            strokeWidth: 2,
            strokeDashArray: [5, 5],
            selectable: true,
            hasRotatingPoint: false,
            cornerColor: '#00ff00',
            cornerSize: 12,
            transparentCorners: false,
            borderColor: '#00ff00'
        });

        canvas.add(cropRect);
        canvas.setActiveObject(cropRect);
        canvas.renderAll();
        updateSizeInfo();
    }

    function calculateHeightFromRatio(width, ratio) {
        if (ratio === 'free') return width * 0.75;

        const ratios = {
            '1:1': 1,
            '3:2': 2/3,
            '4:3': 3/4,
            '5:4': 4/5,
            '16:9': 9/16,
            '21:9': 9/21,
            '4:5': 5/4,
            '2.63:1': 1/2.63,
            '3:1': 1/3,
            '2:1': 1/2
        };

        return width * (ratios[ratio] || 0.75);
    }

    function updateCropRect() {
        if (!cropRect || currentAspectRatio === 'free') return;

        // Calculate new dimensions while maintaining position
        const currentWidth = cropRect.width;
        const newHeight = calculateHeightFromRatio(currentWidth, currentAspectRatio);

        // Store current position
        const currentLeft = cropRect.left;
        const currentTop = cropRect.top;

        // Update dimensions without changing position
        cropRect.set({
            height: newHeight,
            scaleX: 1,
            scaleY: 1,
            left: currentLeft,
            top: currentTop
        });

        constrainCropRect();
        canvas.renderAll();
        updateSizeInfo();
    }

    function handleCropMoving(e) {
        if (e.target === cropRect) {
            constrainCropRect();
        }
    }

    function handleCropScaling(e) {
        if (e.target === cropRect) {
            if (currentAspectRatio !== 'free') {
                // Maintain aspect ratio during scaling
                const currentWidth = cropRect.width * cropRect.scaleX;
                const newHeight = calculateHeightFromRatio(currentWidth, currentAspectRatio);

                // Reset scale factors and adjust dimensions directly
                cropRect.set({
                    width: currentWidth,
                    height: newHeight,
                    scaleX: 1,
                    scaleY: 1
                });
            } else {
                // For free form, normalize the scaling
                cropRect.set({
                    width: cropRect.width * cropRect.scaleX,
                    height: cropRect.height * cropRect.scaleY,
                    scaleX: 1,
                    scaleY: 1
                });
            }
            constrainCropRect();
        }
    }

    function constrainCropRect() {
        if (!cropRect || !currentImage) return;

        const img = currentImage;
        const imgBounds = {
            left: img.left,
            top: img.top,
            right: img.left + img.width * img.scaleX,
            bottom: img.top + img.height * img.scaleY
        };

        const rectWidth = cropRect.width * cropRect.scaleX;
        const rectHeight = cropRect.height * cropRect.scaleY;

        let newLeft = Math.max(imgBounds.left, cropRect.left);
        let newTop = Math.max(imgBounds.top, cropRect.top);

        newLeft = Math.min(newLeft, imgBounds.right - rectWidth);
        newTop = Math.min(newTop, imgBounds.bottom - rectHeight);

        cropRect.set({
            left: newLeft,
            top: newTop
        });
    }

    function updateSizeInfo() {
        if (!cropRect) {
            $('#sizeInfo').text('Select crop area');
            return;
        }

        const width = Math.round(cropRect.width * cropRect.scaleX);
        const height = Math.round(cropRect.height * cropRect.scaleY);
        $('#sizeInfo').text(`${width} × ${height}px`);
    }

    function applyCrop() {
        if (!cropRect || !currentImage) {
            alert('Please select a crop area first.');
            return;
        }

        const img = currentImage;

        // Get actual crop rectangle dimensions and position
        const cropLeft = cropRect.left;
        const cropTop = cropRect.top;
        const cropWidth = cropRect.width;
        const cropHeight = cropRect.height;

        // Calculate relative position within the image bounds
        const relativeLeft = Math.max(0, cropLeft - img.left);
        const relativeTop = Math.max(0, cropTop - img.top);
        const maxWidth = Math.min(cropWidth, (img.left + img.width * img.scaleX) - cropLeft);
        const maxHeight = Math.min(cropHeight, (img.top + img.height * img.scaleY) - cropTop);

        const cropData = {
            left: relativeLeft,
            top: relativeTop,
            width: maxWidth,
            height: maxHeight
        };

        performCrop(img, cropData);
    }

    function performCrop(img, cropData) {
        // Create a temporary canvas for precise cropping
        const tempCanvas = document.createElement('canvas');
        const tempCtx = tempCanvas.getContext('2d');

        // Calculate actual crop coordinates relative to the original image
        const imgElement = img.getElement();
        const originalWidth = imgElement.naturalWidth || imgElement.width;
        const originalHeight = imgElement.naturalHeight || imgElement.height;

        // Calculate the scale factor between displayed image and original image
        const displayWidth = img.width * img.scaleX;
        const displayHeight = img.height * img.scaleY;
        const scaleFactorX = originalWidth / displayWidth;
        const scaleFactorY = originalHeight / displayHeight;

        // Convert crop coordinates to original image coordinates
        const sourceX = Math.max(0, cropData.left * scaleFactorX);
        const sourceY = Math.max(0, cropData.top * scaleFactorY);
        const sourceWidth = Math.min(originalWidth - sourceX, cropData.width * scaleFactorX);
        const sourceHeight = Math.min(originalHeight - sourceY, cropData.height * scaleFactorY);

        // Set canvas dimensions to match the crop area
        tempCanvas.width = sourceWidth;
        tempCanvas.height = sourceHeight;

        // Draw the cropped portion
        tempCtx.drawImage(imgElement, sourceX, sourceY, sourceWidth, sourceHeight, 0, 0, sourceWidth, sourceHeight);

        const croppedDataURL = tempCanvas.toDataURL('image/png');

        fabric.Image.fromURL(croppedDataURL, function(croppedImg) {
            canvas.clear();

            // Position the cropped image at the crop area location
            const canvasWidth = canvas.getWidth();
            const canvasHeight = canvas.getHeight();
            const imgWidth = croppedImg.width;
            const imgHeight = croppedImg.height;

            // Scale to fit canvas if necessary
            const maxScale = Math.min(canvasWidth / imgWidth, canvasHeight / imgHeight, 1);

            croppedImg.set({
                scaleX: maxScale,
                scaleY: maxScale,
                left: (canvasWidth - imgWidth * maxScale) / 2,
                top: (canvasHeight - imgHeight * maxScale) / 2,
                selectable: true
            });

            canvas.add(croppedImg);
            canvas.renderAll();

            currentImage = croppedImg;
            cropRect = null;
            isCropping = false;
            $('#cropSubmenu').slideUp(300);
            $('#startCrop').text('Start Crop');

            saveState();
        });
    }

    function rotateCropArea() {
        if (!cropRect) {
            alert('Please select a crop area first.');
            return;
        }

        // Store current dimensions and position
        const currentWidth = cropRect.width;
        const currentHeight = cropRect.height;
        const currentLeft = cropRect.left;
        const currentTop = cropRect.top;

        // Calculate center point for rotation
        const centerX = currentLeft + currentWidth / 2;
        const centerY = currentTop + currentHeight / 2;

        // Swap dimensions
        const newWidth = currentHeight;
        const newHeight = currentWidth;

        // Calculate new position to maintain center point
        const newLeft = centerX - newWidth / 2;
        const newTop = centerY - newHeight / 2;

        // Update crop rectangle with swapped dimensions
        cropRect.set({
            width: newWidth,
            height: newHeight,
            left: newLeft,
            top: newTop,
            scaleX: 1,
            scaleY: 1
        });

        // Update aspect ratio if needed
        if (currentAspectRatio !== 'free') {
            // Update the aspect ratio to the rotated equivalent
            const ratioMap = {
                '3:2': '2:3',
                '2:3': '3:2',
                '4:3': '3:4',
                '3:4': '4:3',
                '5:4': '4:5',
                '4:5': '5:4',
                '16:9': '9:16',
                '9:16': '16:9',
                '21:9': '9:21',
                '9:21': '21:9',
                '2.63:1': '1:2.63',
                '1:2.63': '2.63:1',
                '3:1': '1:3',
                '1:3': '3:1',
                '2:1': '1:2',
                '1:2': '2:1'
            };

            const newRatio = ratioMap[currentAspectRatio] || currentAspectRatio;
            if (newRatio !== currentAspectRatio) {
                currentAspectRatio = newRatio;
                $('.aspect-ratio-buttons button').removeClass('active');
                $(`.aspect-ratio-buttons button[data-ratio="${newRatio}"]`).addClass('active');
            }
        }

        constrainCropRect();
        canvas.renderAll();
        updateSizeInfo();
    }

    function deleteCropArea() {
        if (cropRect) {
            canvas.remove(cropRect);
            cropRect = null;
            canvas.renderAll();
            $('#sizeInfo').text('Select crop area');
        }
    }

    function resetCropArea() {
        if (currentImage) {
            createCropRect();
            $('.aspect-ratio-buttons button').removeClass('active');
            $('.aspect-ratio-buttons button[data-ratio="free"]').addClass('active');
            currentAspectRatio = 'free';
        }
    }

    /* Object Manipulation Functions */
    function copySelectedObject() {
        const activeObject = canvas.getActiveObject();
        if (!activeObject) return;

        activeObject.clone(function(cloned) {
            cloned.set({
                left: cloned.left + 10,
                top: cloned.top + 10,
                selectable: true
            });
            canvas.add(cloned);
            canvas.setActiveObject(cloned);
            saveState();
        });
    }

    function bringSelectedForward() {
        const activeObject = canvas.getActiveObject();
        if (!activeObject) return;

        activeObject.bringForward();
        canvas.renderAll();
        saveState();
    }

    function sendSelectedBackward() {
        const activeObject = canvas.getActiveObject();
        if (!activeObject) return;

        activeObject.sendBackwards();
        canvas.renderAll();
        saveState();
    }

    function deleteSelectedObject() {
        const activeObject = canvas.getActiveObject();
        if (!activeObject) return;

        canvas.remove(activeObject);
        canvas.discardActiveObject();
        saveState();
        updateObjectToolbarButtons();
    }

    function saveState() {
        // Only keep up to 20 states in history
        if (history.length >= 20) {
            history.shift();
        }

        // Clear redo stack when new action is performed
        if (historyIndex < history.length - 1) {
            history = history.slice(0, historyIndex + 1);
        }

        // Save current canvas state
        const state = JSON.stringify(canvas);
        history.push(state);
        historyIndex = history.length - 1;

        updateObjectToolbarButtons();
    }

    function performUndo() {
        if (historyIndex <= 0) return;

        historyIndex--;
        const state = history[historyIndex];
        canvas.clear();
        canvas.loadFromJSON(state, function() {
            canvas.renderAll();
            // Find the current image after undo
            const objects = canvas.getObjects();
            for (let obj of objects) {
                if (obj.type === 'image') {
                    currentImage = obj;
                    break;
                }
            }
            updateObjectToolbarButtons();
        });
    }

    function performRedo() {
        if (historyIndex >= history.length - 1) return;

        historyIndex++;
        const state = history[historyIndex];
        canvas.clear();
        canvas.loadFromJSON(state, function() {
            canvas.renderAll();
            // Find the current image after redo
            const objects = canvas.getObjects();
            for (let obj of objects) {
                if (obj.type === 'image') {
                    currentImage = obj;
                    break;
                }
            }
            updateObjectToolbarButtons();
        });
    }

    function updateObjectToolbarButtons() {
        const hasSelection = canvas.getActiveObject() !== null;
        const canUndo = historyIndex > 0;
        const canRedo = historyIndex < history.length - 1;

        $('#copyObject').prop('disabled', !hasSelection);
        $('#bringForward').prop('disabled', !hasSelection);
        $('#sendBackward').prop('disabled', !hasSelection);
        $('#deleteObject').prop('disabled', !hasSelection);
        $('#undoAction').prop('disabled', !canUndo);
        $('#redoAction').prop('disabled', !canRedo);
    }
</script>

</html>
