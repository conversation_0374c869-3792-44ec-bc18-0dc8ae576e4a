 * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 900px;
            margin: 0 auto;
        }

        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }

        button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        button:hover {
            background-color: #0056b3;
        }

        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }

        #uploadBtn {
            background-color: #28a745;
        }

        #uploadBtn:hover {
            background-color: #1e7e34;
        }

        #startCrop {
            background-color: #ffc107;
            color: #212529;
        }

        #startCrop:hover {
            background-color: #e0a800;
        }

        #startDrawing {
            background-color: #6f42c1;
        }

        #startDrawing:hover {
            background-color: #5a32a3;
        }

        .file-input-wrapper {
            position: relative;
            overflow: hidden;
            display: inline-block;
        }

        .file-input-wrapper input[type=file] {
            position: absolute;
            left: -9999px;
        }

        .canvas-container {
            text-align: center;
            margin-top: 20px;
            position: relative;
        }

        #canvas {
            border: 2px solid #dee2e6;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .tool-submenu {
            display: none;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .aspect-ratio-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 15px;
        }

        .aspect-ratio-buttons button {
            padding: 8px 12px;
            font-size: 12px;
            background-color: #6c757d;
            min-width: 60px;
        }

        .aspect-ratio-buttons button:hover {
            background-color: #5a6268;
        }

        .aspect-ratio-buttons button.active {
            background-color: #007bff;
        }

        .tool-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            align-items: center;
            border-top: 1px solid #dee2e6;
            padding-top: 15px;
        }

        .tool-actions button {
            font-size: 13px;
        }

        #cropImage {
            background-color: #dc3545;
        }

        #cropImage:hover {
            background-color: #c82333;
        }

        #rotateCrop {
            background-color: #17a2b8;
        }

        #rotateCrop:hover {
            background-color: #138496;
        }

        #deleteCrop {
            background-color: #6c757d;
        }

        #deleteCrop:hover {
            background-color: #5a6268;
        }

        #resetCrop {
            background-color: #fd7e14;
        }

        #resetCrop:hover {
            background-color: #e8690b;
        }

        .instructions {
            margin-top: 15px;
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 4px;
            font-size: 14px;
            color: #495057;
        }

        .size-info {
            font-size: 12px;
            color: #6c757d;
            margin-left: 10px;
        }

        .drawing-controls {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            align-items: center;
        }

        .drawing-controls label {
            display: flex;
            flex-direction: column;
            align-items: center;
            font-size: 12px;
        }

        .drawing-controls .info {
            font-size: 11px;
        }

        .drawing-controls input[type="range"] {
            width: 80px;
        }

        .drawing-controls select {
            padding: 5px;
            border-radius: 4px;
            border: 1px solid #ced4da;
        }

        /* Object Manipulation Toolbar */
        .object-toolbar {
            display: none;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            z-index: 1000;
            min-width: 420px;
        }

        .object-toolbar .tool-actions {
            border-top: none;
            padding-top: 0;
        }

        .object-toolbar button {
            min-width: 100px;
        }
        .preview-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 1.8rem;
            font-weight: bold;
            color: rgba(255, 255, 255, 0.7);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            pointer-events: none;
            text-align: center;
            max-width: 90%;
        }

        .text-tools-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            align-items: center;
            justify-content: center;
        }

        .text-tool {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 120px;
        }

        .text-tool input[type="color"] {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            padding: 0;
        }

        .text-tool input[type="color"]::-webkit-color-swatch {
            border: none;
            border-radius: 50%;
        }

        .text-tool input[type="color"]::-webkit-color-swatch-wrapper {
            padding: 0;
        }

        .text-tool label {
            margin-top: 5px;
            font-size: 0.8rem;
            color: #555;
            font-weight: 500;
        }

        .text-slider {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 120px;
        }

        .text-slider input {
            width: 100%;
        }

        #textToolbar { display: none; }
