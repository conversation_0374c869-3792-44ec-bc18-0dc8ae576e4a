<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crop Functionality with Image Upload</title>
    <link href="https://cdn.jsdelivr.net/npm/cropperjs@1.5.12/dist/cropper.min.css"  rel="stylesheet">
    <style>
        :root {
            --accent-color: #4A90E2;
        }

        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }

        .toolbar-button {
            padding: 10px 15px;
            margin: 5px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
            display: inline-block;
            margin-bottom: 20px;
        }

        .toolbar-button:hover {
            background: var(--accent-color);
            color: white;
        }

        .modal {
            position: fixed;
            top: 0; left: 0;
            width: 100%; height: 100%;
            background: rgba(0,0,0,0.7);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            padding: 20px;
            border-radius: 8px;
            max-width: 90%;
            max-height: 90%;
            overflow: auto;
            position: relative;
        }

        #cropperImage {
            display: block;
            max-width: 100%;
            max-height: 70vh;
        }

        .aspect-ratio-container {
            margin-top: 10px;
        }

        .ratio-row {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-bottom: 10px;
        }

        .ratio-row:last-child {
            margin-bottom: 0;
        }

        .ratio-label {
            font-weight: bold;
            margin-bottom: 5px;
            display: block;
        }

        .ratio-button {
            padding: 5px 10px;
            background: #f0f0f0;
            border: 1px solid #ccc;
            cursor: pointer;
            transition: all 0.2s;
            min-width: 80px;
            text-align: center;
        }

        .ratio-button:hover {
            background: var(--accent-color);
            color: white;
            border-color: var(--accent-color);
        }

        .ratio-button.active {
            background: var(--accent-color);
            color: white;
            border-color: var(--accent-color);
        }

        #applyCropBtn {
            margin-top: 15px;
            padding: 8px 15px;
            background: var(--accent-color);
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .error-message {
            color: red;
            margin-top: 10px;
        }

        .upload-container {
            margin-bottom: 20px;
        }

        .upload-button {
            padding: 10px 15px;
            margin-right: 10px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .upload-button:hover {
            background: #45a049;
        }

        #fileInput {
            display: none;
        }

        @media (max-width: 600px) {
            .ratio-button {
                flex: 1 1 45%;
                text-align: center;
            }

            .ratio-row {
                gap: 5px;
            }
        }
    </style>
</head>
<body>
    <h2>Crop Functionality with Image Upload</h2>

    <!-- Image Upload Section -->
    <div class="upload-container">
        <label for="fileInput" class="upload-button">Upload Image</label>
        <input type="file" id="fileInput" accept="image/*">
        <span id="fileName">No file selected</span>
    </div>

    <!-- Crop Button -->
    <button class="toolbar-button" onclick="initCrop()" id="cropBtn">Start Crop</button>
    <div class="error-message" id="errorMessage"></div>

    <!-- Cropper Modal -->
    <div class="modal" id="cropperModal">
        <div class="modal-content">
            <img id="cropperImage">

            <div class="aspect-ratio-container">
                <span class="ratio-label">Vertical (Portrait) Ratios:</span>
                <div class="ratio-row" id="verticalRatios">
                    <!-- Buttons will be added by JavaScript -->
                </div>

                <span class="ratio-label">Horizontal (Landscape) Ratios:</span>
                <div class="ratio-row" id="horizontalRatios">
                    <!-- Buttons will be added by JavaScript -->
                </div>
            </div>

            <button id="applyCropBtn">Apply Crop</button>
        </div>
    </div>

    <!-- Canvas for demonstration -->
    <canvas id="demoCanvas" width="800" height="600" style="border: 1px solid #ccc; margin-top: 20px;"></canvas>

    <!-- Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/fabric@5.3.0/dist/fabric.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/cropperjs@1.5.12/dist/cropper.min.js"></script>

    <script>
        // Check if libraries are loaded
        function loadScript(src, callback) {
            const script = document.createElement('script');
            script.src = src;
            script.onload = () => {
                console.log(`${src} loaded successfully`);
                callback();
            };
            script.onerror = () => {
                document.getElementById('errorMessage').textContent =
                    `Failed to load library: ${src}`;
            };
            document.head.appendChild(script);
        }

        // Load required libraries
        loadScript('https://cdn.jsdelivr.net/npm/fabric@5.3.0/dist/fabric.min.js',  () => {
            loadScript('https://cdn.jsdelivr.net/npm/cropperjs@1.5.12/dist/cropper.min.js',  initApp);
        });

        let cropper = null;
        let currentImage = null;
        let demoCanvas = null;
        let imageLoaded = false;

        // Define the aspect ratios with corrected vertical ratios
        const aspectRatios = {
            vertical: [
                { label: "1:1", value: "1:1" },
                { label: "2:3", value: "2:3" },
                { label: "3:4", value: "3:4" },
                { label: "4:5", value: "4:5" },
                { label: "9:16", value: "9:16" },
                { label: "9:21", value: "9:21" },
                { label: "5:4", value: "5:4" },
                { label: "1:2.63", value: "1:2.63" },
                { label: "1:3", value: "1:3" },
                { label: "1:2", value: "1:2" }
            ],
            horizontal: [
                { label: "Free", value: "free" },
                { label: "3:2", value: "3:2" },
                { label: "4:3", value: "4:3" },
                { label: "5:4", value: "5:4" },
                { label: "16:9", value: "16:9" },
                { label: "21:9", value: "21:9" },
                { label: "2.63:1", value: "2.63:1" },
                { label: "3:1", value: "3:1" },
                { label: "2:1", value: "2:1" }
            ]
        };

        function parseAspectRatio(ratio) {
            if (ratio === 'free') return null;
            const [width, height] = ratio.split(':').map(Number);
            return width / height;
        }

        function createRatioButtons() {
            const verticalContainer = document.getElementById('verticalRatios');
            const horizontalContainer = document.getElementById('horizontalRatios');

            // Create vertical ratio buttons
            aspectRatios.vertical.forEach(ratio => {
                const button = document.createElement('button');
                button.className = 'ratio-button';
                button.textContent = `${ratio.label} (Portrait)`;
                button.dataset.ratio = ratio.value;

                if (ratio.value === '1:1') {
                    button.classList.add('active');
                }

                button.addEventListener('click', () => {
                    setAspectRatio(button.dataset.ratio);
                });

                verticalContainer.appendChild(button);
            });

            // Create horizontal ratio buttons
            aspectRatios.horizontal.forEach(ratio => {
                const button = document.createElement('button');
                button.className = 'ratio-button';
                button.textContent = `${ratio.label} (Landscape)`;
                button.dataset.ratio = ratio.value;

                if (ratio.value === 'free') {
                    button.classList.add('active');
                }

                button.addEventListener('click', () => {
                    setAspectRatio(button.dataset.ratio);
                });

                horizontalContainer.appendChild(button);
            });
        }

        function initApp() {
            // Initialize ratio buttons
            createRatioButtons();

            // Demo canvas setup
            demoCanvas = new fabric.Canvas('demoCanvas', {
                preserveObjectStacking: true
            });

            // Add event listener for file upload
            document.getElementById('fileInput').addEventListener('change', handleImageUpload);
        }

        function handleImageUpload(event) {
            const file = event.target.files[0];
            if (file) {
                // Update file name display
                document.getElementById('fileName').textContent = file.name;

                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = new Image();
                    img.onload = function() {
                        const fabricImg = new fabric.Image(img, {
                            left: 100,
                            top: 100,
                            scaleX: Math.min(600 / img.width, 1),
                            scaleY: Math.min(600 / img.height, 1)
                        });

                        demoCanvas.clear();
                        demoCanvas.add(fabricImg);
                        demoCanvas.setActiveObject(fabricImg);
                        imageLoaded = true;
                    };
                    img.src = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        }

        function initCrop() {
            if (!imageLoaded) {
                alert('Please upload an image first');
                return;
            }

            currentImage = demoCanvas.getActiveObject();
            if (currentImage && currentImage.type === 'image') {
                const imgElement = document.getElementById('cropperImage');
                imgElement.src = currentImage.toDataURL();
                document.getElementById('cropperModal').style.display = 'flex';

                // Destroy existing cropper if exists
                if (cropper) {
                    cropper.destroy();
                }

                // Initialize cropper with error handling
                try {
                    cropper = new Cropper(imgElement, {
                        aspectRatio: parseAspectRatio('1:1'),
                        viewMode: 1,
                        dragMode: 'move',
                        preview: '.img-preview',
                        zoomable: true,
                        rotatable: true,
                        scalable: false,
                        highlight: true,
                        cropBoxMovable: true,
                        cropBoxResizable: true,
                        toggleDragModeOnTouch: true
                    });
                } catch (error) {
                    console.error('Cropper initialization error:', error);
                    document.getElementById('errorMessage').textContent =
                        'Error initializing cropper. Please try again.';
                    closeCrop();
                }
            } else {
                alert('Please select an image object on the canvas first');
            }
        }

        function setAspectRatio(ratio) {
            if (cropper) {
                // Update cropper aspect ratio
                cropper.setAspectRatio(parseAspectRatio(ratio));

                // Update active button styling
                document.querySelectorAll('.ratio-button').forEach(btn => {
                    btn.classList.remove('active');

                    // Match button label with selected ratio
                    const selectedRatio = ratio;
                    const buttonRatio = btn.dataset.ratio;

                    if (buttonRatio === selectedRatio) {
                        btn.classList.add('active');
                    }
                });
            }
        }

        function applyCrop() {
            if (cropper && currentImage) {
                try {
                    cropper.getCroppedCanvas().toBlob((blob) => {
                        if (!blob) {
                            throw new Error('Failed to generate cropped image');
                        }

                        const reader = new FileReader();
                        reader.onload = function() {
                            fabric.Image.fromURL(reader.result, (img) => {
                                // Preserve position and transformation
                                img.set({
                                    left: currentImage.left,
                                    top: currentImage.top,
                                    angle: currentImage.angle,
                                    scaleX: currentImage.scaleX,
                                    scaleY: currentImage.scaleY
                                });

                                demoCanvas.remove(currentImage);
                                demoCanvas.add(img);
                                demoCanvas.setActiveObject(img);
                                demoCanvas.renderAll();
                            });
                        };
                        reader.readAsDataURL(blob);
                    });
                } catch (error) {
                    console.error('Crop application error:', error);
                    document.getElementById('errorMessage').textContent =
                        'Error applying crop. Please try again.';
                }
            }
            closeCrop();
        }

        function closeCrop() {
            if (cropper) {
                cropper.destroy();
                cropper = null;
            }
            document.getElementById('cropperModal').style.display = 'none';
        }

        // Event listeners
        document.getElementById('cropperModal')?.addEventListener('click', function(e) {
            if (e.target === this) {
                closeCrop();
            }
        });

        document.getElementById('applyCropBtn')?.addEventListener('click', applyCrop);
    </script>
</body>
</html>
