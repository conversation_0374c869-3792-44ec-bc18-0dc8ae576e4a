<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <script src="jquery-3.2.0.min.js"></script>
    <script src="fabric.js"></script>
</head>

<body>
    
            <div class="file-input-wrapper">
                 <button id="uploadBtn">
                <i class="fas fa-upload"></i> Upload Photo
            </button>
            <input type="file" id="fileInput" accept="image/*">
        </div>
            <canvas id="canvas"></canvas>
    <button id="crop">crop</button>
    <button id="startCrop" style="border:1px solid #000000;">startCrop</button>
    <br>
    <canvas style="visibility: hidden;" id="canvas_crop"></canvas>
</body>

<script>
    var canvas = new fabric.Canvas('canvas', {
        width: 500,
        height: 400,
        strokeWidth: 5,
        stroke: 'rgba(100,200,200,0.5)'
    });

    var el;
    var object, lastActive;
    var selection_object_left = 0;
    var selection_object_top = 0;
    var isCropping = false;


     function loadImageToCanvas(imageSrc) {
        canvas.clear();
        canvas.isDrawingMode = false;

        fabric.Image.fromURL(imageSrc, function(img) {
            const canvasWidth = canvas.getWidth();
            const canvasHeight = canvas.getHeight();
            const imgWidth = img.width;
            const imgHeight = img.height;

            const scaleX = (canvasWidth - 40) / imgWidth;
            const scaleY = (canvasHeight - 40) / imgHeight;
            const scale = Math.min(scaleX, scaleY, 1);

            img.set({
                scaleX: scale,
                scaleY: scale,
                selectable: true,
                left: (canvasWidth - imgWidth * scale) / 2,
                top: (canvasHeight - imgHeight * scale) / 2
            });

            canvas.add(img);
            canvas.renderAll();

            currentImage = img;
            originalImageData = {
                src: imageSrc,
                left: img.left,
                top: img.top,
                scaleX: img.scaleX,
                scaleY: img.scaleY
            };

            if (isCropping) {
                createCropRect();
            }

    $('#crop').on('click', function(event) {
        $('button#crop').hide();
        if (!isCropping) {
            alert("Please select the cropping area first。");
            return;
        }

        var left = el.left - object.left;
        var top = el.top - object.top;

        left *= 1;
        top *= 1;

        var width = el.width * 1;
        var height = el.height * 1;

        //Export the current frame to a new canvas and perform the crop, pausing the history recording during this time.
        cropImage(object, el.left, el.top, parseInt(el.scaleY * height), parseInt(width * el.scaleX));
        for (var i = 0; i < $("#layers li").length; i++) {
            canvas.item(i).selectable = true;
        }
        disabled = true;

        canvas.remove(object);
        canvas.remove(canvas.getActiveObject());
        lastActive = object;
        canvas.renderAll();

        isCropping = false;
    });

    $('#startCrop').on('click', function() {
        $("button#crop").show();
        canvas.remove(el);
        if (canvas.getActiveObject()) {
            if (canvas.getActiveObject().type == 'sprite') {
                alert("所选对象不可裁剪。");
                return;
            }
            object = canvas.getActiveObject();
            if (lastActive !== object) {
                console.log('different object');
            } else {
                console.log('same object');
            }
            if (lastActive && lastActive !== object) {
                lastActive.clipTo = null;
            }

            //Generate a rectangle of the same size as the element to be cropped to frame the cropping area
            el = new fabric.Rect({
                fill: 'rgba(0,0,0,0)',
                originX: 'left',
                originY: 'top',
                stroke: '#ccc',
                //strokeDashArray: [2, 2],
                strokWidth: 5,
                //opacity: 1,
                width: 1,
                height: 1,
                borderColor: '#36fd00',
                cornerColor: 'green',
                hasRotatingPoint: false,
                selectable: true
            });

            el.left = canvas.getActiveObject().left;
            selection_object_left = canvas.getActiveObject().left;
            selection_object_top = canvas.getActiveObject().top;
            el.top = canvas.getActiveObject().top;
            el.width = canvas.getActiveObject().width * canvas.getActiveObject().scaleX;
            el.height = canvas.getActiveObject().height * canvas.getActiveObject().scaleY;


            canvas.add(el);
            canvas.setActiveObject(el);
            for (var i = 0; i < $("#layers li").length; i++) {
                canvas.item(i).selectable = false;
            }
        } else {
            alert("Please select an element.");
        }
        isCropping = true;

    });

    function cropImage(png, left, top, height, width) {
//Put the image into a new canvas, and export a new image after cropping.
//If the user's selection box is larger than the original image, shrink the selection box to the original image size
if (top < png.top) {
            height = height - (png.top - top);
            top = png.top;
        }
        if (left < png.left) {
            width = width - (png.left - left);
            left = png.left;
        }
        if (top + height > png.top + png.height * png.scaleY)
            height = png.top + png.height * png.scaleY - top;
        if (left + width > png.left + png.width * png.scaleX)
            width = png.left + png.width * png.scaleX - left;

        var canvas_crop = new fabric.Canvas("canvas_crop");

        fabric.Image.fromURL(canvas.toDataURL('png'), function(img) {
            img.set('left', -left);
            img.set('top', -top);
            canvas_crop.add(img)
            canvas_crop.setHeight(height);
            canvas_crop.setWidth(width);
            canvas_crop.renderAll();
            fabric.Image.fromURL(canvas_crop.toDataURL('png'), function(croppedImg) {
                croppedImg.set('left', left);
                croppedImg.set('top', top);
                canvas.add(croppedImg).renderAll();
            });
        });

    }

    function getObjLeft(objWidth) {
        return canvas.getWidth() / 2 - objWidth / 2;
    }

    function getObjTop(objHeight) {
        return canvas.getHeight() / 2 - objHeight / 2;
    }
</script>

</html>
