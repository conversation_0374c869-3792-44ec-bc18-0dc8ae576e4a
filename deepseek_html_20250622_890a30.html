<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محرر متكامل للصور والرسم</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    <style>
        :root {
            --primary: #2a4365;
            --secondary: #4299e1;
            --accent: #48bb78;
            --light: #ebf8ff;
            --dark: #1a202c;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
            color: var(--dark);
            line-height: 1.6;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        header {
            background: var(--primary);
            color: white;
            padding: 20px;
            text-align: center;
            border-bottom: 4px solid var(--secondary);
        }
        
        h1 {
            font-size: 2.2rem;
            margin-bottom: 10px;
        }
        
        .main-toolbar {
            display: flex;
            background: var(--secondary);
            padding: 10px;
            justify-content: center;
            flex-wrap: wrap;
            gap: 15px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }
        
        .main-btn {
            background: white;
            color: var(--primary);
            border: none;
            padding: 12px 24px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
        }
        
        .main-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            background: var(--light);
        }
        
        .main-btn.active {
            background: var(--accent);
            color: white;
        }
        
        .sub-toolbar-container {
            background: var(--light);
            padding: 15px;
            overflow-x: auto;
            border-bottom: 2px solid #cbd5e0;
        }
        
        .sub-toolbar {
            display: flex;
            gap: 15px;
            padding: 10px;
            min-width: max-content;
        }
        
        .sub-toolbar.hidden {
            display: none;
        }
        
        .sub-btn {
            background: white;
            color: var(--dark);
            border: 1px solid #cbd5e0;
            padding: 8px 16px;
            border-radius: 30px;
            font-size: 0.95rem;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
        }
        
        .sub-btn:hover {
            background: var(--secondary);
            color: white;
            border-color: var(--secondary);
        }
        
        .canvas-container {
            padding: 20px;
            display: flex;
            justify-content: center;
        }
        
        #c {
            border: 2px solid #5d9eab;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            background: white;
        }
        
        .instructions {
            text-align: center;
            padding: 15px;
            background: #f7fafc;
            border-top: 1px solid #e2e8f0;
            font-size: 0.9rem;
            color: #4a5568;
        }
        
        .info-label {
            font-weight: bold;
            color: var(--primary);
            margin: 0 5px;
        }
        
        .controls-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
            background: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
        }
        
        label {
            display: flex;
            align-items: center;
            gap: 10px;
            white-space: nowrap;
        }
        
        input[type="range"], input[type="color"], select {
            cursor: pointer;
        }
        
        @media (max-width: 768px) {
            .main-toolbar {
                gap: 10px;
            }
            
            .main-btn {
                padding: 10px 15px;
                font-size: 0.95rem;
            }
            
            .sub-toolbar {
                flex-wrap: wrap;
                justify-content: center;
            }
        }
        
        .icon {
            font-size: 1.2rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>محرر الصور والرسم المتكامل</h1>
            <p>أداة متقدمة لتحرير الصور والرسم باستخدام Fabric.js</p>
        </header>
        
        <div class="main-toolbar">
            <button id="add-image-btn" class="main-btn active">
                <span class="icon">🖼️</span> إضافة صورة
            </button>
            <button id="drawing-mode-btn" class="main-btn">
                <span class="icon">✏️</span> أدوات الرسم
            </button>
            <button id="crop-mode-btn" class="main-btn">
                <span class="icon">✂️</span> أدوات القص
            </button>
        </div>
        
        <div class="sub-toolbar-container">
            <div id="sub-toolbar-add-image" class="sub-toolbar">
                <button id="upload-btn" class="sub-btn">تحميل صورة</button>
                <input type="file" id="file-input" multiple style="display: none;">
            </div>
            
            <div id="sub-toolbar-drawing" class="sub-toolbar hidden">
                <button id="drawing-toggle" class="sub-btn">إيقاف وضع الرسم</button>
                <button id="clear-canvas" class="sub-btn">مسح اللوحة</button>
                
                <div class="controls-group">
                    <label for="drawing-mode-selector">
                        <span class="info-label">أداة الرسم:</span>
                        <select id="drawing-mode-selector">
                            <option>قلم رصاص</option>
                            <option>دائرة</option>
                            <option>رش</option>
                            <option>نمط</option>
                            <option>خط أفقي</option>
                            <option>خط عمودي</option>
                            <option>مربع</option>
                            <option>معين</option>
                            <option>نسيج</option>
                        </select>
                    </label>
                </div>
                
                <div class="controls-group">
                    <label for="drawing-line-width">
                        <span class="info-label">سمك الخط:</span>
                        <span id="line-width-info">30</span>
                        <input type="range" min="1" max="150" id="drawing-line-width" value="30">
                    </label>
                </div>
                
                <div class="controls-group">
                    <label for="drawing-color">
                        <span class="info-label">لون الخط:</span>
                        <input type="color" id="drawing-color" value="#76cef4">
                    </label>
                </div>
                
                <div class="controls-group">
                    <label for="drawing-shadow-color">
                        <span class="info-label">لون الظل:</span>
                        <input type="color" id="drawing-shadow-color" value="#5a7896">
                    </label>
                </div>
                
                <div class="controls-group">
                    <label for="drawing-shadow-width">
                        <span class="info-label">حجم الظل:</span>
                        <span id="shadow-width-info">0</span>
                        <input type="range" min="0" max="50" id="drawing-shadow-width" value="0">
                    </label>
                </div>
                
                <div class="controls-group">
                    <label for="drawing-shadow-offset">
                        <span class="info-label">إزاحة الظل:</span>
                        <span id="shadow-offset-info">0</span>
                        <input type="range" min="0" max="50" id="drawing-shadow-offset" value="0">
                    </label>
                </div>
            </div>
            
            <div id="sub-toolbar-crop" class="sub-toolbar hidden">
                <div class="controls-group">
                    <p><span class="info-label">تعليمات القص:</span></p>
                    <p>انقر نقراً مزدوجاً على الصورة لبدء القص</p>
                    <p>حرك أو غيّر حجم منطقة القص كما تريد</p>
                    <p>ألغِ التحديد لإنهاء القص</p>
                </div>
            </div>
        </div>
        
        <div class="canvas-container">
            <canvas id="c" width="800" height="500"></canvas>
        </div>
        
        <div class="instructions">
            <p>استخدم شريط الأدوات العلوي لاختيار الوظيفة المطلوبة، ثم استخدم الأدوات الفرعية للتحكم</p>
        </div>
    </div>

    <script>
        // تهيئة لوحة الرسم
        const canvas = new fabric.Canvas('c', {
            isDrawingMode: false,
            selection: true
        });
        canvas.background = '#FFFFFF';
        canvas.renderAll();
        fabric.Object.prototype.transparentCorners = false;
        
        // عناصر DOM
        const $ = id => document.getElementById(id);
        
        // الأزرار الرئيسية
        const addImageBtn = $('add-image-btn');
        const drawingModeBtn = $('drawing-mode-btn');
        const cropModeBtn = $('crop-mode-btn');
        
        // الأشرطة الفرعية
        const addImageToolbar = $('sub-toolbar-add-image');
        const drawingToolbar = $('sub-toolbar-drawing');
        const cropToolbar = $('sub-toolbar-crop');
        
        // عناصر أدوات الرسم
        const drawingToggle = $('drawing-toggle');
        const clearCanvas = $('clear-canvas');
        const drawingColorEl = $('drawing-color');
        const drawingShadowColorEl = $('drawing-shadow-color');
        const drawingLineWidthEl = $('drawing-line-width');
        const drawingShadowWidth = $('drawing-shadow-width');
        const drawingShadowOffset = $('drawing-shadow-offset');
        const selectorEl = $('drawing-mode-selector');
        
        // إظهار/إخفاء الأشرطة الفرعية
        function showToolbar(toolbarToShow) {
            // إخفاء جميع الأشرطة الفرعية
            addImageToolbar.classList.add('hidden');
            drawingToolbar.classList.add('hidden');
            cropToolbar.classList.add('hidden');
            
            // إزالة النشاط من جميع الأزرار الرئيسية
            addImageBtn.classList.remove('active');
            drawingModeBtn.classList.remove('active');
            cropModeBtn.classList.remove('active');
            
            // إظهار الشريط المطلوب وإضافة النشاط للزر الرئيسي
            toolbarToShow.classList.remove('hidden');
            
            if (toolbarToShow === addImageToolbar) {
                addImageBtn.classList.add('active');
            } else if (toolbarToShow === drawingToolbar) {
                drawingModeBtn.classList.add('active');
            } else if (toolbarToShow === cropToolbar) {
                cropModeBtn.classList.add('active');
            }
        }
        
        // أحداث الأزرار الرئيسية
        addImageBtn.addEventListener('click', () => {
            showToolbar(addImageToolbar);
            canvas.isDrawingMode = false;
            drawingToggle.textContent = 'تفعيل وضع الرسم';
        });
        
        drawingModeBtn.addEventListener('click', () => {
            showToolbar(drawingToolbar);
            canvas.isDrawingMode = true;
            drawingToggle.textContent = 'إيقاف وضع الرسم';
        });
        
        cropModeBtn.addEventListener('click', () => {
            showToolbar(cropToolbar);
            canvas.isDrawingMode = false;
            drawingToggle.textContent = 'تفعيل وضع الرسم';
        });
        
        // تحميل الصور
        $('upload-btn').addEventListener('click', () => {
            $('file-input').click();
        });
        
        $('file-input').addEventListener('change', function(e) {
            const files = e.target.files;
            for (let i = 0; i < files.length; i++) {
                const reader = new FileReader();
                reader.onload = function(f) {
                    const data = f.target.result;
                    fabric.Image.fromURL(data, function(img) {
                        const canvasWidth = canvas.getWidth();
                        const canvasHeight = canvas.getHeight();
                        
                        const scaleFactor = Math.min(
                            canvasWidth / img.width,
                            canvasHeight / img.height
                        );
                        
                        img.scale(scaleFactor);
                        img.set({
                            left: (canvasWidth - img.getScaledWidth()) / 2,
                            top: (canvasHeight - img.getScaledHeight()) / 2,
                            selectable: true
                        });
                        
                        canvas.add(img);
                        canvas.renderAll();
                    });
                };
                reader.readAsDataURL(files[i]);
            }
        });
        
        // وظائف قص الصور
        canvas.on('mouse:dblclick', function(obj) {
            const target = obj.target ? obj.target : null;
            if (target && target.type === 'image') {
                prepareCrop(target);
            }
        });
        
        function prepareCrop(e) {
            const cropRect = new fabric.Rect({
                id: "crop-rect",
                top: e.top,
                left: e.left,
                angle: e.angle,
                width: e.getScaledWidth(),
                height: e.getScaledHeight(),
                stroke: "rgb(42, 67, 101)",
                strokeWidth: 2,
                strokeDashArray: [5, 5],
                fill: "rgba(255, 255, 255, 0.3)",
                globalCompositeOperation: "overlay",
                lockRotation: true,
            });
            
            const overlayRect = new fabric.Rect({
                id: "overlay-rect",
                top: e.top,
                left: e.left,
                angle: e.angle,
                width: e.getScaledWidth(),
                height: e.getScaledHeight(),
                selectable: false,
                fill: "rgba(0, 0, 0, 0.5)",
                lockRotation: true,
            });
            
            const s = e.cropX || 0,
                o = e.cropY || 0,
                c = e.width || e._originalElement.naturalWidth,
                l = e.height || e._originalElement.naturalHeight;
            
            e.set({
                cropX: null,
                cropY: null,
                left: e.left - s * e.scaleX,
                top: e.top - o * e.scaleY,
                width: e._originalElement.naturalWidth,
                height: e._originalElement.naturalHeight,
                dirty: true
            });
            
            cropRect.set({
                left: e.left + s * e.scaleX,
                top: e.top + o * e.scaleY,
                width: c * e.scaleX,
                height: l * e.scaleY,
                dirty: true
            });
            
            overlayRect.set({
                left: e.left,
                top: e.top,
                width: e.width * e.scaleX,
                height: e.height * e.scaleY,
                dirty: true
            });
            
            canvas.add(overlayRect);
            canvas.add(cropRect);
            canvas.discardActiveObject();
            canvas.setActiveObject(cropRect);
            canvas.renderAll();
            
            cropRect.on("moving", function() {
                if (cropRect.top < e.top || cropRect.left < e.left) {
                    cropRect.left = cropRect.left < e.left ? e.left : cropRect.left;
                    cropRect.top = cropRect.top < e.top ? e.top : cropRect.top;
                }
                
                if (cropRect.top + cropRect.getScaledHeight() > e.top + e.getScaledHeight() ||
                    cropRect.left + cropRect.getScaledWidth() > e.left + e.getScaledWidth()) {
                    cropRect.top = cropRect.top + cropRect.getScaledHeight() > e.top + e.getScaledHeight() ?
                        e.top + e.getScaledHeight() - cropRect.getScaledHeight() : cropRect.top;
                    cropRect.left = cropRect.left + cropRect.getScaledWidth() > e.left + e.getScaledWidth() ?
                        e.left + e.getScaledWidth() - cropRect.getScaledWidth() : cropRect.left;
                }
            });
            
            cropRect.on("deselected", function() {
                cropImage(cropRect, e);
                canvas.remove(overlayRect);
            });
        }
        
        function cropImage(cropRect, img) {
            canvas.remove(cropRect);
            
            const s = (cropRect.left - img.left) / img.scaleX,
                o = (cropRect.top - img.top) / img.scaleY,
                c = (cropRect.width * cropRect.scaleX) / img.scaleX,
                l = (cropRect.height * cropRect.scaleY) / img.scaleY;
            
            img.set({
                cropX: s,
                cropY: o,
                width: c,
                height: l,
                top: img.top + o * img.scaleY,
                left: img.left + s * img.scaleX,
                selectable: true,
                cropped: true
            });
            
            canvas.renderAll();
        }
        
        // وظائف أدوات الرسم
        function updateBrush() {
            const brush = canvas.freeDrawingBrush;
            brush.color = drawingColorEl.value;
            brush.width = parseInt(drawingLineWidthEl.value, 10) || 1;
            brush.shadow = new fabric.Shadow({
                blur: parseInt(drawingShadowWidth.value, 10) || 0,
                offsetX: parseInt(drawingShadowOffset.value, 10) || 0,
                offsetY: parseInt(drawingShadowOffset.value, 10) || 0,
                color: drawingShadowColorEl.value,
            });
        }
        
        drawingToggle.onclick = () => {
            canvas.isDrawingMode = !canvas.isDrawingMode;
            drawingToggle.textContent = canvas.isDrawingMode ? 
                'إيقاف وضع الرسم' : 'تفعيل وضع الرسم';
        };
        
        clearCanvas.onclick = () => canvas.clear();
        
        drawingColorEl.onchange = updateBrush;
        drawingShadowColorEl.onchange = updateBrush;
        
        drawingLineWidthEl.oninput = () => {
            $('line-width-info').textContent = drawingLineWidthEl.value;
            updateBrush();
        };
        
        drawingShadowWidth.oninput = () => {
            $('shadow-width-info').textContent = drawingShadowWidth.value;
            updateBrush();
        };
        
        drawingShadowOffset.oninput = () => {
            $('shadow-offset-info').textContent = drawingShadowOffset.value;
            updateBrush();
        };
        
        selectorEl.onchange = () => {
            const val = selectorEl.value;
            if (val === 'خط أفقي') {
                const brush = new fabric.PatternBrush(canvas);
                brush.getPatternSrc = () => {
                    const patternCanvas = document.createElement('canvas');
                    patternCanvas.width = patternCanvas.height = 10;
                    const ctx = patternCanvas.getContext('2d');
                    ctx.strokeStyle = brush.color;
                    ctx.lineWidth = 5;
                    ctx.beginPath();
                    ctx.moveTo(0, 5);
                    ctx.lineTo(10, 5);
                    ctx.stroke();
                    return patternCanvas;
                };
                canvas.freeDrawingBrush = brush;
            } else if (val === 'خط عمودي') {
                const brush = new fabric.PatternBrush(canvas);
                brush.getPatternSrc = () => {
                    const patternCanvas = document.createElement('canvas');
                    patternCanvas.width = patternCanvas.height = 10;
                    const ctx = patternCanvas.getContext('2d');
                    ctx.strokeStyle = brush.color;
                    ctx.lineWidth = 5;
                    ctx.beginPath();
                    ctx.moveTo(5, 0);
                    ctx.lineTo(5, 10);
                    ctx.stroke();
                    return patternCanvas;
                };
                canvas.freeDrawingBrush = brush;
            } else {
                let brushClass;
                switch(val) {
                    case 'قلم رصاص': brushClass = fabric.PencilBrush; break;
                    case 'دائرة': brushClass = fabric.CircleBrush; break;
                    case 'رش': brushClass = fabric.SprayBrush; break;
                    case 'نمط': brushClass = fabric.PatternBrush; break;
                    case 'مربع': brushClass = fabric.SquareBrush; break;
                    case 'معين': brushClass = fabric.DiamondBrush; break;
                    case 'نسيج': brushClass = fabric.TextureBrush; break;
                    default: brushClass = fabric.PencilBrush;
                }
                canvas.freeDrawingBrush = new brushClass(canvas);
            }
            updateBrush();
        };
        
        // تهيئة الفرشاة
        canvas.freeDrawingBrush = new fabric.PencilBrush(canvas);
        updateBrush();
        
        // إظهار شريط إضافة الصور افتراضيًا
        showToolbar(addImageToolbar);
    </script>
</body>
</html>