<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحرير الصور والنصوص - Fabric.js</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/4.5.0/fabric.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #1a2a6c, #b21f1f, #1a2a6c);
            color: #fff;
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        header {
            text-align: center;
            padding: 20px 0;
            margin-bottom: 30px;
            border-bottom: 2px solid rgba(255, 255, 255, 0.2);
        }
        
        header h1 {
            font-size: 2.8rem;
            margin-bottom: 10px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }
        
        header p {
            font-size: 1.2rem;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
            opacity: 0.9;
        }
        
        .app-wrapper {
            display: flex;
            gap: 30px;
            flex-wrap: wrap;
        }
        
        .canvas-container {
            flex: 1;
            min-width: 500px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }
        
        .tools-panel {
            width: 350px;
            background: rgba(20, 30, 48, 0.85);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }
        
        #canvas {
            border: 2px dashed rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            background: rgba(10, 15, 25, 0.5);
            cursor: default;
            width: 100%;
            height: 500px;
        }
        
        .panel-section {
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .panel-title {
            font-size: 1.4rem;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #4a9fe3;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .panel-title i {
            color: #4a9fe3;
        }
        
        .btn-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        button {
            background: linear-gradient(to right, #4a9fe3, #2a5a8c);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            flex: 1;
            min-width: 120px;
        }
        
        button:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            background: linear-gradient(to right, #5aafe3, #3a6a9c);
        }
        
        button:active {
            transform: translateY(1px);
        }
        
        .input-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        input[type="color"],
        input[type="range"],
        select {
            width: 100%;
            padding: 10px;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
        }
        
        input[type="range"] {
            -webkit-appearance: none;
            height: 8px;
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.1);
            outline: none;
        }
        
        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #4a9fe3;
            cursor: pointer;
        }
        
        .color-preview {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-top: 10px;
        }
        
        .color-box {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            border: 2px solid white;
        }
        
        .filter-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
        }
        
        .filter-btn {
            background: rgba(74, 159, 227, 0.2);
            padding: 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
            min-width: auto;
        }
        
        .filter-btn.active {
            background: linear-gradient(to right, #4a9fe3, #2a5a8c);
        }
        
        .filter-btn i {
            font-size: 1.5rem;
        }
        
        .footer-buttons {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }
        
        .btn-save {
            background: linear-gradient(to right, #27ae60, #219653);
        }
        
        .btn-reset {
            background: linear-gradient(to right, #e74c3c, #c0392b);
        }
        
        @media (max-width: 1100px) {
            .app-wrapper {
                flex-direction: column;
            }
            
            .tools-panel {
                width: 100%;
            }
        }
        
        @media (max-width: 600px) {
            .canvas-container {
                min-width: 100%;
            }
            
            .filter-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-image"></i> لوحة تحرير الصور والنصوص</h1>
            <p>قم بتحميل الصور، إضافة النصوص وتطبيق جميع المؤثرات البصرية باستخدام مكتبة Fabric.js المتقدمة</p>
        </header>
        
        <div class="app-wrapper">
            <div class="canvas-container">
                <canvas id="canvas" width="800" height="500"></canvas>
            </div>
            
            <div class="tools-panel">
                <div class="panel-section">
                    <h2 class="panel-title"><i class="fas fa-cloud-upload-alt"></i> تحميل الصور</h2>
                    <div class="btn-group">
                        <button id="loadImage"><i class="fas fa-image"></i> تحميل صورة</button>
                        <button id="addText"><i class="fas fa-font"></i> إضافة نص</button>
                    </div>
                    <input type="file" id="imageLoader" accept="image/*" style="display: none;">
                </div>
                
                <div class="panel-section">
                    <h2 class="panel-title"><i class="fas fa-sliders-h"></i> تحسينات الصورة</h2>
                    <div class="input-group">
                        <label>السطوع: <span id="brightnessValue">100</span>%</label>
                        <input type="range" id="brightness" min="0" max="200" value="100">
                    </div>
                    <div class="input-group">
                        <label>التباين: <span id="contrastValue">100</span>%</label>
                        <input type="range" id="contrast" min="0" max="200" value="100">
                    </div>
                </div>
                
                <div class="panel-section">
                    <h2 class="panel-title"><i class="fas fa-filter"></i> فلاتر الصور</h2>
                    <div class="filter-grid">
                        <button class="filter-btn" data-filter="none"><i class="fas fa-ban"></i> بدون</button>
                        <button class="filter-btn" data-filter="grayscale"><i class="fas fa-moon"></i> تدرج رمادي</button>
                        <button class="filter-btn" data-filter="sepia"><i class="fas fa-sun"></i> سيبيا</button>
                        <button class="filter-btn" data-filter="invert"><i class="fas fa-inverse"></i> عكس الألوان</button>
                        <button class="filter-btn" data-filter="vintage"><i class="fas fa-camera-retro"></i> كلاسيكي</button>
                        <button class="filter-btn" data-filter="blur"><i class="fas fa-wind"></i> ضبابي</button>
                        <button class="filter-btn" data-filter="emboss"><i class="fas fa-mountain"></i> بارز</button>
                        <button class="filter-btn" data-filter="noise"><i class="fas fa-stroopwafel"></i> تشويش</button>
                        <button class="filter-btn" data-filter="pixelate"><i class="fas fa-th-large"></i> تجزيء</button>
                    </div>
                </div>
                
                <div class="panel-section">
                    <h2 class="panel-title"><i class="fas fa-text-height"></i> خصائص النص</h2>
                    <div class="input-group">
                        <label>النص:</label>
                        <input type="text" id="textInput" placeholder="أدخل النص هنا" value="مثال لنص">
                    </div>
                    <div class="input-group">
                        <label>حجم الخط:</label>
                        <input type="range" id="fontSize" min="10" max="120" value="40">
                    </div>
                    <div class="input-group">
                        <label>لون الخط:</label>
                        <input type="color" id="textColor" value="#ffffff">
                    </div>
                    <div class="input-group">
                        <label>لون خلفية النص:</label>
                        <input type="color" id="textBackground" value="#4a9fe3">
                    </div>
                    <div class="input-group">
                        <label>ظل النص:</label>
                        <input type="color" id="textShadow" value="#000000">
                    </div>
                    <div class="input-group">
                        <label>عرض ظل النص:</label>
                        <input type="range" id="shadowBlur" min="0" max="20" value="5">
                    </div>
                </div>
                
                <div class="footer-buttons">
                    <button id="saveBtn" class="btn-save"><i class="fas fa-save"></i> حفظ التصميم</button>
                    <button id="resetBtn" class="btn-reset"><i class="fas fa-redo"></i> إعادة تعيين</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // تهيئة لوحة الرسم
        const canvas = new fabric.Canvas('canvas', {
            backgroundColor: 'rgba(10, 20, 30, 0.7)',
            preserveObjectStacking: true
        });
        
        // متغير لتتبع الكائن النشط
        let activeObject = null;
        
        // تحميل الصورة
        document.getElementById('loadImage').addEventListener('click', function() {
            document.getElementById('imageLoader').click();
        });
        
        document.getElementById('imageLoader').addEventListener('change', function(e) {
            const reader = new FileReader();
            
            reader.onload = function(event) {
                fabric.Image.fromURL(event.target.result, function(img) {
                    img.scaleToWidth(400);
                    img.set({
                        left: 200,
                        top: 150,
                        angle: 0,
                        padding: 10,
                        cornerSize: 20,
                        hasRotatingPoint: true
                    });
                    
                    canvas.add(img);
                    canvas.setActiveObject(img);
                    activeObject = img;
                    updateControls();
                });
            };
            
            reader.readAsDataURL(e.target.files[0]);
        });
        
        // إضافة نص
        document.getElementById('addText').addEventListener('click', function() {
            const text = new fabric.Textbox('نص جديد', {
                left: 250,
                top: 200,
                width: 300,
                fontSize: 40,
                fontFamily: 'Arial',
                fill: '#ffffff',
                textAlign: 'right',
                backgroundColor: '#4a9fe3',
                shadow: new fabric.Shadow({
                    color: '#000000',
                    blur: 5,
                    offsetX: 3,
                    offsetY: 3
                })
            });
            
            canvas.add(text);
            canvas.setActiveObject(text);
            activeObject = text;
            updateControls();
        });
        
        // تحديث عناصر التحكم بناءً على الكائن المحدد
        function updateControls() {
            if (!activeObject) return;
            
            if (activeObject.type === 'textbox') {
                document.getElementById('textInput').value = activeObject.text;
                document.getElementById('fontSize').value = activeObject.fontSize;
                document.getElementById('textColor').value = rgbToHex(activeObject.fill);
                document.getElementById('textBackground').value = activeObject.backgroundColor;
                
                if (activeObject.shadow) {
                    document.getElementById('textShadow').value = activeObject.shadow.color;
                    document.getElementById('shadowBlur').value = activeObject.shadow.blur;
                }
            }
        }
        
        // تحويل RGB إلى HEX
        function rgbToHex(rgb) {
            if (!rgb) return '#000000';
            
            if (rgb.indexOf('rgba') === 0) {
                rgb = rgb.replace('rgba(', '').replace(')', '').split(',');
                return "#" + ((1 << 24) + (parseInt(rgb[0]) << 16) + (parseInt(rgb[1]) << 8) + parseInt(rgb[2])).toString(16).slice(1);
            }
            
            return rgb;
        }
        
        // تحديث النص
        document.getElementById('textInput').addEventListener('input', function() {
            if (activeObject && activeObject.type === 'textbox') {
                activeObject.set('text', this.value);
                canvas.renderAll();
            }
        });
        
        // تحديث حجم الخط
        document.getElementById('fontSize').addEventListener('input', function() {
            if (activeObject && activeObject.type === 'textbox') {
                activeObject.set('fontSize', parseInt(this.value));
                canvas.renderAll();
            }
        });
        
        // تحديث لون النص
        document.getElementById('textColor').addEventListener('input', function() {
            if (activeObject && activeObject.type === 'textbox') {
                activeObject.set('fill', this.value);
                canvas.renderAll();
            }
        });
        
        // تحديث لون خلفية النص
        document.getElementById('textBackground').addEventListener('input', function() {
            if (activeObject && activeObject.type === 'textbox') {
                activeObject.set('backgroundColor', this.value);
                canvas.renderAll();
            }
        });
        
        // تحديث ظل النص
        document.getElementById('textShadow').addEventListener('input', function() {
            if (activeObject && activeObject.type === 'textbox') {
                if (!activeObject.shadow) {
                    activeObject.set('shadow', new fabric.Shadow({
                        color: this.value,
                        blur: 5,
                        offsetX: 3,
                        offsetY: 3
                    }));
                } else {
                    activeObject.shadow.color = this.value;
                }
                canvas.renderAll();
            }
        });
        
        // تحديث شدة ظل النص
        document.getElementById('shadowBlur').addEventListener('input', function() {
            if (activeObject && activeObject.type === 'textbox' && activeObject.shadow) {
                activeObject.shadow.blur = parseInt(this.value);
                canvas.renderAll();
            }
        });
        
        // تحديث السطوع
        document.getElementById('brightness').addEventListener('input', function() {
            document.getElementById('brightnessValue').textContent = this.value;
            if (activeObject && activeObject.type === 'image') {
                activeObject.filters = activeObject.filters || [];
                const filter = new fabric.Image.filters.Brightness({
                    brightness: (parseInt(this.value) - 100) / 100
                });
                
                applyFilter(filter, 'brightness');
            }
        });
        
        // تحديث التباين
        document.getElementById('contrast').addEventListener('input', function() {
            document.getElementById('contrastValue').textContent = this.value;
            if (activeObject && activeObject.type === 'image') {
                const filter = new fabric.Image.filters.Contrast({
                    contrast: (parseInt(this.value) - 100) / 100
                });
                
                applyFilter(filter, 'contrast');
            }
        });
        
        // تطبيق الفلاتر
        const filterButtons = document.querySelectorAll('.filter-btn');
        filterButtons.forEach(btn => {
            btn.addEventListener('click', function() {
                // إزالة التحديد من جميع الأزرار
                filterButtons.forEach(b => b.classList.remove('active'));
                
                // تحديد الزر الحالي
                this.classList.add('active');
                
                const filterType = this.getAttribute('data-filter');
                
                if (activeObject && activeObject.type === 'image') {
                    let filter;
                    
                    switch(filterType) {
                        case 'grayscale':
                            filter = new fabric.Image.filters.Grayscale();
                            break;
                        case 'sepia':
                            filter = new fabric.Image.filters.Sepia();
                            break;
                        case 'invert':
                            filter = new fabric.Image.filters.Invert();
                            break;
                        case 'vintage':
                            filter = new fabric.Image.filters.Vintage();
                            break;
                        case 'blur':
                            filter = new fabric.Image.filters.Blur({
                                blur: 0.2
                            });
                            break;
                        case 'emboss':
                            filter = new fabric.Image.filters.Convolute({
                                matrix: [ 1,   1,  1,
                                          1, 0.7, -1,
                                         -1,  -1, -1 ]
                            });
                            break;
                        case 'noise':
                            filter = new fabric.Image.filters.Noise({
                                noise: 200
                            });
                            break;
                        case 'pixelate':
                            filter = new fabric.Image.filters.Pixelate({
                                blocksize: 8
                            });
                            break;
                        case 'none':
                        default:
                            filter = null;
                    }
                    
                    applyFilter(filter, filterType);
                }
            });
        });
        
        // تطبيق الفلتر على الصورة
        function applyFilter(filter, filterName) {
            if (!activeObject || activeObject.type !== 'image') return;
            
            // إزالة الفلاتر القديمة من نفس النوع
            activeObject.filters = activeObject.filters.filter(f => 
                f.type !== filterName && f.type !== 'brightness' && f.type !== 'contrast'
            );
            
            // إضافة الفلتر الجديد إذا كان موجوداً
            if (filter) {
                activeObject.filters.push(filter);
            }
            
            // تطبيق الفلاتر المتبقية مع السطوع والتباين
            const brightness = parseFloat(document.getElementById('brightness').value);
            if (brightness !== 100) {
                activeObject.filters.push(new fabric.Image.filters.Brightness({
                    brightness: (brightness - 100) / 100
                }));
            }
            
            const contrast = parseFloat(document.getElementById('contrast').value);
            if (contrast !== 100) {
                activeObject.filters.push(new fabric.Image.filters.Contrast({
                    contrast: (contrast - 100) / 100
                }));
            }
            
            // تطبيق الفلاتر
            activeObject.applyFilters();
            canvas.renderAll();
        }
        
        // حفظ التصميم
        document.getElementById('saveBtn').addEventListener('click', function() {
            const link = document.createElement('a');
            link.download = 'تصميمي.png';
            link.href = canvas.toDataURL({
                format: 'png',
                quality: 0.95
            });
            link.click();
        });
        
        // إعادة تعيين
        document.getElementById('resetBtn').addEventListener('click', function() {
            canvas.clear();
            activeObject = null;
            
            // إعادة تعيين عناصر التحكم
            document.getElementById('brightness').value = 100;
            document.getElementById('contrast').value = 100;
            document.getElementById('brightnessValue').textContent = '100';
            document.getElementById('contrastValue').textContent = '100';
            
            // إزالة التحديد من أزرار الفلاتر
            filterButtons.forEach(btn => btn.classList.remove('active'));
            
            // إضافة صورة افتراضية
            fabric.Image.fromURL('https://picsum.photos/600/400', function(img) {
                img.scaleToWidth(400);
                img.set({
                    left: 200,
                    top: 150,
                    angle: 0,
                    padding: 10,
                    cornerSize: 20,
                    hasRotatingPoint: true
                });
                
                canvas.add(img);
                canvas.setActiveObject(img);
                activeObject = img;
            });
        });
        
        // تحديث الكائن النشط عند النقر
        canvas.on('selection:created', function(e) {
            activeObject = e.selected[0];
            updateControls();
        });
        
        canvas.on('selection:updated', function(e) {
            activeObject = e.selected[0];
            updateControls();
        });
        
        canvas.on('selection:cleared', function() {
            activeObject = null;
        });
        
        // إضافة صورة افتراضية عند التحميل
        window.onload = function() {
            fabric.Image.fromURL('https://picsum.photos/600/400', function(img) {
                img.scaleToWidth(400);
                img.set({
                    left: 200,
                    top: 150,
                    angle: 0,
                    padding: 10,
                    cornerSize: 20,
                    hasRotatingPoint: true
                });
                
                canvas.add(img);
                canvas.setActiveObject(img);
                activeObject = img;
            });
            
            // إضافة نص افتراضي
            const text = new fabric.Textbox('مرحباً بكم في لوحة التحرير', {
                left: 250,
                top: 50,
                width: 300,
                fontSize: 40,
                fontFamily: 'Arial',
                fill: '#ffffff',
                textAlign: 'right',
                backgroundColor: '#4a9fe3',
                shadow: new fabric.Shadow({
                    color: '#000000',
                    blur: 5,
                    offsetX: 3,
                    offsetY: 3
                })
            });
            
            canvas.add(text);
        };
    </script>
</body>
</html>