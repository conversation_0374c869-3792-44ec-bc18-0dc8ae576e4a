// src/app/api/test/route.js
// [!] هذا الملف يستخدم لاختبار الاتصال بالخادم

import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // إنشاء كائن تاريخ للتحقق من أن الخادم يعمل
    const now = new Date();
    
    // إعداد بيانات الاستجابة
    const responseData = {
      status: 'success',
      message: 'تم الاتصال بالخادم بنجاح',
      serverTime: now.toISOString(),
      environment: process.env.NODE_ENV || 'development'
    };
    
    // إرجاع استجابة JSON
    return NextResponse.json(responseData, { status: 200 });
  } catch (error) {
    console.error('Test endpoint error:', error);
    return NextResponse.json(
      { status: 'error', message: 'حدث خطأ في الخادم' },
      { status: 500 }
    );
  }
}
