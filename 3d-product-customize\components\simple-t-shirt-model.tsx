"use client"

import { useRef, useState, useEffect } from "react"
import { useFrame } from "@react-three/fiber"
import * as THREE from "three"

interface SimpleTShirtModelProps {
  position: [number, number, number]
  color: string
  metalness: number
  roughness: number
  envMapIntensity: number
  onPartClick: (event: any, partName: string) => void
  partTextures: Record<string, string>
  partColors: Record<string, string>
}

export function SimpleTShirtModel({
  position,
  color,
  metalness,
  roughness,
  envMapIntensity,
  onPartClick,
  partTextures,
  partColors,
}: SimpleTShirtModelProps) {
  const groupRef = useRef<THREE.Group>(null)

  // Material refs to update textures
  const bodyMaterialRef = useRef<THREE.MeshStandardMaterial>(null)
  const leftSleeveMaterialRef = useRef<THREE.MeshStandardMaterial>(null)
  const rightSleeveMaterialRef = useRef<THREE.MeshStandardMaterial>(null)
  const collarMaterialRef = useRef<THREE.MeshStandardMaterial>(null)
  const frontPrintMaterialRef = useRef<THREE.MeshStandardMaterial>(null)

  // Texture objects
  const [textures, setTextures] = useState<Record<string, THREE.Texture | null>>({
    body: null,
    leftSleeve: null,
    rightSleeve: null,
    collar: null,
    frontPrint: null,
  })

  // Simple animation
  useFrame((state) => {
    if (groupRef.current) {
      groupRef.current.rotation.y = Math.sin(state.clock.getElapsedTime() * 0.2) * 0.1
    }
  })

  // Create default front print texture
  const defaultFrontPrintTexture = (() => {
    const canvas = document.createElement("canvas")
    canvas.width = 256
    canvas.height = 256
    const ctx = canvas.getContext("2d")
    if (ctx) {
      ctx.fillStyle = "rgba(255, 255, 255, 0)"
      ctx.fillRect(0, 0, 256, 256)

      // Draw a circle
      ctx.fillStyle = "#ff6b6b"
      ctx.beginPath()
      ctx.arc(128, 80, 40, 0, Math.PI * 2)
      ctx.fill()

      // Add text
      ctx.fillStyle = "#5f27cd"
      ctx.font = "bold 24px Arial"
      ctx.textAlign = "center"
      ctx.fillText("نص العرض", 128, 160)

      // Add Arabic text
      ctx.font = "bold 18px Arial"
      ctx.fillText("نص العرض", 128, 190)
    }
    const texture = new THREE.CanvasTexture(canvas)
    texture.needsUpdate = true
    return texture
  })()

  // Load textures when partTextures change
  useEffect(() => {
    console.log("Loading textures from partTextures:", Object.keys(partTextures))

    const textureLoader = new THREE.TextureLoader()
    const newTextures: Record<string, THREE.Texture | null> = { ...textures }

    // Load each texture
    Object.entries(partTextures).forEach(([partName, textureUrl]) => {
      if (textureUrl) {
        console.log(`Loading texture for ${partName}`)

        // Dispose old texture if it exists
        if (newTextures[partName]) {
          newTextures[partName]?.dispose()
        }

        // Load new texture
        try {
          const texture = textureLoader.load(
            textureUrl,
            (loadedTexture) => {
              console.log(`Texture for ${partName} loaded successfully`)
              loadedTexture.wrapS = THREE.RepeatWrapping
              loadedTexture.wrapT = THREE.RepeatWrapping
              loadedTexture.needsUpdate = true

              // Update material directly
              updateMaterialTexture(partName, loadedTexture)
            },
            undefined,
            (error) => {
              console.error(`Error loading texture for ${partName}:`, error)
            },
          )

          newTextures[partName] = texture
        } catch (error) {
          console.error(`Error creating texture for ${partName}:`, error)
        }
      } else {
        newTextures[partName] = null
      }
    })

    setTextures(newTextures)

    // Cleanup function
    return () => {
      Object.values(newTextures).forEach((texture) => {
        if (texture) texture.dispose()
      })
    }
  }, [partTextures])

  // Update material textures directly and handle color properly
  const updateMaterialTexture = (partName: string, texture: THREE.Texture) => {
    console.log(`Updating material texture for ${partName}`)

    // Function to update material with texture
    const updateMaterial = (material: THREE.MeshStandardMaterial | null) => {
      if (material) {
        material.map = texture
        // When a texture is applied, set the color to white to show true texture colors
        material.color.set("#ffffff")
        material.needsUpdate = true
      }
    }

    switch (partName) {
      case "body":
        updateMaterial(bodyMaterialRef.current)
        break
      case "leftSleeve":
        updateMaterial(leftSleeveMaterialRef.current)
        break
      case "rightSleeve":
        updateMaterial(rightSleeveMaterialRef.current)
        break
      case "collar":
        updateMaterial(collarMaterialRef.current)
        break
      case "frontPrint":
        updateMaterial(frontPrintMaterialRef.current)
        break
    }
  }

  // Update colors when partColors change
  useEffect(() => {
    // Function to update material color based on whether a texture is applied
    const updateMaterialColor = (
      material: THREE.MeshStandardMaterial | null,
      partName: string,
      partColor: string | undefined,
    ) => {
      if (material) {
        // Only apply color if no texture is applied or if it's explicitly set
        if (!material.map || partColor) {
          material.color.set(partColor || color)
        } else {
          // If texture is applied and no specific color is set, use white to show true texture colors
          material.color.set("#ffffff")
        }
        material.needsUpdate = true
      }
    }

    // Update each part's material color
    updateMaterialColor(bodyMaterialRef.current, "body", partColors.body)
    updateMaterialColor(leftSleeveMaterialRef.current, "leftSleeve", partColors.leftSleeve)
    updateMaterialColor(rightSleeveMaterialRef.current, "rightSleeve", partColors.rightSleeve)
    updateMaterialColor(collarMaterialRef.current, "collar", partColors.collar)

    // Front print is handled differently as it's primarily for textures
    if (frontPrintMaterialRef.current) {
      frontPrintMaterialRef.current.color.set("#ffffff") // Always white for front print
      frontPrintMaterialRef.current.needsUpdate = true
    }
  }, [partColors, color, partTextures])

  // Handle double click on parts
  const handlePartClick = (event: any, partName: string) => {
    event.stopPropagation()
    onPartClick(event, partName)
  }

  return (
    <group ref={groupRef} position={position}>
      {/* T-shirt body */}
      <mesh castShadow receiveShadow position={[0, 0, 0]} onDoubleClick={(e) => handlePartClick(e, "body")}>
        <cylinderGeometry args={[0.6, 0.8, 1.2, 32]} />
        <meshStandardMaterial
          ref={bodyMaterialRef}
          color={partTextures.body ? "#ffffff" : partColors.body || color}
          metalness={metalness}
          roughness={roughness}
          envMapIntensity={envMapIntensity}
        />
      </mesh>

      {/* Left sleeve */}
      <mesh
        castShadow
        receiveShadow
        position={[-0.7, 0, 0]}
        rotation={[0, 0, Math.PI * 0.15]}
        onDoubleClick={(e) => handlePartClick(e, "leftSleeve")}
      >
        <cylinderGeometry args={[0.2, 0.25, 0.6, 16]} />
        <meshStandardMaterial
          ref={leftSleeveMaterialRef}
          color={partTextures.leftSleeve ? "#ffffff" : partColors.leftSleeve || color}
          metalness={metalness}
          roughness={roughness}
          envMapIntensity={envMapIntensity}
        />
      </mesh>

      {/* Right sleeve */}
      <mesh
        castShadow
        receiveShadow
        position={[0.7, 0, 0]}
        rotation={[0, 0, -Math.PI * 0.15]}
        onDoubleClick={(e) => handlePartClick(e, "rightSleeve")}
      >
        <cylinderGeometry args={[0.2, 0.25, 0.6, 16]} />
        <meshStandardMaterial
          ref={rightSleeveMaterialRef}
          color={partTextures.rightSleeve ? "#ffffff" : partColors.rightSleeve || color}
          metalness={metalness}
          roughness={roughness}
          envMapIntensity={envMapIntensity}
        />
      </mesh>

      {/* Collar */}
      <mesh castShadow receiveShadow position={[0, 0.6, 0]} onDoubleClick={(e) => handlePartClick(e, "collar")}>
        <torusGeometry args={[0.3, 0.1, 16, 32, Math.PI * 2]} />
        <meshStandardMaterial
          ref={collarMaterialRef}
          color={partTextures.collar ? "#ffffff" : partColors.collar || color}
          metalness={metalness}
          roughness={roughness}
          envMapIntensity={envMapIntensity}
        />
      </mesh>

      {/* Front print */}
      <mesh position={[0, 0.1, 0.41]} rotation={[0, 0, 0]} onDoubleClick={(e) => handlePartClick(e, "frontPrint")}>
        <planeGeometry args={[0.8, 0.8]} />
        <meshStandardMaterial
          ref={frontPrintMaterialRef}
          transparent
          opacity={0.9}
          metalness={0}
          roughness={0.5}
          map={defaultFrontPrintTexture}
          color="#ffffff" // Always white for front print to show true texture colors
        />
      </mesh>
    </group>
  )
}
