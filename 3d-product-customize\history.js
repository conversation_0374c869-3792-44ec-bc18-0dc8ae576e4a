/**
 * History - Manages undo/redo functionality for the editor
 */
class History {
  constructor(editorCore) {
    this.editor = editorCore
    this.states = []
    this.currentStateIndex = -1
    this.maxStates = 30 // Maximum number of states to store

    // Initialize with current state
    this.saveState()
  }

  /**
   * Save current canvas state
   */
  saveState() {
    // Get current canvas state as JSON
    const json = this.editor.canvas.toJSON(["id", "customProperty"])
    const state = JSON.stringify(json)

    // If we're not at the end of the states array, remove future states
    if (this.currentStateIndex < this.states.length - 1) {
      this.states = this.states.slice(0, this.currentStateIndex + 1)
    }

    // Add new state
    this.states.push(state)
    this.currentStateIndex = this.states.length - 1

    // Limit the number of states
    if (this.states.length > this.maxStates) {
      this.states.shift()
      this.currentStateIndex--
    }

    // Update UI if needed
    this.updateUI()
  }

  /**
   * Undo the last action
   */
  undo() {
    if (this.currentStateIndex <= 0) return

    this.currentStateIndex--
    this.loadState(this.currentStateIndex)

    // Update UI
    this.updateUI()
  }

  /**
   * Redo the last undone action
   */
  redo() {
    if (this.currentStateIndex >= this.states.length - 1) return

    this.currentStateIndex++
    this.loadState(this.currentStateIndex)

    // Update UI
    this.updateUI()
  }

  /**
   * Load state at the specified index
   */
  loadState(index) {
    if (index < 0 || index >= this.states.length) return

    const state = JSON.parse(this.states[index])

    this.editor.canvas.loadFromJSON(state, () => {
      this.editor.canvas.renderAll()
      this.editor.threeDSync.syncWithThreeJs()
    })
  }

  /**
   * Update UI elements related to history
   */
  updateUI() {
    // This would update UI elements like undo/redo buttons
    // For example, disable undo button if at beginning of history
    const canUndo = this.currentStateIndex > 0
    const canRedo = this.currentStateIndex < this.states.length - 1

    // Dispatch custom event for UI to listen to
    const event = new CustomEvent("historyChange", {
      detail: { canUndo, canRedo },
    })
    document.dispatchEvent(event)
  }

  /**
   * Clear all history
   */
  clearHistory() {
    const currentState = this.states[this.currentStateIndex]
    this.states = [currentState]
    this.currentStateIndex = 0
    this.updateUI()
  }
}
