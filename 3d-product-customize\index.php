<?php
// Include authentication check
require_once 'auth_check.php';

// Check if user is logged in
if (!isLoggedIn()) {
    // Not logged in, redirect to login page
    redirectToLogin();
}

// Check if user has an active subscription
$userId = $_SESSION['user_id'];
if (!hasActiveSubscription($conn, $userId)) {
    // No active subscription, redirect to subscription page
    $_SESSION['subscription_status'] = 'none';
    redirectToSubscription();
}

// User is logged in and has an active subscription, redirect to editor
header("Location: editor.php");
exit;
?>
