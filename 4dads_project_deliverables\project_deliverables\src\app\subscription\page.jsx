// src/app/subscription/page.jsx
// [!] هذه الصفحة تعرض خطط الاشتراك وتتيح للمستخدم الاشتراك باستخدام PayPal

import React from 'react';
import SubscriptionPlans from '../../components/subscription/SubscriptionPlans';
import PayPalSubscription from '../../components/subscription/PayPalSubscription';
import { useState } from 'react';

export const metadata = {
  title: 'خطط الاشتراك - 4dads.pro',
  description: 'اختر خطة الاشتراك المناسبة لك في موقع 4dads.pro',
};

export default function SubscriptionPage() {
  const [selectedPlan, setSelectedPlan] = useState(null);
  
  const handlePlanSelect = (plan) => {
    setSelectedPlan(plan);
    // التمرير إلى قسم الدفع
    document.getElementById('payment-section').scrollIntoView({ behavior: 'smooth' });
  };
  
  const handlePaymentSuccess = (data) => {
    // يمكن إضافة توجيه إلى صفحة تأكيد أو لوحة التحكم
    console.log('Payment successful:', data);
  };
  
  const handlePaymentError = (error) => {
    console.error('Payment error:', error);
  };
  
  const handlePaymentCancel = () => {
    console.log('Payment cancelled');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* رأس الصفحة */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <h1 className="text-3xl font-bold text-gray-900">خطط الاشتراك</h1>
        </div>
      </div>
      
      {/* محتوى الصفحة */}
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {/* عرض خطط الاشتراك */}
        <SubscriptionPlans onSelectPlan={handlePlanSelect} />
        
        {/* قسم الدفع */}
        <div id="payment-section" className="mt-10">
          {selectedPlan ? (
            <div>
              <h2 className="text-2xl font-bold text-center text-gray-900 mb-6">إتمام عملية الاشتراك</h2>
              <PayPalSubscription 
                plan={selectedPlan}
                onSuccess={handlePaymentSuccess}
                onError={handlePaymentError}
                onCancel={handlePaymentCancel}
              />
            </div>
          ) : (
            <div className="text-center py-10">
              <p className="text-gray-500">يرجى اختيار خطة اشتراك من الأعلى للمتابعة</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
