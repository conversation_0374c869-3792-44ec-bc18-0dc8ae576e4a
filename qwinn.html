<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Fabric.js Full Editor</title>

  <!-- External Libraries -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/hammer.js/2.0.8/hammer.min.js"></script>

  <style>
    * { box-sizing: border-box; }
    html, body {
      margin: 0;
      padding: 0;
      height: 100%;
      font-family: sans-serif;
      background: #f4f4f4;
    }

    .container {
      display: flex;
      flex-direction: column;
      height: 100vh;
      overflow: hidden;
    }

    .toolbar-top {
      display: flex;
      justify-content: space-around;
      align-items: center;
      background: #222;
      color: white;
      padding: 0.5em;
      flex-shrink: 0;
    }

    .toolbar-bottom {
      display: flex;
      flex-wrap: wrap;
      justify-content: start;
      align-items: center;
      background: #eee;
      padding: 0.5em;
      gap: 10px;
      flex-shrink: 0;
    }

    .toolbar-button {
      cursor: pointer;
      padding: 8px 12px;
      border-radius: 6px;
      background: #fff;
      border: 1px solid #ccc;
      font-size: 14px;
      transition: background 0.2s;
    }

    .toolbar-button:hover {
      background: #ddd;
    }

    .canvas-container {
      flex: 1;
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: auto;
    }

    canvas {
      max-width: 100%;
      max-height: 100%;
      touch-action: none;
    }

    /* Modal styles */
    .modal {
      display: none;
      position: fixed;
      top: 0; left: 0;
      width: 100%; height: 100%;
      background: rgba(0,0,0,0.7);
      z-index: 999;
    }

    .modal-content {
      background: white;
      padding: 20px;
      margin: 50px auto;
      width: 90%;
      max-width: 800px;
      max-height: 80vh;
      overflow-y: auto;
      border-radius: 10px;
    }

    .thumbnail {
      width: 64px;
      height: 64px;
      margin: 5px;
      cursor: pointer;
      border: 2px solid transparent;
    }

    .thumbnail:hover {
      border-color: #007bff;
    }

    [data-tooltip] {
      position: relative;
      cursor: help;
    }

    [data-tooltip]::after {
      content: attr(data-tooltip);
      visibility: hidden;
      width: 120px;
      background: black;
      color: white;
      text-align: center;
      border-radius: 4px;
      padding: 4px 8px;
      position: absolute;
      z-index: 1;
      bottom: 125%;
      left: 50%;
      margin-left: -60px;
      opacity: 0;
      transition: opacity 0.3s;
    }

    [data-tooltip]:hover::after {
      visibility: visible;
      opacity: 1;
    }

    input[type="range"], input[type="color"] {
      width: 100px;
    }

    select, input[type="text"] {
      padding: 4px 8px;
      font-size: 14px;
    }

    label {
      margin-right: 5px;
    }

    .flex-row {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .context-menu {
      position: absolute;
      background: white;
      border: 1px solid #ccc;
      border-radius: 4px;
      padding: 5px 0;
      z-index: 9999;
      display: none;
      box-shadow: 0 2px 6px rgba(0,0,0,0.2);
    }

    .context-menu button {
      display: block;
      width: 100%;
      padding: 6px 12px;
      text-align: left;
      background: none;
      border: none;
      cursor: pointer;
    }

    .context-menu button:hover {
      background: #eee;
    }
  </style>
</head>
<body>

<div class="container">
  <!-- Top Toolbar -->
  <div class="toolbar-top" id="topToolbar">
    <button class="toolbar-button data-tooltip" data-category="draw" data-tooltip="Draw">🖌️</button>
    <button class="toolbar-button data-tooltip" data-category="shapes" data-tooltip="Shapes">🟥</button>
    <button class="toolbar-button data-tooltip" data-category="text" data-tooltip="Text">A</button>
    <button class="toolbar-button data-tooltip" data-category="images" data-tooltip="Images">🖼️</button>
    <button class="toolbar-button data-tooltip" data-category="crop" data-tooltip="Crop">✂️</button>
    <button class="toolbar-button data-tooltip" data-category="effects" data-tooltip="Effects">🎨</button>
    <button class="toolbar-button data-tooltip" data-category="library" data-tooltip="PNG Library">📦</button>
    <button class="toolbar-button data-tooltip" data-category="view" data-tooltip="View">🔍</button>
    <button class="toolbar-button data-tooltip" data-category="export" data-tooltip="Export">💾</button>
  </div>

  <!-- Bottom Sub-Toolbar -->
  <div class="toolbar-bottom" id="subToolbar"></div>

  <!-- Main Canvas -->
  <div class="canvas-container" id="canvasWrapper">
    <canvas id="canvas" width="800" height="600"></canvas>
  </div>
</div>

<!-- Context Menu -->
<div class="context-menu" id="contextMenu">
  <button onclick="duplicate()">Duplicate</button>
  <button onclick="remove()">Delete</button>
  <button onclick="sendToBack()">Send to Back</button>
  <button onclick="bringToFront()">Bring to Front</button>
</div>

<!-- Modal for PNG Library -->
<div class="modal" id="pngModal">
  <div class="modal-content">
    <h3>Select a PNG Icon</h3>
    <div style="display: flex; flex-wrap: wrap;">
      <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/0/0b/Lightbulb_icon.svg/120px-Lightbulb_icon.svg.png"
           class="thumbnail" onclick="insertPNG(this.src)" />
      <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/0/0d/Fire_icon.svg/120px-Fire_icon.svg.png"
           class="thumbnail" onclick="insertPNG(this.src)" />
      <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/2/2e/Add_icon.svg/120px-Add_icon.svg.png"
           class="thumbnail" onclick="insertPNG(this.src)" />
    </div>
    <button onclick="closeModal()">Close</button>
  </div>
</div>

<script>
  const canvas = new fabric.Canvas('canvas', {
    isDrawingMode: false,
    preserveObjectStacking: true,
    selection: true,
    backgroundColor: '#ffffff'
  });

  let drawingMode = 'pencil';
  let undoStack = [];
  let redoStack = [];

  function saveState() {
    undoStack.push(JSON.stringify(canvas));
    redoStack = [];
  }

  function undo() {
    if (undoStack.length > 1) {
      redoStack.push(undoStack.pop());
      canvas.loadFromJSON(undoStack[undoStack.length - 1], () => {
        canvas.renderAll();
      });
    }
  }

  function redo() {
    if (redoStack.length > 0) {
      undoStack.push(redoStack.pop());
      canvas.loadFromJSON(undoStack[undoStack.length - 1], () => {
        canvas.renderAll();
      });
    }
  }

  function addShape(type) {
    let shape;
    switch (type) {
      case 'rect':
        shape = new fabric.Rect({ width: 100, height: 100, fill: 'red', left: 100, top: 100 });
        break;
      case 'circle':
        shape = new fabric.Circle({ radius: 50, fill: 'blue', left: 150, top: 150 });
        break;
      case 'triangle':
        shape = new fabric.Triangle({ width: 100, height: 100, fill: 'green', left: 200, top: 200 });
        break;
    }
    canvas.add(shape);
    saveState();
  }

  function addText() {
    const defaultText = prompt("Enter your text:");
    if (!defaultText) return;

    const text = new fabric.Textbox(defaultText, {
      left: 100,
      top: 100,
      fontSize: 24,
      fontFamily: 'Arial',
      editable: true,
      hasControls: true,
      borderColor: '#666',
      cornerColor: '#333'
    });
    canvas.add(text);
    saveState();
  }

  // Image Upload
  const fileInput = document.createElement('input');
  fileInput.type = 'file';
  fileInput.multiple = true;
  fileInput.accept = 'image/*';
  fileInput.style.display = 'none';
  fileInput.addEventListener('change', e => {
    Array.from(e.target.files).forEach(file => {
      const reader = new FileReader();
      reader.onload = function (f) {
        fabric.Image.fromURL(f.target.result, img => {
          img.scaleToWidth(100);
          img.set({ left: Math.random() * 300, top: Math.random() * 300 });
          canvas.add(img);
          saveState();
        });
      };
      reader.readAsDataURL(file);
    });
  });
  document.body.appendChild(fileInput);

  function uploadImages() {
    fileInput.click();
  }

  // Crop Tool
  let cropRect = null;
  let currentRatio = 1;
  const ratios = {
    'Instagram': 1, 'YouTube': 16 / 9, 'TikTok': 9 / 16,
    'A4': 210 / 297, 'A5': 148 / 210, 'A6': 105 / 148
  };

  function enableCropTool(ratio = 1) {
    currentRatio = ratio;
    canvas.isDrawingMode = false;
    canvas.selection = false;
    canvas.forEachObject(obj => obj.selectable = false);
    canvas.on('mouse:down', startCrop);
  }

  function startCrop(o) {
    const pointer = canvas.getPointer(o.e);
    cropRect = new fabric.Rect({
      left: pointer.x, top: pointer.y,
      width: 0, height: 0,
      fill: 'rgba(0,0,0,0)',
      stroke: 'red',
      strokeWidth: 2,
      selectable: false
    });
    canvas.add(cropRect);
    canvas.on('mouse:move', drawCrop);
    canvas.on('mouse:up', endCrop);
  }

  function drawCrop(o) {
    const pointer = canvas.getPointer(o.e);
    let width = pointer.x - cropRect.left;
    let height = pointer.y - cropRect.top;
    if (currentRatio) {
      height = width / currentRatio;
    }
    cropRect.set({ width, height });
    canvas.renderAll();
  }

  function endCrop() {
    canvas.off('mouse:move');
    canvas.off('mouse:up');
    const img = canvas.getActiveObject();
    if (img && img.type === 'image' && cropRect) {
      const scaleX = img.scaleX || 1;
      const scaleY = img.scaleY || 1;
      const x = (cropRect.left - img.left) / scaleX;
      const y = (cropRect.top - img.top) / scaleY;
      const w = cropRect.width / scaleX;
      const h = cropRect.height / scaleY;

      const croppedImg = new Image();
      const tempCanvas = document.createElement('canvas');
      const ctx = tempCanvas.getContext('2d');

      croppedImg.onload = function () {
        tempCanvas.width = w;
        tempCanvas.height = h;
        ctx.drawImage(croppedImg, x, y, w, h, 0, 0, w, h);
        fabric.Image.fromURL(tempCanvas.toDataURL(), imgObj => {
          imgObj.set({ left: img.left + x * scaleX, top: img.top + y * scaleY });
          canvas.remove(img);
          canvas.add(imgObj);
          canvas.remove(cropRect);
          cropRect = null;
          saveState();
        });
      };
      croppedImg.src = img.getSrc();
    }
  }

  function openModal() {
    document.getElementById('pngModal').style.display = 'block';
  }

  function closeModal() {
    document.getElementById('pngModal').style.display = 'none';
  }

  function insertPNG(url) {
    fabric.Image.fromURL(url, img => {
      img.scaleToWidth(100);
      img.set({ left: 100, top: 100 });
      canvas.add(img);
      saveState();
    });
    closeModal();
  }

  function applyFilter(filterType, value) {
    const obj = canvas.getActiveObject();
    if (obj && obj.filters) {
      let filter;
      switch (filterType) {
        case 'brightness': filter = new fabric.Image.filters.Brightness({ brightness: value }); break;
        case 'contrast': filter = new fabric.Image.filters.Contrast({ contrast: value }); break;
        case 'grayscale': filter = new fabric.Image.filters.Grayscale(); break;
        case 'invert': filter = new fabric.Image.filters.Invert(); break;
        case 'blur': filter = new fabric.Image.filters.Blur({ blur: value }); break;
        case 'sharpen': filter = new fabric.Image.filters.Convolute({ matrix: [0, -1, 0, -1, 5, -1, 0, -1, 0] }); break;
        case 'saturation': filter = new fabric.Image.filters.Saturation({ saturation: value }); break;
        case 'sepia': filter = new fabric.Image.filters.Sepia(); break;
      }

      obj.filters[obj.filters.findIndex(f => f.type === filterType.toUpperCase()) || obj.filters.length] = filter;
      obj.applyFilters();
      canvas.renderAll();
      saveState();
    }
  }

  function setStroke(color, width) {
    const obj = canvas.getActiveObject();
    if (obj) {
      obj.set({ stroke: color, strokeWidth: width });
      canvas.renderAll();
      saveState();
    }
  }

  function setShadow(color, blur, offsetX, offsetY, opacity) {
    const obj = canvas.getActiveObject();
    if (obj) {
      obj.shadow = new fabric.Shadow({
        color: color,
        blur: blur,
        offsetX: offsetX,
        offsetY: offsetY,
        opacity: opacity
      });
      canvas.renderAll();
      saveState();
    }
  }

  function updateTextProperty(prop, value) {
    const obj = canvas.getActiveObject();
    if (obj && obj.type.startsWith('text')) {
      obj.set(prop, value);
      canvas.renderAll();
      saveState();
    }
  }

  function setDrawingMode(mode) {
    drawingMode = mode;
    canvas.freeDrawingBrush = new fabric[mode.charAt(0).toUpperCase() + mode.slice(1) + 'Brush'](canvas);
    canvas.isDrawingMode = true;
  }

  // Object manipulation
  function duplicate() {
    const active = canvas.getActiveObject();
    if (active) {
      active.clone(cloned => {
        cloned.set({ left: active.left + 10, top: active.top + 10 });
        canvas.add(cloned);
        canvas.setActiveObject(cloned);
        canvas.renderAll();
        saveState();
      });
    }
  }

  function remove() {
    const active = canvas.getActiveObject();
    if (active) {
      canvas.remove(active);
      canvas.discardActiveObject();
      canvas.renderAll();
      saveState();
    }
  }

  function bringToFront() {
    const active = canvas.getActiveObject();
    if (active) {
      canvas.bringToFront(active);
      canvas.renderAll();
      saveState();
    }
  }

  function sendToBack() {
    const active = canvas.getActiveObject();
    if (active) {
      canvas.sendToBack(active);
      canvas.renderAll();
      saveState();
    }
  }

  function alignLeft() {
    const active = canvas.getActiveObject();
    if (active) {
      active.set({ left: 0 }).setCoords();
      canvas.renderAll();
      saveState();
    }
  }

  function alignCenter() {
    const active = canvas.getActiveObject();
    if (active) {
      active.set({ left: canvas.width / 2 - active.width / 2 }).setCoords();
      canvas.renderAll();
      saveState();
    }
  }

  function alignRight() {
    const active = canvas.getActiveObject();
    if (active) {
      active.set({ left: canvas.width - active.width }).setCoords();
      canvas.renderAll();
      saveState();
    }
  }

  function lockToggle() {
    const active = canvas.getActiveObject();
    if (active) {
      active.set('lockMovementX', !active.lockMovementX);
      active.set('lockMovementY', !active.lockMovementY);
      canvas.renderAll();
      saveState();
    }
  }

  function groupObjects() {
    const activeGroup = canvas.getActiveObject();
    if (activeGroup && activeGroup.type === 'activeSelection') {
      activeGroup.toGroup();
      canvas.discardActiveObject();
      canvas.renderAll();
      saveState();
    }
  }

  function ungroupObjects() {
    const activeGroup = canvas.getActiveObject();
    if (activeGroup && activeGroup.type === 'group') {
      activeGroup.toActiveSelection();
      canvas.discardActiveObject();
      canvas.renderAll();
      saveState();
    }
  }

  // Right-click context menu
  canvas.on('mouse:down', function(opt) {
    const evt = opt.e;
    if (evt.button === 2) {
      const menu = document.getElementById('contextMenu');
      menu.style.left = `${evt.clientX}px`;
      menu.style.top = `${evt.clientY}px`;
      menu.style.display = 'block';
      evt.preventDefault();
    }
  });

  window.addEventListener('click', () => {
    document.getElementById('contextMenu').style.display = 'none';
  });

  // Hammer.js for touch gestures
  const hammerCanvas = new Hammer(document.getElementById('canvas'));
  hammerCanvas.get('pinch').set({ enable: true });
  hammerCanvas.on('pinch', ev => {
    canvas.setZoom(canvas.getZoom() * ev.scale);
    ev.preventDefault();
  });

  hammerCanvas.on('pan', ev => {
    const vpt = canvas.viewportTransform;
    vpt[4] += ev.deltaX;
    vpt[5] += ev.deltaY;
    canvas.setViewportTransform(vpt);
  });

  // Handle toolbar logic
  const subToolbar = document.getElementById('subToolbar');
  const topToolbar = document.getElementById('topToolbar');

  const tools = {
    draw: `
      <button class="toolbar-button" onclick="setDrawingMode('pencil')">Pencil</button>
      <button class="toolbar-button" onclick="setDrawingMode('brush')">Brush</button>
      <button class="toolbar-button" onclick="setDrawingMode('spray')">Spray</button>
      <button class="toolbar-button" onclick="canvas.isDrawingMode = false">Stop Drawing</button>
    `,
    shapes: `
      <button class="toolbar-button" onclick="addShape('rect')">Rectangle</button>
      <button class="toolbar-button" onclick="addShape('circle')">Circle</button>
      <button class="toolbar-button" onclick="addShape('triangle')">Triangle</button>
    `,
    text: `<button class="toolbar-button" onclick="addText()">Add Text</button>`,
    images: `<button class="toolbar-button" onclick="uploadImages()">Upload Images</button>`,
    crop: `
      <select onchange="enableCropTool(parseFloat(this.value))">
        <option value="1">Custom</option>
        <option value="1">Instagram</option>
        <option value="1.7777">YouTube</option>
        <option value="0.5625">TikTok</option>
        <option value="0.7089">A4</option>
        <option value="0.7037">A5</option>
        <option value="0.7333">A6</option>
      </select>
    `,
    effects: `
      <label>Brightness:</label><input type="range" min="-1" max="1" step="0.1" onchange="applyFilter('brightness', this.value)">
      <label>Contrast:</label><input type="range" min="-1" max="1" step="0.1" onchange="applyFilter('contrast', this.value)">
      <label>Blur:</label><input type="range" min="0" max="10" step="0.5" onchange="applyFilter('blur', this.value)">
      <label>Saturation:</label><input type="range" min="-1" max="1" step="0.1" onchange="applyFilter('saturation', this.value)">
      <button onclick="applyFilter('grayscale')">Grayscale</button>
      <button onclick="applyFilter('invert')">Invert</button>
    `,
    library: `<button class="toolbar-button" onclick="openModal()">Open PNG Library</button>`,
    view: `
      <button class="toolbar-button" onclick="canvas.zoomToPoint({x: canvas.width/2, y: canvas.height/2}, 1)">Reset Zoom</button>
      <button class="toolbar-button" onclick="undo()">Undo</button>
      <button class="toolbar-button" onclick="redo()">Redo</button>
    `,
    export: `<button class="toolbar-button" onclick="alert(canvas.toDataURL())">Export JSON</button>`
  };

  topToolbar.addEventListener('click', e => {
    if (e.target.dataset.category) {
      const cat = e.target.dataset.category;
      subToolbar.innerHTML = tools[cat];
    }
  });

  saveState();

  window.addEventListener('keydown', e => {
    if ((e.ctrlKey || e.metaKey) && e.code === 'KeyZ') {
      e.preventDefault();
      undo();
    } else if ((e.ctrlKey || e.metaKey) && e.code === 'KeyY') {
      e.preventDefault();
      redo();
    }
  });
</script>
</body>
</html>
