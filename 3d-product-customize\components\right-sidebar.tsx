"use client"

import Image from "next/image"

interface RightSidebarProps {
  environments: string[]
  selectedEnvironment: number
  setSelectedEnvironment: (index: number) => void
}

export function RightSidebar({ environments, selectedEnvironment, setSelectedEnvironment }: RightSidebarProps) {
  return (
    <div className="hidden w-[80px] flex-col gap-2 overflow-y-auto bg-[#182949] p-2 md:flex">
      {environments.map((env, index) => (
        <div
          key={index}
          className={`cursor-pointer overflow-hidden rounded-md ${selectedEnvironment === index ? "border-2 border-[#D6A25E]" : "border border-[#344464]"}`}
          onClick={() => setSelectedEnvironment(index)}
        >
          <Image
            src={env || "/placeholder.svg"}
            alt={`Environment ${index + 1}`}
            width={80}
            height={60}
            className="h-[60px] w-full object-cover"
          />
        </div>
      ))}
    </div>
  )
}
