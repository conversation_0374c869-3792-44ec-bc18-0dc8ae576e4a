<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Combined Crop and Draw Tool</title>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
  <style>
    .button-bar {
      display: flex;
      overflow-x: auto;
      white-space: nowrap;
      padding: 5px;
      border-bottom: 1px solid #ccc;
    }
    .button-bar button {
      margin-right: 5px;
    }
    .sub-bar {
      display: flex;
      overflow-x: auto;
      white-space: nowrap;
      padding: 5px;
      border-bottom: 1px solid #ccc;
    }
    canvas {
      border: 1px solid #ccc;
      margin-top: 10px;
    }
  </style>
</head>
<body>

<div class="button-bar">
  <button id="show-crop-options">Crop Options</button>
  <button id="show-draw-options">Draw Options</button>
</div>

<div id="crop-sub-bar" class="sub-bar" style="display: none;">
  <button id="add-images-btn">Add Images</button>
  <!-- Add other crop-specific options here -->
</div>

<div id="draw-sub-bar" class="sub-bar" style="display: none;">
  <button id="drawing-mode">Cancel drawing mode</button>
  <button id="clear-canvas">Clear</button>
  <label>Mode:
    <select id="drawing-mode-selector">
      <option>Pencil</option>
      <option>Circle</option>
      <option>Spray</option>
      <option>Pattern</option>
      <option>hline</option>
      <option>vline</option>
      <option>square</option>
      <option>diamond</option>
      <option>texture</option>
    </select>
  </label>
  <label>Line width:
    <span id="line-width-info">30</span>
    <input type="range" min="1" max="150" id="drawing-line-width" value="30">
  </label>
  <label>Line color:
    <input type="color" id="drawing-color" value="#76cef4">
  </label>
  <label>Shadow color:
    <input type="color" id="drawing-shadow-color" value="#5a7896">
  </label>
  <label>Shadow width:
    <span id="shadow-width-info">0</span>
    <input type="range" min="0" max="50" id="drawing-shadow-width" value="0">
  </label>
  <label>Shadow offset:
    <span id="shadow-offset-info">0</span>
    <input type="range" min="0" max="50" id="drawing-shadow-offset" value="0">
  </label>
</div>

<canvas id="c" width="800" height="500"></canvas>

<script>
  const canvas = new fabric.Canvas('c');

  // Toggle sub-bars
  document.getElementById('show-crop-options').onclick = () => {
    document.getElementById('crop-sub-bar').style.display = 'flex';
    document.getElementById('draw-sub-bar').style.display = 'none';
  };

  document.getElementById('show-draw-options').onclick = () => {
    document.getElementById('crop-sub-bar').style.display = 'none';
    document.getElementById('draw-sub-bar').style.display = 'flex';
  };

  // Implement Crop functionality from first file
  document.getElementById('add-images-btn').addEventListener('click', function (e) {
    e.stopPropagation();
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = 'image/*';
    fileInput.multiple = true;
    fileInput.click();
    fileInput.onchange = function (ev) {
      const files = ev.target.files;
      for (let i = 0; i < files.length; i++) {
        const reader = new FileReader();
        reader.onload = function (f) {
          fabric.Image.fromURL(f.target.result, function (img) {
            img.set({
              left: 100,
              top: 100
            });
            canvas.add(img);
          });
        };
        reader.readAsDataURL(files[i]);
      }
    };
  });

  // Implement Draw functionality
  canvas.isDrawingMode = true;
  const drawingModeEl = document.getElementById('drawing-mode');
  drawingModeEl.onclick = () => {
    canvas.isDrawingMode = !canvas.isDrawingMode;
    drawingModeEl.textContent = canvas.isDrawingMode ? 'Cancel drawing mode' : 'Enter drawing mode';
  };

  const updateBrush = () => {
    const brush = canvas.freeDrawingBrush;
    brush.color = document.getElementById('drawing-color').value;
    brush.width = parseInt(document.getElementById('drawing-line-width').value);
    brush.shadow = new fabric.Shadow({
      blur: parseInt(document.getElementById('drawing-shadow-width').value),
      offsetX: parseInt(document.getElementById('drawing-shadow-offset').value),
      offsetY: parseInt(document.getElementById('drawing-shadow-offset').value),
      color: document.getElementById('drawing-shadow-color').value,
    });
  };

  document.getElementById('drawing-color').onchange = updateBrush;
  document.getElementById('drawing-line-width').oninput = function() {
    document.getElementById('line-width-info').textContent = this.value;
    updateBrush();
  };
  document.getElementById('drawing-shadow-width').oninput = function() {
    document.getElementById('shadow-width-info').textContent = this.value;
    updateBrush();
  };
  document.getElementById('drawing-shadow-offset').oninput = function() {
    document.getElementById('shadow-offset-info').textContent = this.value;
    updateBrush();
  };

  document.getElementById('clear-canvas').onclick = () => canvas.clear();

  document.getElementById('drawing-mode-selector').onchange = function() {
    const val = this.value;
    if (val === 'hline' || val === 'vline') {
      const brush = new fabric.PatternBrush(canvas);
      brush.getPatternSrc = () => {
        const patternCanvas = document.createElement('canvas');
        patternCanvas.width = patternCanvas.height = 10;
        const ctx = patternCanvas.getContext('2d');
        ctx.strokeStyle = brush.color;
        ctx.lineWidth = 5;
        ctx.beginPath();
        if (val === 'hline') {
          ctx.moveTo(0, 5);
          ctx.lineTo(10, 5);
        } else {
          ctx.moveTo(5, 0);
          ctx.lineTo(5, 10);
        }
        ctx.stroke();
        return patternCanvas;
      };
      canvas.freeDrawingBrush = brush;
    } else {
      canvas.freeDrawingBrush = new fabric[val + 'Brush'](canvas);
    }
    updateBrush();
  };

  updateBrush();
</script>
</body>
</html>
