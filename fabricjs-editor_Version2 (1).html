<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Fabric.js Ultimate Canvas Editor</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <!-- Fabric.js CDN -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
  <!-- Hammer.js CDN -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/hammer.js/2.0.8/hammer.min.js"></script>
  <style>
    :root {
      --toolbar-height: 56px;
      --subtoolbar-height: 54px;
      --main-bg: #181818;
      --panel-bg: #222;
      --toolbar-bg: #252525;
      --toolbar-btn: #353535;
      --toolbar-btn-active: #444;
      --toolbar-btn-hover: #333;
      --primary: #23afe3;
      --accent: #ffd700;
      --danger: #e43c3c;
      --radius: 9px;
      --shadow: 0 2px 10px 0 rgba(0,0,0,0.24);
      --scrollbar: #2b2b2b;
      --icon: #b0b0b0;
      --icon-active: #23afe3;
    }
    html, body {
      height: 100%;
      margin: 0;
      padding: 0;
      background: var(--main-bg);
      color: #f0f0f0;
      font-family: 'Segoe UI', Arial, sans-serif;
      box-sizing: border-box;
      overscroll-behavior: none;
      touch-action: none;
    }
    body {
      min-height: 100vh;
      display: flex;
      flex-direction: column;
    }
    #app-root {
      flex: 1 1 auto;
      display: flex;
      flex-direction: column;
      height: 100vh;
      overflow: hidden;
    }
    .toolbar, .subtoolbar {
      display: flex;
      align-items: center;
      background: var(--toolbar-bg);
      height: var(--toolbar-height);
      z-index: 20;
      box-shadow: var(--shadow);
      user-select: none;
      touch-action: manipulation;
    }
    .toolbar {
      border-bottom: 1px solid #303030;
    }
    .subtoolbar {
      background: var(--panel-bg);
      height: var(--subtoolbar-height);
      border-top: 1px solid #303030;
      box-shadow: none;
      overflow-x: auto;
      scrollbar-color: var(--scrollbar) var(--panel-bg);
    }
    .toolbar-btn, .subtoolbar-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: var(--toolbar-btn);
      border: none;
      outline: none;
      padding: 6px 14px;
      margin: 0 4px;
      border-radius: var(--radius);
      color: var(--icon);
      font-size: 22px;
      cursor: pointer;
      transition: background .16s, color .16s;
      min-width: 44px;
      min-height: 44px;
      position: relative;
      touch-action: manipulation;
    }
    .toolbar-btn.active, .toolbar-btn:active,
    .subtoolbar-btn.active, .subtoolbar-btn:active {
      background: var(--toolbar-btn-active);
      color: var(--icon-active);
    }
    .toolbar-btn:hover, .subtoolbar-btn:hover {
      background: var(--toolbar-btn-hover);
      color: var(--primary);
    }
    .toolbar-btn[aria-label], .subtoolbar-btn[aria-label] {
      position: relative;
    }
    .toolbar-btn[aria-label]:hover::after,
    .subtoolbar-btn[aria-label]:hover::after {
      content: attr(aria-label);
      position: absolute;
      bottom: -28px;
      left: 50%;
      background: #222d;
      color: #fff;
      font-size: 12px;
      padding: 2px 8px;
      border-radius: 6px;
      white-space: nowrap;
      pointer-events: none;
      transform: translateX(-50%);
      z-index: 200;
    }
    .toolbar-btn svg, .subtoolbar-btn svg {
      width: 24px;
      height: 24px;
      display: block;
      pointer-events: none;
      margin: 0 auto 2px;
    }
    .toolbar-btn span, .subtoolbar-btn span {
      font-size: 12px;
      margin-top: 1px;
      text-align: center;
      pointer-events: none;
      display: block;
    }
    .canvas-container-wrap {
      flex: 1 1 auto;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--main-bg);
      overflow: auto;
      position: relative;
      min-height: 0;
      min-width: 0;
    }
    #fabric-canvas {
      background: #fff;
      border-radius: var(--radius);
      box-shadow: 0 2px 24px 1px #000b;
      touch-action: none;
      transition: box-shadow .12s;
      max-width: 100vw;
      max-height: 100%;
      width: 100vw;
      height: 65vw;
      min-width: 320px;
      min-height: 320px;
      outline: 2px solid #2226;
      display: block;
    }
    @media (min-width: 600px) {
      .canvas-container-wrap {
        padding: 20px;
      }
      #fabric-canvas {
        width: 900px;
        height: 600px;
      }
    }
    @media (max-width: 599px) {
      .canvas-container-wrap {
        padding: 0;
      }
      #fabric-canvas {
        width: 100vw !important;
        height: 70vw !important;
        min-width: 230px;
        min-height: 230px;
      }
    }
    /* Modal */
    .modal-backdrop {
      position: fixed;
      inset: 0;
      background: #0009;
      z-index: 999;
      display: none;
      align-items: center;
      justify-content: center;
    }
    .modal-backdrop.active {
      display: flex;
    }
    .modal {
      background: var(--panel-bg);
      border-radius: 14px;
      max-width: 95vw;
      max-height: 88vh;
      min-width: 300px;
      box-shadow: 0 2px 32px #000a;
      padding: 24px 18px 18px 18px;
      position: relative;
      overflow: auto;
      animation: modal-in .25s cubic-bezier(.47,1.64,.41,.8);
    }
    @keyframes modal-in { from{ opacity: 0; transform: scale(.93);} to {opacity:1; transform: scale(1);} }
    .modal-close-btn {
      position: absolute;
      top: 8px; right: 10px;
      background: transparent;
      border: none;
      color: #fff;
      font-size: 26px;
      cursor: pointer;
    }
    /* PNG Library Modal */
    .png-library-modal {
      display: flex;
      flex-direction: column;
      min-width: 320px;
      max-width: 700px;
      min-height: 260px;
      max-height: 85vh;
    }
    .png-library-tabs {
      display: flex;
      gap: 8px;
      margin-bottom: 12px;
      flex-wrap: wrap;
    }
    .png-library-tab {
      padding: 4px 12px;
      background: #27384b;
      color: #b0e4ff;
      border-radius: 5px;
      cursor: pointer;
      font-size: 15px;
      border: none;
    }
    .png-library-tab.active {
      background: var(--primary);
      color: #fff;
    }
    .png-library-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(64px, 1fr));
      gap: 14px;
      background: #1c1c1c;
      padding: 10px;
      border-radius: 8px;
      overflow-y: auto;
      max-height: 55vh;
    }
    .png-library-item {
      width: 64px;
      height: 64px;
      background: #fff0;
      border-radius: 7px;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: grab;
      border: 1px solid #3338;
      box-shadow: 0 2px 8px #0002;
      transition: box-shadow .13s;
    }
    .png-library-item:active { box-shadow: 0 2px 14px #0007; }
    .png-library-item img {
      max-width: 100%; max-height: 100%;
      display: block;
      pointer-events: none;
      border-radius: 7px;
    }
    /* Layer Panel */
    .layer-panel {
      position: absolute;
      top: 12px; right: 12px;
      background: #21232aeb;
      color: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 18px #0005;
      padding: 12px 8px 8px 12px;
      z-index: 30;
      min-width: 180px;
      max-width: 250px;
      font-size: 13px;
      display: none;
      flex-direction: column;
      gap: 7px;
      max-height: 60vh;
      overflow-y: auto;
      user-select: none;
    }
    .layer-panel.active {
      display: flex;
      animation: modal-in .19s;
    }
    .layer-panel-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-weight: 600;
      margin-bottom: 4px;
    }
    .layer-item {
      display: flex;
      align-items: center;
      gap: 8px;
      background: #2228;
      border-radius: 6px;
      padding: 3px 8px 3px 0;
      margin: 1px 0;
      cursor: pointer;
    }
    .layer-item.selected {
      background: var(--primary);
      color: #fff;
    }
    .layer-item span {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 14px;
    }
    .layer-item .layer-eye, .layer-item .layer-lock {
      font-size: 16px;
      cursor: pointer;
      color: #aaf;
      margin-left: 3px;
    }
    .layer-item .layer-eye[aria-checked="false"] { color: #777; }
    .layer-item .layer-lock[aria-checked="true"] { color: #eebb22; }
    /* Canvas Size Modal */
    .canvas-size-modal {
      display: flex;
      flex-direction: column;
      gap: 14px;
      width: 320px;
      max-width: 95vw;
    }
    .canvas-size-modal label {
      font-size: 15px; margin-bottom: 2px;
    }
    .canvas-size-modal input, .canvas-size-modal select {
      width: 100%;
      padding: 4px 8px;
      font-size: 16px;
      border-radius: 5px;
      border: 1px solid #444;
      background: #232323;
      color: #fff;
      margin-bottom: 6px;
    }
    .canvas-size-modal .modal-actions {
      display: flex;
      gap: 8px;
      justify-content: flex-end;
      margin-top: 8px;
    }
    .canvas-size-modal button {
      padding: 5px 13px; font-size: 15px; border-radius: 5px; border: none; cursor: pointer;
      background: var(--primary); color: #fff;
      margin-left: 7px;
    }
    /* Hide input[type=file] */
    #upload-images-input {
      display: none;
    }
    /* Scrollbars */
    ::-webkit-scrollbar { width: 10px; background: var(--panel-bg);}
    ::-webkit-scrollbar-thumb { background: var(--scrollbar); border-radius: 7px;}
    /* Misc */
    .hidden { display: none !important; }
    .flex-row { display: flex; flex-direction: row; }
    .flex-col { display: flex; flex-direction: column; }
    .gap-8 { gap: 8px; }
    .gap-16 { gap: 16px; }
    .mt-12 { margin-top: 12px; }
    .ml-8 { margin-left: 8px; }
    .mr-8 { margin-right: 8px; }
    .w100 { width: 100%; }
    .nowrap { white-space: nowrap; }
  </style>
</head>
<body>
  <div id="app-root">
    <!-- Top Toolbar -->
    <nav class="toolbar" id="toolbar-main" role="toolbar" aria-label="Main Tools">
      <button class="toolbar-btn active" id="cat-draw" aria-label="Draw" tabindex="1">
        <!-- Pencil Icon -->
        <svg viewBox="0 0 24 24"><path fill="currentColor" d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25M21.41 6.34a2 2 0 0 0 0-2.83l-1.92-1.92a2 2 0 0 0-2.83 0l-1.13 1.13l3.75 3.75l1.13-1.13z"/></svg>
        <span>Draw</span>
      </button>
      <button class="toolbar-btn" id="cat-shapes" aria-label="Shapes" tabindex="2">
        <svg viewBox="0 0 24 24"><path fill="currentColor" d="M5,3A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3H5M5,5H19V19H5V5Z"/></svg>
        <span>Shapes</span>
      </button>
      <button class="toolbar-btn" id="cat-text" aria-label="Text" tabindex="3">
        <svg viewBox="0 0 24 24"><path fill="currentColor" d="M5 4v3h5.5v12h3V7H19V4z"/></svg>
        <span>Text</span>
      </button>
      <button class="toolbar-btn" id="cat-images" aria-label="Images" tabindex="4">
        <svg viewBox="0 0 24 24"><path fill="currentColor" d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L15.5 12l4.5 6H4l4.5-6z"/></svg>
        <span>Images</span>
      </button>
      <button class="toolbar-btn" id="cat-crop" aria-label="Crop" tabindex="5">
        <svg viewBox="0 0 24 24"><path fill="currentColor" d="M6 2v2H4a2 2 0 0 0-2 2v2h2V6h2V2m8 0v2h2v2h2v2h2V6a2 2 0 0 0-2-2h-2V2m0 16v2h2a2 2 0 0 0 2-2v-2h-2v2h-2m-8 0H4v-2H2v2a2 2 0 0 0 2 2h2v-2m3-13v2h8v8h2V5a2 2 0 0 0-2-2h-2V2h-2V2h-2v2H8V2H6v2h2v2h2v2H8V4H6v2h2v2h2v2H8v-2H6v2h2v2h2v2H8v-2H6v2h2v2h2v2H8V20H6v-2H4a2 2 0 0 0-2-2v-2h2v2h2v2h2v-2h2v-2h2v2h2v-2H8v-2h2v-2h2v2h2v-2h2v-2h-2v-2h2v-2h-2v-2h-2V4h-2z"/></svg>
        <span>Crop</span>
      </button>
      <button class="toolbar-btn" id="cat-effects" aria-label="Effects" tabindex="6">
        <svg viewBox="0 0 24 24"><path fill="currentColor" d="M12,17V19A7,7 0 0,1 5,12H7A5,5 0 0,0 12,17M12,2A10,10 0 0,1 22,12H20A8,8 0 0,0 12,4V2M12,5A7,7 0 0,1 19,12H17A5,5 0 0,0 12,7V5Z"/></svg>
        <span>Effects</span>
      </button>
      <button class="toolbar-btn" id="cat-library" aria-label="Library" tabindex="7">
        <svg viewBox="0 0 24 24"><path fill="currentColor" d="M21,2A2,2 0 0,1 23,4V20A2,2 0 0,1 21,22H3A2,2 0 0,1 1,20V4A2,2 0 0,1 3,2H21M3,4V20H21V4H3Z"/></svg>
        <span>Library</span>
      </button>
      <button class="toolbar-btn" id="cat-view" aria-label="View" tabindex="8">
        <svg viewBox="0 0 24 24"><path fill="currentColor" d="M12 4.5C7 4.5 2.7 7.6 1 12c1.7 4.4 6 7.5 11 7.5s9.3-3.1 11-7.5C21.3 7.6 17 4.5 12 4.5zm0 11c-2.5 0-4.5-2-4.5-4.5S9.5 6.5 12 6.5s4.5 2 4.5 4.5-2 4.5-4.5 4.5z"/></svg>
        <span>View</span>
      </button>
      <button class="toolbar-btn" id="cat-export" aria-label="Export" tabindex="9">
        <svg viewBox="0 0 24 24"><path fill="currentColor" d="M5,20h14a1,1 0 0,0 1-1v-9H18v7H6v-7H3v9A1,1 0 0,0 5,20M19,8l-7-7l-7,7h4v6h6V8h4z"/></svg>
        <span>Export</span>
      </button>
    </nav>
    <!-- Dynamic Sub Toolbar -->
    <nav class="subtoolbar" id="subtoolbar"></nav>
    <!-- Canvas Area -->
    <div class="canvas-container-wrap">
      <canvas id="fabric-canvas" width="900" height="600" tabindex="10"></canvas>
      <!-- Layer Panel -->
      <div id="layer-panel" class="layer-panel"></div>
    </div>

  </div>

  <!-- Hidden file input for image uploads -->
  <input type="file" id="upload-images-input" accept="image/*" multiple />

  <!-- Modal Backdrop -->
  <div id="modal-backdrop" class="modal-backdrop"></div>

  <script>
    // -- UI Globals --
    const toolbarCategories = [
      'draw','shapes','text','images','crop','effects','library','view','export'
    ];
    const subtoolbars = {
      draw: [
        { id: "draw-pencil", icon: "✏️", label: "Pencil" },
        { id: "draw-brush", icon: "🖌️", label: "Brush" },
        { id: "draw-marker", icon: "🖊️", label: "Marker" },
        { id: "draw-spray", icon: "🖍️", label: "Spray" },
        { id: "draw-dotted", icon: "⋯", label: "Dotted Line" },
        { id: "draw-eraser", icon: "🧽", label: "Eraser" },
        { type: "divider" },
        { id: "draw-color", icon: `<svg width=22 height=22><circle cx="11" cy="11" r="9" fill="#23afe3" stroke="#333" stroke-width="2"/></svg>`, label: "Color Picker", input: "color" },
        { id: "draw-size", icon: "⬤", label: "Brush Size", input: "range", min: 1, max: 80, value: 9 },
        { id: "draw-opacity", icon: "☀️", label: "Opacity", input: "range", min: 0.1, max: 1, step: 0.05, value: 1 }
      ],
      shapes: [
        { id: "shape-rect", icon: "▭", label: "Rectangle" },
        { id: "shape-circle", icon: "●", label: "Circle" },
        { id: "shape-ellipse", icon: "⬭", label: "Ellipse" },
        { id: "shape-line", icon: "／", label: "Line" },
        { id: "shape-polygon", icon: "⬠", label: "Polygon" },
        { id: "shape-polyline", icon: "⎯", label: "Polyline" },
        { id: "shape-path", icon: "∾", label: "Path" },
        { id: "shape-svg", icon: "🖼️", label: "SVG" }
      ],
      text: [
        { id: "add-text", icon: "📝", label: "Add Text" },
        { id: "add-textbox", icon: "🗒️", label: "Add Textbox" },
        { type: "divider" },
        { id: "font-family", icon: "𝓐", label: "Font", input: "select", options: [
            { value: "Arial", label: "Arial" },
            { value: "Tahoma", label: "Tahoma" },
            { value: "Times New Roman", label: "Times New Roman" },
            { value: "Cairo", label: "Cairo (Arabic)" },
            { value: "Amiri", label: "Amiri (Arabic)" },
            { value: "Noto Naskh Arabic", label: "Noto Naskh Arabic" }
          ]
        },
        { id: "font-size", icon: "🔠", label: "Font Size", input: "range", min: 8, max: 120, value: 32 },
        { id: "font-color", icon: `<svg width=22 height=22><rect x="2" y="15" width="18" height="5" fill="#23afe3" stroke="#333" stroke-width="2"/><text x="5" y="13" font-size="12" fill="#333">A</text></svg>`, label: "Font Color", input: "color" },
        { id: "text-bg-color", icon: "🟦", label: "Background", input: "color" },
        { id: "text-stroke-color", icon: "🖍️", label: "Stroke Color", input: "color" },
        { id: "text-stroke-width", icon: "⬛", label: "Stroke Width", input: "range", min: 0, max: 10, value: 1 },
        { id: "text-shadow", icon: "🌫️", label: "Text Shadow", input: "color" },
        { id: "text-align", icon: "☰", label: "Alignment", input: "select", options: [
          { value: "left", label: "Left" },
          { value: "center", label: "Center" },
          { value: "right", label: "Right" },
          { value: "justify", label: "Justify" }
        ] }
      ],
      images: [
        { id: "upload-img", icon: "📤", label: "Upload Images", input: "file" },
        { id: "img-border-color", icon: "⬛", label: "Border Color", input: "color" },
        { id: "img-border-width", icon: "▤", label: "Border Width", input: "range", min: 0, max: 20, value: 0 },
        { id: "img-shadow-color", icon: "🌫️", label: "Shadow Color", input: "color" },
        { id: "img-shadow-blur", icon: "💧", label: "Shadow Blur", input: "range", min: 0, max: 32, value: 0 },
        { id: "img-shadow-offsetx", icon: "↔️", label: "Shadow X", input: "range", min: -40, max: 40, value: 0 },
        { id: "img-shadow-offsety", icon: "↕️", label: "Shadow Y", input: "range", min: -40, max: 40, value: 0 },
        { id: "img-shadow-opacity", icon: "☁️", label: "Shadow Opacity", input: "range", min: 0, max: 1, step: 0.05, value: 1 }
      ],
      crop: [
        { id: "crop-apply", icon: "✂️", label: "Crop Apply" },
        { id: "crop-cancel", icon: "❌", label: "Cancel Crop" },
        { type: "divider" },
        { id: "preset-a4", icon: "📄", label: "A4" },
        { id: "preset-a5", icon: "📄", label: "A5" },
        { id: "preset-a6", icon: "📄", label: "A6" },
        { id: "preset-instagram", icon: "📸", label: "Instagram" },
        { id: "preset-youtube", icon: "▶️", label: "YouTube" },
        { id: "preset-facebook", icon: "📘", label: "Facebook" },
        { id: "preset-tiktok", icon: "🎵", label: "TikTok" },
        { id: "crop-portrait", icon: "🔲", label: "Portrait" },
        { id: "crop-landscape", icon: "🔳", label: "Landscape" },
      ],
      effects: [
        { id: "fx-brightness", icon: "☀️", label: "Brightness", input: "range", min: -1, max: 1, step: 0.01, value: 0 },
        { id: "fx-contrast", icon: "🌓", label: "Contrast", input: "range", min: -1, max: 1, step: 0.01, value: 0 },
        { id: "fx-blur", icon: "💧", label: "Blur", input: "range", min: 0, max: 1, step: 0.01, value: 0 },
        { id: "fx-saturation", icon: "🌈", label: "Saturation", input: "range", min: -1, max: 1, step: 0.01, value: 0 },
        { id: "fx-grayscale", icon: "⚫️", label: "Grayscale", input: "toggle" },
        { id: "fx-bw", icon: "⬛", label: "B&W", input: "toggle" },
        { id: "fx-sharpen", icon: "🔪", label: "Sharpen", input: "toggle" },
        { id: "fx-invert", icon: "🔄", label: "Invert", input: "toggle" }
      ],
      library: [
        { id: "open-png-library", icon: "🖼️", label: "Open PNG Library" }
      ],
      view: [
        { id: "show-layers", icon: "🗂️", label: "Layers" },
        { id: "snap-grid", icon: "🔲", label: "Snap to Grid", input: "toggle" },
        { id: "undo", icon: "↩️", label: "Undo" },
        { id: "redo", icon: "↪️", label: "Redo" },
        { id: "canvas-size", icon: "📏", label: "Canvas Size" },
        { id: "zoom-in", icon: "➕", label: "Zoom In" },
        { id: "zoom-out", icon: "➖", label: "Zoom Out" },
        { id: "reset-view", icon: "🔄", label: "Reset View" }
      ],
      export: [
        { id: "export-png", icon: "🖼️", label: "Export PNG" },
        { id: "export-jpg", icon: "🖼️", label: "Export JPG" },
        { id: "export-svg", icon: "🖼️", label: "Export SVG" },
        { id: "export-json", icon: "💾", label: "Export JSON" },
        { id: "import-json", icon: "📥", label: "Import JSON" }
      ]
    };
    // -- PNG Library Sample Data (Base64 PNGs, replace with actual sources) --
    const pngLibrary = {
      "Icons": [
        { src: "https://cdn-icons-png.flaticon.com/128/25/25231.png", label: "GitHub" },
        { src: "https://cdn-icons-png.flaticon.com/128/5968/5968852.png", label: "React" },
        { src: "https://cdn-icons-png.flaticon.com/128/226/226777.png", label: "Java" }
      ],
      "Shapes": [
        { src: "https://upload.wikimedia.org/wikipedia/commons/thumb/0/0d/Basic_square.png/64px-Basic_square.png", label: "Square" },
        { src: "https://upload.wikimedia.org/wikipedia/commons/thumb/2/2d/Circle_-_black_simple.svg/64px-Circle_-_black_simple.svg.png", label: "Circle" }
      ],
      "UI": [
        { src: "https://upload.wikimedia.org/wikipedia/commons/thumb/9/99/OOjs_UI_icon_alert_destructive.svg/64px-OOjs_UI_icon_alert_destructive.svg.png", label: "Alert" }
      ]
    };
    // -- State --
    let currentCategory = "draw";
    let currentSubToolbar = [];
    let canvas, cropRect, cropActive = false, history = [], historyStep = -1;
    let snappingEnabled = false, gridSpacing = 40;
    let layerPanelOpen = false;
    let touchScale = 1, touchX = 0, touchY = 0, isPanning = false;
    // -- Utility --
    function el(id) { return document.getElementById(id); }
    function createEl(tag, attrs) {
      let e = document.createElement(tag);
      for (let k in attrs) {
        if(k === "class") e.className = attrs[k];
        else if(k === "html") e.innerHTML = attrs[k];
        else if(k === "text") e.textContent = attrs[k];
        else e.setAttribute(k, attrs[k]);
      }
      return e;
    }
    // --- Toolbar and Subtoolbar Rendering ---
    function setToolbarCategory(cat) {
      currentCategory = cat;
      for(let c of toolbarCategories) {
        const btn = el('cat-' + c);
        if(btn) btn.classList.toggle('active', c === cat);
      }
      renderSubToolbar(cat);
    }
    function renderSubToolbar(cat) {
      // Rebuild subtoolbar for the selected category
      const bar = el('subtoolbar');
      bar.innerHTML = "";
      let tools = subtoolbars[cat] || [];
      currentSubToolbar = tools;
      for (let t of tools) {
        if (t.type === "divider") {
          let div = createEl('div', {class:"ml-8 mr-8", style:"width:1px; height:28px; background:#444;"});
          bar.appendChild(div);
          continue;
        }
        let btn = createEl('button', {
          class: 'subtoolbar-btn',
          id: t.id,
          tabindex: 0,
          'aria-label': t.label
        });
        if (t.icon && typeof t.icon === "string" && t.icon.startsWith("<")) btn.innerHTML = t.icon;
        else btn.innerHTML = t.icon ? `<span style="font-size:20px">${t.icon}</span>` : "";
        if (t.input) {
          // attach appropriate input
          let input;
          if(t.input === "color") {
            input = createEl('input', {type:"color", style:"margin-left:4px;", value: t.value || "#23afe3"});
            input.addEventListener("change", e => handleSubToolbarInput(t.id, e.target.value));
          } else if(t.input === "range") {
            input = createEl('input', {type:"range", min:t.min, max:t.max, step:t.step || 1, value:t.value});
            input.style.width = "70px";
            input.addEventListener("input", e => handleSubToolbarInput(t.id, e.target.value));
          } else if(t.input === "select") {
            input = createEl('select', {});
            for (let opt of t.options) {
              let option = createEl('option', {value: opt.value, text: opt.label});
              input.appendChild(option);
            }
            input.addEventListener("change", e => handleSubToolbarInput(t.id, e.target.value));
          } else if(t.input === "toggle") {
            input = createEl('input', {type:"checkbox"});
            input.style.marginLeft = "7px";
            input.addEventListener("change", e => handleSubToolbarInput(t.id, input.checked));
          } else if(t.input === "file") {
            input = createEl('input', {type:"file", multiple:"", accept:"image/*", style:"display:none;"});
            input.addEventListener("change", handleImageUploadInput);
            btn.appendChild(input);
          }
          btn.appendChild(input);
        }
        btn.appendChild(createEl('span', {text:t.label}));
        btn.addEventListener('click', () => handleSubToolbarBtn(t));
        bar.appendChild(btn);
      }
    }
    function handleSubToolbarBtn(tool) {
      // Some tools are buttons, some are inputs (handled in input event).
      // E.g. for "upload-img", trigger click on input[type=file]
      if(tool.input === "file") {
        let btn = el(tool.id);
        let input = btn.querySelector('input[type=file]');
        if(input) input.click();
      } else if(tool.id === "open-png-library") {
        openPngLibraryModal();
      } else if(tool.id === "show-layers") {
        toggleLayerPanel();
      } else if(tool.id === "undo") {
        undo();
      } else if(tool.id === "redo") {
        redo();
      } else if(tool.id === "canvas-size") {
        openCanvasSizeModal();
      } else if(tool.id === "zoom-in") {
        setZoom(canvas.getZoom() * 1.2);
      } else if(tool.id === "zoom-out") {
        setZoom(canvas.getZoom() / 1.2);
      } else if(tool.id === "reset-view") {
        setZoom(1); canvas.absolutePan({x:0, y:0});
      }
      // Drawing tools will set drawingMode and brush
      else if(tool.id.startsWith("draw-")) {
        setDrawingTool(tool.id);
      }
      // Effects and crop handled in input handler
      // Shapes
      else if(tool.id.startsWith("shape-")) {
        addShape(tool.id.replace("shape-",""));
      }
      // Add Text/Textbox
      else if(tool.id === "add-text") {
        addText();
      }
      else if(tool.id === "add-textbox") {
        addTextbox();
      }
      // Crop
      else if(tool.id === "crop-apply") {
        applyCrop();
      }
      else if(tool.id === "crop-cancel") {
        cancelCrop();
      }
      else if(tool.id.startsWith("preset-")) {
        setCropPreset(tool.id.replace("preset-",""));
      }
      else if(tool.id === "crop-portrait") {
        setCropOrientation("portrait");
      }
      else if(tool.id === "crop-landscape") {
        setCropOrientation("landscape");
      }
      // Export
      else if(tool.id.startsWith("export-")) {
        exportCanvas(tool.id.replace("export-",""));
      }
      else if(tool.id === "import-json") {
        importCanvasJson();
      }
    }
    function handleSubToolbarInput(id, value) {
      // Drawing tools: brush color, size, opacity
      if(id === "draw-color") {
        canvas.freeDrawingBrush.color = value;
      } else if(id === "draw-size") {
        canvas.freeDrawingBrush.width = parseInt(value);
      } else if(id === "draw-opacity") {
        canvas.freeDrawingBrush.opacity = parseFloat(value);
      }
      // Text tools
      let activeObj = canvas.getActiveObject();
      if(id === "font-family" && activeObj && activeObj.isType && (activeObj.isType('text') || activeObj.isType('textbox'))) {
        activeObj.set({fontFamily:value});
        canvas.requestRenderAll();
      } else if(id === "font-size" && activeObj && (activeObj.isType('text') || activeObj.isType('textbox'))) {
        activeObj.set({fontSize: parseInt(value)});
        canvas.requestRenderAll();
      } else if(id === "font-color" && activeObj && (activeObj.isType('text') || activeObj.isType('textbox'))) {
        activeObj.set({fill: value});
        canvas.requestRenderAll();
      } else if(id === "text-bg-color" && activeObj && (activeObj.isType('text') || activeObj.isType('textbox'))) {
        activeObj.set({backgroundColor: value});
        canvas.requestRenderAll();
      } else if(id === "text-stroke-color" && activeObj && (activeObj.isType('text') || activeObj.isType('textbox'))) {
        activeObj.set({stroke: value});
        canvas.requestRenderAll();
      } else if(id === "text-stroke-width" && activeObj && (activeObj.isType('text') || activeObj.isType('textbox'))) {
        activeObj.set({strokeWidth: parseInt(value)});
        canvas.requestRenderAll();
      } else if(id === "text-shadow" && activeObj && (activeObj.isType('text') || activeObj.isType('textbox'))) {
        activeObj.set({shadow: value+" 2px 2px 2px"});
        canvas.requestRenderAll();
      } else if(id === "text-align" && activeObj && (activeObj.isType('text') || activeObj.isType('textbox'))) {
        activeObj.set({textAlign:value});
        canvas.requestRenderAll();
      }
      // Image settings
      if(activeObj && activeObj.isType && activeObj.isType('image')) {
        if(id === "img-border-color") {
          activeObj.set({stroke: value});
          canvas.requestRenderAll();
        }
        if(id === "img-border-width") {
          activeObj.set({strokeWidth: parseInt(value)});
          canvas.requestRenderAll();
        }
        if(id === "img-shadow-color") {
          let shadow = activeObj.shadow || {color:"#000", blur:0, offsetX:0, offsetY:0, opacity:1};
          shadow.color = value;
          activeObj.set({shadow});
          canvas.requestRenderAll();
        }
        if(id === "img-shadow-blur") {
          let shadow = activeObj.shadow || {color:"#000", blur:0, offsetX:0, offsetY:0, opacity:1};
          shadow.blur = parseInt(value);
          activeObj.set({shadow});
          canvas.requestRenderAll();
        }
        if(id === "img-shadow-offsetx") {
          let shadow = activeObj.shadow || {color:"#000", blur:0, offsetX:0, offsetY:0, opacity:1};
          shadow.offsetX = parseInt(value);
          activeObj.set({shadow});
          canvas.requestRenderAll();
        }
        if(id === "img-shadow-offsety") {
          let shadow = activeObj.shadow || {color:"#000", blur:0, offsetX:0, offsetY:0, opacity:1};
          shadow.offsetY = parseInt(value);
          activeObj.set({shadow});
          canvas.requestRenderAll();
        }
        if(id === "img-shadow-opacity") {
          let shadow = activeObj.shadow || {color:"#000", blur:0, offsetX:0, offsetY:0, opacity:1};
          shadow.opacity = parseFloat(value);
          activeObj.set({shadow});
          canvas.requestRenderAll();
        }
      }
      // Image filters
      if((id.startsWith("fx-")) && activeObj && activeObj.isType && activeObj.isType('image')) {
        applyImageFilters(activeObj);
      }
      // Snap to grid
      if(id === "snap-grid") {
        snappingEnabled = value;
      }
    }
    // --- FABRIC.JS CANVAS INIT ---
    function initFabricCanvas() {
      canvas = new fabric.Canvas('fabric-canvas', {
        selection: true,
        backgroundColor: "#fff",
        preserveObjectStacking: true,
        fireRightClick: true,
        stopContextMenu: true
      });
      // Drawing brush default
      canvas.isDrawingMode = true;
      canvas.freeDrawingBrush = new fabric.PencilBrush(canvas);
      canvas.freeDrawingBrush.width = 9;
      canvas.freeDrawingBrush.color = "#23afe3";
      canvas.freeDrawingBrush.opacity = 1;
      // Grid
      drawGrid();
      // Object events
      canvas.on('object:added', saveHistory);
      canvas.on('object:modified', saveHistory);
      canvas.on('object:removed', saveHistory);
      canvas.on('selection:created', updateLayerPanelSelection);
      canvas.on('selection:updated', updateLayerPanelSelection);
      canvas.on('selection:cleared', updateLayerPanelSelection);
      // Snapping
      canvas.on('object:moving', snapToGridHandler);
      canvas.on('object:scaling', snapToGridHandler);
      // Keyboard
      window.addEventListener('keydown', handleKeyboardShortcuts);
      // Touch
      setupTouchGestures();
    }
    // --- Drawing Tools ---
    function setDrawingTool(toolId) {
      // toolId: draw-pencil, draw-brush, etc.
      const mode = toolId.replace('draw-', '');
      canvas.isDrawingMode = !["eraser"].includes(mode);
      if(mode === "pencil") {
        canvas.freeDrawingBrush = new fabric.PencilBrush(canvas);
      } else if(mode === "brush") {
        canvas.freeDrawingBrush = new fabric.CircleBrush(canvas);
      } else if(mode === "marker") {
        canvas.freeDrawingBrush = new fabric.PatternBrush(canvas);
      } else if(mode === "spray") {
        canvas.freeDrawingBrush = new fabric.SprayBrush(canvas);
      } else if(mode === "dotted") {
        let brush = new fabric.PencilBrush(canvas);
        brush.strokeLineCap = 'round';
        brush.strokeDashArray = [1, 8];
        canvas.freeDrawingBrush = brush;
      } else if(mode === "eraser") {
        canvas.isDrawingMode = true;
        canvas.freeDrawingBrush = new fabric.EraserBrush(canvas);
      }
      // Restore color/size/opacity to brush
      canvas.freeDrawingBrush.color = "#23afe3";
      canvas.freeDrawingBrush.width = 9;
      canvas.freeDrawingBrush.opacity = 1;
    }
    // --- SHAPES ---
    function addShape(type) {
      let obj;
      switch(type) {
        case "rect":
          obj = new fabric.Rect({ left: 120, top: 120, width: 120, height: 80, fill: "#23afe3", rx: 7, ry: 7, stroke:"#333", strokeWidth:1 });
          break;
        case "circle":
          obj = new fabric.Circle({ left: 180, top: 90, radius: 55, fill: "#ffd700", stroke:"#333", strokeWidth:1 });
          break;
        case "ellipse":
          obj = new fabric.Ellipse({ left: 150, top: 170, rx: 70, ry: 36, fill: "#23afe3", stroke:"#333", strokeWidth:1 });
          break;
        case "polygon":
          obj = new fabric.Polygon([{x:100,y:0},{x:200,y:50},{x:170,y:200},{x:30,y:200},{x:0,y:50}], { left: 220, top: 120, fill: "#f0a", stroke:"#333", strokeWidth:1 });
          break;
        case "polyline":
          obj = new fabric.Polyline([{x:0,y:0},{x:50,y:60},{x:120,y:10}], { left: 300, top: 200, fill: "", stroke:"#23afe3", strokeWidth:4 });
          break;
        case "line":
          obj = new fabric.Line([20, 20, 200, 80], { left: 220, top: 220, stroke: "#23afe3", strokeWidth: 6 });
          break;
        case "path":
          obj = new fabric.Path("M 10 10 L 90 90 Q 120 50 70 10 z", { left: 140, top: 170, fill: "#6e2", stroke: "#333", strokeWidth: 2 });
          break;
        case "svg":
          fabric.loadSVGFromURL('https://upload.wikimedia.org/wikipedia/commons/3/3e/Logo_Bootstrap.svg', function(objects, options) {
            let obj = fabric.util.groupSVGElements(objects, options);
            obj.set({ left: 180, top: 40, scaleX:0.15, scaleY:0.15 });
            canvas.add(obj);
            canvas.setActiveObject(obj);
            canvas.requestRenderAll();
          });
          return;
      }
      canvas.add(obj);
      canvas.setActiveObject(obj);
      canvas.requestRenderAll();
    }
    // --- TEXT ---
    function addText() {
      let text = new fabric.Text('Sample Text', { left: 290, top: 210, fill: "#111", fontSize: 40, fontFamily: "Arial", fontWeight: "bold", stroke:"#fff", strokeWidth:2, shadow:"rgba(0,0,0,0.2) 2px 2px 2px", backgroundColor: "#fff1" });
      canvas.add(text);
      canvas.setActiveObject(text);
      canvas.requestRenderAll();
    }
    function addTextbox() {
      let textbox = new fabric.Textbox('Edit me.\nArabic: مرحبا', { left: 260, top: 120, fill: "#23afe3", fontSize: 32, fontFamily: "Cairo", width: 300, textAlign: "center", backgroundColor: "#ffd70044" });
      canvas.add(textbox);
      canvas.setActiveObject(textbox);
      canvas.requestRenderAll();
    }
    // --- IMAGES ---
    function handleImageUploadInput(e) {
      let files = e.target.files;
      if(!files || files.length === 0) return;
      for (let file of files) {
        if (!file.type.startsWith('image/')) continue;
        let reader = new FileReader();
        reader.onload = function(evt) {
          fabric.Image.fromURL(evt.target.result, function(img) {
            img.set({
              left: 120 + Math.random()*140,
              top: 120 + Math.random()*180,
              scaleX: 0.45,
              scaleY: 0.45,
              selectable: true,     // تم التفعيل
              evented: true,        // تم التفعيل
              hasControls: true,    // تم التفعيل
              hasBorders: true,     // تم التفعيل
              lockMovementX: false,
              lockMovementY: false,
              lockScalingX: false,
              lockScalingY: false,
              lockRotation: false,
              cornerStyle: 'circle', // شكل الزوايا
              borderColor: '#23afe3', // لون الحدود عند التحديد
              cornerColor: '#ffd700', // لون الزوايا
              transparentCorners: false // ظهور الزوايا
            });
            // إضافة خاصية التحكم في جميع الصور السابقة عند الإضافة
            img.on('mousedown', function() {
              canvas.setActiveObject(img);
            });
            canvas.add(img);
            canvas.setActiveObject(img);
            canvas.requestRenderAll();
          }, { crossOrigin: "Anonymous" });
        };
        reader.readAsDataURL(file);
      }
      // Reset input so the same files can be re-uploaded
      e.target.value = "";
    }
    // --- PNG LIBRARY ---
    function openPngLibraryModal() {
      showModal({
        title: "PNG Library",
        content: renderPngLibraryModal()
      });
    }
    function renderPngLibraryModal() {
      let wrap = createEl('div', {class:'png-library-modal'});
      // Tabs
      let tabs = createEl('div', {class:'png-library-tabs'});
      let categories = Object.keys(pngLibrary);
      let activeCat = categories[0];
      let grid = null;
      function setActiveTab(cat) {
        activeCat = cat;
        for(let t of tabs.children) {
          t.classList.toggle('active', t.textContent === cat);
        }
        grid.innerHTML = "";
        for(let item of pngLibrary[cat]) {
          let div = createEl('div', {class:'png-library-item', draggable:"true"});
          let img = createEl('img', {src: item.src, alt: item.label});
          div.appendChild(img);
          div.title = item.label;
          div.addEventListener('click', () => {
            fabric.Image.fromURL(item.src, function(imgObj) {
              imgObj.set({
                left: 180+Math.random()*70,
                top: 140+Math.random()*80,
                scaleX: 0.6,
                scaleY: 0.6,
                selectable: true,
                evented: true,
                hasControls: true,
                hasBorders: true,
                lockMovementX: false,
                lockMovementY: false,
                lockScalingX: false,
                lockScalingY: false,
                lockRotation: false,
                cornerStyle: 'circle',
                borderColor: '#23afe3',
                cornerColor: '#ffd700',
                transparentCorners: false
              });
              canvas.add(imgObj);
              canvas.setActiveObject(imgObj);
              canvas.requestRenderAll();
            }, {crossOrigin:"Anonymous"});
            closeModal();
          });
          // Drag-and-drop
          div.addEventListener('dragstart', ev => {
            ev.dataTransfer.setData('text/plain', item.src);
          });
          grid.appendChild(div);
        }
      }
      for(let cat of categories) {
        let tab = createEl('button', {class:'png-library-tab'+(cat===activeCat?' active':''), text:cat});
        tab.addEventListener('click', ()=>setActiveTab(cat));
        tabs.appendChild(tab);
      }
      wrap.appendChild(tabs);
      grid = createEl('div', {class:'png-library-grid'});
      wrap.appendChild(grid);
      setActiveTab(activeCat);
      // Canvas drop handler (for drag from png lib)
      grid.addEventListener('dragend', ev => {
        // No-op
      });
      el('fabric-canvas').addEventListener('dragover', e => e.preventDefault());
      el('fabric-canvas').addEventListener('drop', function(e) {
        let src = e.dataTransfer.getData('text/plain');
        if(src) {
          fabric.Image.fromURL(src, function(imgObj) {
            let rect = el('fabric-canvas').getBoundingClientRect();
            let x = e.clientX - rect.left;
            let y = e.clientY - rect.top;
            imgObj.set({
              left: x-30, top: y-30, scaleX: 0.6, scaleY: 0.6,
              selectable: true,
              evented: true,
              hasControls: true,
              hasBorders: true,
              lockMovementX: false,
              lockMovementY: false,
              lockScalingX: false,
              lockScalingY: false,
              lockRotation: false,
              cornerStyle: 'circle',
              borderColor: '#23afe3',
              cornerColor: '#ffd700',
              transparentCorners: false
            });
            canvas.add(imgObj);
            canvas.setActiveObject(imgObj);
            canvas.requestRenderAll();
          }, {crossOrigin:"Anonymous"});
        }
        closeModal();
      });
      return wrap;
    }
    // --- MODAL ---
    function showModal({title, content}) {
      let backdrop = el('modal-backdrop');
      backdrop.innerHTML = "";
      backdrop.classList.add('active');
      let modal = createEl('div', {class:'modal'});
      let closeBtn = createEl('button', {class:'modal-close-btn', html: '&times;'});
      closeBtn.addEventListener('click', closeModal);
      modal.appendChild(closeBtn);
      if(title) {
        let h = createEl('h2', {text:title, style:'margin-top:0; margin-bottom:18px; font-size:21px;'});
        modal.appendChild(h);
      }
      if(content instanceof HTMLElement) modal.appendChild(content);
      else if(typeof content === "string") {
        modal.innerHTML += content;
      }
      backdrop.appendChild(modal);
      backdrop.addEventListener('click', function(e){
        if(e.target === backdrop) closeModal();
      });
    }
    function closeModal() {
      el('modal-backdrop').classList.remove('active');
      el('modal-backdrop').innerHTML = "";
    }
    // --- LAYERS ---
    function toggleLayerPanel() {
      layerPanelOpen = !layerPanelOpen;
      renderLayerPanel();
    }
    function renderLayerPanel() {
      let panel = el('layer-panel');
      if(!layerPanelOpen) { panel.classList.remove('active'); return; }
      panel.classList.add('active');
      panel.innerHTML = "";
      let head = createEl('div', {class:'layer-panel-header'});
      head.appendChild(createEl('span', {text:'Layers'}));
      let close = createEl('button', {class:'modal-close-btn', html:'&times;'});
      close.style.fontSize = "21px";
      close.addEventListener('click', ()=>{layerPanelOpen=false; renderLayerPanel();});
      head.appendChild(close);
      panel.appendChild(head);
      // List all objects (reverse for z-index)
      let objs = canvas.getObjects().slice().reverse();
      for(let i=0; i < objs.length; i++) {
        let obj = objs[i];
        let div = createEl('div', {class:'layer-item'+(canvas.getActiveObject()===obj?' selected':''), tabindex:0});
        let type = obj.type.charAt(0).toUpperCase() + obj.type.slice(1);
        div.appendChild(createEl('span', {text:type}));
        // Eye for visible
        let eye = createEl('span', {class:'layer-eye', html:'👁️', 'aria-checked': (!obj.visible) ? "false":"true"});
        eye.addEventListener('click', e=>{
          obj.visible = !obj.visible;
          canvas.requestRenderAll();
          renderLayerPanel();
          e.stopPropagation();
        });
        div.appendChild(eye);
        // Lock
        let lock = createEl('span', {class:'layer-lock', html:'🔒', 'aria-checked': obj.lockMovementX ? "true":"false"});
        lock.addEventListener('click', e=>{
          obj.lockMovementX = obj.lockMovementY = obj.lockScalingX = obj.lockScalingY = obj.lockRotation = !obj.lockMovementX;
          renderLayerPanel();
          e.stopPropagation();
        });
        div.appendChild(lock);
        // Z Up/Down
        let up = createEl('button', {html:'⬆️', style:'font-size:13px; margin-left:3px;'});
        up.addEventListener('click', e=>{
          canvas.bringForward(obj, true); renderLayerPanel(); canvas.requestRenderAll(); e.stopPropagation();
        });
        let down = createEl('button', {html:'⬇️', style:'font-size:13px;'});
        down.addEventListener('click', e=>{
          canvas.sendBackwards(obj, true); renderLayerPanel(); canvas.requestRenderAll(); e.stopPropagation();
        });
        div.appendChild(up); div.appendChild(down);
        div.addEventListener('click', ()=>{canvas.setActiveObject(obj); canvas.requestRenderAll(); renderLayerPanel();});
        panel.appendChild(div);
      }
    }
    function updateLayerPanelSelection() {
      if(layerPanelOpen) renderLayerPanel();
    }
    // --- Grid ---
    function drawGrid() {
      let w = canvas.width, h = canvas.height;
      for(let i = Math.ceil(w/gridSpacing); i--;) {
        canvas.add(new fabric.Line([ i*gridSpacing, 0, i*gridSpacing, h ], { stroke: '#eee', selectable: false, evented:false, excludeFromExport:true }));
      }
      for(let i = Math.ceil(h/gridSpacing); i--;) {
        canvas.add(new fabric.Line([ 0, i*gridSpacing, w, i*gridSpacing ], { stroke: '#eee', selectable: false, evented:false, excludeFromExport:true }));
      }
    }
    function snapToGridHandler(e) {
      if(!snappingEnabled) return;
      let obj = e.target;
      obj.set({
        left: Math.round(obj.left / gridSpacing) * gridSpacing,
        top: Math.round(obj.top / gridSpacing) * gridSpacing
      });
    }
    // --- Undo/Redo ---
    function saveHistory() {
      // Debounce? We'll just push for now
      let json = canvas.toDatalessJSON();
      if(historyStep < history.length - 1) history = history.slice(0, historyStep + 1);
      history.push(json);
      historyStep++;
    }
    function undo() {
      if(historyStep <= 0) return;
      historyStep--;
      canvas.loadFromJSON(history[historyStep], ()=>canvas.requestRenderAll());
    }
    function redo() {
      if(historyStep >= history.length - 1) return;
      historyStep++;
      canvas.loadFromJSON(history[historyStep], ()=>canvas.requestRenderAll());
    }
    // --- Keyboard ---
    function handleKeyboardShortcuts(e) {
      if(e.ctrlKey && e.key === "z") { e.preventDefault(); undo(); }
      if(e.ctrlKey && e.key === "y") { e.preventDefault(); redo(); }
      if(e.key === "Delete" || e.key === "Backspace") {
        let obj = canvas.getActiveObject();
        if(obj) { canvas.remove(obj); }
      }
      if(e.ctrlKey && e.key === "g") {
        let objs = canvas.getActiveObjects(); if(objs.length>1) {
          let group = new fabric.Group(objs);
          canvas.discardActiveObject();
          objs.forEach(o=>canvas.remove(o));
          canvas.add(group);
          canvas.setActiveObject(group);
        }
        e.preventDefault();
      }
      if(e.ctrlKey && e.key === "u") {
        let obj = canvas.getActiveObject();
        if(obj && obj.type === "group") {
          obj.toActiveSelection();
          canvas.requestRenderAll();
        }
        e.preventDefault();
      }
      if(e.ctrlKey && e.key === "d") {
        let obj = canvas.getActiveObject();
        if(obj) {
          let clone; obj.clone(function(cloneObj){
            clone = cloneObj;
            clone.set({ left: obj.left+25, top: obj.top+25 });
            canvas.add(clone);
            canvas.setActiveObject(clone);
          });
        }
        e.preventDefault();
      }
      // Arrow keys: move
      let obj = canvas.getActiveObject();
      if(obj && ["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"].includes(e.key)) {
        let delta = e.shiftKey ? 10 : 2;
        if(e.key==="ArrowUp")   obj.top -= delta;
        if(e.key==="ArrowDown") obj.top += delta;
        if(e.key==="ArrowLeft") obj.left -= delta;
        if(e.key==="ArrowRight")obj.left += delta;
        obj.setCoords();
        canvas.requestRenderAll();
        e.preventDefault();
      }
    }
    // --- Image Filters ---
    function applyImageFilters(img) {
      let fx = {};
      for (let t of currentSubToolbar) {
        if(!t.id || !t.id.startsWith("fx-")) continue;
        let input = el(t.id)?.querySelector("input,select");
        if(input) {
          if(t.input === "range") fx[t.id] = parseFloat(input.value);
          else if(t.input === "toggle") fx[t.id] = input.checked;
        }
      }
      let filters = [];
      if(fx["fx-brightness"]) filters.push(new fabric.Image.filters.Brightness({brightness: fx["fx-brightness"]}));
      if(fx["fx-contrast"])   filters.push(new fabric.Image.filters.Contrast({contrast: fx["fx-contrast"]}));
      if(fx["fx-blur"])      filters.push(new fabric.Image.filters.Blur({blur: fx["fx-blur"]}));
      if(fx["fx-saturation"])filters.push(new fabric.Image.filters.Saturation({saturation: fx["fx-saturation"]}));
      if(fx["fx-grayscale"]) filters.push(new fabric.Image.filters.Grayscale());
      if(fx["fx-bw"])        filters.push(new fabric.Image.filters.BlackWhite());
      if(fx["fx-sharpen"])   filters.push(new fabric.Image.filters.Convolute({matrix:[
         0,-1, 0,-1, 5,-1, 0,-1, 0
      ]}));
      if(fx["fx-invert"])    filters.push(new fabric.Image.filters.Invert());
      img.filters = filters;
      img.applyFilters();
      canvas.requestRenderAll();
    }
    // --- Crop ---
    function startCrop() {
      if(cropActive) return;
      cropRect = new fabric.Rect({
        left: canvas.width/4, top: canvas.height/4,
        width: canvas.width/2, height: canvas.height/2,
        fill: "rgba(36,170,230,0.12)", stroke: "#23afe3", strokeDashArray:[6,6], strokeWidth:2,
        selectable: true, hasBorders:true, hasControls:true
      });
      cropActive = true;
      canvas.add(cropRect);
      canvas.setActiveObject(cropRect);
      canvas.requestRenderAll();
    }
    function applyCrop() {
      if(!cropActive || !cropRect) return;
      // Crop all objects to the cropRect bounds
      let left = cropRect.left, top = cropRect.top, w = cropRect.width * cropRect.scaleX, h = cropRect.height * cropRect.scaleY;
      let dataURL = canvas.toDataURL({left, top, width:w, height:h, format:"png"});
      fabric.Image.fromURL(dataURL, function(img){
        img.set({left:20, top:20, scaleX:1, scaleY:1});
        canvas.clear();
        drawGrid();
        canvas.add(img);
        cropActive = false;
        cropRect = null;
        canvas.requestRenderAll();
        saveHistory();
      });
    }
    function cancelCrop() {
      if(!cropActive || !cropRect) return;
      canvas.remove(cropRect);
      cropRect = null;
      cropActive = false;
      canvas.requestRenderAll();
    }
    function setCropPreset(preset) {
      if(!cropRect) return;
      let ratios = {
        a4: [210,297], a5: [148,210], a6: [105,148],
        instagram: [1080,1080], youtube: [1280,720],
        facebook: [1200,628], tiktok: [1080,1920]
      };
      let r = ratios[preset.toLowerCase()];
      if(r) {
        let w = 260, h = 120;
        if(r[0]/r[1] > 1) { w = 300; h = w*r[1]/r[0]; }
        else { h = 260; w = h*r[0]/r[1]; }
        cropRect.set({width:w, height:h, scaleX:1, scaleY:1});
        canvas.requestRenderAll();
      }
    }
    function setCropOrientation(orient) {
      if(!cropRect) return;
      let w = cropRect.width, h = cropRect.height;
      if((orient==="portrait" && w>h) || (orient==="landscape" && h>w)) {
        cropRect.set({width: h, height: w});
        canvas.requestRenderAll();
      }
    }
    // --- Canvas Size Modal ---
    function openCanvasSizeModal() {
      let wrap = createEl('div', {class:'canvas-size-modal'});
      let wIn = createEl('input', {type:"number", min:100, max:5000, value:canvas.width});
      let hIn = createEl('input', {type:"number", min:100, max:5000, value:canvas.height});
      let labelW = createEl('label', {text:'Width (px)'});
      let labelH = createEl('label', {text:'Height (px)'});
      wrap.appendChild(labelW); wrap.appendChild(wIn);
      wrap.appendChild(labelH); wrap.appendChild(hIn);
      // Presets
      let sel = createEl('select', {});
      sel.appendChild(createEl('option', {value:"", text:"-- Preset --"}));
      sel.appendChild(createEl('option', {value:"900x600", text:"Desktop (900x600)"}));
      sel.appendChild(createEl('option', {value:"600x600", text:"Square (600x600)"}));
      sel.appendChild(createEl('option', {value:"1080x1920", text:"Mobile (1080x1920)"}));
      sel.appendChild(createEl('option', {value:"1280x720", text:"YouTube (1280x720)"}));
      sel.addEventListener('change', e=>{
        let [w,h] = e.target.value.split("x").map(Number);
        if(w>0&&h>0){ wIn.value=w; hIn.value=h;}
      });
      wrap.appendChild(sel);
      let actions = createEl('div', {class:'modal-actions'});
      let ok = createEl('button', {text:"Apply"});
      ok.addEventListener('click', ()=>{
        let w = parseInt(wIn.value), h = parseInt(hIn.value);
        if(w>=100 && h>=100) {
          canvas.setWidth(w); canvas.setHeight(h);
          canvas.calcOffset();
          canvas.requestRenderAll();
          closeModal();
        }
      });
      let cancel = createEl('button', {text:"Cancel"});
      cancel.addEventListener('click', closeModal);
      actions.appendChild(ok); actions.appendChild(cancel);
      wrap.appendChild(actions);
      showModal({title:"Canvas Size", content:wrap});
    }
    // --- Export/Import ---
    function exportCanvas(format) {
      let dataURL, fileName = "canvas";
      if(format==="png") {
        dataURL = canvas.toDataURL({format:"png"});
        fileName += ".png";
      } else if(format==="jpg"||format==="jpeg") {
        dataURL = canvas.toDataURL({format:"jpeg"});
        fileName += ".jpg";
      } else if(format==="svg") {
        let svg = canvas.toSVG();
        let blob = new Blob([svg], {type:"image/svg+xml"});
        let url = URL.createObjectURL(blob);
        downloadURL(url, fileName+".svg");
        return;
      } else if(format==="json") {
        let json = JSON.stringify(canvas.toDatalessJSON());
        let blob = new Blob([json], {type:"application/json"});
        let url = URL.createObjectURL(blob);
        downloadURL(url, fileName+".json");
        return;
      }
      if(dataURL) downloadURL(dataURL, fileName);
    }
    function downloadURL(url, fileName) {
      let a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      setTimeout(()=>{document.body.removeChild(a);}, 200);
    }
    function importCanvasJson() {
      let input = createEl('input', {type:"file", accept:".json"});
      input.addEventListener('change', function(e){
        let file = e.target.files[0];
        if(file) {
          let reader = new FileReader();
          reader.onload = function(evt) {
            let json = evt.target.result;
            canvas.loadFromJSON(json, ()=>canvas.requestRenderAll());
          };
          reader.readAsText(file);
        }
      });
      input.click();
    }
    // --- Zoom ---
    function setZoom(zoom) {
      zoom = Math.max(0.1, Math.min(zoom, 10));
      canvas.setZoom(zoom);
      canvas.requestRenderAll();
    }
    // --- Touch with Hammer.js ---
    function setupTouchGestures() {
      let canvasEl = el('fabric-canvas');
      let mc = new Hammer.Manager(canvasEl);
      mc.add(new Hammer.Pan({ direction: Hammer.DIRECTION_ALL, threshold: 2 }));
      mc.add(new Hammer.Pinch({ threshold: 0 }));
      mc.add(new Hammer.Rotate({ threshold: 0 }));
      mc.add(new Hammer.Tap({ event: 'tap', taps: 1 }));
      mc.add(new Hammer.Tap({ event: 'doubletap', taps: 2 }));
      mc.add(new Hammer.Press({ time: 450 }));
      let lastCenter = null, lastScale = 1;
      mc.on("pinchstart", function(e) {
        lastScale = canvas.getZoom();
      });
      mc.on("pinchmove", function(e) {
        setZoom(lastScale * e.scale);
      });
      mc.on("panstart", function(e) {
        isPanning = true;
        touchX = e.center.x;
        touchY = e.center.y;
      });
      mc.on("panmove", function(e) {
        if(isPanning) {
          let delta = { x: e.deltaX, y: e.deltaY };
          canvas.relativePan(delta);
        }
      });
      mc.on("panend", function(e) {
        isPanning = false;
      });
      mc.on("doubletap", function(e) {
        setZoom(1); canvas.absolutePan({x:0,y:0});
      });
    }
    // --- Init everything ---
    function init() {
      // Toolbar listeners
      for(let cat of toolbarCategories) {
        let btn = el('cat-'+cat);
        btn.addEventListener('click', ()=>setToolbarCategory(cat));
      }
      setToolbarCategory(currentCategory);
      // Subtoolbar
      renderSubToolbar(currentCategory);
      // Canvas
      initFabricCanvas();
      // Upload images (main input for mobile)
      el('upload-images-input').addEventListener('change', handleImageUploadInput);
    }
    window.onload = init;
  </script>
</body>
</html>