/**
 * Enhanced CanvasSizeManager - <PERSON>les canvas size presets and adjustments
 * Provides presets for common aspect ratios and ability to toggle orientation
 * Ensures minimum canvas size requirements for quality
 */
class CanvasSizeManager {
  constructor(editorCore) {
    this.editor = editorCore
    this.dropdown = null
    this.orientationToggle = null
    this.isLandscape = false

    this.presets = [
      { name: "1:1 Square", width: 1024, height: 1024, ratio: "1:1" },
      { name: "4:5 Portrait", width: 1024, height: 1280, ratio: "4:5" },
      { name: "5:7 Portrait", width: 1024, height: 1433, ratio: "5:7" },
      { name: "16:9 Widescreen", width: 1280, height: 720, ratio: "16:9" },
      { name: "9:16 Mobile", width: 1080, height: 1920, ratio: "9:16" },
      { name: "4:3 Classic", width: 1024, height: 768, ratio: "4:3" },
      { name: "2:1 Panorama", width: 2048, height: 1024, ratio: "2:1" }
    ]

    this.init()
  }

  /**
   * Initialize the canvas size manager
   */
  init() {
    this.createSizeControls()
  }

  /**
   * Create size controls UI
   */
  createSizeControls() {
    // Create container
    const container = document.createElement("div")
    container.className = "canvas-size-controls"
    container.style.cssText = `
      position: absolute;
      top: 20px;
      left: 20px;
      z-index: 100;
      display: flex;
      gap: 8px;
      align-items: center;
    `

    // Create dropdown
    this.dropdown = document.createElement("select")
    this.dropdown.style.cssText = `
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      background: white;
      cursor: pointer;
    `

    // Add options to dropdown
    this.presets.forEach((preset, index) => {
      const option = document.createElement("option")
      option.value = index.toString()
      option.textContent = `${preset.name} (${preset.width}x${preset.height})`
      this.dropdown.appendChild(option)
    })

    // Create orientation toggle
    this.orientationToggle = document.createElement("button")
    this.orientationToggle.innerHTML = "🔄 Flip Orientation"
    this.orientationToggle.style.cssText = `
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      background: white;
      cursor: pointer;
    `

    // Add event listeners
    this.dropdown.addEventListener("change", () => this.handleSizeChange())
    this.orientationToggle.addEventListener("click", () => this.toggleOrientation())

    // Add elements to container
    container.appendChild(this.dropdown)
    container.appendChild(this.orientationToggle)

    // Add container to document
    document.body.appendChild(container)
  }

  /**
   * Handle size change from dropdown
   */
  handleSizeChange() {
    const selectedIndex = Number.parseInt(this.dropdown.value)
    const preset = this.presets[selectedIndex]

    let width = preset.width
    let height = preset.height

    // Apply orientation if needed
    if (this.isLandscape && width < height) {
      ;[width, height] = [height, width]
    } else if (!this.isLandscape && width > height) {
      ;[width, height] = [height, width]
    }

    this.resizeCanvas(width, height)
  }

  /**
   * Toggle orientation between portrait and landscape
   */
  toggleOrientation() {
    this.isLandscape = !this.isLandscape

    // Update button text
    this.orientationToggle.innerHTML = this.isLandscape ? "🔄 Portrait" : "🔄 Landscape"

    // Apply the change
    this.handleSizeChange()
  }

  /**
   * Resize the canvas to the specified dimensions
   */
  resizeCanvas(width, height) {
    // Ensure minimum width
    width = Math.max(width, 1024)

    // Get current canvas content
    const json = this.editor.canvas.toJSON()

    // Set new dimensions
    this.editor.canvas.setWidth(width)
    this.editor.canvas.setHeight(height)

    // Restore content
    this.editor.canvas.loadFromJSON(json, () => {
      this.editor.canvas.renderAll()

      // Center objects if needed
      this.centerObjects()

      // Save state and sync with 3D
      this.editor.history.saveState()
      this.editor.threeDSync.syncWithThreeJs()
    })
  }

  /**
   * Center objects on the canvas after resize
   */
  centerObjects() {
    const objects = this.editor.canvas.getObjects()
    const canvasCenter = {
      x: this.editor.canvas.width / 2,
      y: this.editor.canvas.height / 2,
    }

    // Skip if no objects
    if (objects.length === 0) return

    // Calculate current center of all objects
    let minX = Number.MAX_VALUE
    let minY = Number.MAX_VALUE
    let maxX = Number.MIN_VALUE
    let maxY = Number.MIN_VALUE

    objects.forEach((obj) => {
      const objBounds = obj.getBoundingRect()
      minX = Math.min(minX, objBounds.left)
      minY = Math.min(minY, objBounds.top)
      maxX = Math.max(maxX, objBounds.left + objBounds.width)
      maxY = Math.max(maxY, objBounds.top + objBounds.height)
    })

    const objectsCenter = {
      x: (minX + maxX) / 2,
      y: (minY + maxY) / 2,
    }

    // Calculate offset
    const offsetX = canvasCenter.x - objectsCenter.x
    const offsetY = canvasCenter.y - objectsCenter.y

    // Move all objects
    objects.forEach((obj) => {
      obj.set({
        left: obj.left + offsetX,
        top: obj.top + offsetY,
      })
      obj.setCoords()
    })

    this.editor.canvas.renderAll()
  }

  /**
   * Get current canvas dimensions
   */
  getCurrentDimensions() {
    return {
      width: this.editor.canvas.width,
      height: this.editor.canvas.height,
    }
  }
}
