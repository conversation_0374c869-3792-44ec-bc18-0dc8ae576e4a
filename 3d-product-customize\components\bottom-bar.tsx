"use client"

import { But<PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Download, Camera, Type, ChevronDown, Maximize } from "lucide-react"

interface BottomBarProps {
  setShowTextEditor: (show: boolean) => void
}

export function BottomBar({ setShowTextEditor }: BottomBarProps) {
  return (
    <div className="absolute bottom-0 left-0 right-0 z-10 flex flex-col">
      <div className="flex items-center justify-center gap-2 overflow-x-auto p-2">
        <Button
          variant="outline"
          className="border-[#D6A25E] bg-[#1F2A45] text-[#D6A25E] hover:bg-[#D6A25E] hover:text-[#1F2A45]"
        >
          لطيف
        </Button>
        <Button
          variant="outline"
          className="border-[#D6A25E] bg-[#1F2A45] text-[#D6A25E] hover:bg-[#D6A25E] hover:text-[#1F2A45]"
        >
          روضة
        </Button>
        <Button
          variant="outline"
          className="border-[#D6A25E] bg-[#1F2A45] text-[#D6A25E] hover:bg-[#D6A25E] hover:text-[#1F2A45]"
        >
          القاهرة
        </Button>
        <Button
          variant="outline"
          className="border-[#D6A25E] bg-[#1F2A45] text-[#D6A25E] hover:bg-[#D6A25E] hover:text-[#1F2A45]"
        >
          كوفاح
        </Button>
        <Button
          variant="outline"
          className="border-[#D6A25E] bg-[#1F2A45] text-[#D6A25E] hover:bg-[#D6A25E] hover:text-[#1F2A45]"
        >
          الشارقة
        </Button>
      </div>

      <div className="flex items-center justify-between bg-[#1F2A45] p-2">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="icon"
            className="h-10 w-10 border-[#D6A25E] bg-[#1F2A45] text-[#D6A25E] hover:bg-[#D6A25E] hover:text-[#1F2A45]"
          >
            <Camera className="h-5 w-5" />
          </Button>

          <Button
            variant="outline"
            size="icon"
            className="h-10 w-10 border-[#D6A25E] bg-[#1F2A45] text-[#D6A25E] hover:bg-[#D6A25E] hover:text-[#1F2A45]"
            onClick={() => setShowTextEditor((prev) => !prev)}
          >
            <Type className="h-5 w-5" />
          </Button>

          <Button
            variant="outline"
            size="icon"
            className="h-10 w-10 border-[#D6A25E] bg-[#1F2A45] text-[#D6A25E] hover:bg-[#D6A25E] hover:text-[#1F2A45]"
          >
            <Maximize className="h-5 w-5" />
          </Button>
        </div>

        <div className="flex items-center gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                className="border-[#D6A25E] bg-[#1F2A45] text-[#D6A25E] hover:bg-[#D6A25E] hover:text-[#1F2A45]"
              >
                1920×1080 (HD) <ChevronDown className="ml-1 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>1920×1080 (HD)</DropdownMenuItem>
              <DropdownMenuItem>3840×2160 (4K)</DropdownMenuItem>
              <DropdownMenuItem>1280×720 (720p)</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                className="border-[#D6A25E] bg-[#1F2A45] text-[#D6A25E] hover:bg-[#D6A25E] hover:text-[#1F2A45]"
              >
                MP4 <ChevronDown className="ml-1 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>MP4</DropdownMenuItem>
              <DropdownMenuItem>PNG</DropdownMenuItem>
              <DropdownMenuItem>JPG</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Button
            variant="outline"
            className="border-[#D6A25E] bg-[#1F2A45] text-[#D6A25E] hover:bg-[#D6A25E] hover:text-[#1F2A45]"
          >
            <Download className="mr-1 h-4 w-4" /> تنزيل
          </Button>
        </div>
      </div>
    </div>
  )
}
