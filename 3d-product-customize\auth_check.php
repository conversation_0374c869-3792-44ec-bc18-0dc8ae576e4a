<?php
/**
 * Authentication and Subscription Check
 * 
 * This file provides functionality to:
 * 1. Check if user is logged in
 * 2. Verify if user has an active subscription
 * 3. Enforce the service lock mechanism
 */

// Include configuration file
require_once 'config.php';

/**
 * Checks if the user is logged in
 * If not, redirects to login page
 * 
 * @return boolean True if user is logged in, false otherwise
 */
function isLoggedIn() {
    if (!isset($_SESSION['user_id'])) {
        return false;
    }
    return true;
}

/**
 * Redirects to login page
 */
function redirectToLogin() {
    header("Location: login.php");
    exit;
}

/**
 * Checks if user has an active subscription
 * 
 * @param mysqli $conn Database connection
 * @param int $userId User ID
 * @return array|false Subscription data if active, false otherwise
 */
function hasActiveSubscription($conn, $userId) {
    $sql = "SELECT s.*, 
                DATE_FORMAT(s.expiry_date, '%M %d, %Y') as formatted_expiry_date,
                DATE_FORMAT(s.next_renewal_date, '%M %d, %Y') as formatted_renewal_date
            FROM subscriptions s 
            WHERE s.user_id = ? AND s.status = 'active'
            ORDER BY s.expiry_date DESC LIMIT 1";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $subscription = $result->fetch_assoc();
        
        // Check if subscription has expired
        $now = new DateTime();
        $expiryDate = new DateTime($subscription['expiry_date']);
        
        if ($now > $expiryDate) {
            // Update subscription status to expired
            $updateSql = "UPDATE subscriptions SET status = 'expired' WHERE subscription_id = ?";
            $updateStmt = $conn->prepare($updateSql);
            $updateStmt->bind_param("i", $subscription['subscription_id']);
            $updateStmt->execute();
            $updateStmt->close();
            
            // Subscription has expired
            return false;
        }
        
        return $subscription;
    }
    
    $stmt->close();
    return false;
}

/**
 * Redirects to subscription page
 */
function redirectToSubscription() {
    header("Location: subscription.php");
    exit;
}

/**
 * Updates download counters for user
 * 
 * @param mysqli $conn Database connection
 * @param int $userId User ID
 * @param string $fileType Type of file ('mp4', 'web', 'jpg', 'png')
 * @param string $fileName Name of the file
 * @return boolean True if update successful, false otherwise
 */
function recordDownload($conn, $userId, $fileType, $fileName) {
    // First, check if user has an active subscription
    $subscription = hasActiveSubscription($conn, $userId);
    
    if (!$subscription) {
        return false;
    }
    
    // Insert into download history
    $historySql = "INSERT INTO download_history (user_id, file_type, file_name) VALUES (?, ?, ?)";
    $historyStmt = $conn->prepare($historySql);
    $historyStmt->bind_param("iss", $userId, $fileType, $fileName);
    $historyResult = $historyStmt->execute();
    $historyStmt->close();
    
    // Update counters in subscription
    $updateSql = "";
    if ($fileType === 'mp4' || $fileType === 'web') {
        $updateSql = "UPDATE subscriptions SET mp4_web_video_used = mp4_web_video_used + 1 
                      WHERE subscription_id = ?";
    } elseif ($fileType === 'jpg' || $fileType === 'png') {
        $updateSql = "UPDATE subscriptions SET jpg_png_image_used = jpg_png_image_used + 1 
                      WHERE subscription_id = ?";
    } else {
        return false;
    }
    
    $updateStmt = $conn->prepare($updateSql);
    $updateStmt->bind_param("i", $subscription['subscription_id']);
    $updateResult = $updateStmt->execute();
    $updateStmt->close();
    
    return $historyResult && $updateResult;
}

/**
 * Checks if user has reached download limits
 * 
 * @param mysqli $conn Database connection
 * @param int $userId User ID
 * @param string $fileType Type of file ('video' or 'image')
 * @return boolean True if limit reached, false otherwise
 */
function hasReachedDownloadLimit($conn, $userId, $fileType) {
    $subscription = hasActiveSubscription($conn, $userId);
    
    if (!$subscription) {
        return true; // No active subscription means limit reached
    }
    
    if ($fileType === 'video') {
        return $subscription['mp4_web_video_used'] >= $subscription['mp4_web_video_limit'];
    } elseif ($fileType === 'image') {
        return $subscription['jpg_png_image_used'] >= $subscription['jpg_png_image_limit'];
    }
    
    return true; // Unknown file type means limit reached
}

/**
 * Enforces authentication - redirects to login if not logged in
 */
function enforceAuth() {
    if (!isLoggedIn()) {
        redirectToLogin();
    }
}

/**
 * Enforces subscription - redirects to subscription page if no active subscription
 * 
 * @param mysqli $conn Database connection
 */
function enforceSubscription($conn) {
    enforceAuth();
    
    $userId = $_SESSION['user_id'];
    $subscription = hasActiveSubscription($conn, $userId);
    
    if (!$subscription) {
        $_SESSION['subscription_status'] = 'none';
        redirectToSubscription();
    }
    
    $_SESSION['subscription_status'] = 'active';
    $_SESSION['subscription_expiry'] = $subscription['expiry_date'];
    $_SESSION['subscription_type'] = $subscription['plan_type'];
}

/**
 * Gets remaining download counts for user
 * 
 * @param mysqli $conn Database connection
 * @param int $userId User ID
 * @return array Array with remaining download counts
 */
function getRemainingDownloads($conn, $userId) {
    $subscription = hasActiveSubscription($conn, $userId);
    
    $remaining = [
        'video' => 0,
        'image' => 0
    ];
    
    if ($subscription) {
        $remaining['video'] = max(0, $subscription['mp4_web_video_limit'] - $subscription['mp4_web_video_used']);
        $remaining['image'] = max(0, $subscription['jpg_png_image_limit'] - $subscription['jpg_png_image_used']);
    }
    
    return $remaining;
}
?>
