// ملف دعم اللغة العربية
document.addEventListener('DOMContentLoaded', function() {
    // تطبيق الخطوط العربية
    applyArabicFonts();
    
    // إعداد اتجاه النص
    setupTextDirection();
    
    // تحسين عرض النصوص العربية
    enhanceArabicDisplay();
});

// تطبيق الخطوط العربية
function applyArabicFonts() {
    const arabicElements = document.querySelectorAll('[lang="ar"], .arabic-text');
    arabicElements.forEach(element => {
        if (!element.style.fontFamily) {
            element.style.fontFamily = 'Tajawal, Almarai, Cairo, sans-serif';
        }
    });
}

// إعداد اتجاه النص
function setupTextDirection() {
    const currentLang = document.documentElement.lang || 'ar';
    
    if (currentLang === 'ar') {
        document.body.style.direction = 'rtl';
        document.body.style.textAlign = 'right';
    } else {
        document.body.style.direction = 'ltr';
        document.body.style.textAlign = 'left';
    }
}

// تحسين عرض النصوص العربية
function enhanceArabicDisplay() {
    // تحسين عرض الأرقام العربية
    const numberElements = document.querySelectorAll('.arabic-numbers');
    numberElements.forEach(element => {
        element.style.unicodeBidi = 'embed';
        element.style.direction = 'ltr';
    });
    
    // تحسين عرض النصوص المختلطة
    const mixedTextElements = document.querySelectorAll('.mixed-text');
    mixedTextElements.forEach(element => {
        element.style.unicodeBidi = 'embed';
    });
}

// تحويل الأرقام إلى العربية
function convertToArabicNumbers(text) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return text.replace(/[0-9]/g, function(match) {
        return arabicNumbers[parseInt(match)];
    });
}

// تحويل الأرقام إلى الإنجليزية
function convertToEnglishNumbers(text) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return text.replace(/[٠-٩]/g, function(match) {
        return arabicNumbers.indexOf(match).toString();
    });
}

// تطبيق التنسيق العربي على عنصر
function applyArabicFormatting(element) {
    element.style.fontFamily = 'Tajawal, Almarai, Cairo, sans-serif';
    element.style.direction = 'rtl';
    element.style.textAlign = 'right';
    element.style.lineHeight = '1.6';
}

// إزالة التنسيق العربي من عنصر
function removeArabicFormatting(element) {
    element.style.direction = 'ltr';
    element.style.textAlign = 'left';
    element.style.fontFamily = '';
}

// تصدير الوظائف
window.convertToArabicNumbers = convertToArabicNumbers;
window.convertToEnglishNumbers = convertToEnglishNumbers;
window.applyArabicFormatting = applyArabicFormatting;
window.removeArabicFormatting = removeArabicFormatting;

