/**
 * main.js
 * الملف الرئيسي للتطبيق الذي يربط بين جميع المكونات
 */

// المتغيرات العامة
let fabricCanvas;
let threeSceneInstance;
let interactionInstance;

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة لوحة Fabric.js
    initFabricCanvas();
    
    // تهيئة مشهد Three.js
    initThreeScene();
    
    // تهيئة التفاعل بين Fabric.js و Three.js
    initInteraction();
    
    // إعداد أشرطة الأدوات
    setupToolbars();
    
    // إعداد أزرار التحكم
    setupControls();
    
    // إعداد قائمة اختيار حجم اللوحة
    setupCanvasSizeSelector();
    
    // إعداد سلوك الاستجابة التلقائي
    setupResponsiveBehavior();
});

// تهيئة لوحة Fabric.js
function initFabricCanvas() {
    fabricCanvas = fabricTools.initFabricCanvas('fabric-canvas');
    
    // تعيين حجم اللوحة الافتراضي
    fabricTools.setCanvasPreset(fabricCanvas, 'instagram');
}

// تهيئة مشهد Three.js
function initThreeScene() {
    threeSceneInstance = threeScene.init('three-container');
}

// تهيئة التفاعل بين Fabric.js و Three.js
function initInteraction() {
    interactionInstance = interaction.init(fabricCanvas, threeSceneInstance);
}

// إعداد أشرطة الأدوات
function setupToolbars() {
    const toolbar = document.querySelector('.toolbar');
    const subtoolbar = document.querySelector('.subtoolbar');
    
    // إضافة أزرار مجموعات الأدوات إلى شريط الأدوات الرئيسي
    for (const groupKey in fabricTools.toolGroups) {
        const group = fabricTools.toolGroups[groupKey];
        const button = document.createElement('button');
        button.textContent = `${group.icon} ${group.name}`;
        button.dataset.group = groupKey;
        button.addEventListener('click', function() {
            showSubToolbar(groupKey);
        });
        toolbar.appendChild(button);
    }
    
    // إظهار شريط الأدوات الفرعي للمجموعة المحددة
    function showSubToolbar(groupKey) {
        // تحديد الزر النشط في شريط الأدوات الرئيسي
        const toolbarButtons = toolbar.querySelectorAll('button');
        toolbarButtons.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.group === groupKey);
        });
        
        // مسح شريط الأدوات الفرعي
        subtoolbar.innerHTML = '';
        
        // إضافة أزرار الأدوات إلى شريط الأدوات الفرعي
        const tools = fabricTools.toolGroups[groupKey].tools;
        for (const toolKey in tools) {
            const tool = tools[toolKey];
            const button = document.createElement('button');
            button.textContent = `${tool.icon} ${tool.name}`;
            button.dataset.tool = toolKey;
            button.addEventListener('click', function() {
                handleToolClick(groupKey, toolKey);
            });
            subtoolbar.appendChild(button);
        }
    }
    
    // معالجة النقر على أداة
    function handleToolClick(groupKey, toolKey) {
        // تحديد الزر النشط في شريط الأدوات الفرعي
        const subtoolbarButtons = subtoolbar.querySelectorAll('button');
        subtoolbarButtons.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.tool === toolKey);
        });
        
        // تنفيذ الإجراء المناسب بناءً على الأداة المحددة
        switch (groupKey) {
            case 'shapes':
                handleShapeTool(toolKey);
                break;
            case 'text':
                handleTextTool(toolKey);
                break;
            case 'images':
                handleImageTool(toolKey);
                break;
            case 'drawing':
                handleDrawingTool(toolKey);
                break;
            case 'filters':
                handleFilterTool(toolKey);
                break;
            case 'controls':
                handleControlTool(toolKey);
                break;
            case 'background':
                handleBackgroundTool(toolKey);
                break;
            case 'textEditing':
                handleTextEditingTool(toolKey);
                break;
            case 'exportImport':
                handleExportImportTool(toolKey);
                break;
            case 'history':
                handleHistoryTool(toolKey);
                break;
            case 'layers':
                handleLayerTool(toolKey);
                break;
        }
    }
    
    // معالجة أدوات الأشكال
    function handleShapeTool(toolKey) {
        switch (toolKey) {
            case 'rectangle':
                fabricTools.addRectangle(fabricCanvas);
                break;
            case 'circle':
                fabricTools.addCircle(fabricCanvas);
                break;
            case 'triangle':
                fabricTools.addTriangle(fabricCanvas);
                break;
            case 'line':
                fabricTools.addLine(fabricCanvas);
                break;
            case 'arrow':
                fabricTools.addArrow(fabricCanvas);
                break;
        }
    }
    
    // معالجة أدوات النص
    function handleTextTool(toolKey) {
        switch (toolKey) {
            case 'text':
                fabricTools.addText(fabricCanvas);
                break;
            case 'itext':
                fabricTools.addIText(fabricCanvas);
                break;
            case 'textbox':
                fabricTools.addTextbox(fabricCanvas);
                break;
        }
    }
    
    // معالجة أدوات الصور
    function handleImageTool(toolKey) {
        switch (toolKey) {
            case 'uploadImage':
                // إنشاء عنصر إدخال ملف مؤقت
                const fileInput = document.createElement('input');
                fileInput.type = 'file';
                fileInput.accept = 'image/*';
                fileInput.style.display = 'none';
                document.body.appendChild(fileInput);
                
                fileInput.addEventListener('change', function(e) {
                    if (e.target.files && e.target.files[0]) {
                        fabricTools.uploadImage(fabricCanvas, e.target.files[0]);
                    }
                    document.body.removeChild(fileInput);
                });
                
                fileInput.click();
                break;
            case 'imageFromURL':
                const url = prompt('أدخل رابط الصورة:');
                if (url) {
                    fabricTools.addImageFromURL(fabricCanvas, url);
                }
                break;
        }
    }
    
    // معالجة أدوات الرسم
    function handleDrawingTool(toolKey) {
        switch (toolKey) {
            case 'pencil':
                fabricTools.enablePencilBrush(fabricCanvas);
                break;
            case 'brush':
                fabricTools.enableBrush(fabricCanvas);
                break;
            case 'eraser':
                fabricTools.enableEraser(fabricCanvas);
                break;
        }
    }
    
    // معالجة أدوات الفلاتر
    function handleFilterTool(toolKey) {
        fabricTools.applyFilter(fabricCanvas, toolKey);
    }
    
    // معالجة أدوات التحكم
    function handleControlTool(toolKey) {
        switch (toolKey) {
            case 'move':
                fabricTools.disableDrawingMode(fabricCanvas);
                break;
            case 'resize':
                // تفعيل وضع تغيير الحجم
                break;
            case 'rotate':
                // تفعيل وضع التدوير
                break;
            case 'skew':
                // تفعيل وضع الإمالة
                break;
            case 'zIndex':
                // فتح قائمة ترتيب الطبقات
                const activeObject = fabricCanvas.getActiveObject();
                if (activeObject) {
                    const actions = ['إحضار إلى المقدمة', 'إرسال إلى الخلف', 'إحضار للأمام', 'إرسال للخلف'];
                    const action = prompt(`اختر إجراء ترتيب الطبقات:\n${actions.join('\n')}`);
                    
                    if (action === actions[0]) {
                        activeObject.bringToFront();
                    } else if (action === actions[1]) {
                        activeObject.sendToBack();
                    } else if (action === actions[2]) {
                        activeObject.bringForward();
                    } else if (action === actions[3]) {
                        activeObject.sendBackwards();
                    }
                    
                    fabricCanvas.renderAll();
                }
                break;
            case 'snapToGrid':
                // تفعيل/تعطيل اللصق على الشبكة
                break;
            case 'lockUnlock':
                // قفل/إلغاء قفل الكائن المحدد
                const obj = fabricCanvas.getActiveObject();
                if (obj) {
                    obj.set('lockMovementX', !obj.lockMovementX);
                    obj.set('lockMovementY', !obj.lockMovementY);
                    obj.set('lockRotation', !obj.lockRotation);
                    obj.set('lockScalingX', !obj.lockScalingX);
                    obj.set('lockScalingY', !obj.lockScalingY);
                    fabricCanvas.renderAll();
                }
                break;
        }
    }
    
    // معالجة أدوات الخلفية
    function handleBackgroundTool(toolKey) {
        switch (toolKey) {
            case 'backgroundColor':
                const color = prompt('أدخل لون الخلفية (مثل #ffffff أو red):');
                if (color) {
                    fabricTools.setBackgroundColor(fabricCanvas, color);
                }
                break;
            case 'grid':
                // تفعيل/تعطيل الشبكة
                break;
            case 'alignmentGuides':
                // تفعيل/تعطيل مساعدات المحاذاة
                break;
        }
    }
    
    // معالجة أدوات تحرير النص
    function handleTextEditingTool(toolKey) {
        const activeObject = fabricCanvas.getActiveObject();
        if (!activeObject || !activeObject.isType('text') && !activeObject.isType('i-text') && !activeObject.isType('textbox')) {
            alert('يرجى تحديد كائن نص أولاً');
            return;
        }
        
        switch (toolKey) {
            case 'fontFamily':
                const fonts = ['Arial', 'Times New Roman', 'Courier New', 'Georgia', 'Verdana', 'Tahoma'];
                const font = prompt(`اختر الخط:\n${fonts.join('\n')}`);
                if (font && fonts.includes(font)) {
                    activeObject.set('fontFamily', font);
                    fabricCanvas.renderAll();
                }
                break;
            case 'fontSize':
                const size = prompt('أدخل حجم الخط:');
                if (size && !isNaN(size)) {
                    activeObject.set('fontSize', parseInt(size));
                    fabricCanvas.renderAll();
                }
                break;
            case 'alignment':
                const alignments = ['right', 'center', 'left', 'justify'];
                const alignment = prompt(`اختر المحاذاة:\n${alignments.join('\n')}`);
                if (alignment && alignments.includes(alignment)) {
                    activeObject.set('textAlign', alignment);
                    fabricCanvas.renderAll();
                }
                break;
            case 'textColor':
                const color = prompt('أدخل لون النص (مثل #000000 أو black):');
                if (color) {
                    activeObject.set('fill', color);
                    fabricCanvas.renderAll();
                }
                break;
            case 'textShadow':
                const shadowColor = prompt('أدخل لون الظل (مثل #000000 أو black):');
                if (shadowColor) {
                    activeObject.set('shadow', new fabric.Shadow({
                        color: shadowColor,
                        blur: 5,
                        offsetX: 5,
                        offsetY: 5
                    }));
                    fabricCanvas.renderAll();
                }
                break;
        }
    }
    
    // معالجة أدوات التصدير/الاستيراد
    function handleExportImportTool(toolKey) {
        switch (toolKey) {
            case 'exportJSON':
                const json = fabricTools.exportToJSON(fabricCanvas);
                downloadFile('canvas.json', json, 'application/json');
                break;
            case 'importJSON':
                // إنشاء عنصر إدخال ملف مؤقت
                const fileInput = document.createElement('input');
                fileInput.type = 'file';
                fileInput.accept = '.json';
                fileInput.style.display = 'none';
                document.body.appendChild(fileInput);
                
                fileInput.addEventListener('change', function(e) {
                    if (e.target.files && e.target.files[0]) {
                        const reader = new FileReader();
                        reader.onload = function(event) {
                            fabricTools.importFromJSON(fabricCanvas, event.target.result);
                        };
                        reader.readAsText(e.target.files[0]);
                    }
                    document.body.removeChild(fileInput);
                });
                
                fileInput.click();
                break;
            case 'exportSVG':
                const svg = fabricTools.exportToSVG(fabricCanvas);
                downloadFile('canvas.svg', svg, 'image/svg+xml');
                break;
            case 'importSVG':
                // إنشاء عنصر إدخال ملف مؤقت
                const svgInput = document.createElement('input');
                svgInput.type = 'file';
                svgInput.accept = '.svg';
                svgInput.style.display = 'none';
                document.body.appendChild(svgInput);
                
                svgInput.addEventListener('change', function(e) {
                    if (e.target.files && e.target.files[0]) {
                        const reader = new FileReader();
                        reader.onload = function(event) {
                            fabricTools.importSVG(fabricCanvas, event.target.result);
                        };
                        reader.readAsText(e.target.files[0]);
                    }
                    document.body.removeChild(svgInput);
                });
                
                svgInput.click();
                break;
            case 'exportPNG':
                const png = fabricTools.exportToPNG(fabricCanvas);
                downloadFile('canvas.png', png, 'image/png');
                break;
            case 'exportJPEG':
                const jpeg = fabricTools.exportToJPEG(fabricCanvas);
                downloadFile('canvas.jpg', jpeg, 'image/jpeg');
                break;
        }
    }
    
    // معالجة أدوات التاريخ
    function handleHistoryTool(toolKey) {
        switch (toolKey) {
            case 'undo':
                fabricTools.undo(fabricCanvas);
                break;
            case 'redo':
                fabricTools.redo(fabricCanvas);
                break;
            case 'copy':
                fabricTools.copySelectedObject(fabricCanvas);
                break;
            case 'paste':
                fabricTools.pasteObject(fabricCanvas);
                break;
            case 'delete':
                fabricTools.deleteSelectedObject(fabricCanvas);
                break;
        }
    }
    
    // معالجة أدوات الطبقات
    function handleLayerTool(toolKey) {
        switch (toolKey) {
            case 'layerPanel':
                // فتح لوحة الطبقات
                alert('لوحة الطبقات غير متاحة حاليًا');
                break;
        }
    }
    
    // إظهار شريط الأدوات الفرعي للمجموعة الأولى افتراضيًا
    showSubToolbar(Object.keys(fabricTools.toolGroups)[0]);
}

// إعداد أزرار التحكم
function setupControls() {
    // زر تبديل موضع اللوحة
    const togglePositionBtn = document.getElementById('toggle-position');
    togglePositionBtn.addEventListener('click', function() {
        const container = document.querySelector('.container');
        
        // تبديل الصفوف بين النصف العلوي والسفلي في الشاشات الضيقة
        if (window.innerWidth < 768) {
            container.classList.toggle('fabric-top');
        } 
        // تبديل الأعمدة بين اليمين واليسار في الشاشات العريضة
        else {
            container.classList.toggle('fabric-left');
        }
        
        // تحديث حجم المُصيِّر
        threeSceneInstance.onWindowResize();
    });
    
    // زر إظهار/إخفاء اللوحة
    const toggleCanvasBtn = document.getElementById('toggle-canvas');
    toggleCanvasBtn.addEventListener('click', function() {
        const fabricSection = document.querySelector('.fabric-section');
        fabricSection.classList.toggle('hidden');
        
        // تحديث نص الزر
        this.textContent = fabricSection.classList.contains('hidden') ? 'إظهار اللوحة' : 'إخفاء اللوحة';
        
        // تحديث حجم المُصيِّر
        threeSceneInstance.onWindowResize();
    });
}

// إعداد قائمة اختيار حجم اللوحة
function setupCanvasSizeSelector() {
    const canvasSizeSelector = document.getElementById('canvas-size');
    canvasSizeSelector.addEventListener('change', function() {
        fabricTools.setCanvasPreset(fabricCanvas, this.value);
    });
}

// إعداد سلوك الاستجابة التلقائي
function setupResponsiveBehavior() {
    // تطبيق التخطيط المناسب بناءً على حجم الشاشة
    function applyResponsiveLayout() {
        const container = document.querySelector('.container');
        
        // في الشاشات الضيقة، ضع اللوحة في الأسفل
        if (window.innerWidth < 768) {
            container.classList.remove('fabric-left');
            container.classList.remove('fabric-top');
        } 
        // في الشاشات العريضة، ضع اللوحة على اليمين
        else {
            container.classList.remove('fabric-top');
            container.classList.add('fabric-left');
        }
        
        // تحديث حجم المُصيِّر
        if (threeSceneInstance) {
            threeSceneInstance.onWindowResize();
        }
    }
    
    // تطبيق التخطيط المستجيب عند تحميل الصفحة
    applyResponsiveLayout();
    
    // تطبيق التخطيط المستجيب عند تغيير حجم النافذة
    window.addEventListener('resize', applyResponsiveLayout);
    
    // تطبيق التخطيط المستجيب عند تغيير اتجاه الشاشة
    window.addEventListener('orientationchange', applyResponsiveLayout);
}

// وظيفة مساعدة لتنزيل الملفات
function downloadFile(filename, content, contentType) {
    const element = document.createElement('a');
    
    // إذا كان المحتوى يبدأ بـ data:، فهو بالفعل URL بيانات
    const dataUrl = content.startsWith('data:') ? content : `data:${contentType};charset=utf-8,${encodeURIComponent(content)}`;
    
    element.setAttribute('href', dataUrl);
    element.setAttribute('download', filename);
    element.style.display = 'none';
    
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
}
