{"asset": {"version": "2.0", "generator": "babylon.js glTF exporter for 3dsmax 2021 v20210607.3"}, "scene": 0, "scenes": [{"nodes": [0]}], "nodes": [{"mesh": 0, "translation": [0.0, -1.14751673, -5.015955e-08], "name": "Cylinder001"}], "meshes": [{"primitives": [{"attributes": {"POSITION": 1, "NORMAL": 2, "TEXCOORD_0": 3}, "indices": 0, "material": 0}, {"attributes": {"POSITION": 5, "NORMAL": 6, "TEXCOORD_0": 7}, "indices": 4, "material": 1}, {"attributes": {"POSITION": 9, "NORMAL": 10, "TEXCOORD_0": 11}, "indices": 8, "material": 2}, {"attributes": {"POSITION": 13, "NORMAL": 14, "TEXCOORD_0": 15}, "indices": 12, "material": 3}, {"attributes": {"POSITION": 17, "NORMAL": 18, "TEXCOORD_0": 19}, "indices": 16, "material": 4}], "name": "Cylinder001"}], "accessors": [{"bufferView": 0, "componentType": 5123, "count": 858, "type": "SCALAR", "name": "accessorIndices"}, {"bufferView": 1, "componentType": 5126, "count": 858, "max": [0.9673312, -0.0665806457, 0.9673312], "min": [-0.9673312, -0.09678768, -0.9673312], "type": "VEC3", "name": "accessorPositions"}, {"bufferView": 1, "byteOffset": 10296, "componentType": 5126, "count": 858, "type": "VEC3", "name": "accessorNormals"}, {"bufferView": 2, "componentType": 5126, "count": 858, "type": "VEC2", "name": "accessorUVs"}, {"bufferView": 0, "byteOffset": 1716, "componentType": 5123, "count": 14592, "type": "SCALAR", "name": "accessorIndices"}, {"bufferView": 1, "byteOffset": 20592, "componentType": 5126, "count": 14592, "max": [1.01214123, 2.33892131, 1.02725148], "min": [-1.01214123, -0.09678768, -1.01214123], "type": "VEC3", "name": "accessorPositions"}, {"bufferView": 1, "byteOffset": 195696, "componentType": 5126, "count": 14592, "type": "VEC3", "name": "accessorNormals"}, {"bufferView": 2, "byteOffset": 6864, "componentType": 5126, "count": 14592, "type": "VEC2", "name": "accessorUVs"}, {"bufferView": 0, "byteOffset": 30900, "componentType": 5123, "count": 1620, "type": "SCALAR", "name": "accessorIndices"}, {"bufferView": 1, "byteOffset": 370800, "componentType": 5126, "count": 1620, "max": [1.006612, 2.3633275, 1.006612], "min": [-1.006612, 2.338921, -1.006612], "type": "VEC3", "name": "accessorPositions"}, {"bufferView": 1, "byteOffset": 390240, "componentType": 5126, "count": 1620, "type": "VEC3", "name": "accessorNormals"}, {"bufferView": 2, "byteOffset": 123600, "componentType": 5126, "count": 1620, "type": "VEC2", "name": "accessorUVs"}, {"bufferView": 0, "byteOffset": 34140, "componentType": 5123, "count": 318, "type": "SCALAR", "name": "accessorIndices"}, {"bufferView": 1, "byteOffset": 409680, "componentType": 5126, "count": 318, "max": [0.9454922, 2.3548696, 0.9454922], "min": [-0.9454921, 0.0617354736, -0.945492268], "type": "VEC3", "name": "accessorPositions"}, {"bufferView": 1, "byteOffset": 413496, "componentType": 5126, "count": 318, "type": "VEC3", "name": "accessorNormals"}, {"bufferView": 2, "byteOffset": 136560, "componentType": 5126, "count": 318, "type": "VEC2", "name": "accessorUVs"}, {"bufferView": 0, "byteOffset": 34776, "componentType": 5123, "count": 11520, "type": "SCALAR", "name": "accessorIndices"}, {"bufferView": 1, "byteOffset": 417312, "componentType": 5126, "count": 11520, "max": [0.196413681, 2.12139344, 1.90955186], "min": [-0.196414649, 0.165008187, 1.00927675], "type": "VEC3", "name": "accessorPositions"}, {"bufferView": 1, "byteOffset": 555552, "componentType": 5126, "count": 11520, "type": "VEC3", "name": "accessorNormals"}, {"bufferView": 2, "byteOffset": 139104, "componentType": 5126, "count": 11520, "type": "VEC2", "name": "accessorUVs"}], "bufferViews": [{"buffer": 0, "byteLength": 57816, "name": "bufferViewScalar"}, {"buffer": 0, "byteOffset": 57816, "byteLength": 693792, "byteStride": 12, "name": "bufferViewFloatVec3"}, {"buffer": 0, "byteOffset": 751608, "byteLength": 231264, "byteStride": 8, "name": "bufferViewFloatVec2"}], "buffers": [{"uri": "model.bin", "byteLength": 982872}], "materials": [{"pbrMetallicRoughness": {"metallicFactor": 0.0, "roughnessFactor": 0.9}, "doubleSided": true, "name": "01 - <PERSON><PERSON><PERSON>"}, {"pbrMetallicRoughness": {"baseColorTexture": {"index": 0}, "metallicFactor": 0.0, "roughnessFactor": 0.9}, "doubleSided": true, "name": "02 - <PERSON><PERSON><PERSON>"}, {"pbrMetallicRoughness": {"baseColorFactor": [0.0, 1.0, 0.117647067, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.9}, "doubleSided": true, "name": "03 - <PERSON><PERSON><PERSON>"}, {"pbrMetallicRoughness": {"baseColorFactor": [0.2784314, 0.0, 0.9921569, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.9}, "doubleSided": true, "name": "04 - <PERSON><PERSON><PERSON>"}, {"pbrMetallicRoughness": {"baseColorFactor": [0.9725491, 0.776470661, 0.0, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.9}, "doubleSided": true, "name": "05 - <PERSON><PERSON><PERSON>"}], "textures": [{"sampler": 0, "source": 0, "name": "texture.jpg"}], "images": [{"uri": "C:/Users/<USER>/Desktop/texture.jpg"}], "samplers": [{"magFilter": 9729, "minFilter": 9987}]}