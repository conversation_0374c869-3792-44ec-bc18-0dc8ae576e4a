"use client"

import { useEffect, useState } from "react"
import Image from "next/image"

interface PayPalButtonsProps {
  plan: string | null
}

export function PayPalButtons({ plan }: PayPalButtonsProps) {
  const [isClient, setIsClient] = useState(false)

  // This is a mock implementation - in a real app, you would use the PayPal SDK
  useEffect(() => {
    setIsClient(true)

    // In a real implementation, you would load the PayPal SDK here
    // const loadPayPalScript = async () => {
    //   const script = document.createElement("script");
    //   script.src = `https://www.paypal.com/sdk/js?client-id=${YOUR_PAYPAL_CLIENT_ID}&vault=true&intent=subscription`;
    //   script.async = true;
    //   document.body.appendChild(script);
    // };
    // loadPayPalScript();
  }, [])

  if (!isClient) {
    return <div className="h-12 w-full animate-pulse rounded-md bg-[#1F2A45]"></div>
  }

  return (
    <div className="flex flex-col gap-4">
      <p className="text-center text-sm text-white/70">
        سيتم تحويلك إلى PayPal لإتمام عملية الدفع بأمان. يمكنك إلغاء الاشتراك في أي وقت.
      </p>

      <button className="flex h-12 w-full items-center justify-center rounded-md bg-[#0070ba] px-4 py-2 text-white transition-colors hover:bg-[#003087]">
        <Image src="/placeholder.svg?height=24&width=80" alt="PayPal" width={80} height={24} />
      </button>

      <div className="text-center text-xs text-white/50">
        بالضغط على زر الدفع، أنت توافق على شروط الخدمة وسياسة الخصوصية.
      </div>
    </div>
  )
}
