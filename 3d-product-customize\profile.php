<?php
require_once 'config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit;
}

// Get user information
$userId = $_SESSION['user_id'];

// Get user data
$sql = "SELECT full_name, email, mobile_number, created_at, last_login FROM users WHERE user_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();
$stmt->close();

// Process form submission for profile update
$successMessage = '';
$errorMessage = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_profile'])) {
    $fullName = trim($_POST['full_name']);
    $mobileNumber = trim($_POST['mobile_number']);
    $currentPassword = $_POST['current_password'];
    $newPassword = $_POST['new_password'];
    $confirmPassword = $_POST['confirm_password'];
    
    // Validate inputs
    if (empty($fullName)) {
        $errorMessage = 'Full name is required';
    } elseif (empty($mobileNumber)) {
        $errorMessage = 'Mobile number is required';
    } else {
        // Check if current password is provided and user wants to change password
        if (!empty($currentPassword)) {
            // Verify current password
            $checkPasswordSql = "SELECT password FROM users WHERE user_id = ?";
            $checkPasswordStmt = $conn->prepare($checkPasswordSql);
            $checkPasswordStmt->bind_param("i", $userId);
            $checkPasswordStmt->execute();
            $checkPasswordStmt->bind_result($hashedPassword);
            $checkPasswordStmt->fetch();
            $checkPasswordStmt->close();
            
            if (!password_verify($currentPassword, $hashedPassword)) {
                $errorMessage = 'Current password is incorrect';
            } elseif (empty($newPassword)) {
                $errorMessage = 'New password is required';
            } elseif (strlen($newPassword) < 8) {
                $errorMessage = 'New password must be at least 8 characters long';
            } elseif ($newPassword !== $confirmPassword) {
                $errorMessage = 'New password and confirmation do not match';
            } else {
                // Update profile with new password
                $hashedNewPassword = password_hash($newPassword, PASSWORD_DEFAULT);
                $updateProfileSql = "UPDATE users SET full_name = ?, mobile_number = ?, password = ? WHERE user_id = ?";
                $updateProfileStmt = $conn->prepare($updateProfileSql);
                $updateProfileStmt->bind_param("sssi", $fullName, $mobileNumber, $hashedNewPassword, $userId);
                
                if ($updateProfileStmt->execute()) {
                    $successMessage = 'Profile and password updated successfully';
                    $_SESSION['full_name'] = $fullName;
                } else {
                    $errorMessage = 'Error updating profile: ' . $updateProfileStmt->error;
                }
                
                $updateProfileStmt->close();
            }
        } else {
            // Update profile without changing password
            $updateProfileSql = "UPDATE users SET full_name = ?, mobile_number = ? WHERE user_id = ?";
            $updateProfileStmt = $conn->prepare($updateProfileSql);
            $updateProfileStmt->bind_param("ssi", $fullName, $mobileNumber, $userId);
            
            if ($updateProfileStmt->execute()) {
                $successMessage = 'Profile updated successfully';
                $_SESSION['full_name'] = $fullName;
                
                // Refresh user data
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("i", $userId);
                $stmt->execute();
                $result = $stmt->get_result();
                $user = $result->fetch_assoc();
                $stmt->close();
            } else {
                $errorMessage = 'Error updating profile: ' . $updateProfileStmt->error;
            }
            
            $updateProfileStmt->close();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Profile - 3D Product Customizer</title>
    <link rel="stylesheet" href="styles/subscription.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
    <div class="dashboard-container">
        <div class="dashboard-sidebar">
            <div class="dashboard-logo">
                <h2>3D Customizer</h2>
            </div>
            
            <ul class="dashboard-nav">
                <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                <li><a href="index.html"><i class="fas fa-cube"></i> 3D Editor</a></li>
                <li><a href="subscription.php"><i class="fas fa-credit-card"></i> Subscription</a></li>
                <li><a href="profile.php" class="active"><i class="fas fa-user"></i> Profile</a></li>
                <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
            </ul>
        </div>
        
        <div class="dashboard-content">
            <div class="dashboard-header">
                <h1>User Profile</h1>
                
                <div class="user-info">
                    <span><?php echo htmlspecialchars($user['full_name']); ?></span>
                </div>
            </div>
            
            <?php if (!empty($successMessage)): ?>
                <div class="message success">
                    <?php echo htmlspecialchars($successMessage); ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($errorMessage)): ?>
                <div class="message error">
                    <?php echo htmlspecialchars($errorMessage); ?>
                </div>
            <?php endif; ?>
            
            <div class="profile-container">
                <div class="profile-card">
                    <div class="profile-header">
                        <h2>Personal Information</h2>
                        <p>Update your personal details and password</p>
                    </div>
                    
                    <form id="profile-form" method="post" action="profile.php">
                        <div class="form-group">
                            <label for="full_name">Full Name</label>
                            <input type="text" id="full_name" name="full_name" value="<?php echo htmlspecialchars($user['full_name']); ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="email">Email Address</label>
                            <input type="email" id="email" value="<?php echo htmlspecialchars($user['email']); ?>" disabled>
                            <p class="form-note">Email address cannot be changed</p>
                        </div>
                        
                        <div class="form-group">
                            <label for="mobile_number">Mobile Number</label>
                            <input type="tel" id="mobile_number" name="mobile_number" value="<?php echo htmlspecialchars($user['mobile_number']); ?>" required>
                        </div>
                        
                        <div class="profile-section">
                            <h3>Change Password</h3>
                            <p>Leave blank to keep your current password</p>
                        </div>
                        
                        <div class="form-group">
                            <label for="current_password">Current Password</label>
                            <input type="password" id="current_password" name="current_password">
                        </div>
                        
                        <div class="form-group">
                            <label for="new_password">New Password</label>
                            <input type="password" id="new_password" name="new_password">
                        </div>
                        
                        <div class="form-group">
                            <label for="confirm_password">Confirm New Password</label>
                            <input type="password" id="confirm_password" name="confirm_password">
                        </div>
                        
                        <div class="form-group">
                            <button type="submit" name="update_profile" class="btn-primary">Save Changes</button>
                        </div>
                    </form>
                </div>
                
                <div class="profile-card">
                    <div class="profile-header">
                        <h2>Account Information</h2>
                        <p>Your account details and activity</p>
                    </div>
                    
                    <div class="account-info">
                        <div class="info-item">
                            <div class="info-label">Member Since</div>
                            <div class="info-value"><?php echo date('F j, Y', strtotime($user['created_at'])); ?></div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">Last Login</div>
                            <div class="info-value">
                                <?php echo ($user['last_login']) ? date('F j, Y, g:i a', strtotime($user['last_login'])) : 'N/A'; ?>
                            </div>
                        </div>
                        
                        <?php
                        // Get subscription data
                        $subQuery = "SELECT s.plan_type, s.expiry_date, s.amount, 
                                    DATE_FORMAT(s.expiry_date, '%M %d, %Y') as formatted_expiry_date
                                  FROM subscriptions s 
                                  WHERE s.user_id = ? AND s.status = 'active'
                                  ORDER BY s.expiry_date DESC LIMIT 1";
                        $subStmt = $conn->prepare($subQuery);
                        $subStmt->bind_param("i", $userId);
                        $subStmt->execute();
                        $subResult = $subStmt->get_result();
                        
                        if ($subResult->num_rows > 0):
                            $subscription = $subResult->fetch_assoc();
                        ?>
                            <div class="info-item">
                                <div class="info-label">Current Plan</div>
                                <div class="info-value"><?php echo ucfirst($subscription['plan_type']); ?> ($<?php echo number_format($subscription['amount'], 2); ?>)</div>
                            </div>
                            
                            <div class="info-item">
                                <div class="info-label">Expires On</div>
                                <div class="info-value"><?php echo $subscription['formatted_expiry_date']; ?></div>
                            </div>
                        <?php 
                        else: 
                        ?>
                            <div class="info-item">
                                <div class="info-label">Current Plan</div>
                                <div class="info-value">No active subscription</div>
                            </div>
                        <?php 
                        endif;
                        $subStmt->close();
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .profile-container {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
        }
        
        .profile-card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 25px;
            margin-bottom: 20px;
        }
        
        .profile-header {
            margin-bottom: 25px;
        }
        
        .profile-header h2 {
            color: var(--primary-color);
            margin-bottom: 5px;
        }
        
        .profile-header p {
            color: #777;
            margin: 0;
        }
        
        .profile-section {
            margin: 25px 0 15px;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }
        
        .profile-section h3 {
            color: var(--primary-color);
            margin-bottom: 5px;
        }
        
        .profile-section p {
            color: #777;
            margin: 0;
            font-size: 14px;
        }
        
        .form-note {
            color: #777;
            font-size: 13px;
            margin-top: 5px;
        }
        
        .account-info {
            margin-top: 20px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .info-label {
            color: #777;
            font-weight: bold;
        }
        
        .info-value {
            color: var(--dark-color);
        }
        
        @media screen and (max-width: 992px) {
            .profile-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
    
    <script src="js/validation.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const profileForm = document.getElementById('profile-form');
            
            profileForm.addEventListener('submit', function(e) {
                const currentPassword = document.getElementById('current_password').value;
                const newPassword = document.getElementById('new_password').value;
                const confirmPassword = document.getElementById('confirm_password').value;
                
                // Check if user is trying to change password
                if (currentPassword || newPassword || confirmPassword) {
                    if (!currentPassword) {
                        e.preventDefault();
                        showMessage('message-container', 'Current password is required to change password', 'error');
                        return;
                    }
                    
                    if (!newPassword) {
                        e.preventDefault();
                        showMessage('message-container', 'New password is required', 'error');
                        return;
                    }
                    
                    if (newPassword.length < 8) {
                        e.preventDefault();
                        showMessage('message-container', 'New password must be at least 8 characters long', 'error');
                        return;
                    }
                    
                    if (newPassword !== confirmPassword) {
                        e.preventDefault();
                        showMessage('message-container', 'New password and confirmation do not match', 'error');
                        return;
                    }
                }
            });
        });
    </script>
</body>
</html>
