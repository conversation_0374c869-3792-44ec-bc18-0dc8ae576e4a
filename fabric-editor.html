<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محرر الصور والرسم المتقدم</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .editor-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .main-toolbar {
            display: flex;
            justify-content: center;
            gap: 10px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.8);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            flex-wrap: wrap;
        }

        .main-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }

        .main-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .main-btn.active {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            padding: 20px;
            background: #f8f9fa;
        }

        #canvas {
            border: 3px solid #667eea;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .toolbar-section {
            display: none;
            padding: 20px;
            background: rgba(255, 255, 255, 0.9);
            border-top: 1px solid rgba(0, 0, 0, 0.1);
        }

        .toolbar-section.active {
            display: block;
            animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .toolbar-row {
            display: flex;
            gap: 15px;
            align-items: center;
            justify-content: flex-start;
            overflow-x: auto;
            padding: 10px 0;
            white-space: nowrap;
            scrollbar-width: thin;
            scrollbar-color: #667eea transparent;
        }

        .toolbar-row::-webkit-scrollbar {
            height: 6px;
        }

        .toolbar-row::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 3px;
        }

        .toolbar-row::-webkit-scrollbar-thumb {
            background: #667eea;
            border-radius: 3px;
        }

        .tool-btn {
            background: linear-gradient(45deg, #4facfe, #00f2fe);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(79, 172, 254, 0.3);
            flex-shrink: 0;
        }

        .tool-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }

        .tool-group {
            display: flex;
            align-items: center;
            gap: 10px;
            background: rgba(255, 255, 255, 0.7);
            padding: 10px 15px;
            border-radius: 15px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            flex-shrink: 0;
        }

        .tool-group label {
            font-size: 12px;
            font-weight: 600;
            color: #333;
            margin-right: 8px;
        }

        input[type="range"] {
            width: 80px;
            accent-color: #667eea;
        }

        input[type="color"] {
            width: 40px;
            height: 30px;
            border: none;
            border-radius: 50%;
            cursor: pointer;
        }

        select {
            padding: 5px 10px;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: white;
            font-size: 12px;
        }

        .info-badge {
            background: #667eea;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: bold;
            min-width: 25px;
            text-align: center;
        }

        .instructions {
            background: linear-gradient(45deg, #ffeaa7, #fdcb6e);
            padding: 15px;
            margin: 20px;
            border-radius: 15px;
            text-align: center;
            color: #2d3436;
            font-weight: 500;
        }

        .instructions small {
            display: block;
            margin: 5px 0;
            font-size: 13px;
        }

        #file-input {
            display: none;
        }

        @media (max-width: 768px) {
            .main-toolbar {
                padding: 15px 10px;
            }
            
            .main-btn {
                padding: 10px 16px;
                font-size: 12px;
            }
            
            .canvas-container {
                padding: 10px;
            }
            
            #canvas {
                max-width: 100%;
                height: auto;
            }
        }
    </style>
</head>
<body>
    <div class="editor-container">
        <div class="header">
            <h1>محرر الصور والرسم المتقدم</h1>
            <p>أداة متكاملة لتحرير الصور والرسم الحر</p>
        </div>

        <div class="main-toolbar">
            <button class="main-btn" onclick="toggleSection('image-tools')">🖼️ أدوات الصور</button>
            <button class="main-btn" onclick="toggleSection('draw-tools')">🎨 أدوات الرسم</button>
            <button class="main-btn" onclick="toggleSection('crop-tools')">✂️ قص الصور</button>
            <button class="main-btn" onclick="clearCanvas()">🗑️ مسح الكل</button>
            <button class="main-btn" onclick="downloadCanvas()">💾 تحميل</button>
        </div>

        <div class="canvas-container">
            <canvas id="canvas" width="800" height="500"></canvas>
        </div>

        <div id="image-tools" class="toolbar-section">
            <div class="toolbar-row">
                <button class="tool-btn" onclick="document.getElementById('file-input').click()">📁 اختيار صور</button>
                <div class="tool-group">
                    <label>شفافية:</label>
                    <input type="range" id="opacity-slider" min="0" max="100" value="100" oninput="updateOpacity(this.value)">
                    <span class="info-badge" id="opacity-info">100</span>
                </div>
                <button class="tool-btn" onclick="flipHorizontal()">🔄 قلب أفقي</button>
                <button class="tool-btn" onclick="flipVertical()">🔃 قلب عمودي</button>
                <button class="tool-btn" onclick="bringToFront()">⬆️ للأمام</button>
                <button class="tool-btn" onclick="sendToBack()">⬇️ للخلف</button>
            </div>
        </div>

        <div id="draw-tools" class="toolbar-section">
            <div class="toolbar-row">
                <button class="tool-btn" id="drawing-mode-btn" onclick="toggleDrawingMode()">🎨 تفعيل الرسم</button>
                <div class="tool-group">
                    <label>نوع الفرشاة:</label>
                    <select id="brush-selector" onchange="changeBrushType()">
                        <option value="Pencil">قلم رصاص</option>
                        <option value="Circle">دائرة</option>
                        <option value="Spray">رذاذ</option>
                        <option value="Pattern">نمط</option>
                        <option value="hline">خط أفقي</option>
                        <option value="vline">خط عمودي</option>
                        <option value="square">مربع</option>
                        <option value="diamond">معين</option>
                    </select>
                </div>
                <div class="tool-group">
                    <label>سمك الخط:</label>
                    <input type="range" id="line-width" min="1" max="150" value="5" oninput="updateLineWidth(this.value)">
                    <span class="info-badge" id="line-width-info">5</span>
                </div>
                <div class="tool-group">
                    <label>لون الخط:</label>
                    <input type="color" id="line-color" value="#667eea" onchange="updateLineColor()">
                </div>
                <div class="tool-group">
                    <label>لون الظل:</label>
                    <input type="color" id="shadow-color" value="#333333" onchange="updateShadowColor()">
                </div>
                <div class="tool-group">
                    <label>عرض الظل:</label>
                    <input type="range" id="shadow-width" min="0" max="50" value="0" oninput="updateShadowWidth(this.value)">
                    <span class="info-badge" id="shadow-width-info">0</span>
                </div>
                <div class="tool-group">
                    <label>إزاحة الظل:</label>
                    <input type="range" id="shadow-offset" min="0" max="50" value="0" oninput="updateShadowOffset(this.value)">
                    <span class="info-badge" id="shadow-offset-info">0</span>
                </div>
            </div>
        </div>

        <div id="crop-tools" class="toolbar-section">
            <div class="instructions">
                <small>🖱️ اضغط مرتين على الصورة لبدء القص</small>
                <small>📐 حرك أو غير حجم منطقة القص حسب الرغبة</small>
                <small>✅ انقر خارج المنطقة لإنهاء القص</small>
            </div>
            <div class="toolbar-row">
                <button class="tool-btn" onclick="enableCropMode()">✂️ تفعيل وضع القص</button>
                <button class="tool-btn" onclick="resetCrop()">🔄 إعادة تعيين القص</button>
                <div class="tool-group">
                    <label>نسبة القص:</label>
                    <select id="crop-ratio" onchange="applyCropRatio()">
                        <option value="free">حر</option>
                        <option value="1:1">مربع (1:1)</option>
                        <option value="4:3">4:3</option>
                        <option value="16:9">16:9</option>
                        <option value="3:2">3:2</option>
                    </select>
                </div>
            </div>
        </div>

        <input type="file" id="file-input" accept="image/*" multiple onchange="handleImageUpload(event)">
    </div>

    <script>
        // إنشاء الكانفاس
        const canvas = new fabric.Canvas('canvas', {
            backgroundColor: '#ffffff'
        });

        fabric.Object.prototype.transparentCorners = false;
        fabric.Object.prototype.cornerColor = '#667eea';
        fabric.Object.prototype.cornerStrokeColor = '#667eea';
        fabric.Object.prototype.borderColor = '#667eea';

        let isDrawingMode = false;
        let isCropMode = false;
        let cropRect = null;
        let overlayRect = null;

        // تبديل الأقسام
        function toggleSection(sectionId) {
            const sections = document.querySelectorAll('.toolbar-section');
            const buttons = document.querySelectorAll('.main-btn');
            
            sections.forEach(section => {
                if (section.id === sectionId) {
                    section.classList.toggle('active');
                } else {
                    section.classList.remove('active');
                }
            });

            buttons.forEach(btn => {
                btn.classList.remove('active');
            });

            if (document.getElementById(sectionId).classList.contains('active')) {
                event.target.classList.add('active');
            }
        }

        // تحميل الصور
        function handleImageUpload(event) {
            const files = event.target.files;
            for (let i = 0; i < files.length; i++) {
                const reader = new FileReader();
                reader.onload = function(f) {
                    const data = f.target.result;
                    fabric.Image.fromURL(data, function(img) {
                        const canvasWidth = canvas.getWidth();
                        const canvasHeight = canvas.getHeight();
                        
                        const scaleFactor = Math.min(
                            (canvasWidth * 0.8) / img.width,
                            (canvasHeight * 0.8) / img.height
                        );
                        
                        img.scale(scaleFactor);
                        img.set({
                            left: (canvasWidth - img.getScaledWidth()) / 2,
                            top: (canvasHeight - img.getScaledHeight()) / 2,
                        });
                        
                        canvas.add(img);
                        canvas.setActiveObject(img);
                        canvas.renderAll();
                    });
                };
                reader.readAsDataURL(files[i]);
            }
        }

        // وظائف أدوات الصور
        function updateOpacity(value) {
            document.getElementById('opacity-info').textContent = value;
            const activeObject = canvas.getActiveObject();
            if (activeObject) {
                activeObject.set('opacity', value / 100);
                canvas.renderAll();
            }
        }

        function flipHorizontal() {
            const activeObject = canvas.getActiveObject();
            if (activeObject) {
                activeObject.set('flipX', !activeObject.flipX);
                canvas.renderAll();
            }
        }

        function flipVertical() {
            const activeObject = canvas.getActiveObject();
            if (activeObject) {
                activeObject.set('flipY', !activeObject.flipY);
                canvas.renderAll();
            }
        }

        function bringToFront() {
            const activeObject = canvas.getActiveObject();
            if (activeObject) {
                canvas.bringToFront(activeObject);
                canvas.renderAll();
            }
        }

        function sendToBack() {
            const activeObject = canvas.getActiveObject();
            if (activeObject) {
                canvas.sendToBack(activeObject);
                canvas.renderAll();
            }
        }

        // وظائف الرسم
        function toggleDrawingMode() {
            isDrawingMode = !isDrawingMode;
            canvas.isDrawingMode = isDrawingMode;
            const btn = document.getElementById('drawing-mode-btn');
            btn.textContent = isDrawingMode ? '🛑 إيقاف الرسم' : '🎨 تفعيل الرسم';
            btn.style.background = isDrawingMode ? 'linear-gradient(45deg, #ff6b6b, #ee5a24)' : 'linear-gradient(45deg, #4facfe, #00f2fe)';
            
            if (isDrawingMode) {
                canvas.freeDrawingBrush = new fabric.PencilBrush(canvas);
                updateBrush();
            }
        }

        function changeBrushType() {
            const val = document.getElementById('brush-selector').value;
            
            if (val === 'hline') {
                const brush = new fabric.PatternBrush(canvas);
                brush.getPatternSrc = function() {
                    const patternCanvas = document.createElement('canvas');
                    patternCanvas.width = patternCanvas.height = 10;
                    const ctx = patternCanvas.getContext('2d');
                    ctx.strokeStyle = this.color;
                    ctx.lineWidth = 5;
                    ctx.beginPath();
                    ctx.moveTo(0, 5);
                    ctx.lineTo(10, 5);
                    ctx.stroke();
                    return patternCanvas;
                };
                canvas.freeDrawingBrush = brush;
            } else if (val === 'vline') {
                const brush = new fabric.PatternBrush(canvas);
                brush.getPatternSrc = function() {
                    const patternCanvas = document.createElement('canvas');
                    patternCanvas.width = patternCanvas.height = 10;
                    const ctx = patternCanvas.getContext('2d');
                    ctx.strokeStyle = this.color;
                    ctx.lineWidth = 5;
                    ctx.beginPath();
                    ctx.moveTo(5, 0);
                    ctx.lineTo(5, 10);
                    ctx.stroke();
                    return patternCanvas;
                };
                canvas.freeDrawingBrush = brush;
            } else {
                const brushClass = fabric[val + 'Brush'] || fabric.PencilBrush;
                canvas.freeDrawingBrush = new brushClass(canvas);
            }
            updateBrush();
        }

        function updateLineWidth(value) {
            document.getElementById('line-width-info').textContent = value;
            updateBrush();
        }

        function updateLineColor() {
            updateBrush();
        }

        function updateShadowColor() {
            updateBrush();
        }

        function updateShadowWidth(value) {
            document.getElementById('shadow-width-info').textContent = value;
            updateBrush();
        }

        function updateShadowOffset(value) {
            document.getElementById('shadow-offset-info').textContent = value;
            updateBrush();
        }

        function updateBrush() {
            if (canvas.freeDrawingBrush) {
                const brush = canvas.freeDrawingBrush;
                brush.color = document.getElementById('line-color').value;
                brush.width = parseInt(document.getElementById('line-width').value, 10) || 1;
                brush.shadow = new fabric.Shadow({
                    blur: parseInt(document.getElementById('shadow-width').value, 10) || 0,
                    offsetX: parseInt(document.getElementById('shadow-offset').value, 10) || 0,
                    offsetY: parseInt(document.getElementById('shadow-offset').value, 10) || 0,
                    color: document.getElementById('shadow-color').value,
                });
            }
        }

        // وظائف القص
        canvas.on('mouse:dblclick', function(e) {
            const target = e.target;
            if (target && target.type === 'image') {
                prepareCrop(target);
            }
        });

        function prepareCrop(img) {
            // إزالة أي مستطيلات قص سابقة
            if (cropRect) {
                canvas.remove(cropRect);
            }
            if (overlayRect) {
                canvas.remove(overlayRect);
            }

            // إنشاء مستطيل القص
            cropRect = new fabric.Rect({
                top: img.top,
                left: img.left,
                width: img.getScaledWidth(),
                height: img.getScaledHeight(),
                stroke: '#667eea',
                strokeWidth: 2,
                strokeDashArray: [5, 5],
                fill: 'rgba(255, 255, 255, 0.3)',
                lockRotation: true,
                transparentCorners: false,
                cornerColor: '#667eea',
                cornerStrokeColor: '#667eea',
                borderColor: '#667eea'
            });

            // إنشاء طبقة التظليل
            overlayRect = new fabric.Rect({
                top: img.top,
                left: img.left,
                width: img.getScaledWidth(),
                height: img.getScaledHeight(),
                fill: 'rgba(0, 0, 0, 0.5)',
                selectable: false,
                evented: false,
                lockRotation: true
            });

            // حفظ إعدادات القص الحالية
            const currentCropX = img.cropX || 0;
            const currentCropY = img.cropY || 0;
            const currentWidth = img.width;
            const currentHeight = img.height;

            // إعادة تعيين الصورة للحجم الأصلي
            img.set({
                cropX: null,
                cropY: null,
                left: img.left - currentCropX * img.scaleX,
                top: img.top - currentCropY * img.scaleY,
                width: img._originalElement ? img._originalElement.naturalWidth : currentWidth,
                height: img._originalElement ? img._originalElement.naturalHeight : currentHeight
            });

            // تحديث موضع مستطيل القص
            cropRect.set({
                left: img.left + currentCropX * img.scaleX,
                top: img.top + currentCropY * img.scaleY,
                width: currentWidth * img.scaleX,
                height: currentHeight * img.scaleY
            });

            // إضافة المستطيلات للكانفاس
            canvas.add(overlayRect);
            canvas.add(cropRect);
            canvas.setActiveObject(cropRect);
            canvas.renderAll();

            // إضافة الأحداث لمستطيل القص
            cropRect.on('moving', function() {
                constrainCropRect(cropRect, img);
            });

            cropRect.on('scaling', function() {
                constrainCropRect(cropRect, img);
            });

            cropRect.on('deselected', function() {
                finalizeCrop(cropRect, img);
            });
        }

        function constrainCropRect(cropRect, img) {
            // تقييد حركة مستطيل القص داخل حدود الصورة
            if (cropRect.left < img.left) {
                cropRect.set('left', img.left);
            }
            if (cropRect.top < img.top) {
                cropRect.set('top', img.top);
            }
            if (cropRect.left + cropRect.getScaledWidth() > img.left + img.getScaledWidth()) {
                cropRect.set('left', img.left + img.getScaledWidth() - cropRect.getScaledWidth());
            }
            if (cropRect.top + cropRect.getScaledHeight() > img.top + img.getScaledHeight()) {
                cropRect.set('top', img.top + img.getScaledHeight() - cropRect.getScaledHeight());
            }
        }

        function finalizeCrop(cropRect, img) {
            // حساب إحداثيات القص
            const cropX = (cropRect.left - img.left) / img.scaleX;
            const cropY = (cropRect.top - img.top) / img.scaleY;
            const cropWidth = (cropRect.width * cropRect.scaleX) / img.scaleX;
            const cropHeight = (cropRect.height * cropRect.scaleY) / img.scaleY;

            // تطبيق القص على الصورة
            img.set({
                cropX: cropX,
                cropY: cropY,
                width: cropWidth,
                height: cropHeight,
                left: img.left + cropX * img.scaleX,
                top: img.top + cropY * img.scaleY
            });

            // إزالة مستطيلات القص
            canvas.remove(cropRect);
            canvas.remove(overlayRect);
            cropRect = null;
            overlayRect = null;

            canvas.setActiveObject(img);
            canvas.renderAll();
        }

        function enableCropMode() {
            isCropMode = !isCropMode;
            const activeObject = canvas.getActiveObject();
            if (isCropMode && activeObject && activeObject.type === 'image') {
                prepareCrop(activeObject);
            }
        }

        function resetCrop() {
            const activeObject = canvas.getActiveObject();
            if (activeObject && activeObject.type === 'image') {
                activeObject.set({
                    cropX: null,
                    cropY: null,
                    width: activeObject._originalElement ? activeObject._originalElement.naturalWidth : activeObject.width,
                    height: activeObject._originalElement ? activeObject._originalElement.naturalHeight : activeObject.height
                });
                canvas.renderAll();
            }
        }

        function applyCropRatio() {
            const ratio = document.getElementById('crop-ratio').value;
            if (cropRect && ratio !== 'free') {
                const [width, height] = ratio.split(':').map(Number);
                const aspectRatio = width / height;
                
                const newWidth = cropRect.width;
                const newHeight = newWidth / aspectRatio;
                
                cropRect.set({
                    height: newHeight,
                    scaleY: 1
                });
                canvas.renderAll();
            }
        }

        // وظائف عامة
        function clearCanvas() {
            if (confirm('هل أنت متأكد من مسح كل المحتويات؟')) {
                canvas.clear();
                canvas.backgroundColor = '#ffffff';
                canvas.renderAll();
            }
        }

        function downloadCanvas() {
            const link = document.createElement('a');
            link.download = 'canvas-image.png';
            link.href = canvas.toDataURL();
            link.click();
        }

        // تهيئة أولية
        updateBrush();
        
        // إضافة مؤثرات بصرية للأزرار
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('.main-btn, .tool-btn');
            buttons.forEach(button => {
                button.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });
                button.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html>