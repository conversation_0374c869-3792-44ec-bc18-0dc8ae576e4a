/* تنسيق عام */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
}

h1 {
    color: #2c3e50;
    margin-bottom: 10px;
}

h2 {
    color: #3498db;
    margin-bottom: 15px;
    font-size: 1.5rem;
}

/* تنسيق منطقة العمل */
.workspace {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 30px;
}

/* منطقة التصميم */
.design-area {
    flex: 1;
    min-width: 300px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.canvas-container {
    width: 100%;
    height: 400px;
    border: 1px solid #ddd;
    margin-bottom: 15px;
    position: relative;
    overflow: hidden;
}

#fabric-canvas {
    width: 100%;
    height: 100%;
}

.design-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* منطقة المعاينة */
.preview-area {
    flex: 1;
    min-width: 300px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.three-container {
    width: 100%;
    height: 400px;
    border: 1px solid #ddd;
    margin-bottom: 15px;
    position: relative;
    overflow: hidden;
}

#three-canvas {
    width: 100%;
    height: 100%;
}

/* أزرار التحكم بالتأثير */
.effect-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.effect-selector {
    display: flex;
    align-items: center;
    gap: 10px;
}

.effect-selector label {
    font-weight: bold;
}

.effect-selector select {
    padding: 8px;
    border-radius: 4px;
    border: 1px solid #ddd;
    background-color: #f9f9f9;
    flex-grow: 1;
}

.animation-controls {
    display: flex;
    gap: 10px;
}

/* تنسيق الأزرار */
button {
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    transition: background-color 0.3s, transform 0.1s;
}

button:hover {
    transform: translateY(-2px);
}

button:active {
    transform: translateY(0);
}

#add-text, #add-image {
    background-color: #3498db;
    color: white;
}

#clear-canvas {
    background-color: #e74c3c;
    color: white;
}

#play-btn {
    background-color: #2ecc71;
    color: white;
}

#pause-btn {
    background-color: #f39c12;
    color: white;
}

#stop-btn {
    background-color: #e74c3c;
    color: white;
}

/* تنسيق التذييل */
footer {
    text-align: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #ddd;
    color: #7f8c8d;
}

/* تنسيق متجاوب */
@media (max-width: 768px) {
    .workspace {
        flex-direction: column;
    }
    
    .design-area, .preview-area {
        width: 100%;
    }
    
    .canvas-container, .three-container {
        height: 300px;
    }
}
