<?php
// Include authentication check
require_once 'auth_check.php';

// Enforce subscription - will redirect if not logged in or no active subscription
enforceSubscription($conn);

// Get user information
$userId = $_SESSION['user_id'];
$userName = $_SESSION['full_name'];
$subscriptionType = $_SESSION['subscription_type'];
$subscriptionExpiry = $_SESSION['subscription_expiry'];

// Get remaining downloads
$remaining = getRemainingDownloads($conn, $userId);
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>3D Product Customizer - Design Studio</title>
  <link rel="stylesheet" href="styles/subscription.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: Arial, sans-serif;
      background-color: #f5f5f5;
    }
    
    .editor-container {
      display: flex;
      flex-direction: column;
      height: 100vh;
    }
    
    .editor-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 20px;
      background-color: #2c3e50;
      color: white;
    }
    
    .editor-title {
      font-size: 20px;
      font-weight: bold;
    }
    
    .user-controls {
      display: flex;
      align-items: center;
    }
    
    .user-controls .user-name {
      margin-right: 15px;
    }
    
    .user-controls a {
      color: white;
      text-decoration: none;
      margin-left: 15px;
    }
    
    .user-controls a:hover {
      text-decoration: underline;
    }
    
    .toolbar {
      display: flex;
      padding: 10px;
      background-color: #34495e;
      color: white;
    }
    
    .toolbar-section {
      display: flex;
      margin-right: 20px;
    }
    
    .toolbar-section h3 {
      margin: 0 10px 0 0;
      font-size: 14px;
      line-height: 32px;
    }
    
    .toolbar button {
      margin-right: 5px;
      padding: 5px 10px;
      background-color: #555;
      color: white;
      border: none;
      border-radius: 3px;
      cursor: pointer;
    }
    
    .toolbar button:hover {
      background-color: #777;
    }
    
    .canvas-container {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 20px;
      overflow: auto;
    }
    
    #fabric-canvas {
      background-color: white;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }
    
    .preview-container {
      position: fixed;
      bottom: 20px;
      left: 20px;
      width: 300px;
      height: 200px;
      background-color: #333;
      border-radius: 5px;
      overflow: hidden;
    }
    
    .preview-container h3 {
      margin: 0;
      padding: 10px;
      background-color: #222;
      color: white;
      font-size: 14px;
    }
    
    .preview-content {
      height: calc(100% - 35px);
      display: flex;
      justify-content: center;
      align-items: center;
    }
    
    .preview-content canvas {
      max-width: 100%;
      max-height: 100%;
    }
    
    .stats-container {
      position: fixed;
      bottom: 20px;
      right: 20px;
      width: 250px;
      background-color: #fff;
      border-radius: 5px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      padding: 15px;
    }
    
    .stats-title {
      font-size: 16px;
      font-weight: bold;
      color: #2c3e50;
      margin-bottom: 10px;
    }
    
    .stats-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
    }
    
    .stats-label {
      color: #555;
    }
    
    .stats-value {
      font-weight: bold;
      color: #2c3e50;
    }
    
    .stats-alert {
      color: #e74c3c;
      font-size: 12px;
      margin-top: 10px;
      display: none;
    }
    
    .stats-alert.show {
      display: block;
    }
  </style>
</head>
<body>
  <div class="editor-container">
    <div class="editor-header">
      <div class="editor-title">3D Product Customizer</div>
      <div class="user-controls">
        <span class="user-name"><?php echo htmlspecialchars($userName); ?></span>
        <span class="subscription-badge"><?php echo ucfirst(htmlspecialchars($subscriptionType)); ?> Plan</span>
        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
        <a href="logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
      </div>
    </div>
    
    <div class="toolbar">
      <div class="toolbar-section">
        <h3>Add</h3>
        <button id="add-text-btn">Text</button>
        <button id="add-image-btn">Image</button>
      </div>
      
      <div class="toolbar-section">
        <h3>Edit</h3>
        <button id="delete-btn">Delete</button>
        <button id="undo-btn">Undo</button>
        <button id="redo-btn">Redo</button>
      </div>
      
      <div class="toolbar-section">
        <h3>Arrange</h3>
        <button id="group-btn">Group</button>
        <button id="ungroup-btn">Ungroup</button>
      </div>
      
      <div class="toolbar-section">
        <h3>Export</h3>
        <button id="export-btn">Export Image</button>
        <button id="export-json-btn">Export JSON</button>
        <button id="import-json-btn">Import JSON</button>
      </div>
    </div>
    
    <div class="canvas-container">
      <canvas id="fabric-canvas"></canvas>
    </div>
    
    <div class="preview-container">
      <h3>3D Preview</h3>
      <div class="preview-content">
        <canvas id="three-preview"></canvas>
      </div>
    </div>
    
    <div class="stats-container">
      <div class="stats-title">Subscription Usage</div>
      <div class="stats-item">
        <div class="stats-label">Plan:</div>
        <div class="stats-value"><?php echo ucfirst(htmlspecialchars($subscriptionType)); ?></div>
      </div>
      <div class="stats-item">
        <div class="stats-label">Expires:</div>
        <div class="stats-value"><?php echo date('M d, Y', strtotime($subscriptionExpiry)); ?></div>
      </div>
      <div class="stats-item">
        <div class="stats-label">Video Downloads:</div>
        <div class="stats-value"><?php echo $remaining['video']; ?> remaining</div>
      </div>
      <div class="stats-item">
        <div class="stats-label">Image Downloads:</div>
        <div class="stats-value"><?php echo $remaining['image']; ?> remaining</div>
      </div>
      <div class="stats-alert" id="download-alert">
        You've reached your download limit. Please upgrade your plan to continue downloading.
      </div>
    </div>
  </div>
  
  <!-- Load Fabric.js -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.1/fabric.min.js"></script>
  
  <!-- Load Three.js (for 3D preview) -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
  
  <!-- Load our scripts -->
  <script src="editor-core.js"></script>
  <script src="control-panel.js"></script>
  <script src="canvas-size-manager.js"></script>
  <script src="history.js"></script>
  <script src="three-d-sync.js"></script>
  <script src="font-manager.js"></script>
  <script src="main.js"></script>
  
  <script>
    // Custom script for download enforcement
    document.addEventListener('DOMContentLoaded', function() {
      const exportBtn = document.getElementById('export-btn');
      const exportJsonBtn = document.getElementById('export-json-btn');
      const videoRemaining = <?php echo $remaining['video']; ?>;
      const imageRemaining = <?php echo $remaining['image']; ?>;
      const downloadAlert = document.getElementById('download-alert');
      
      // Function to check if download is allowed
      function checkDownloadAllowed(type) {
        if (type === 'image' && imageRemaining <= 0) {
          downloadAlert.textContent = 'You\'ve reached your image download limit. Please upgrade your plan to continue downloading.';
          downloadAlert.classList.add('show');
          return false;
        } else if (type === 'video' && videoRemaining <= 0) {
          downloadAlert.textContent = 'You\'ve reached your video download limit. Please upgrade your plan to continue downloading.';
          downloadAlert.classList.add('show');
          return false;
        }
        return true;
      }
      
      // Override the normal export function
      const originalExportImage = window.exportCanvas || function() {};
      window.exportCanvas = function() {
        if (checkDownloadAllowed('image')) {
          // Record the download via AJAX
          fetch('record_download.php', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'file_type=jpg&file_name=canvas_export.jpg'
          })
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              // Proceed with original export function
              originalExportImage();
              
              // Update displayed counts
              const imageValue = document.querySelector('.stats-item:nth-child(4) .stats-value');
              const newCount = parseInt(imageValue.textContent) - 1;
              imageValue.textContent = newCount + ' remaining';
            } else {
              alert('Error: ' + data.message);
            }
          })
          .catch(error => {
            console.error('Error recording download:', error);
          });
        }
      };
      
      // Override the JSON export
      const originalExportJson = window.exportJSON || function() {};
      window.exportJSON = function() {
        if (checkDownloadAllowed('video')) {
          // Record the download via AJAX
          fetch('record_download.php', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'file_type=web&file_name=canvas_export.json'
          })
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              // Proceed with original export function
              originalExportJson();
              
              // Update displayed counts
              const videoValue = document.querySelector('.stats-item:nth-child(3) .stats-value');
              const newCount = parseInt(videoValue.textContent) - 1;
              videoValue.textContent = newCount + ' remaining';
            } else {
              alert('Error: ' + data.message);
            }
          })
          .catch(error => {
            console.error('Error recording download:', error);
          });
        }
      };
    });
  </script>
</body>
</html>
