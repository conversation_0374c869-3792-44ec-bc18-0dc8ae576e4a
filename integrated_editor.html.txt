  $('apply-crop').onclick = () => {
    if (!cropRect || !activeImage) return;
    
    try {
      // الحصول على معلومات الصورة الأصلية
      const imgElement = activeImage.getElement();
      const imgMatrix = activeImage.calcTransformMatrix();
      const cropMatrix = cropRect.calcTransformMatrix();
      
      // حساب الأبعاد الفعلية للقص
      const cropWidth = Math.abs(cropRect.width * cropRect.scaleX);
      const cropHeight = Math.abs(cropRect.height * cropRect.scaleY);
      
      // إنشاء canvas مؤقت بحجم منطقة القص
      const tempCanvas = document.createElement('canvas');
      const tempCtx = tempCanvas.getContext('2d');
      tempCanvas.width = cropWidth;
      tempCanvas.height = cropHeight;
      
      // حفظ حالة السياق
      tempCtx.save();
      
      // حساب الإحداثيات النسبية لمنطقة القص بالنسبة للصورة
      const cropCenterX = cropRect.left + (cropRect.width * cropRect.scaleX) / 2;
      const cropCenterY = cropRect.top + (cropRect.height * cropRect.scaleY) / 2;
      
      const imgCenterX = activeImage.left;
      const imgCenterY = activeImage.top;
      
      // حساب الإزاحة النسبية
      const offsetX = (cropCenterX - imgCenterX) / (activeImage.scaleX || 1);
      const offsetY = (cropCenterY - imgCenterY) / (activeImage.scaleY || 1);
      
      // تطبيق التحويلات في canvas المؤقت
      tempCtx.translate(cropWidth / 2, cropHeight / 2);
      
      // تطبيق دوران الصورة إذا كان موجوداً
      if (activeImage.angle) {
        tempCtx.rotate((activeImage.angle * Math.PI) / 180);
      }
      
      // حساب النقطة التي نريد رسم الصورة منها
      const drawX = -offsetX - (imgElement.width / 2);
      const drawY = -offsetY - (imgElement.height / 2);
      
      // رسم الصورة مع التحويلات المطلوبة
      tempCtx.drawImage(
        imgElement,
        drawX,
        drawY,
        imgElement.width,
        imgElement.height
      );
      
      // استعادة حالة السياق
      tempCtx.restore();
      
      // إنشاء صورة جديدة من النتيجة المقصوصة
      const croppedDataURL = tempCanvas.toDataURL('image/png');
      
      fabric.Image.fromURL(croppedDataURL, (croppedImg) => {
        // تعيين موقع وخصائص الصورة الجديدة
        croppedImg.set({
          left: cropRect.left,
          top: cropRect.top,
          selectable: true,
          evented: true,
          hasControls: true,
          hasBorders: true,
          angle: cropRect.angle || 0
        });
        
        // إزالة الصورة الأصلية وإطار القص
        canvas.remove(activeImage);
        canvas.remove(cropRect);
        
        // إضافة الصورة المقصوصة
        canvas.add(croppedImg);
        canvas.setActiveObject(croppedImg);
        canvas.renderAll();
        
        // إخفاء لوحات التحكم وإعادة تعيين المتغيرات
        hideAllPanels();
        cropRect = null;
        activeImage = null;
        
        console.log('تم تطبيق القص بنجاح');
      }, {
        crossOrigin: 'anonymous'
      });
      
    } catch (error) {
      console.error('خطأ في تطبيق القص:', error);
      alert('حدث خطأ أثناء تطبيق القص. يرجى المحاولة مرة أخرى.');
    }
  };