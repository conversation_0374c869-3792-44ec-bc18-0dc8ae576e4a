# Changelog

## 1.2.0 (Apr 26, 2021)

- Support to paste image file or URL (#40).

## 1.1.5 (Nov 30, 2019)

- Fix the issue of drag and drop (#31).

## 1.1.4 (Oct 26, 2019)

- Upgrade to Cropper.js v1.5.6.

## 1.1.3 (Sep 23, 2018)

- Fix a `TypeError` of read property from `null`.

## 1.1.2 (Jun 23, 2018)

- Fix `dataset` accessing problem in IE 10 (#21).

## 1.1.1 (May 20, 2017)

- Add namespace to vuex modules.

## 1.1.0 (May 14, 2017)

- Upgrade to Cropper.js v1.0+ and Vue.js v2.0+.

## 1.0.1 (Jan 2, 2016)

- Supports to complete the cropping when dblclick on the crop box.
- Restore the full cropping state after undo cropping.

## 1.0.0 (Jan 1, 2016)

- Supports to upload, move, zoom, rotate, flip and crop an image.
- Keyboard support
