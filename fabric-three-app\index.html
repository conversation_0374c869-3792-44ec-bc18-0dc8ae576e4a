<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محرر Fabric.js و Three.js</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <header class="app-header">
        <h1>محرر Fabric.js و Three.js</h1>
        <div class="header-controls">
            <div class="canvas-size-selector">
                <label for="canvas-size">حجم اللوحة:</label>
                <select id="canvas-size">
                    <option value="instagram">منشور إنستغرام (1080×1080)</option>
                    <option value="youtube">صورة مصغرة يوتيوب (1280×720)</option>
                    <option value="facebook">غلاف فيسبوك (820×312)</option>
                    <option value="a4">ورق A4 (2480×3508)</option>
                    <option value="custom">مخصص</option>
                </select>
            </div>
        </div>
    </header>

    <div class="container">
        <div id="three-container" class="section three-section">
            <!-- مشهد Three.js -->
            <div class="loading-indicator">
                <div class="spinner"></div>
                <p>جاري تحميل النموذج...</p>
            </div>
        </div>
        
        <div id="fabric-container" class="section fabric-section hidden">
            <div class="toolbar-container">
                <div class="toolbar horizontal-scrollable">
                    <!-- أزرار شريط الأدوات ستضاف هنا بواسطة JavaScript -->
                </div>
                <div class="subtoolbar horizontal-scrollable">
                    <!-- القوائم الفرعية ستضاف هنا بواسطة JavaScript -->
                </div>
            </div>
            
            <div class="canvas-wrapper">
                <canvas id="fabric-canvas"></canvas>
            </div>
        </div>
    </div>
    
    <div class="controls">
        <button id="toggle-position" class="control-btn"><i class="fas fa-exchange-alt"></i> تبديل موضع اللوحة</button>
        <button id="toggle-canvas" class="control-btn"><i class="fas fa-eye"></i> إظهار اللوحة</button>
    </div>
    
    <!-- المكتبات -->
    <script src="js/fabric.min.js"></script>
    <script src="js/three.min.js"></script>
    <script src="js/GLTFLoader.js"></script>
    <script src="js/OrbitControls.js"></script>
    
    <!-- ملفات JavaScript الخاصة بالتطبيق -->
    <script src="js/fabric-tools.js"></script>
    <script src="js/three-scene.js"></script>
    <script src="js/interaction.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
