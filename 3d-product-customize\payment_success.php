<?php
require_once 'config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit;
}

// Get user ID and subscription data from PayPal
$userId = $_SESSION['user_id'];
$subscriptionId = isset($_GET['subscription_id']) ? $_GET['subscription_id'] : '';
$planType = isset($_GET['plan_type']) ? $_GET['plan_type'] : '';

// Validate data
if (empty($subscriptionId) || empty($planType)) {
    $_SESSION['error'] = "Invalid subscription data";
    header("Location: subscription.php");
    exit;
}

// Validate plan type
if (!in_array($planType, ['free', 'monthly', 'annual', 'arab'])) {
    $_SESSION['error'] = "Invalid plan type";
    header("Location: subscription.php");
    exit;
}

// Set subscription details based on plan type
$amount = 0;
$mp4WebVideoLimit = 0;
$jpgPngImageLimit = 0;
$startDate = date('Y-m-d H:i:s');
$expiryDate = '';
$nextRenewalDate = '';

switch ($planType) {
    case 'free':
        $amount = 1.00;
        $mp4WebVideoLimit = $downloadLimits['free']['mp4_web_videos'];
        $jpgPngImageLimit = $downloadLimits['free']['jpg_png_images'];
        $expiryDate = date('Y-m-d H:i:s', strtotime('+1 year'));
        $nextRenewalDate = date('Y-m-d H:i:s', strtotime('+1 year'));
        break;
    case 'monthly':
        $amount = 5.00;
        $mp4WebVideoLimit = $downloadLimits['monthly']['mp4_web_videos'];
        $jpgPngImageLimit = $downloadLimits['monthly']['jpg_png_images'];
        $expiryDate = date('Y-m-d H:i:s', strtotime('+1 month'));
        $nextRenewalDate = date('Y-m-d H:i:s', strtotime('+1 month'));
        break;
    case 'annual':
        $amount = 50.00;
        $mp4WebVideoLimit = $downloadLimits['annual']['mp4_web_videos'];
        $jpgPngImageLimit = $downloadLimits['annual']['jpg_png_images'];
        $expiryDate = date('Y-m-d H:i:s', strtotime('+1 year'));
        $nextRenewalDate = date('Y-m-d H:i:s', strtotime('+1 year'));
        break;
    case 'arab':
        $amount = 25.00;
        $mp4WebVideoLimit = $downloadLimits['arab']['mp4_web_videos'];
        $jpgPngImageLimit = $downloadLimits['arab']['jpg_png_images'];
        $expiryDate = date('Y-m-d H:i:s', strtotime('+1 year'));
        $nextRenewalDate = date('Y-m-d H:i:s', strtotime('+1 year'));
        break;
}

// Check if user already has an active subscription
$checkSubSql = "SELECT subscription_id FROM subscriptions WHERE user_id = ? AND status = 'active'";
$checkStmt = $conn->prepare($checkSubSql);
$checkStmt->bind_param("i", $userId);
$checkStmt->execute();
$checkStmt->store_result();

if ($checkStmt->num_rows > 0) {
    // Update existing subscription to inactive
    $updateSql = "UPDATE subscriptions SET status = 'inactive' WHERE user_id = ? AND status = 'active'";
    $updateStmt = $conn->prepare($updateSql);
    $updateStmt->bind_param("i", $userId);
    $updateStmt->execute();
    $updateStmt->close();
}
$checkStmt->close();

// Insert new subscription
$insertSql = "INSERT INTO subscriptions (user_id, paypal_subscription_id, plan_type, amount, status, 
             start_date, expiry_date, next_renewal_date, mp4_web_video_limit, jpg_png_image_limit) 
             VALUES (?, ?, ?, ?, 'active', ?, ?, ?, ?, ?)";

$insertStmt = $conn->prepare($insertSql);
$insertStmt->bind_param("issdsssii", $userId, $subscriptionId, $planType, $amount, 
                      $startDate, $expiryDate, $nextRenewalDate, $mp4WebVideoLimit, $jpgPngImageLimit);

if ($insertStmt->execute()) {
    $subscriptionDbId = $conn->insert_id;
    
    // Insert into payment history
    $paymentSql = "INSERT INTO payment_history (user_id, subscription_id, transaction_id, amount, payment_status) 
                  VALUES (?, ?, ?, ?, 'completed')";
    $paymentStmt = $conn->prepare($paymentSql);
    $paymentStmt->bind_param("iisd", $userId, $subscriptionDbId, $subscriptionId, $amount);
    $paymentStmt->execute();
    $paymentStmt->close();
    
    // Update session variables
    $_SESSION['subscription_status'] = 'active';
    $_SESSION['subscription_type'] = $planType;
    $_SESSION['subscription_expiry'] = $expiryDate;
    
    // Send confirmation email
    sendConfirmationEmail($userId, $planType, $amount, $expiryDate);
    
    // Success message
    $_SESSION['success'] = "Your subscription has been activated successfully!";
} else {
    // Error message
    $_SESSION['error'] = "Failed to activate subscription: " . $insertStmt->error;
}

$insertStmt->close();

// Redirect to dashboard
header("Location: dashboard.php");
exit;

/**
 * Send confirmation email to user
 */
function sendConfirmationEmail($userId, $planType, $amount, $expiryDate) {
    global $conn;
    
    // Get user email
    $sql = "SELECT email, full_name FROM users WHERE user_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $stmt->bind_result($email, $fullName);
    $stmt->fetch();
    $stmt->close();
    
    // Format expiry date
    $formattedDate = date('F j, Y', strtotime($expiryDate));
    
    // Format plan type for display
    $displayPlanType = ucfirst($planType);
    
    // Email subject
    $subject = "Subscription Confirmation - 3D Product Customizer";
    
    // Email message
    $message = "
    <html>
    <head>
        <title>Subscription Confirmation</title>
    </head>
    <body>
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd;'>
            <h2 style='color: #2c3e50;'>Thank you for your subscription!</h2>
            <p>Hello $fullName,</p>
            <p>Your subscription to our 3D Product Customizer service has been activated successfully.</p>
            
            <div style='background-color: #f9f9f9; padding: 15px; margin: 20px 0;'>
                <h3 style='margin-top: 0; color: #3498db;'>Subscription Details:</h3>
                <p><strong>Plan:</strong> $displayPlanType</p>
                <p><strong>Amount:</strong> $$amount</p>
                <p><strong>Valid Until:</strong> $formattedDate</p>
            </div>
            
            <p>You now have access to all features included in your subscription plan. If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
            
            <p>Best regards,<br>The 3D Product Customizer Team</p>
        </div>
    </body>
    </html>
    ";
    
    // Headers for HTML email
    $headers = "MIME-Version: 1.0\r\n";
    $headers .= "Content-type: text/html; charset=UTF-8\r\n";
    $headers .= "From: 3D Product Customizer <<EMAIL>>\r\n";
    
    // Send the email
    mail($email, $subject, $message, $headers);
}
?>
