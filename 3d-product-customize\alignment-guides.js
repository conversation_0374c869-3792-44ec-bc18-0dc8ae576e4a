/**
 * AlignmentGuides - Provides visual guides and snapping functionality for canvas objects
 * Helps users align objects more precisely during editing
 */
class AlignmentGuides {
  constructor(editorCore) {
    this.editor = editorCore;
    this.canvas = editorCore.canvas;
    this.isEnabled = true;
    this.snapThreshold = 10;
    this.guidelines = [];
    
    // Initialize
    this.init();
  }

  /**
   * Initialize alignment guides
   */
  init() {
    // Set up event listeners
    this.setupEventListeners();
  }

  /**
   * Set up event listeners for alignment guides
   */
  setupEventListeners() {
    // Add guides during object movement
    this.canvas.on('object:moving', (e) => {
      if (!this.isEnabled) return;
      
      this.clearGuides();
      this.showGuides(e.target);
      this.snapToGuides(e.target);
    });

    // Clear guides when object movement stops
    this.canvas.on('object:modified', () => {
      this.clearGuides();
    });
  }

  /**
   * Generate and show alignment guides for an object
   * @param {Object} activeObject - The object being moved
   */
  showGuides(activeObject) {
    const canvasObjects = this.canvas.getObjects();
    
    // Skip if the active object is the only one
    if (canvasObjects.length <= 1) return;
    
    const activeObjectCenter = activeObject.getCenterPoint();
    const activeObjectBounds = activeObject.getBoundingRect();
    
    // Create guides for alignment with other objects
    canvasObjects.forEach(obj => {
      if (obj === activeObject) return;
      
      const objCenter = obj.getCenterPoint();
      const objBounds = obj.getBoundingRect();
      
      // Vertical center alignment
      if (Math.abs(activeObjectCenter.x - objCenter.x) < this.snapThreshold) {
        this.addVerticalGuide(objCenter.x);
      }
      
      // Horizontal center alignment
      if (Math.abs(activeObjectCenter.y - objCenter.y) < this.snapThreshold) {
        this.addHorizontalGuide(objCenter.y);
      }
      
      // Left edge alignment
      if (Math.abs(activeObjectBounds.left - objBounds.left) < this.snapThreshold) {
        this.addVerticalGuide(objBounds.left);
      }
      
      // Right edge alignment
      if (Math.abs(activeObjectBounds.left + activeObjectBounds.width - (objBounds.left + objBounds.width)) < this.snapThreshold) {
        this.addVerticalGuide(objBounds.left + objBounds.width);
      }
      
      // Top edge alignment
      if (Math.abs(activeObjectBounds.top - objBounds.top) < this.snapThreshold) {
        this.addHorizontalGuide(objBounds.top);
      }
      
      // Bottom edge alignment
      if (Math.abs(activeObjectBounds.top + activeObjectBounds.height - (objBounds.top + objBounds.height)) < this.snapThreshold) {
        this.addHorizontalGuide(objBounds.top + objBounds.height);
      }
    });
    
    // Add canvas center guides
    const canvasCenter = {
      x: this.canvas.width / 2,
      y: this.canvas.height / 2
    };
    
    if (Math.abs(activeObjectCenter.x - canvasCenter.x) < this.snapThreshold) {
      this.addVerticalGuide(canvasCenter.x);
    }
    
    if (Math.abs(activeObjectCenter.y - canvasCenter.y) < this.snapThreshold) {
      this.addHorizontalGuide(canvasCenter.y);
    }
  }

  /**
   * Add a vertical guideline
   * @param {number} x - X coordinate for the guide
   */
  addVerticalGuide(x) {
    const guide = new fabric.Line(
      [x, 0, x, this.canvas.height],
      {
        stroke: '#4285f4',
        strokeWidth: 1,
        strokeDashArray: [5, 5],
        selectable: false,
        evented: false,
        excludeFromExport: true,
        guideLine: true
      }
    );
    
    this.canvas.add(guide);
    this.guidelines.push(guide);
    this.canvas.renderAll();
  }

  /**
   * Add a horizontal guideline
   * @param {number} y - Y coordinate for the guide
   */
  addHorizontalGuide(y) {
    const guide = new fabric.Line(
      [0, y, this.canvas.width, y],
      {
        stroke: '#4285f4',
        strokeWidth: 1,
        strokeDashArray: [5, 5],
        selectable: false,
        evented: false,
        excludeFromExport: true,
        guideLine: true
      }
    );
    
    this.canvas.add(guide);
    this.guidelines.push(guide);
    this.canvas.renderAll();
  }

  /**
   * Snap object position to guidelines
   * @param {Object} activeObject - The object being moved
   */
  snapToGuides(activeObject) {
    const canvasObjects = this.canvas.getObjects();
    const activeObjectCenter = activeObject.getCenterPoint();
    const activeObjectBounds = activeObject.getBoundingRect();
    
    // Skip if the active object is the only one
    if (canvasObjects.length <= 1) return;
    
    let snappedX = false;
    let snappedY = false;
    
    // Snap to other objects
    canvasObjects.forEach(obj => {
      if (obj === activeObject || snappedX && snappedY) return;
      
      const objCenter = obj.getCenterPoint();
      const objBounds = obj.getBoundingRect();
      
      // Snap to vertical center alignment
      if (!snappedX && Math.abs(activeObjectCenter.x - objCenter.x) < this.snapThreshold) {
        const newLeft = activeObject.left + (objCenter.x - activeObjectCenter.x);
        activeObject.set({ left: newLeft });
        snappedX = true;
      }
      
      // Snap to horizontal center alignment
      if (!snappedY && Math.abs(activeObjectCenter.y - objCenter.y) < this.snapThreshold) {
        const newTop = activeObject.top + (objCenter.y - activeObjectCenter.y);
        activeObject.set({ top: newTop });
        snappedY = true;
      }
      
      // Snap left edges
      if (!snappedX && Math.abs(activeObjectBounds.left - objBounds.left) < this.snapThreshold) {
        const newLeft = activeObject.left + (objBounds.left - activeObjectBounds.left);
        activeObject.set({ left: newLeft });
        snappedX = true;
      }
      
      // Snap right edges
      if (!snappedX && Math.abs(activeObjectBounds.left + activeObjectBounds.width - 
          (objBounds.left + objBounds.width)) < this.snapThreshold) {
        const newLeft = activeObject.left + ((objBounds.left + objBounds.width) - 
                                         (activeObjectBounds.left + activeObjectBounds.width));
        activeObject.set({ left: newLeft });
        snappedX = true;
      }
      
      // Snap top edges
      if (!snappedY && Math.abs(activeObjectBounds.top - objBounds.top) < this.snapThreshold) {
        const newTop = activeObject.top + (objBounds.top - activeObjectBounds.top);
        activeObject.set({ top: newTop });
        snappedY = true;
      }
      
      // Snap bottom edges
      if (!snappedY && Math.abs(activeObjectBounds.top + activeObjectBounds.height - 
          (objBounds.top + objBounds.height)) < this.snapThreshold) {
        const newTop = activeObject.top + ((objBounds.top + objBounds.height) - 
                                       (activeObjectBounds.top + activeObjectBounds.height));
        activeObject.set({ top: newTop });
        snappedY = true;
      }
    });
    
    // Snap to canvas center
    const canvasCenter = {
      x: this.canvas.width / 2,
      y: this.canvas.height / 2
    };
    
    if (!snappedX && Math.abs(activeObjectCenter.x - canvasCenter.x) < this.snapThreshold) {
      const newLeft = activeObject.left + (canvasCenter.x - activeObjectCenter.x);
      activeObject.set({ left: newLeft });
    }
    
    if (!snappedY && Math.abs(activeObjectCenter.y - canvasCenter.y) < this.snapThreshold) {
      const newTop = activeObject.top + (canvasCenter.y - activeObjectCenter.y);
      activeObject.set({ top: newTop });
    }
    
    // Update object coordinates and render
    activeObject.setCoords();
    this.canvas.renderAll();
  }

  /**
   * Clear all guidelines from the canvas
   */
  clearGuides() {
    this.guidelines.forEach(guide => {
      this.canvas.remove(guide);
    });
    this.guidelines = [];
    this.canvas.renderAll();
  }

  /**
   * Enable or disable alignment guides
   * @param {boolean} enabled - Whether guides should be enabled
   */
  setEnabled(enabled) {
    this.isEnabled = enabled;
    if (!enabled) {
      this.clearGuides();
    }
  }

  /**
   * Set the snap threshold for alignment
   * @param {number} threshold - Snap threshold in pixels
   */
  setSnapThreshold(threshold) {
    this.snapThreshold = threshold;
  }
}
