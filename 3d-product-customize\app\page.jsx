'use client';

import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export default function HomePage() {
  const router = useRouter();

  // Function to navigate to the editor
  const goToEditor = () => {
    router.push('/editor');
  };

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #2c3e50 0%, #34495e 100%)',
      padding: '20px',
      color: 'white',
      fontFamily: 'Arial, sans-serif'
    }}>
      <h1 style={{ 
        fontSize: '3rem', 
        marginBottom: '2rem',
        textAlign: 'center'
      }}>
        3D Product Customizer
      </h1>
      
      <div style={{
        background: 'rgba(255, 255, 255, 0.1)',
        borderRadius: '10px',
        padding: '2rem',
        maxWidth: '800px',
        textAlign: 'center',
        marginBottom: '3rem'
      }}>
        <h2 style={{ marginTop: 0 }}>Welcome to our Advanced 3D Editor</h2>
        <p style={{ fontSize: '1.2rem', lineHeight: '1.6' }}>
          Design and customize products with our powerful Fabric.js editor.
          Create stunning designs with text, images, and see them in real-time 3D preview.
        </p>
      </div>
      
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        gap: '20px',
        flexWrap: 'wrap'
      }}>
        <button 
          onClick={goToEditor}
          style={{
            background: '#3498db',
            color: 'white',
            border: 'none',
            padding: '15px 30px',
            fontSize: '1.2rem',
            borderRadius: '5px',
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            boxShadow: '0 4px 6px rgba(0,0,0,0.1)'
          }}
          onMouseOver={(e) => e.currentTarget.style.background = '#2980b9'}
          onMouseOut={(e) => e.currentTarget.style.background = '#3498db'}
        >
          Launch Editor
        </button>
        
        <a 
          href="#features"
          style={{
            background: 'transparent',
            color: 'white',
            border: '2px solid white',
            padding: '15px 30px',
            fontSize: '1.2rem',
            borderRadius: '5px',
            cursor: 'pointer',
            textDecoration: 'none',
            transition: 'all 0.3s ease'
          }}
          onMouseOver={(e) => {
            e.currentTarget.style.background = 'rgba(255,255,255,0.1)';
          }}
          onMouseOut={(e) => {
            e.currentTarget.style.background = 'transparent';
          }}
        >
          Learn More
        </a>
      </div>
      
      <div id="features" style={{
        marginTop: '5rem',
        width: '100%',
        maxWidth: '1200px'
      }}>
        <h2 style={{ textAlign: 'center', marginBottom: '3rem' }}>Key Features</h2>
        
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
          gap: '30px',
          padding: '0 20px'
        }}>
          <div style={{
            background: 'rgba(255, 255, 255, 0.1)',
            padding: '30px',
            borderRadius: '8px',
            textAlign: 'center'
          }}>
            <h3>Real-time 3D Preview</h3>
            <p>See your design applied to 3D models in real-time as you edit</p>
          </div>
          
          <div style={{
            background: 'rgba(255, 255, 255, 0.1)',
            padding: '30px',
            borderRadius: '8px',
            textAlign: 'center'
          }}>
            <h3>Multiple Canvas Sizes</h3>
            <p>Choose from various aspect ratios and customize your canvas size</p>
          </div>
          
          <div style={{
            background: 'rgba(255, 255, 255, 0.1)',
            padding: '30px',
            borderRadius: '8px',
            textAlign: 'center'
          }}>
            <h3>Advanced Text Controls</h3>
            <p>Extensive text styling options including shadows, borders, and alignment</p>
          </div>
        </div>
      </div>
      
      <footer style={{
        marginTop: '5rem',
        textAlign: 'center',
        padding: '20px',
        borderTop: '1px solid rgba(255,255,255,0.1)',
        width: '100%'
      }}>
        <p>&copy; 2024 3D Product Customizer. All rights reserved.</p>
      </footer>
    </div>
  );
}
