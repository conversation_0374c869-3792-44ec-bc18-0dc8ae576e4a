<!DOCTYPE html>
<html lang="ar">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>مشهد Three.js ومحرر Pixie متجاوب</title>
  <style>
    /* الأساسيات */
    html, body {
      height: 100%;
      margin: 0;
      padding: 0;
    }
    /* الحاوية الرئيسية للتقسيم */
    #app {
      display: flex;
      flex-direction: column; /* الوضع العمودي افتراضيًا */
      height: 100vh;
    }
    /* عند الوضع الأفقي، نقسم الشاشة إلى عمودين */
    @media (orientation: landscape) {
      #app {
        flex-direction: row;
      }
    }
    /* الحاويتان للمشهد والمحرر */
    #scene-container,
    #editor-container {
      flex: 1;
      position: relative;
      overflow: hidden;
    }
    /* ض<PERSON>ان احتلال جميع عناصر canvas المساحة المخصصة لها */
    #scene-container canvas,
    #editor-container canvas {
      width: 100%;
      height: 100%;
      display: block;
    }
    /* في حال أضفت Pixie عناصر داخلية خاصة */
    .pixie-container,
    .pixie-editor-canvas {
      width: 100%;
      height: 100%;
    }
    /* تنسيق أدوات التحكم في المشهد */
    #controls, #background-controls {
      position: absolute;
      background: rgba(255,255,255,0.9);
      padding: 10px;
      border-radius: 8px;
      box-shadow: 0 4px 6px rgba(0,0,0,0.1);
      z-index: 10;
    }
    #controls {
      top: 10px;
      left: 10px;
    }
    #background-controls {
      bottom: 10px;
      left: 10px;
    }
  </style>
  
  <!-- تحميل مكتبات Three.js المطلوبة -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r134/three.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/three/examples/js/controls/OrbitControls.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/three/examples/js/loaders/GLTFLoader.js"></script>
</head>
<body>
  <!-- الحاوية الرئيسية -->
  <div id="app">
    <!-- قسم المشهد ثلاثي الأبعاد -->
    <div id="scene-container">
      <canvas id="modelCanvas"></canvas>
      <div id="controls">
        <h3>تحرير النموذج</h3>
        <input type="color" id="color-picker" title="اختيار لون"><br><br>
        <button id="image-picker" title="رفع صورة">🖼️</button>
        <input type="file" id="image-input" accept="image/*" style="display:none">
      </div>
      <div id="background-controls">
        <h3>خلفية النموذج</h3>
        <input type="color" id="background-color" value="#808080">
      </div>
    </div>
  
    <!-- قسم محرر Pixie -->
    <div id="editor-container">
      <!-- تحميل Pixie -->
      <script src="js/pixie.umd.js?29"></script>
      <script>
        // تهيئة Pixie داخل الحاوية المُخصصة له
        const pixie = new Pixie({
          selector: "#editor-container",
          baseUrl: "../jquery.fancybox.min",
          activeLanguage: "ar",
          ui: {
            activeTheme: "i2img",
            themes: [{
              name: "i2img",
              colors: {
                "--be-primary": "#7961f2",
                "--be-primary-light": "#d0caff",
                "--be-primary-dark": "#5139ff"
              }
            }]
          },
          tools: {
            text: { defaultText: "مرحبا" }
          }
        });
        window.pixie = pixie;
      </script>
    </div>
  </div>
  
  <script>
    // تأخير بسيط لضمان تحميل محتوى Pixie داخل #editor-container
    setTimeout(function(){
      // تحديد أول canvas داخل محرر Pixie
      const pixieCanvas = document.querySelector("#editor-container canvas");
      if (!pixieCanvas) {
        console.error("لم يتم العثور على canvas داخل محرر Pixie");
        return;
      }
      // إنشاء نسيج ديناميكي باستخدام canvas محرر Pixie
      const pixieTexture = new THREE.CanvasTexture(pixieCanvas);
      
      let scene, camera, renderer, model, orbit;
      const sceneContainer = document.getElementById("scene-container");
      
      function init() {
        scene = new THREE.Scene();
        camera = new THREE.PerspectiveCamera(
          60,
          sceneContainer.clientWidth / sceneContainer.clientHeight,
          0.1,
          1000
        );
        renderer = new THREE.WebGLRenderer({
          canvas: document.getElementById("modelCanvas"),
          antialias: true
        });
        renderer.setSize(sceneContainer.clientWidth, sceneContainer.clientHeight);
        camera.position.set(0, 1, 3);
        orbit = new THREE.OrbitControls(camera, renderer.domElement);
  
        // تحميل نموذج glTF وتطبيق نسيج Pixie عليه
        const loader = new THREE.GLTFLoader();
        loader.load("model.gltf", function(gltf) {
          model = gltf.scene;
          model.traverse(function(child) {
            if (child.isMesh) {
              child.material.map = pixieTexture;
              child.material.needsUpdate = true;
            }
          });
          scene.add(model);
        }, undefined, function(error) {
          console.error("خطأ أثناء تحميل النموذج:", error);
        });
  
        // إضافة إضاءة للمشهد
        const light = new THREE.DirectionalLight(0xffffff, 1);
        light.position.set(1, 1, 1);
        scene.add(light);
  
        animate();
      }
      
      function animate() {
        requestAnimationFrame(animate);
        orbit.update();
        // تحديث النسيج في كل إطار لتعكس التعديلات في Pixie
        pixieTexture.needsUpdate = true;
        renderer.render(scene, camera);
      }
      init();
  
      // تغيير لون النموذج عند اختيار لون جديد
      document.getElementById("color-picker").addEventListener("input", function() {
        if (model) {
          model.traverse(function(child) {
            if (child.isMesh) {
              child.material.color.set(this.value);
            }
          }.bind(this));
        }
      });
  
      // تغيير خلفية المشهد
      document.getElementById("background-color").addEventListener("input", function() {
        scene.background = new THREE.Color(this.value);
      });
  
      // تحديث أبعاد المشهد عند تغيير حجم النافذة أو اتجاه الهاتف
      window.addEventListener("resize", function() {
        camera.aspect = sceneContainer.clientWidth / sceneContainer.clientHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(sceneContainer.clientWidth, sceneContainer.clientHeight);
      });
    }, 1500); // تأخير بسيط لضمان تحميل Pixie
  </script>
</body>
</html>