/**
 * three-scene.js
 * يحتوي على تعريفات وإعدادات مشهد Three.js
 */

const threeScene = {
    // المتغيرات الرئيسية
    scene: null,
    camera: null,
    renderer: null,
    controls: null,
    model: null,
    mixer: null,
    clock: new THREE.Clock(),
    
    // تهيئة مشهد Three.js
    init: function(containerId) {
        // الحصول على حاوية المشهد
        const container = document.getElementById(containerId);
        const width = container.clientWidth;
        const height = container.clientHeight;
        
        // إنشاء المشهد
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x222222);
        
        // إنشاء الكاميرا
        this.camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
        this.camera.position.set(0, 1, 5);
        
        // إنشاء المُصيِّر
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.renderer.setSize(width, height);
        this.renderer.setPixelRatio(window.devicePixelRatio);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        container.appendChild(this.renderer.domElement);
        
        // إضافة عناصر التحكم في الكاميرا
        this.controls = new OrbitControls(this.camera, this.renderer.domElement);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;
        
        // إضافة الإضاءة
        this.addLights();
        
        // إضافة الأرضية
        this.addFloor();
        
        // تحميل النموذج الافتراضي
        this.loadDefaultModel();
        
        // إضافة مستمع لتغيير حجم النافذة
        window.addEventListener('resize', () => this.onWindowResize());
        
        // بدء حلقة الرسم
        this.animate();
        
        return this;
    },
    
    // إضافة الإضاءة إلى المشهد
    addLights: function() {
        // إضاءة محيطية
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
        this.scene.add(ambientLight);
        
        // إضاءة اتجاهية
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(5, 10, 7.5);
        directionalLight.castShadow = true;
        
        // إعدادات ظل الإضاءة الاتجاهية
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 50;
        directionalLight.shadow.camera.left = -10;
        directionalLight.shadow.camera.right = 10;
        directionalLight.shadow.camera.top = 10;
        directionalLight.shadow.camera.bottom = -10;
        
        this.scene.add(directionalLight);
        
        // إضاءة نقطية
        const pointLight = new THREE.PointLight(0xffffff, 1, 100);
        pointLight.position.set(-5, 5, -5);
        this.scene.add(pointLight);
    },
    
    // إضافة أرضية إلى المشهد
    addFloor: function() {
        const floorGeometry = new THREE.PlaneGeometry(20, 20);
        const floorMaterial = new THREE.MeshStandardMaterial({
            color: 0x444444,
            roughness: 0.7,
            metalness: 0.1
        });
        const floor = new THREE.Mesh(floorGeometry, floorMaterial);
        floor.rotation.x = -Math.PI / 2; // تدوير الأرضية لتكون أفقية
        floor.position.y = -1;
        floor.receiveShadow = true;
        this.scene.add(floor);
        
        // إضافة شبكة
        const gridHelper = new THREE.GridHelper(20, 20, 0x888888, 0x444444);
        gridHelper.position.y = -0.99;
        this.scene.add(gridHelper);
    },
    
    // تحميل نموذج GLTF افتراضي
    loadDefaultModel: function() {
        // إنشاء نموذج مكعب افتراضي حتى يتم تحميل النموذج GLTF
        const geometry = new THREE.BoxGeometry(1, 1, 1);
        const material = new THREE.MeshStandardMaterial({ color: 0x00ff00 });
        const cube = new THREE.Mesh(geometry, material);
        cube.castShadow = true;
        cube.position.y = 0.5;
        this.scene.add(cube);
        this.defaultModel = cube;
        
        // محاولة تحميل نموذج GLTF
        const loader = new GLTFLoader();
        
        // يمكن استبدال هذا الرابط بنموذج GLTF محلي أو عن بعد
        const modelUrl = 'https://threejs.org/examples/models/gltf/DamagedHelmet/glTF/DamagedHelmet.gltf';
        
        loader.load(
            modelUrl,
            (gltf) => {
                // إزالة النموذج الافتراضي
                this.scene.remove(this.defaultModel);
                
                // إضافة النموذج المحمل
                this.model = gltf.scene;
                this.model.traverse((node) => {
                    if (node.isMesh) {
                        node.castShadow = true;
                        node.receiveShadow = true;
                    }
                });
                
                // تعديل حجم وموضع النموذج
                const box = new THREE.Box3().setFromObject(this.model);
                const size = box.getSize(new THREE.Vector3()).length();
                const center = box.getCenter(new THREE.Vector3());
                
                this.model.position.x = -center.x;
                this.model.position.y = -center.y + size / 2;
                this.model.position.z = -center.z;
                
                this.scene.add(this.model);
                
                // إعداد الرسوم المتحركة إذا كانت موجودة
                if (gltf.animations && gltf.animations.length) {
                    this.mixer = new THREE.AnimationMixer(this.model);
                    const action = this.mixer.clipAction(gltf.animations[0]);
                    action.play();
                }
            },
            (xhr) => {
                console.log((xhr.loaded / xhr.total * 100) + '% loaded');
            },
            (error) => {
                console.error('Error loading model:', error);
            }
        );
    },
    
    // تحميل نموذج GLTF من URL
    loadModelFromURL: function(url) {
        const loader = new GLTFLoader();
        
        loader.load(
            url,
            (gltf) => {
                // إزالة النموذج الحالي إذا كان موجودًا
                if (this.model) {
                    this.scene.remove(this.model);
                }
                
                // إضافة النموذج الجديد
                this.model = gltf.scene;
                this.model.traverse((node) => {
                    if (node.isMesh) {
                        node.castShadow = true;
                        node.receiveShadow = true;
                    }
                });
                
                // تعديل حجم وموضع النموذج
                const box = new THREE.Box3().setFromObject(this.model);
                const size = box.getSize(new THREE.Vector3()).length();
                const center = box.getCenter(new THREE.Vector3());
                
                this.model.position.x = -center.x;
                this.model.position.y = -center.y + size / 2;
                this.model.position.z = -center.z;
                
                this.scene.add(this.model);
                
                // إعداد الرسوم المتحركة إذا كانت موجودة
                if (gltf.animations && gltf.animations.length) {
                    this.mixer = new THREE.AnimationMixer(this.model);
                    const action = this.mixer.clipAction(gltf.animations[0]);
                    action.play();
                }
            },
            (xhr) => {
                console.log((xhr.loaded / xhr.total * 100) + '% loaded');
            },
            (error) => {
                console.error('Error loading model:', error);
            }
        );
    },
    
    // تحديث حجم المُصيِّر عند تغيير حجم النافذة
    onWindowResize: function() {
        const container = this.renderer.domElement.parentElement;
        const width = container.clientWidth;
        const height = container.clientHeight;
        
        this.camera.aspect = width / height;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(width, height);
    },
    
    // حلقة الرسم
    animate: function() {
        requestAnimationFrame(() => this.animate());
        
        // تحديث عناصر التحكم
        this.controls.update();
        
        // تحديث الرسوم المتحركة
        if (this.mixer) {
            this.mixer.update(this.clock.getDelta());
        }
        
        // تدوير النموذج الافتراضي
        if (this.defaultModel && this.scene.children.includes(this.defaultModel)) {
            this.defaultModel.rotation.y += 0.01;
        }
        
        // رسم المشهد
        this.renderer.render(this.scene, this.camera);
    },
    
    // تطبيق نسيج من لوحة Fabric.js على النموذج
    applyTextureFromCanvas: function(canvas) {
        if (!this.model) return;
        
        // إنشاء نسيج من لوحة Fabric.js
        const texture = new THREE.CanvasTexture(canvas.getElement());
        texture.needsUpdate = true;
        
        // تطبيق النسيج على جميع المواد في النموذج
        this.model.traverse((node) => {
            if (node.isMesh && node.material) {
                if (Array.isArray(node.material)) {
                    node.material.forEach(material => {
                        material.map = texture;
                        material.needsUpdate = true;
                    });
                } else {
                    node.material.map = texture;
                    node.material.needsUpdate = true;
                }
            }
        });
    },
    
    // تغيير لون النموذج
    changeModelColor: function(color) {
        if (!this.model) return;
        
        const newColor = new THREE.Color(color);
        
        this.model.traverse((node) => {
            if (node.isMesh && node.material) {
                if (Array.isArray(node.material)) {
                    node.material.forEach(material => {
                        material.color = newColor;
                        material.needsUpdate = true;
                    });
                } else {
                    node.material.color = newColor;
                    node.material.needsUpdate = true;
                }
            }
        });
    },
    
    // إضافة كائن ثلاثي الأبعاد بناءً على شكل من Fabric.js
    addObjectFromFabricShape: function(fabricObject) {
        if (!fabricObject) return;
        
        let geometry, material, mesh;
        
        // إنشاء هندسة بناءً على نوع كائن Fabric.js
        switch (fabricObject.type) {
            case 'rect':
                geometry = new THREE.BoxGeometry(
                    fabricObject.width * 0.01,
                    fabricObject.height * 0.01,
                    0.1
                );
                break;
            case 'circle':
                geometry = new THREE.SphereGeometry(
                    fabricObject.radius * 0.01,
                    32,
                    32
                );
                break;
            case 'triangle':
                geometry = new THREE.ConeGeometry(
                    fabricObject.width * 0.005,
                    fabricObject.height * 0.01,
                    3
                );
                break;
            default:
                // كائن افتراضي للأنواع غير المدعومة
                geometry = new THREE.BoxGeometry(0.5, 0.5, 0.5);
        }
        
        // إنشاء مادة بلون الكائن
        material = new THREE.MeshStandardMaterial({
            color: fabricObject.fill || 0xff0000,
            roughness: 0.7,
            metalness: 0.3
        });
        
        // إنشاء شبكة
        mesh = new THREE.Mesh(geometry, material);
        
        // تعيين موضع الشبكة بناءً على موضع كائن Fabric.js
        // تحويل إحداثيات Fabric.js إلى إحداثيات Three.js
        const canvasWidth = fabricObject.canvas.width;
        const canvasHeight = fabricObject.canvas.height;
        
        mesh.position.x = (fabricObject.left - canvasWidth / 2) * 0.01;
        mesh.position.y = (canvasHeight / 2 - fabricObject.top) * 0.01;
        mesh.position.z = 0;
        
        // تطبيق الدوران
        if (fabricObject.angle) {
            mesh.rotation.z = -fabricObject.angle * Math.PI / 180;
        }
        
        // إضافة الشبكة إلى المشهد
        this.scene.add(mesh);
        
        return mesh;
    }
};
