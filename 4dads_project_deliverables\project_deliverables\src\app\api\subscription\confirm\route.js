// src/app/api/subscription/confirm/route.js
// [!] هذا الملف يتعامل مع تأكيد الاشتراكات من PayPal
// [!] يجب تعديل متغيرات الاتصال بقاعدة البيانات وPayPal حسب إعداداتك

import { NextResponse } from 'next/server';

// [!] متغيرات يجب تعديلها: DB_HOST, DB_USER, DB_PASSWORD, DB_NAME, PAYPAL_CLIENT_ID, PAYPAL_SECRET
const DB_CONFIG = {
  host: 'DB_HOST',      // مثال: 'mysql-4dads.hostinger.com'
  user: 'DB_USER',      // مثال: 'u123456789_4dads'
  password: 'DB_PASSWORD', // كلمة المرور الخاصة بقاعدة البيانات
  database: 'DB_NAME',  // مثال: 'u123456789_4dads'
};

const PAYPAL_CONFIG = {
  clientId: 'PAYPAL_CLIENT_ID',
  secret: 'PAYPAL_SECRET',
  environment: 'sandbox' // استخدم 'production' في بيئة الإنتاج
};

export async function POST(request) {
  try {
    const { subscriptionId, planId, details } = await request.json();

    // التحقق من البيانات المدخلة
    if (!subscriptionId || !planId) {
      return NextResponse.json(
        { success: false, message: 'بيانات الاشتراك غير مكتملة' },
        { status: 400 }
      );
    }

    // [!] في بيئة الإنتاج، يجب التحقق من صحة الاشتراك مع PayPal
    // هذا مثال لكيفية تنفيذ ذلك
    
    /*
    // الحصول على رمز الوصول من PayPal
    const auth = Buffer.from(`${PAYPAL_CONFIG.clientId}:${PAYPAL_CONFIG.secret}`).toString('base64');
    const tokenResponse = await fetch(`https://api.${PAYPAL_CONFIG.environment === 'sandbox' ? 'sandbox.' : ''}paypal.com/v1/oauth2/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${auth}`
      },
      body: 'grant_type=client_credentials'
    });
    
    const { access_token } = await tokenResponse.json();
    
    // التحقق من تفاصيل الاشتراك
    const subscriptionResponse = await fetch(`https://api.${PAYPAL_CONFIG.environment === 'sandbox' ? 'sandbox.' : ''}paypal.com/v1/billing/subscriptions/${subscriptionId}`, {
      headers: {
        'Authorization': `Bearer ${access_token}`,
        'Content-Type': 'application/json'
      }
    });
    
    const subscription = await subscriptionResponse.json();
    
    // التحقق من حالة الاشتراك
    if (subscription.status !== 'ACTIVE' && subscription.status !== 'APPROVED') {
      return NextResponse.json(
        { success: false, message: 'الاشتراك غير نشط' },
        { status: 400 }
      );
    }
    
    // استيراد مكتبة mysql2
    import mysql from 'mysql2/promise';
    
    // إنشاء اتصال بقاعدة البيانات
    const connection = await mysql.createConnection(DB_CONFIG);
    
    // الحصول على معرف المستخدم من الجلسة
    // [!] يجب تنفيذ نظام الجلسات والمصادقة
    const userId = 1; // قيمة وهمية للاختبار
    
    // الحصول على تفاصيل خطة الاشتراك
    const [planRows] = await connection.execute(
      'SELECT * FROM subscription_plans WHERE id = ?',
      [planId]
    );
    
    if (planRows.length === 0) {
      await connection.end();
      return NextResponse.json(
        { success: false, message: 'خطة الاشتراك غير موجودة' },
        { status: 400 }
      );
    }
    
    const plan = planRows[0];
    
    // حساب تاريخ انتهاء الاشتراك
    const startDate = new Date();
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + plan.duration_days);
    
    // إنشاء اشتراك جديد
    const [result] = await connection.execute(
      'INSERT INTO user_subscriptions (user_id, plan_id, status, start_date, end_date, paypal_subscription_id, auto_renew, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())',
      [userId, planId, 'active', startDate, endDate, subscriptionId, true]
    );
    
    const subscriptionDbId = result.insertId;
    
    // تسجيل المدفوعات
    await connection.execute(
      'INSERT INTO payments (user_id, subscription_id, amount, currency, payment_method, transaction_id, status, payment_date, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())',
      [userId, subscriptionDbId, plan.price, 'USD', 'paypal', subscription.id, 'completed']
    );
    
    // إغلاق الاتصال بقاعدة البيانات
    await connection.end();
    */
    
    // للاختبار، نعيد استجابة نجاح
    return NextResponse.json(
      { 
        success: true, 
        message: 'تم تأكيد الاشتراك بنجاح',
        subscription: {
          id: 1, // قيمة وهمية للاختبار
          plan_id: planId,
          paypal_subscription_id: subscriptionId,
          start_date: new Date().toISOString(),
          end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 يوم من الآن
        }
      },
      { status: 200 }
    );
    
  } catch (error) {
    console.error('Subscription confirmation error:', error);
    return NextResponse.json(
      { success: false, message: 'حدث خطأ في الخادم أثناء تأكيد الاشتراك' },
      { status: 500 }
    );
  }
}
