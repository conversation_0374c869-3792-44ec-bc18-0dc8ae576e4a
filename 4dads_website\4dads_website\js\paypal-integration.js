// ملف تكامل PayPal
let paypalButtonsRendered = false;

// إعداد أزرار PayPal
function initializePayPalButtons() {
    if (paypalButtonsRendered) return;
    
    // التحقق من وجود PayPal SDK
    if (typeof paypal === 'undefined') {
        console.error('PayPal SDK غير محمل');
        return;
    }
    
    // رندر أزرار PayPal للاشتراك الشهري والسنوي
    paypal.Buttons({
        style: {
            shape: 'rect',
            color: 'gold',
            layout: 'vertical',
            label: 'subscribe'
        },
        createSubscription: function(data, actions) {
            const selectedPlan = document.querySelector('input[name="plan"]:checked').value;
            let planId;
            
            switch(selectedPlan) {
                case 'monthly':
                    planId = 'P-MONTHLY-PLAN-ID'; // استبدل بمعرف الخطة الشهرية الفعلي
                    break;
                case 'yearly':
                    planId = 'P-YEARLY-PLAN-ID'; // استبدل بمعرف الخطة السنوية الفعلي
                    break;
                default:
                    planId = 'P-MONTHLY-PLAN-ID';
            }
            
            return actions.subscription.create({
                plan_id: planId
            });
        },
        onApprove: function(data, actions) {
            // حفظ معرف الاشتراك
            document.getElementById('subscription_id').value = data.subscriptionID;
            
            // إظهار زر التسجيل
            document.getElementById('registerButton').style.display = 'block';
            
            // إظهار رسالة نجاح
            showMessage('تم الاشتراك بنجاح! يمكنك الآن إكمال التسجيل.', 'success');
        },
        onError: function(err) {
            console.error('خطأ في PayPal:', err);
            showMessage('حدث خطأ في عملية الدفع. يرجى المحاولة مرة أخرى.', 'error');
        },
        onCancel: function(data) {
            showMessage('تم إلغاء عملية الدفع.', 'error');
        }
    }).render('#paypal-button-container');
    
    paypalButtonsRendered = true;
}

// معالجة تغيير نوع الاشتراك
function handleSubscriptionChange() {
    const planRadios = document.querySelectorAll('input[name="plan"]');
    
    // حاويات معلومات الاشتراك
    const monthlyInfo = document.getElementById('monthly-info');
    const yearlyInfo = document.getElementById('yearly-info');
    const arabInfo = document.getElementById('arab-info');
    const freeInfo = document.getElementById('free-info');
    
    // حاويات أزرار الدفع
    const standardContainer = document.getElementById('standard-subscription-container');
    const arabContainer = document.getElementById('arab-subscription-container');
    const freeContainer = document.getElementById('free-subscription-container');
    
    function hideAllContainers() {
        // إخفاء كروت المعلومات
        if (monthlyInfo) monthlyInfo.style.display = 'none';
        if (yearlyInfo) yearlyInfo.style.display = 'none';
        if (arabInfo) arabInfo.style.display = 'none';
        if (freeInfo) freeInfo.style.display = 'none';
        
        // إخفاء حاويات الدفع
        if (standardContainer) standardContainer.style.display = 'none';
        if (arabContainer) arabContainer.style.display = 'none';
        if (freeContainer) freeContainer.style.display = 'none';
    }
    
    planRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            hideAllContainers();
            
            switch(this.value) {
                case 'monthly':
                    if (monthlyInfo) monthlyInfo.style.display = 'block';
                    if (standardContainer) standardContainer.style.display = 'block';
                    break;
                case 'yearly':
                    if (yearlyInfo) yearlyInfo.style.display = 'block';
                    if (standardContainer) standardContainer.style.display = 'block';
                    break;
                case 'arab':
                    if (arabInfo) arabInfo.style.display = 'block';
                    if (arabContainer) arabContainer.style.display = 'block';
                    break;
                case 'free':
                    if (freeInfo) freeInfo.style.display = 'block';
                    if (freeContainer) freeContainer.style.display = 'block';
                    break;
            }
        });
    });
    
    // تفعيل الخيار الافتراضي
    const defaultOption = document.querySelector('input[name="plan"]:checked');
    if (defaultOption) {
        defaultOption.dispatchEvent(new Event('change'));
    }
}

// زر اختبار الدفع (للتطوير فقط)
function setupTestPayment() {
    const testButton = document.getElementById('testPaymentButton');
    if (testButton) {
        testButton.addEventListener('click', function() {
            // محاكاة نجاح الدفع للاختبار
            document.getElementById('subscription_id').value = 'TEST_SUBSCRIPTION_' + Date.now();
            document.getElementById('registerButton').style.display = 'block';
            showMessage('تم الاشتراك بنجاح (وضع الاختبار)! يمكنك الآن إكمال التسجيل.', 'success');
        });
    }
}

// تهيئة PayPal عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // انتظار تحميل PayPal SDK
    const checkPayPal = setInterval(() => {
        if (typeof paypal !== 'undefined') {
            clearInterval(checkPayPal);
            initializePayPalButtons();
        }
    }, 100);
    
    // إعداد معالجات الأحداث
    handleSubscriptionChange();
    setupTestPayment();
});

// دالة مساعدة لعرض الرسائل
function showMessage(message, type) {
    const messageDiv = document.getElementById('formMessage');
    if (messageDiv) {
        messageDiv.textContent = message;
        messageDiv.className = `message ${type}`;
        messageDiv.style.display = 'block';
        
        setTimeout(() => {
            messageDiv.style.display = 'none';
        }, 5000);
    }
}

