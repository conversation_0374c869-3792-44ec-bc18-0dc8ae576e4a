"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"

interface DebugPanelProps {
  partTextures: Record<string, string>
  partColors: Record<string, string>
}

export function DebugPanel({ partTextures, partColors }: DebugPanelProps) {
  const [isOpen, setIsOpen] = useState(false)

  if (!isOpen) {
    return (
      <Button
        className="absolute bottom-20 left-4 z-50 bg-red-600 text-white hover:bg-red-700"
        onClick={() => setIsOpen(true)}
      >
        Debug
      </Button>
    )
  }

  return (
    <div className="absolute bottom-20 left-4 z-50 max-h-80 w-80 overflow-auto rounded-md border border-gray-300 bg-white p-4 text-black shadow-lg">
      <div className="mb-2 flex items-center justify-between">
        <h3 className="text-lg font-bold">Debug Panel</h3>
        <Button variant="ghost" size="sm" onClick={() => setIsOpen(false)}>
          Close
        </Button>
      </div>

      <div className="mb-4">
        <h4 className="mb-1 font-semibold">Part Textures:</h4>
        {Object.keys(partTextures).length > 0 ? (
          <ul className="space-y-2">
            {Object.entries(partTextures).map(([part, textureUrl]) => (
              <li key={part} className="text-xs">
                <div className="font-medium">{part}:</div>
                <div className="mt-1 max-h-20 overflow-hidden text-gray-500">
                  {textureUrl ? (
                    <>
                      <div className="mb-1 truncate">{textureUrl.substring(0, 50)}...</div>
                      <img
                        src={textureUrl || "/placeholder.svg"}
                        alt={`Texture for ${part}`}
                        className="h-16 w-16 rounded border border-gray-300 object-cover"
                      />
                    </>
                  ) : (
                    "No texture"
                  )}
                </div>
              </li>
            ))}
          </ul>
        ) : (
          <div className="text-sm text-gray-500">No textures applied</div>
        )}
      </div>

      <div>
        <h4 className="mb-1 font-semibold">Part Colors:</h4>
        {Object.keys(partColors).length > 0 ? (
          <ul className="space-y-2">
            {Object.entries(partColors).map(([part, color]) => (
              <li key={part} className="flex items-center gap-2 text-sm">
                <div className="h-4 w-4 rounded-full border border-gray-300" style={{ backgroundColor: color }}></div>
                <span>
                  {part}: {color}
                </span>
              </li>
            ))}
          </ul>
        ) : (
          <div className="text-sm text-gray-500">No custom colors applied</div>
        )}
      </div>
    </div>
  )
}
