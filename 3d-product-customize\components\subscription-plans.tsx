"use client"

import { Check } from "lucide-react"

interface SubscriptionPlansProps {
  selectedPlan: string | null
  onSelectPlan: (plan: string) => void
}

export function SubscriptionPlans({ selectedPlan, onSelectPlan }: SubscriptionPlansProps) {
  const plans = [
    {
      id: "basic",
      name: "الأساسية",
      price: "$9.99",
      period: "شهرياً",
      features: ["تصميم 10 منتجات", "تصدير بجودة عادية", "دعم فني بالبريد الإلكتروني"],
    },
    {
      id: "pro",
      name: "الاحترافية",
      price: "$19.99",
      period: "شهرياً",
      features: ["تصميم غير محدود", "تصدير بجودة عالية", "دعم فني على مدار الساعة", "مكتبة نماذج إضافية"],
      popular: true,
    },
    {
      id: "premium",
      name: "المميزة",
      price: "$29.99",
      period: "شهرياً",
      features: [
        "جميع مميزات الخطة الاحترافية",
        "تصدير بجودة 4K",
        "دعم فني مخصص",
        "مكتبة نماذج حصرية",
        "تخصيص متقدم للمواد",
      ],
    },
  ]

  return (
    <div className="grid gap-4 md:grid-cols-3">
      {plans.map((plan) => (
        <div
          key={plan.id}
          className={`relative cursor-pointer rounded-lg border p-4 transition-all hover:border-[#D6A25E] ${
            selectedPlan === plan.id ? "border-[#D6A25E] bg-[#344464] shadow-md" : "border-[#344464] bg-[#1F2A45]"
          }`}
          onClick={() => onSelectPlan(plan.id)}
        >
          {plan.popular && (
            <div className="absolute -top-2 right-4 rounded-md bg-[#D6A25E] px-2 py-0.5 text-xs font-medium text-[#1F2A45]">
              الأكثر شيوعاً
            </div>
          )}
          <div className="mb-4 text-center">
            <h4 className="text-lg font-bold text-[#D6A25E]">{plan.name}</h4>
            <div className="mt-2">
              <span className="text-2xl font-bold text-white">{plan.price}</span>
              <span className="text-sm text-white/70"> / {plan.period}</span>
            </div>
          </div>
          <ul className="space-y-2">
            {plan.features.map((feature, index) => (
              <li key={index} className="flex items-center gap-2 text-sm">
                <Check className="h-4 w-4 text-[#D6A25E]" />
                <span className="text-white">{feature}</span>
              </li>
            ))}
          </ul>
        </div>
      ))}
    </div>
  )
}
