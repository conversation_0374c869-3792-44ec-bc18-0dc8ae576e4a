/**
 * Validation functions for the subscription system
 */

/**
 * Validates an email address format
 * @param {string} email - The email address to validate
 * @returns {boolean} - True if the email is valid, false otherwise
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Validates a mobile number format
 * @param {string} mobile - The mobile number to validate
 * @returns {boolean} - True if the mobile number is valid, false otherwise
 */
function isValidMobile(mobile) {
    const mobileRegex = /^\+?[0-9]{8,15}$/;
    return mobileRegex.test(mobile);
}

/**
 * Validates a password format (at least 8 characters with at least one number)
 * @param {string} password - The password to validate
 * @returns {boolean} - True if the password is valid, false otherwise
 */
function isValidPassword(password) {
    // Password must be at least 8 characters and contain at least one number
    const passwordRegex = /^(?=.*\d).{8,}$/;
    return passwordRegex.test(password);
}

/**
 * Shows a message in a message container
 * @param {string} containerId - The ID of the message container element
 * @param {string} message - The message to display
 * @param {string} type - The type of message (success, error, warning)
 * @param {number} timeout - The time in milliseconds after which to hide the message (default: 5000)
 */
function showMessage(containerId, message, type, timeout = 5000) {
    const container = document.getElementById(containerId);
    if (container) {
        container.innerHTML = `<div class="message ${type}">${message}</div>`;
        
        if (timeout > 0) {
            setTimeout(() => {
                container.innerHTML = '';
            }, timeout);
        }
    }
}

/**
 * Validates a registration form
 * @param {HTMLFormElement} form - The registration form element
 * @returns {boolean} - True if the form is valid, false otherwise
 */
function validateRegistrationForm(form) {
    const fullName = form.querySelector('#full_name').value.trim();
    const email = form.querySelector('#email').value.trim();
    const mobile = form.querySelector('#mobile').value.trim();
    const password = form.querySelector('#password').value;
    const confirmPassword = form.querySelector('#confirm_password').value;
    
    // Check full name
    if (fullName === '') {
        showMessage('message-container', 'Please enter your full name', 'error');
        return false;
    }
    
    // Check email
    if (!isValidEmail(email)) {
        showMessage('message-container', 'Please enter a valid email address', 'error');
        return false;
    }
    
    // Check mobile
    if (!isValidMobile(mobile)) {
        showMessage('message-container', 'Please enter a valid mobile number', 'error');
        return false;
    }
    
    // Check password
    if (!isValidPassword(password)) {
        showMessage('message-container', 'Password must be at least 8 characters long and contain at least one number', 'error');
        return false;
    }
    
    // Check password confirmation
    if (password !== confirmPassword) {
        showMessage('message-container', 'Passwords do not match', 'error');
        return false;
    }
    
    return true;
}

/**
 * Validates a login form
 * @param {HTMLFormElement} form - The login form element
 * @returns {boolean} - True if the form is valid, false otherwise
 */
function validateLoginForm(form) {
    const email = form.querySelector('#email').value.trim();
    const password = form.querySelector('#password').value;
    
    // Check email
    if (!isValidEmail(email)) {
        showMessage('message-container', 'Please enter a valid email address', 'error');
        return false;
    }
    
    // Check password
    if (password === '') {
        showMessage('message-container', 'Please enter your password', 'error');
        return false;
    }
    
    return true;
}

/**
 * Submits a form via AJAX
 * @param {HTMLFormElement} form - The form element to submit
 * @param {Function} successCallback - Function to call on successful submission
 */
function submitFormAjax(form, successCallback) {
    const formData = new FormData(form);
    const url = form.getAttribute('action');
    
    fetch(url, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('message-container', data.message, 'success');
            if (successCallback) {
                successCallback(data);
            }
        } else {
            showMessage('message-container', data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('message-container', 'An error occurred. Please try again later.', 'error');
    });
}

// Initialize forms when the document is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Registration form
    const registrationForm = document.getElementById('registration-form');
    if (registrationForm) {
        registrationForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (validateRegistrationForm(this)) {
                submitFormAjax(this, function(data) {
                    // Redirect to subscription page after successful registration
                    setTimeout(() => {
                        window.location.href = 'subscription.php';
                    }, 1500);
                });
            }
        });
    }
    
    // Login form
    const loginForm = document.getElementById('login-form');
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (validateLoginForm(this)) {
                submitFormAjax(this, function(data) {
                    // Redirect to appropriate page based on server response
                    setTimeout(() => {
                        window.location.href = data.redirect || 'dashboard.php';
                    }, 1000);
                });
            }
        });
    }
});
