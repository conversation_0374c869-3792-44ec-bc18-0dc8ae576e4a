<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أداة القص (Crop) المستقلة</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #1a2a6c, #b21f1f, #1a2a6c);
            color: #fff;
            min-height: 100vh;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .container {
            max-width: 1000px;
            width: 100%;
            margin: 0 auto;
            background: rgba(20, 30, 48, 0.85);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
        }
        
        header {
            text-align: center;
            padding: 25px;
            background: rgba(10, 15, 25, 0.7);
            border-bottom: 2px solid rgba(255, 255, 255, 0.1);
        }
        
        header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }
        
        header p {
            font-size: 1.1rem;
            max-width: 700px;
            margin: 0 auto;
            line-height: 1.6;
            opacity: 0.9;
            color: #a0c6ff;
        }
        
        .app-content {
            display: flex;
            flex-direction: column;
            gap: 25px;
            padding: 30px;
        }
        
        .image-section {
            position: relative;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 500px;
        }
        
        .image-container {
            position: relative;
            width: 100%;
            height: 400px;
            border: 2px dashed rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            background: rgba(10, 15, 25, 0.5);
            overflow: hidden;
            margin-top: 20px;
        }
        
        #imagePreview {
            width: 100%;
            height: 100%;
            object-fit: contain;
            display: none;
        }
        
        .crop-overlay {
            position: absolute;
            background: rgba(0, 0, 0, 0.5);
            z-index: 5;
            pointer-events: none;
            display: none;
        }
        
        .crop-rect {
            position: absolute;
            border: 2px dashed #4a9fe3;
            box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5);
            cursor: move;
            z-index: 6;
            display: none;
        }
        
        .crop-handle {
            position: absolute;
            width: 12px;
            height: 12px;
            background: #4a9fe3;
            border-radius: 50%;
            z-index: 7;
        }
        
        .crop-handle.nw { top: -6px; left: -6px; cursor: nw-resize; }
        .crop-handle.ne { top: -6px; right: -6px; cursor: ne-resize; }
        .crop-handle.sw { bottom: -6px; left: -6px; cursor: sw-resize; }
        .crop-handle.se { bottom: -6px; right: -6px; cursor: se-resize; }
        
        .crop-bar {
            width: 100%;
            background: rgba(30, 40, 60, 0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        .crop-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #4a9fe3;
        }
        
        .crop-header h2 {
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .crop-header i {
            color: #4a9fe3;
        }
        
        .crop-scroll {
            overflow-x: auto;
            padding: 10px 0;
            display: flex;
            gap: 15px;
        }
        
        .crop-scroll::-webkit-scrollbar {
            height: 8px;
        }
        
        .crop-scroll::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }
        
        .crop-scroll::-webkit-scrollbar-thumb {
            background: #4a9fe3;
            border-radius: 4px;
        }
        
        .crop-btn {
            background: rgba(74, 159, 227, 0.2);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 12px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            min-width: 120px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            white-space: nowrap;
        }
        
        .crop-btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            background: linear-gradient(to right, #4a9fe3, #2a5a8c);
        }
        
        .crop-btn i {
            font-size: 1.8rem;
        }
        
        .crop-btn.active {
            background: linear-gradient(to right, #4a9fe3, #2a5a8c);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }
        
        .action-btn {
            flex: 1;
            padding: 15px;
            border-radius: 12px;
            border: none;
            color: white;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        
        #loadImageBtn {
            background: linear-gradient(to right, #4a9fe3, #2a5a8c);
        }
        
        #applyCropBtn {
            background: linear-gradient(to right, #27ae60, #219653);
        }
        
        #resetBtn {
            background: linear-gradient(to right, #e74c3c, #c0392b);
        }
        
        #resultContainer {
            display: none;
            margin-top: 30px;
            text-align: center;
            padding: 20px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 15px;
        }
        
        #croppedImage {
            max-width: 100%;
            max-height: 300px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            margin-top: 15px;
        }
        
        .preview-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 1.8rem;
            font-weight: bold;
            color: rgba(255, 255, 255, 0.5);
            text-align: center;
            width: 100%;
            z-index: 2;
            padding: 0 20px;
        }
        
        .file-input-container {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }
        
        .file-input-label {
            background: linear-gradient(to right, #4a9fe3, #2a5a8c);
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .file-input-label:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
        }
        
        #imageLoader {
            display: none;
        }
        
        @media (max-width: 768px) {
            header h1 {
                font-size: 2rem;
            }
            
            .image-container {
                height: 350px;
            }
            
            .crop-btn {
                min-width: 100px;
                padding: 12px 20px;
                font-size: 0.9rem;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
        
        @media (max-width: 480px) {
            .app-content {
                padding: 20px 15px;
            }
            
            header {
                padding: 20px 15px;
            }
            
            header h1 {
                font-size: 1.7rem;
            }
            
            .image-container {
                height: 300px;
            }
            
            .crop-btn {
                min-width: 85px;
                padding: 10px 15px;
            }
            
            .crop-btn i {
                font-size: 1.5rem;
            }
            
            .preview-text {
                font-size: 1.4rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-crop-alt"></i> أداة القص (Crop) المستقلة</h1>
            <p>أداة متكاملة لقص الصور بنسب مختلفة مع واجهة مستخدم سهلة الاستخدام</p>
        </header>
        
        <div class="app-content">
            <div class="image-section">
                <div class="preview-text" id="previewText">قم بتحميل صورة لبدء استخدام أداة القص</div>
                
                <div class="image-container">
                    <img id="imagePreview" alt="الصورة المحددة">
                    <div class="crop-overlay" id="cropOverlay"></div>
                    <div class="crop-rect" id="cropRect">
                        <div class="crop-handle nw"></div>
                        <div class="crop-handle ne"></div>
                        <div class="crop-handle sw"></div>
                        <div class="crop-handle se"></div>
                    </div>
                </div>
                
                <div class="file-input-container">
                    <label for="imageLoader" class="file-input-label">
                        <i class="fas fa-cloud-upload-alt"></i> تحميل صورة
                    </label>
                    <input type="file" id="imageLoader" accept="image/*">
                </div>
            </div>
            
            <div class="crop-bar">
                <div class="crop-header">
                    <h2><i class="fas fa-crop-alt"></i> نسب القص</h2>
                </div>
                
                <div class="crop-scroll">
                    <button class="crop-btn" data-ratio="custom">
                        <i class="fas fa-ruler-combined"></i>
                        مخصص
                    </button>
                    <button class="crop-btn" data-ratio="1:1">
                        <i class="fas fa-square"></i>
                        1:1 (مربع)
                    </button>
                    <button class="crop-btn" data-ratio="4:3">
                        <i class="fas fa-tablet-alt"></i>
                        4:3
                    </button>
                    <button class="crop-btn" data-ratio="16:9">
                        <i class="fas fa-tv"></i>
                        16:9
                    </button>
                    <button class="crop-btn" data-ratio="5:3">
                        <i class="fas fa-camera"></i>
                        5:3
                    </button>
                    <button class="crop-btn" data-ratio="5:4">
                        <i class="fas fa-image"></i>
                        5:4
                    </button>
                    <button class="crop-btn" data-ratio="6:4">
                        <i class="fas fa-rectangle-portrait"></i>
                        6:4
                    </button>
                    <button class="crop-btn" data-ratio="7:5">
                        <i class="fas fa-mobile-alt"></i>
                        7:5
                    </button>
                </div>
                
                <div class="action-buttons">
                    <button id="applyCropBtn" class="action-btn">
                        <i class="fas fa-check"></i> تطبيق القص
                    </button>
                    <button id="resetBtn" class="action-btn">
                        <i class="fas fa-redo"></i> إعادة تعيين
                    </button>
                </div>
            </div>
            
            <div id="resultContainer">
                <h3><i class="fas fa-image"></i> الصورة بعد القص</h3>
                <img id="croppedImage" alt="الصورة بعد القص">
            </div>
        </div>
    </div>

    <script>
        // متغيرات التطبيق
        let originalImage = null;
        let cropRect = {
            x: 0,
            y: 0,
            width: 0,
            height: 0
        };
        let cropRatio = null;
        let isDragging = false;
        let activeHandle = null;
        let dragStartX, dragStartY;
        let cropStartX, cropStartY, cropStartWidth, cropStartHeight;
        
        // عناصر DOM
        const imageLoader = document.getElementById('imageLoader');
        const imagePreview = document.getElementById('imagePreview');
        const cropOverlay = document.getElementById('cropOverlay');
        const cropRectEl = document.getElementById('cropRect');
        const applyCropBtn = document.getElementById('applyCropBtn');
        const resetBtn = document.getElementById('resetBtn');
        const croppedImage = document.getElementById('croppedImage');
        const resultContainer = document.getElementById('resultContainer');
        const previewText = document.getElementById('previewText');
        const cropBtns = document.querySelectorAll('.crop-btn');
        
        // تحميل الصورة
        imageLoader.addEventListener('change', function(e) {
            if (e.target.files && e.target.files[0]) {
                const reader = new FileReader();
                
                reader.onload = function(event) {
                    imagePreview.src = event.target.result;
                    imagePreview.style.display = 'block';
                    previewText.style.display = 'none';
                    
                    // تهيئة القص بعد تحميل الصورة
                    imagePreview.onload = function() {
                        initCropMode();
                    };
                };
                
                reader.readAsDataURL(e.target.files[0]);
            }
        });
        
        // تهيئة وضع القص
        function initCropMode() {
            // إخفاء النتيجة السابقة
            resultContainer.style.display = 'none';
            
            // إظهار عناصر القص
            cropOverlay.style.display = 'block';
            cropRectEl.style.display = 'block';
            
            // تعيين حجم مستطيل القص الافتراضي
            const containerWidth = imagePreview.parentElement.clientWidth;
            const containerHeight = imagePreview.parentElement.clientHeight;
            
            const cropSize = Math.min(containerWidth, containerHeight) * 0.6;
            
            cropRect = {
                x: (containerWidth - cropSize) / 2,
                y: (containerHeight - cropSize) / 2,
                width: cropSize,
                height: cropSize
            };
            
            updateCropRect();
            initCropHandlers();
        }
        
        // تحديث مستطيل القص على الشاشة
        function updateCropRect() {
            cropRectEl.style.left = cropRect.x + 'px';
            cropRectEl.style.top = cropRect.y + 'px';
            cropRectEl.style.width = cropRect.width + 'px';
            cropRectEl.style.height = cropRect.height + 'px';
            
            cropOverlay.style.left = '0';
            cropOverlay.style.top = '0';
            cropOverlay.style.width = '100%';
            cropOverlay.style.height = '100%';
        }
        
        // إعادة تعيين وضع القص
        function resetCropMode() {
            cropOverlay.style.display = 'none';
            cropRectEl.style.display = 'none';
            resultContainer.style.display = 'none';
            previewText.style.display = 'block';
            imagePreview.style.display = 'none';
            imageLoader.value = '';
            
            // إزالة التحديد من أزرار القص
            cropBtns.forEach(btn => btn.classList.remove('active'));
        }
        
        // تهيئة معالجات السحب والتعديل
        function initCropHandlers() {
            // معالج بدء السحب
            function startDrag(e) {
                isDragging = true;
                dragStartX = e.clientX;
                dragStartY = e.clientY;
                cropStartX = cropRect.x;
                cropStartY = cropRect.y;
                
                // تحديد المقبض النشط
                if (e.target.classList.contains('crop-handle')) {
                    activeHandle = Array.from(e.target.classList).find(cls => 
                        cls === 'nw' || cls === 'ne' || cls === 'sw' || cls === 'se'
                    );
                    cropStartWidth = cropRect.width;
                    cropStartHeight = cropRect.height;
                } else {
                    activeHandle = null;
                }
            }
            
            // معالج السحب
            function doDrag(e) {
                if (!isDragging) return;
                
                const dx = e.clientX - dragStartX;
                const dy = e.clientY - dragStartY;
                
                if (activeHandle) {
                    // تعديل الحجم باستخدام المقابض
                    switch(activeHandle) {
                        case 'nw':
                            cropRect.x = cropStartX + dx;
                            cropRect.y = cropStartY + dy;
                            cropRect.width = cropStartWidth - dx;
                            cropRect.height = cropStartHeight - dy;
                            break;
                        case 'ne':
                            cropRect.y = cropStartY + dy;
                            cropRect.width = cropStartWidth + dx;
                            cropRect.height = cropStartHeight - dy;
                            break;
                        case 'sw':
                            cropRect.x = cropStartX + dx;
                            cropRect.width = cropStartWidth - dx;
                            cropRect.height = cropStartHeight + dy;
                            break;
                        case 'se':
                            cropRect.width = cropStartWidth + dx;
                            cropRect.height = cropStartHeight + dy;
                            break;
                    }
                    
                    // تطبيق نسبة القص
                    if (cropRatio && cropRatio !== 'custom') {
                        const [w, h] = cropRatio.split(':').map(Number);
                        const aspectRatio = w / h;
                        
                        if (activeHandle === 'ne' || activeHandle === 'se') {
                            cropRect.height = cropRect.width / aspectRatio;
                        } else {
                            cropRect.height = cropRect.width * aspectRatio;
                        }
                    }
                } else {
                    // نقل مستطيل القص
                    cropRect.x = cropStartX + dx;
                    cropRect.y = cropStartY + dy;
                }
                
                // التأكد من بقاء المستطيل داخل حدود الصورة
                constrainCropRect();
                updateCropRect();
            }
            
            // معالج إنهاء السحب
            function stopDrag() {
                isDragging = false;
                activeHandle = null;
            }
            
            // إضافة معالجي الأحداث
            cropRectEl.addEventListener('mousedown', startDrag);
            document.addEventListener('mousemove', doDrag);
            document.addEventListener('mouseup', stopDrag);
            
            // إضافة معالجي الأحداث للمقابض
            const handles = document.querySelectorAll('.crop-handle');
            handles.forEach(handle => {
                handle.addEventListener('mousedown', startDrag);
            });
        }
        
        // التأكد من بقاء مستطيل القص داخل حدود الصورة
        function constrainCropRect() {
            const container = imagePreview.parentElement;
            const containerWidth = container.clientWidth;
            const containerHeight = container.clientHeight;
            
            // التأكد من أن المستطيل داخل حدود الحاوية
            cropRect.x = Math.max(0, Math.min(containerWidth - cropRect.width, cropRect.x));
            cropRect.y = Math.max(0, Math.min(containerHeight - cropRect.height, cropRect.y));
            
            // التأكد من أن الأبعاد لا تقل عن 50 بكسل
            cropRect.width = Math.max(50, cropRect.width);
            cropRect.height = Math.max(50, cropRect.height);
        }
        
        // معالجات أزرار القص
        cropBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const ratio = this.getAttribute('data-ratio');
                
                if (ratio === 'custom') {
                    cropRatio = null;
                } else {
                    cropRatio = ratio;
                    
                    // تطبيق نسبة القص على مستطيل القص الحالي
                    if (cropRatio) {
                        const [w, h] = cropRatio.split(':').map(Number);
                        const aspectRatio = w / h;
                        
                        // الحفاظ على المركز مع تغيير الحجم
                        const centerX = cropRect.x + cropRect.width/2;
                        const centerY = cropRect.y + cropRect.height/2;
                        
                        if (cropRect.width / cropRect.height > aspectRatio) {
                            cropRect.width = cropRect.height * aspectRatio;
                        } else {
                            cropRect.height = cropRect.width / aspectRatio;
                        }
                        
                        cropRect.x = centerX - cropRect.width/2;
                        cropRect.y = centerY - cropRect.height/2;
                        
                        constrainCropRect();
                        updateCropRect();
                    }
                }
                
                // إزالة التحديد من جميع الأزرار
                cropBtns.forEach(b => b.classList.remove('active'));
                
                // تحديد الزر الحالي
                this.classList.add('active');
            });
        });
        
        // تطبيق القص
        applyCropBtn.addEventListener('click', function() {
            if (!imagePreview.src || imagePreview.src === window.location.href) {
                alert('الرجاء تحميل صورة أولاً');
                return;
            }
            
            // إنشاء عنصر canvas مؤقت للقص
            const tempCanvas = document.createElement('canvas');
            const tempCtx = tempCanvas.getContext('2d');
            
            // تعيين أبعاد canvas المؤقت
            tempCanvas.width = cropRect.width;
            tempCanvas.height = cropRect.height;
            
            // حساب الإحداثيات النسبية داخل الصورة
            const container = imagePreview.parentElement;
            const scaleX = imagePreview.naturalWidth / container.clientWidth;
            const scaleY = imagePreview.naturalHeight / container.clientHeight;
            
            // رسم الجزء المطلوب قصه
            tempCtx.drawImage(
                imagePreview,
                cropRect.x * scaleX, 
                cropRect.y * scaleY,
                cropRect.width * scaleX, 
                cropRect.height * scaleY,
                0, 0, 
                cropRect.width, 
                cropRect.height
            );
            
            // عرض الصورة المقصوصة
            croppedImage.src = tempCanvas.toDataURL('image/png');
            resultContainer.style.display = 'block';
            
            // تمرير للنتيجة
            resultContainer.scrollIntoView({ behavior: 'smooth' });
        });
        
        // إعادة تعيين
        resetBtn.addEventListener('click', resetCropMode);
    </script>
</body>
</html>