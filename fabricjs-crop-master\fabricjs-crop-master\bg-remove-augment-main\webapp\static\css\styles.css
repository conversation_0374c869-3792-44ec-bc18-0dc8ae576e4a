label {
	font-size: 20px;
}
.container {
	max-width: 100% !important;
}

.overlay {
	width: 100%;
	height: 100%;
	position: absolute;
	text-align: center;
	display: none;
	background-color: #99999978;
	z-index: 1000 !important;
}

.spinner-border {
	top: 50% !important;
	position: absolute;
}

h2 {
	font-size: 30px;
	text-align: center;
	width: 100%;
	line-height: 30px;
	color: white;
}

span {
	display: block;
}



.input-file-hide {
	display: none;
}

#holder, #holder_result, #holder-empty {
	text-align: center;
	max-height: 800px;
	height: 400px;
	padding: 10px;
}

.holder_default {
	height: 400px;
	border: 3px dashed #ccc;
	text-align: center;
}

.hidden {
	visibility: hidden;
}

.visible {
	visibility: visible;
}

#image_result, #image_droped {
	object-fit: contain;
	max-width: 100%;
	max-height: 100%;
	height: inherit !important;
}

.image_droped {
	object-fit: contain;
	max-width: 100%;
	max-height: 100%;
	height: inherit !important;
	border: 3px dashed #ccc;
}

#image_result {
	object-fit: contain;
	max-width: 100%;
	max-height: 100%;
	height: inherit !important;
}

.result-header {
	text-align: center;
	display: block;
	font-size: 22px;
	font-weight: bold;
	background-color:aliceblue;
	padding: 10px;
	margin-bottom: 10px;
}

div.img-result {
	width: 500px;
	display: block;
}