<!DOCTYPE html>
<html lang="ar">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no" />
  <title>Interactive 3D Model with Real-Time Pixie Editor (جودة أعلى للنسيج)</title>
  <!-- تحميل خط Tajawal -->
  <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700&display=swap" rel="stylesheet">
  <style>
    /* تعبئة الشاشة بالكامل */
    html, body {
      width: 100%;
      height: 100%;
      margin: 0;
      padding: 0;
      direction: rtl;
      font-family: "Tajawal", sans-serif;
      overflow: hidden;
    }
    /* الحاوية الرئيسية */
    #app {
      position: relative;
      width: 100%;
      height: 100vh;
    }
    /* حاوية المشهد */
    #scene-container {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #10161c;
      overflow: hidden;
    }
    /* زر إخفاء المحرر */
    #toggle-editor-btn {
      position: absolute;
      top: 8px;
      left: 8px;
      z-index: 1000;
      padding: 6px 12px;
      background: rgba(0,0,0,0.5);
      color: #fff;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
    }
    #toggle-editor-btn:hover {
      background: rgba(0,0,0,0.7);
    }
    /* زر إظهار المحرر */
    #show-editor-btn {
      position: absolute;
      bottom: 8px;
      right: 8px;
      z-index: 1000;
      padding: 6px 12px;
      background: rgba(0,0,0,0.5);
      color: #fff;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      display: none;
    }
    #show-editor-btn.visible {
      display: block;
    }
    /* حاوية المحرر */
    #editor-container {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      height: 30vh;
      min-height: 150px;
      max-height: 80vh;
      background: #fff;
      box-shadow: 0 -2px 8px rgba(0,0,0,0.3);
      transform: translateY(0);
      transition: transform 0.3s ease;
      overflow: hidden;
      z-index: 500;
    }
    #editor-container.hidden {
      transform: translateY(100%);
    }
    /* نجبر Pixie يملأ الحاوية */
    #editor-container > * {
      width: 100%;
      height: 100%;
    }
  </style>
  <!-- مكتبات Three.js و Pixie -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r134/three.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/three/examples/js/controls/OrbitControls.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/three/examples/js/loaders/GLTFLoader.js"></script>
  <script src="js/pixie.umd.js?29"></script> <!-- عدّل المسار حسب موقعك -->
</head>
<body>
  <div id="app">
    <div id="scene-container">
      <button id="toggle-editor-btn">إخفاء المحرر</button>
      <button id="show-editor-btn">إظهار المحرر</button>
      <!-- سيتم إضافة كانفاس Three.js هنا برمجياً -->
    </div>
    <div id="editor-container">
      <!-- محرر Pixie سيُضاف هنا -->
    </div>
  </div>

  <script>
    // مراجع DOM ومتغيرات
    const sceneContainer = document.getElementById("scene-container");
    const editorContainer = document.getElementById("editor-container");
    const toggleBtn = document.getElementById("toggle-editor-btn");
    const showBtn = document.getElementById("show-editor-btn");

    let scene, camera, renderer, controls, model, ground;
    let pixieTexture = null;
    const gltfLoader = new THREE.GLTFLoader();
    let pixieInstance = null;

    // ==============================
    // تهيئة Three.js
    // ==============================
    function initThree() {
      scene = new THREE.Scene();
      scene.background = new THREE.Color(0x10161c);

      // الكاميرا
      camera = new THREE.PerspectiveCamera(60, sceneContainer.clientWidth / sceneContainer.clientHeight, 1.2, 1000);
      camera.position.set(-1, 2, 2);

      // الرندر مع مراعاة دقة الشاشة
      renderer = new THREE.WebGLRenderer({ antialias: true });
      renderer.setPixelRatio(window.devicePixelRatio); // تحسين الدقة على شاشات Retina وغيرها  [oai_citation:5‡threejs.org](https://threejs.org/docs/?utm_source=chatgpt.com)
      renderer.setSize(sceneContainer.clientWidth, sceneContainer.clientHeight);
      renderer.shadowMap.enabled = true;
      sceneContainer.appendChild(renderer.domElement);

      // الكنترول
      controls = new THREE.OrbitControls(camera, renderer.domElement);
      controls.enableDamping = true;
      controls.dampingFactor = 0.05;

      // تحميل نموذج GLTF
      gltfLoader.load("model.gltf", function (gltf) {
        model = gltf.scene;
        model.scale.set(1, 1, 1);
        model.position.set(0.5, -1, -1);
        // إذا كانت هناك Texture جاهزة مسبقًا، نطبّقها
        model.traverse((child) => {
          if (child.isMesh) {
            child.castShadow = true;
            if (pixieTexture) {
              child.material.map = pixieTexture;
              child.material.needsUpdate = true;
            }
          }
        });
        scene.add(model);
      }, undefined, function(error){
        console.error("خطأ في تحميل النموذج:", error);
      });

      // الأرض
      const groundGeometry = new THREE.PlaneGeometry(10, 10);
      const groundMaterial = new THREE.MeshStandardMaterial({ color: 0x888888 });
      ground = new THREE.Mesh(groundGeometry, groundMaterial);
      ground.rotation.x = -Math.PI / 2;
      ground.position.y = -2.2;
      ground.receiveShadow = true;
      scene.add(ground);

      // الإضاءة
      scene.add(new THREE.AmbientLight(0xedf0f3, 0.5));
      const directionalLight = new THREE.DirectionalLight(0xedf0f3, 0.5);
      directionalLight.position.set(5, 5, 5);
      directionalLight.castShadow = true;
      scene.add(directionalLight);
      const backLight = new THREE.DirectionalLight(0xffffff, 0.5);
      backLight.position.set(-1, 1, -1);
      scene.add(backLight);

      // إعادة ضبط الحجم عند تغيير النافذة
      window.addEventListener("resize", onWindowResizeThree);
    }

    function onWindowResizeThree() {
      const w = sceneContainer.clientWidth;
      const h = sceneContainer.clientHeight;
      camera.aspect = w / h;
      camera.updateProjectionMatrix();
      renderer.setSize(w, h);
    }

    function animateThree() {
      requestAnimationFrame(animateThree);
      controls.update();
      if (pixieTexture) {
        pixieTexture.needsUpdate = true;
      }
      renderer.render(scene, camera);
    }

    // ==============================
    // تهيئة Pixie Editor
    // ==============================
    function initPixie() {
      pixieInstance = new Pixie({
        selector: "#editor-container",
        baseUrl: "../assets", // عدّل المسار حسب ملفات Pixie assets لديك
        image: "",
        activeLanguage: "en",
        onLoad: function() {
          console.log("Pixie Editor Loaded");
          // محاولة استدعاء دوال إعادة القياس إذا متوفرة
          if (pixieInstance && typeof pixieInstance.resize === 'function') {
            pixieInstance.resize();
            console.log("pixieInstance.resize() after load");
          }
          if (pixieInstance && pixieInstance.api && typeof pixieInstance.api.ui.resize === 'function') {
            pixieInstance.api.ui.resize();
            console.log("pixie.api.ui.resize() after load");
          }
        },
        ui: {
          activeTheme: "i2img",
          themes: [{
            name: "i2img",
            colors: {
              "--be-primary": "#7961f2",
              "--be-primary-light": "#d0caff",
              "--be-primary-dark": "#5139ff",
            }
          }],
          nav: {
            position: "top",
            replaceDefault: false,
            items: [{
              name: "Watermark",
              icon: [{
                tag: "path",
                attr: {
                  d: "M18 20H4V6h9V4H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-9h-2v9zm-7.79-3.17-1.96-2.36L5.5 18h11l-3.54-4.71zM20 4V1h-2v3h-3c.01.01 0 2 0 2h3v2.99c.01.01 2 0 2 0V6h-3V4h-3z"
                }
              }],
              action: function(editor) {
                editor.tools.import.uploadAndAddImage();
              }
            }]
          },
          menubar: {
            items: [{
              type: "button",
              icon: [{
                tag: "path",
                attr: {
                  d: "m11.99 18.54-7.37-5.73L3 14.07l9 7 9-7-1.63-1.27zM12 16ل7.36-5.73L21 9ل-9-7-9 7 1.63-1.27L12 16zm0-11.47L17.74 9 12 13.47 6.26 9 12 4.53z",
                }
              }],
              align: "right",
              position: 0,
              action: editor => {
                editor.togglePanel("objects");
              }
            }]
          }
        },
        tools: {
          text: {
            defaultText: "فلسطين داري",
            replaceDefault: false,
            items: [
              {
                family: "Tajawal",
                src: "../editors/fonts/Tajawal.ttf"
              },
              {
                family: "AGA Mishmish مشمش",
                src: "../editors/fonts/arabic1.ttf"
              }
            ]
          },
          export: {
            defaultQuality: 1.0,
            defaultFormat: "png"
          }
        }
      });
      window.pixie = pixieInstance;
    }

    // ==============================
    // ربط Canvas Pixie بـ Three.js Texture مع تحسين الجودة
    // ==============================
    function monitorPixieCanvasUpdates() {
      const canvas = document.querySelector("#editor-container canvas");
      if (canvas) {
        // يمكن محاولة ضبط دقة الكانفاس بناءً على devicePixelRatio إن لزم:
        const width = editorContainer.clientWidth;
        const height = editorContainer.clientHeight;
        const dpr = window.devicePixelRatio || 1;
        // اضبط أبعاد الكانفاس الفعلية لتحسين الجودة (اختياري، تأكد أن Pixie لا يعيد الكتابة عليها):
        // canvas.width = width * dpr;
        // canvas.height = height * dpr;
        // canvas.style.width = width + 'px';
        // canvas.style.height = height + 'px';

        if (!pixieTexture) {
          console.log("إنشاء النسيج لأول مرة...");
          pixieTexture = new THREE.CanvasTexture(canvas);
          // إعدادات الفلاتر والـ Mipmaps للوضوح الأفضل:
          pixieTexture.generateMipmaps = true;
          pixieTexture.minFilter = THREE.LinearMipMapLinearFilter;
          pixieTexture.magFilter = THREE.LinearFilter;
          // ضبط الأنيسوتروبي لأعلى قيمة مدعومة:
          const maxAniso = renderer.capabilities.getMaxAnisotropy();
          pixieTexture.anisotropy = maxAniso;
          console.log("Anisotropy set to", maxAniso);
          pixieTexture.needsUpdate = true;

          if (model) {
            model.traverse(function(child) {
              if (child.isMesh) {
                child.material.map = pixieTexture;
                child.material.needsUpdate = true;
              }
            });
          }
        } else {
          pixieTexture.image = canvas;
          pixieTexture.needsUpdate = true;
        }
      }
      requestAnimationFrame(monitorPixieCanvasUpdates);
    }

    // ==============================
    // إعداد إظهار/إخفاء المحرر
    // ==============================
    function setupEditorToggle() {
      toggleBtn.addEventListener("click", () => {
        console.log("إخفاء المحرر");
        editorContainer.classList.add("hidden");
        toggleBtn.style.display = 'none';
        showBtn.classList.add("visible");
        setTimeout(() => {
          window.dispatchEvent(new Event('resize'));
          console.log("dispatch resize after hide");
        }, 350);
      });
      showBtn.addEventListener("click", () => {
        console.log("إظهار المحرر");
        editorContainer.classList.remove("hidden");
        toggleBtn.style.display = 'block';
        showBtn.classList.remove("visible");
        setTimeout(() => {
          window.dispatchEvent(new Event('resize'));
          console.log("dispatch resize after show");
          // محاولة إعادة قياس Pixie
          if (pixieInstance && typeof pixieInstance.resize === 'function') {
            pixieInstance.resize();
            console.log("pixieInstance.resize() after show");
          }
          if (pixieInstance && pixieInstance.api && typeof pixieInstance.api.ui.resize === 'function') {
            pixieInstance.api.ui.resize();
            console.log("pixie.api.ui.resize() after show");
          }
        }, 350);
      });
    }

    // ==============================
    // مثال: استخدام Export لالتقاط صورة عالية الجودة عند الطلب
    // ==============================
    // يمكنك استدعاء هذه الدالة عند الضغط على زر “تصدير” أو غيره:
    function exportHighResTextureAndApply() {
      if (!pixieInstance) return;
      // مثال تقريبي: تأكد من وجود دالة exportImage أو ما شابه في Pixie SDK
      if (pixieInstance.exportImage) {
        pixieInstance.exportImage({ format: 'png', quality: 1.0 }).then(dataUrl => {
          const img = new Image();
          img.onload = () => {
            const tex = new THREE.Texture(img);
            tex.generateMipmaps = true;
            tex.minFilter = THREE.LinearMipMapLinearFilter;
            tex.magFilter = THREE.LinearFilter;
            tex.anisotropy = renderer.capabilities.getMaxAnisotropy();
            tex.needsUpdate = true;
            // تطبيق على جميع أجزاء النموذج:
            if (model) {
              model.traverse(child => {
                if (child.isMesh) {
                  child.material.map = tex;
                  child.material.needsUpdate = true;
                }
              });
            }
            console.log("تم تطبيق texture عالية الجودة بعد التصدير");
          };
          img.src = dataUrl;
        }).catch(err => {
          console.error("خطأ في التصدير:", err);
        });
      } else {
        console.warn("Pixie instance لا يدعم exportImage بهذا الاسم. راجع التوثيق.");
      }
    }

    // ==============================
    // بدء التهيئة بعد تحميل DOM
    // ==============================
    document.addEventListener("DOMContentLoaded", () => {
      initThree();
      animateThree();
      initPixie();
      monitorPixieCanvasUpdates();
      setupEditorToggle();
      // إذا كنت تريد زر تصدير أعلى الجودة، يمكنك إضافته هنا:
      // مثلاً:
      // const exportBtn = document.createElement('button');
      // exportBtn.textContent = "تصدير جودة عالية";
      // exportBtn.style.position = 'absolute'; exportBtn.style.top='8px'; exportBtn.style.right='8px'; exportBtn.style.zIndex='1000';
      // exportBtn.onclick = exportHighResTextureAndApply;
      // sceneContainer.appendChild(exportBtn);
    });
  </script>
</body>
</html>