// src/components/auth/GoogleAuth.jsx
// [!] هذا المكون يتعامل مع المصادقة باستخدام Google
// [!] يجب تعديل متغيرات Google OAuth حسب إعدادات مشروع Google Cloud الخاص بك

import React, { useEffect } from 'react';

// [!] متغيرات يجب تعديلها: GOOGLE_CLIENT_ID
const GOOGLE_CLIENT_ID = 'YOUR_GOOGLE_CLIENT_ID'; // يجب استبدالها بمعرف العميل الخاص بك من Google Cloud Console

const GoogleAuth = ({ onSuccess, onError }) => {
  useEffect(() => {
    // تحميل مكتبة Google API
    const loadGoogleScript = () => {
      // التحقق من وجود السكريبت بالفعل
      if (document.querySelector(`script[src="https://accounts.google.com/gsi/client"]`)) {
        initializeGoogleButton();
        return;
      }

      // إنشاء عنصر السكريبت
      const script = document.createElement('script');
      script.src = 'https://accounts.google.com/gsi/client';
      script.async = true;
      script.defer = true;
      script.onload = initializeGoogleButton;
      document.body.appendChild(script);
    };

    // تهيئة زر Google
    const initializeGoogleButton = () => {
      if (window.google && window.google.accounts) {
        window.google.accounts.id.initialize({
          client_id: GOOGLE_CLIENT_ID,
          callback: handleGoogleResponse,
          auto_select: false,
          cancel_on_tap_outside: true,
        });

        window.google.accounts.id.renderButton(
          document.getElementById('google-signin-button'),
          { 
            theme: 'outline', 
            size: 'large',
            text: 'signin_with',
            shape: 'rectangular',
            width: '100%',
            locale: 'ar'
          }
        );
      }
    };

    loadGoogleScript();

    // تنظيف عند إزالة المكون
    return () => {
      // إزالة معالج Google إذا كان موجودًا
      if (window.google && window.google.accounts) {
        window.google.accounts.id.cancel();
      }
    };
  }, []);

  // معالجة استجابة Google
  const handleGoogleResponse = async (response) => {
    try {
      if (response.credential) {
        // إرسال رمز المصادقة إلى الخادم
        const serverResponse = await fetch('/api/auth/google', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ token: response.credential }),
        });

        const data = await serverResponse.json();

        if (serverResponse.ok && data.success) {
          // استدعاء دالة النجاح
          onSuccess(data.user);
        } else {
          // استدعاء دالة الخطأ
          onError(data.message || 'فشل المصادقة باستخدام Google');
        }
      }
    } catch (error) {
      console.error('Google authentication error:', error);
      onError('حدث خطأ أثناء المصادقة باستخدام Google');
    }
  };

  return (
    <div id="google-signin-button" className="w-full"></div>
  );
};

export default GoogleAuth;
