<?php
require_once 'config.php';

// Only process POST requests
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $response = ['success' => false, 'message' => ''];
    
    // Get form data
    $email = trim($_POST['email']);
    $password = $_POST['password'];
    
    // Basic validation
    if (empty($email) || empty($password)) {
        $response['message'] = 'Both email and password are required';
        echo json_encode($response);
        exit;
    }
    
    // Check user credentials
    $sql = "SELECT user_id, full_name, email, password FROM users WHERE email = ?";
    if ($stmt = $conn->prepare($sql)) {
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $stmt->store_result();
        
        if ($stmt->num_rows == 1) {
            $stmt->bind_result($userId, $fullName, $email, $hashedPassword);
            $stmt->fetch();
            
            // Verify password
            if (password_verify($password, $hashedPassword)) {
                // Password is correct, check subscription status
                $subSql = "SELECT s.status, s.expiry_date FROM subscriptions s 
                          WHERE s.user_id = ? AND s.status = 'active' 
                          ORDER BY s.expiry_date DESC LIMIT 1";
                
                $subStmt = $conn->prepare($subSql);
                $subStmt->bind_param("i", $userId);
                $subStmt->execute();
                $subStmt->store_result();
                
                // Start session and store user data
                $_SESSION['user_id'] = $userId;
                $_SESSION['full_name'] = $fullName;
                $_SESSION['email'] = $email;
                
                // Set user's IP and user agent for security
                $_SESSION['ip_address'] = $_SERVER['REMOTE_ADDR'];
                $_SESSION['user_agent'] = $_SERVER['HTTP_USER_AGENT'];
                
                // Record login time
                $updateLoginSql = "UPDATE users SET last_login = NOW() WHERE user_id = ?";
                $updateStmt = $conn->prepare($updateLoginSql);
                $updateStmt->bind_param("i", $userId);
                $updateStmt->execute();
                $updateStmt->close();
                
                // Redirect based on subscription status
                if ($subStmt->num_rows > 0) {
                    $subStmt->bind_result($status, $expiryDate);
                    $subStmt->fetch();
                    
                    // Check if subscription is active and not expired
                    if ($status === 'active' && strtotime($expiryDate) > time()) {
                        $_SESSION['subscription_status'] = 'active';
                        $_SESSION['subscription_expiry'] = $expiryDate;
                        $response['redirect'] = 'dashboard.php';
                    } else {
                        // Subscription expired
                        $_SESSION['subscription_status'] = 'expired';
                        $response['redirect'] = 'subscription.php';
                    }
                } else {
                    // No subscription found
                    $_SESSION['subscription_status'] = 'none';
                    $response['redirect'] = 'subscription.php';
                }
                
                $subStmt->close();
                
                $response['success'] = true;
                $response['message'] = 'Login successful! Redirecting...';
            } else {
                // Incorrect password
                $response['message'] = 'Invalid email or password';
            }
        } else {
            // User not found
            $response['message'] = 'Invalid email or password';
        }
        
        $stmt->close();
    } else {
        $response['message'] = 'Database error: ' . $conn->error;
    }
    
    // Return JSON response
    echo json_encode($response);
} else {
    // Redirect if accessed directly
    header("Location: login.php");
    exit;
}
?>
