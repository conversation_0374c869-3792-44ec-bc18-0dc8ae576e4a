
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open('v1').then((cache) => {
      return cache.addAll([
        '/',
        '/index.html',
        '/arabicFonts.css',
        '/libs/hammer/hammer.min.js',
        '/libs/threejs/build/three.min.js',
        '/libs/threejs/modules/js/controls/OrbitControls.min.js',
        '/libs/threejs/modules/js/loaders/GLTFLoader.js',
        '/libs/fabric/fabric.min.js',
        '/img/icons/57.png',
        '/img/icons/60.png',
        '/img/icons/72.png',
        '/img/icons/76.png',
        '/img/icons/114.png',
        '/img/icons/120.png',
        '/img/icons/144.png',
        '/img/icons/152.png',
        '/img/icons/180.png',
        '/img/icons/192.png',
        '/img/icons/32.png',
        '/img/icons/96.png',
        '/img/icons/16.png',
        '/img/icons/512.png',
        '/img/icons/icon.svg',
        '/img/ui/favicon.ico'
      ]);
    })
  );
});

self.addEventListener('fetch', (event) => {
  event.respondWith(
    caches.match(event.request).then((response) => {
      return response || fetch(event.request);
    })
  );
});

