# دليل التثبيت والإعداد لنظام الاشتراكات 4dads.pro

## المتطلبات الأساسية

- Node.js (الإصدار 18 أو أحدث)
- استضافة تدعم Node.js (مثل Hostinger)
- حساب PayPal للمطورين
- مشروع Google Cloud مع تفعيل OAuth

## خطوات التثبيت

### 1. إعداد قاعدة البيانات

1. قم بإنشاء قاعدة بيانات MySQL جديدة في لوحة تحكم Hostinger
2. قم بتنفيذ سكريبت إنشاء الجداول الموجود في `/database/migrations/001_create_tables.sql`
3. لاحظ أنه يجب تعديل المتغيرات التالية في السكريبت:
   - `DB_NAME`: اسم قاعدة البيانات التي أنشأتها
   - `DB_USER`: اسم المستخدم لقاعدة البيانات
   - `DB_PASSWORD`: كلمة المرور لقاعدة البيانات

### 2. إعداد مشروع Google Cloud للمصادقة

1. قم بإنشاء مشروع جديد في [Google Cloud Console](https://console.cloud.google.com/)
2. قم بتفعيل Google Sign-In API
3. قم بإنشاء بيانات اعتماد OAuth 2.0 لتطبيق الويب
4. أضف عنوان URL المسموح به للتوجيه: `https://4dads.pro/api/auth/google`
5. احفظ معرف العميل (Client ID) وسر العميل (Client Secret)

### 3. إعداد حساب PayPal للمطورين

1. قم بإنشاء حساب في [PayPal Developer](https://developer.paypal.com/)
2. قم بإنشاء تطبيق جديد للحصول على بيانات الاعتماد
3. قم بإنشاء خطط اشتراك في لوحة التحكم
4. احفظ معرف العميل (Client ID) وسر العميل (Client Secret)
5. احفظ معرفات خطط الاشتراك (Plan IDs) التي أنشأتها

### 4. تعديل ملفات التكوين

#### تكوين الاتصال بقاعدة البيانات

قم بتعديل المتغيرات التالية في جميع ملفات API التي تتصل بقاعدة البيانات:

```javascript
const DB_CONFIG = {
  host: 'DB_HOST',      // مثال: 'mysql-4dads.hostinger.com'
  user: 'DB_USER',      // مثال: 'u123456789_4dads'
  password: 'DB_PASSWORD', // كلمة المرور الخاصة بقاعدة البيانات
  database: 'DB_NAME',  // مثال: 'u123456789_4dads'
};
```

#### تكوين مصادقة Google

قم بتعديل المتغير التالي في الملفات:
- `/src/components/auth/GoogleAuth.jsx`
- `/src/app/api/auth/google/route.js`

```javascript
const GOOGLE_CLIENT_ID = 'YOUR_GOOGLE_CLIENT_ID';
```

#### تكوين PayPal

قم بتعديل المتغيرات التالية في الملفات:
- `/src/components/subscription/PayPalSubscription.jsx`
- `/src/app/api/subscription/confirm/route.js`
- `/src/app/api/subscription/cancel/route.js`
- `/src/app/api/subscription/renew/route.js`

```javascript
const PAYPAL_CLIENT_ID = 'YOUR_PAYPAL_CLIENT_ID';
const PAYPAL_SECRET = 'YOUR_PAYPAL_SECRET';
```

وفي ملف `/src/components/subscription/PayPalSubscription.jsx` قم بتعديل معرفات خطط الاشتراك:

```javascript
const planId = 'P-FAKE_PLAN_ID'; // استبدلها بمعرف الخطة الحقيقي من PayPal
```

### 5. بناء ونشر التطبيق

1. قم بتثبيت اعتماديات المشروع:
   ```
   npm install
   ```

2. قم ببناء التطبيق للإنتاج:
   ```
   npm run build
   ```

3. قم برفع محتويات مجلد `.next` والملفات الأخرى المطلوبة إلى سيرفر Hostinger
   - يمكنك استخدام FTP أو Git أو أي طريقة أخرى متاحة

4. قم بتكوين Node.js في لوحة تحكم Hostinger لتشغيل التطبيق

## ملاحظات هامة

- تأكد من تفعيل HTTPS على الموقع لضمان أمان عمليات المصادقة والدفع
- قم باختبار عمليات الدفع في بيئة PayPal التجريبية (Sandbox) قبل الانتقال إلى بيئة الإنتاج
- تأكد من تكوين إعدادات CORS بشكل صحيح للسماح بالطلبات من النطاق الصحيح

## استكشاف الأخطاء وإصلاحها

### مشاكل المصادقة مع Google

- تأكد من إضافة نطاق موقعك إلى قائمة النطاقات المسموح بها في مشروع Google Cloud
- تأكد من تكوين عناوين URL المسموح بها للتوجيه بشكل صحيح

### مشاكل الدفع مع PayPal

- تأكد من استخدام بيانات اعتماد البيئة الصحيحة (تجريبية أو إنتاج)
- تحقق من سجلات الخطأ للحصول على تفاصيل حول أي مشكلات في معالجة المدفوعات

### مشاكل قاعدة البيانات

- تأكد من صحة بيانات الاتصال بقاعدة البيانات
- تحقق من وجود الجداول المطلوبة وهيكلها الصحيح
- تأكد من وجود الصلاحيات المناسبة لمستخدم قاعدة البيانات

## الدعم الفني

إذا واجهت أي مشكلات أثناء التثبيت أو الإعداد، يرجى التواصل مع فريق الدعم الفني على البريد الإلكتروني: <EMAIL>
