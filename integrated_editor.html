<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <title>محرر الرسم والقص المتكامل</title>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
  <style>
    body { 
      font-family: Tahoma, Arial, sans-serif; 
      direction: rtl; 
      margin: 0;
      padding: 0;
      background-color: #f8f9fa;
    }
    
    #editor-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      min-height: 100vh;
      padding: 20px;
    }
    
    #main-toolbar {
      display: flex;
      gap: 15px;
      padding: 15px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 10px;
      margin-bottom: 20px;
      box-shadow: 0 4px 15px rgba(0,0,0,0.1);
      width: 100%;
      max-width: 800px;
      justify-content: center;
      flex-wrap: wrap;
    }
    
    #main-toolbar button {
      background: rgba(255, 255, 255, 0.9);
      border: none;
      border-radius: 8px;
      padding: 12px 20px;
      color: #333;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 14px;
      min-width: 100px;
    }
    
    #main-toolbar button:hover {
      background: white;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }
    
    #main-toolbar button.active {
      background: #28a745;
      color: white;
    }
    
    canvas {
      border: 2px solid #dee2e6;
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.1);
      background: white;
    }
    
    .controls-panel {
      display: none;
      width: 100%;
      max-width: 800px;
      background: white;
      border-radius: 10px;
      padding: 20px;
      margin-top: 20px;
      box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .controls-panel.active {
      display: block;
    }
    
    .control-row {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-bottom: 15px;
      align-items: center;
      justify-content: flex-end;
    }
    
    .control-row:last-child {
      margin-bottom: 0;
    }
    
    .control-group {
      display: flex;
      align-items: center;
      gap: 8px;
      background: #f8f9fa;
      padding: 8px 12px;
      border-radius: 6px;
      border: 1px solid #e9ecef;
    }
    
    .control-group label {
      font-weight: 500;
      color: #495057;
      white-space: nowrap;
      margin-left: 8px;
    }
    
    .control-group input, .control-group select {
      border: 1px solid #ced4da;
      border-radius: 4px;
      padding: 4px 8px;
      font-size: 13px;
    }
    
    .control-group input[type="range"] {
      width: 100px;
    }
    
    .control-group input[type="color"] {
      width: 40px;
      height: 30px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    
    .btn-group {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }
    
    .btn {
      background: #6c757d;
      color: white;
      border: none;
      border-radius: 5px;
      padding: 8px 12px;
      cursor: pointer;
      font-size: 12px;
      transition: all 0.2s ease;
      white-space: nowrap;
    }
    
    .btn:hover {
      background: #5a6268;
      transform: translateY(-1px);
    }
    
    .btn.active {
      background: #007bff;
    }
    
    .btn-primary {
      background: #007bff;
    }
    
    .btn-primary:hover {
      background: #0056b3;
    }
    
    .btn-success {
      background: #28a745;
    }
    
    .btn-success:hover {
      background: #1e7e34;
    }
    
    .btn-danger {
      background: #dc3545;
    }
    
    .btn-danger:hover {
      background: #c82333;
    }
    
    .info-badge {
      background: #17a2b8;
      color: white;
      padding: 2px 6px;
      border-radius: 3px;
      font-size: 11px;
      min-width: 25px;
      text-align: center;
    }
    
    .section-title {
      font-size: 16px;
      font-weight: bold;
      color: #343a40;
      margin-bottom: 15px;
      padding-bottom: 5px;
      border-bottom: 2px solid #e9ecef;
    }

    @media (max-width: 768px) {
      #main-toolbar {
        flex-direction: column;
        gap: 10px;
      }
      
      .control-row {
        justify-content: center;
      }
      
      .control-group {
        margin-bottom: 10px;
      }
    }
  </style>
</head>
<body>
<div id="editor-container">
  <!-- شريط الأدوات الرئيسي -->
  <div id="main-toolbar">
    <button id="toggle-draw">🎨 أدوات الرسم</button>
    <button id="toggle-crop">✂️ أدوات القص</button>
    <button id="add-image">🖼️ إضافة صورة</button>
    <button id="clear-canvas" class="btn-danger">🗑️ مسح الكل</button>
  </div>

  <!-- منطقة الرسم -->
  <canvas id="c" width="800" height="500"></canvas>

  <!-- لوحة أدوات الرسم -->
  <div id="drawing-controls" class="controls-panel">
    <div class="section-title">أدوات الرسم والتحرير</div>
    
    <div class="control-row">
      <div class="btn-group">
        <button id="toggle-drawing-mode" class="btn btn-primary">تفعيل وضع الرسم</button>
        <button id="add-text" class="btn">إضافة نص</button>
        <button id="add-shape" class="btn">إضافة شكل</button>
      </div>
    </div>

    <div class="control-row">
      <div class="control-group">
        <label>نوع الفرشاة:</label>
        <select id="drawing-mode-selector">
          <option value="Pencil">قلم رصاص</option>
          <option value="Circle">دائرة</option>
          <option value="Spray">رذاذ</option>
          <option value="Pattern">نمط</option>
          <option value="hline">خط أفقي</option>
          <option value="vline">خط عمودي</option>
          <option value="square">مربع</option>
          <option value="diamond">معين</option>
        </select>
      </div>
      
      <div class="control-group">
        <label>سمك الخط:</label>
        <input type="range" min="1" max="150" id="drawing-line-width" value="30">
        <span class="info-badge" id="line-width-info">30</span>
      </div>
    </div>

    <div class="control-row">
      <div class="control-group">
        <label>لون الخط:</label>
        <input type="color" id="drawing-color" value="#76cef4">
      </div>
      
      <div class="control-group">
        <label>لون الظل:</label>
        <input type="color" id="drawing-shadow-color" value="#5a7896">
      </div>
    </div>

    <div class="control-row">
      <div class="control-group">
        <label>عرض الظل:</label>
        <input type="range" min="0" max="50" id="drawing-shadow-width" value="0">
        <span class="info-badge" id="shadow-width-info">0</span>
      </div>
      
      <div class="control-group">
        <label>إزاحة الظل:</label>
        <input type="range" min="0" max="50" id="drawing-shadow-offset" value="0">
        <span class="info-badge" id="shadow-offset-info">0</span>
      </div>
    </div>
  </div>

  <!-- لوحة أدوات القص -->
  <div id="crop-controls" class="controls-panel">
    <div class="section-title">أدوات القص والتحرير</div>
    
    <div class="control-row">
      <div class="btn-group">
        <button class="btn preset" data-ratio="1:1">1:1 مربع</button>
        <button class="btn preset" data-ratio="4:3">4:3 تقليدي</button>
        <button class="btn preset" data-ratio="16:9">16:9 عريض</button>
        <button class="btn preset" data-ratio="3:2">3:2 صور</button>
      </div>
    </div>
    
    <div class="control-row">
      <div class="btn-group">
        <button class="btn preset" data-ratio="2:3">2:3 بورتريه</button>
        <button class="btn preset" data-ratio="5:7">5:7 مجلة</button>
        <button class="btn preset" data-ratio="9:16">9:16 موبايل</button>
        <button id="free-crop" class="btn">قص حر</button>
      </div>
    </div>

    <div class="control-row">
      <div class="btn-group">
        <button id="rotate-box" class="btn">🔄 دوران الإطار</button>
        <button id="apply-crop" class="btn btn-success">✅ تطبيق القص</button>
        <button id="cancel-crop" class="btn btn-danger">❌ إلغاء القص</button>
      </div>
    </div>
  </div>
</div>

<script>
  // انتظار تحميل المكتبة بالكامل
  document.addEventListener('DOMContentLoaded', function() {
    initializeEditor();
  });

  let canvas;
  let cropRect = null;
  let activeImage = null;
  let currentMode = null;

  function initializeEditor() {
    // إنشاء اللوحة مع الإعدادات المحسنة
    canvas = new fabric.Canvas('c', {
      isDrawingMode: false,
      selection: true,
      preserveObjectStacking: true,
      renderOnAddRemove: true,
      skipTargetFind: false
    });

    // العناصر
    const drawingControls = document.getElementById('drawing-controls');
    const cropControls = document.getElementById('crop-controls');
    const toggleDrawBtn = document.getElementById('toggle-draw');
    const toggleCropBtn = document.getElementById('toggle-crop');

    // إعداد الفرشاة الافتراضية
    canvas.freeDrawingBrush = new fabric.PencilBrush(canvas);
    canvas.freeDrawingBrush.width = 30;
    canvas.freeDrawingBrush.color = '#76cef4';
    
    fabric.Object.prototype.transparentCorners = false;
    fabric.Object.prototype.cornerColor = '#0066cc';
    fabric.Object.prototype.cornerStyle = 'rect';
    
    // إضافة الأحداث والوظائف
    setupEventHandlers();
  }

  function setupEventHandlers() {
    // دوال مساعدة
    const $ = id => document.getElementById(id);

    function hideAllPanels() {
      document.getElementById('drawing-controls').classList.remove('active');
      document.getElementById('crop-controls').classList.remove('active');
      document.getElementById('toggle-draw').classList.remove('active');
      document.getElementById('toggle-crop').classList.remove('active');
      currentMode = null;
    }

    function updateBrush() {
      const brush = canvas.freeDrawingBrush;
      const drawingColorEl = $('drawing-color');
      const drawingShadowColorEl = $('drawing-shadow-color');
      const drawingLineWidthEl = $('drawing-line-width');
      const drawingShadowWidth = $('drawing-shadow-width');
      const drawingShadowOffset = $('drawing-shadow-offset');

      if (brush && drawingColorEl) {
        brush.color = drawingColorEl.value;
        brush.width = parseInt(drawingLineWidthEl.value, 10) || 1;
        brush.shadow = new fabric.Shadow({
          blur: parseInt(drawingShadowWidth.value, 10) || 0,
          offsetX: parseInt(drawingShadowOffset.value, 10) || 0,
          offsetY: parseInt(drawingShadowOffset.value, 10) || 0,
          color: drawingShadowColorEl.value,
        });
      }
    }

    // الأحداث الرئيسية
    document.getElementById('toggle-draw').onclick = () => {
      if (currentMode === 'drawing') {
        hideAllPanels();
      } else {
        hideAllPanels();
        document.getElementById('drawing-controls').classList.add('active');
        document.getElementById('toggle-draw').classList.add('active');
        currentMode = 'drawing';
      }
    };

    document.getElementById('toggle-crop').onclick = () => {
      const selectedObj = canvas.getActiveObject();
      if (!selectedObj || selectedObj.type !== 'image') {
        alert('يرجى تحديد صورة للقص');
        return;
      }
      
      if (currentMode === 'cropping') {
        hideAllPanels();
      } else {
        hideAllPanels();
        activeImage = selectedObj;
        document.getElementById('crop-controls').classList.add('active');
        document.getElementById('toggle-crop').classList.add('active');
        currentMode = 'cropping';
        createCropRect();
      }
    };

    function createCropRect() {
      if (cropRect) {
        canvas.remove(cropRect);
      }
      
      const bounds = activeImage.getBoundingRect();
      cropRect = new fabric.Rect({
        left: bounds.left + bounds.width * 0.1,
        top: bounds.top + bounds.height * 0.1,
        width: bounds.width * 0.8,
        height: bounds.height * 0.8,
        fill: 'rgba(255, 255, 255, 0.3)',
        stroke: '#ff4757',
        strokeWidth: 2,
        hasBorders: true,
        hasControls: true,
        selectable: true,
        objectCaching: false,
        transparentCorners: false,
        cornerColor: '#ff4757',
        cornerSize: 10,
        angle: 0,
      });
      canvas.add(cropRect);
      canvas.setActiveObject(cropRect);
      canvas.renderAll();
    }

  // أدوات الرسم
  $('toggle-drawing-mode').onclick = () => {
    canvas.isDrawingMode = !canvas.isDrawingMode;
    $('toggle-drawing-mode').textContent = canvas.isDrawingMode ? 'إيقاف وضع الرسم' : 'تفعيل وضع الرسم';
    $('toggle-drawing-mode').classList.toggle('active', canvas.isDrawingMode);
  };

  $('add-text').onclick = () => {
    const text = new fabric.Textbox('اكتب هنا', {
      left: 100,
      top: 100,
      width: 200,
      fontSize: 20,
      fill: '#333'
    });
    canvas.add(text);
    canvas.setActiveObject(text);
  };

  $('add-shape').onclick = () => {
    const circle = new fabric.Circle({
      left: 100,
      top: 100,
      radius: 50,
      fill: '#76cef4',
      stroke: '#5a7896',
      strokeWidth: 2
    });
    canvas.add(circle);
    canvas.setActiveObject(circle);
  };

  $('clear-canvas').onclick = () => {
    if (confirm('هل أنت متأكد من مسح جميع المحتويات؟')) {
      canvas.clear();
      hideAllPanels();
    }
  };

    // إضافة صورة محسّنة
    $('add-image').onclick = () => {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = 'image/*';
      input.onchange = (e) => {
        const file = e.target.files[0];
        if (!file) return;
        
        // التحقق من نوع الملف
        if (!file.type.startsWith('image/')) {
          alert('يرجى اختيار ملف صورة صحيح');
          return;
        }
        
        const reader = new FileReader();
        reader.onload = (event) => {
          const imgData = event.target.result;
          
          // إنشاء عنصر صورة HTML
          const imgElement = new Image();
          imgElement.crossOrigin = 'anonymous';
          
          imgElement.onload = () => {
            // إنشاء صورة Fabric.js
            const fabricImage = new fabric.Image(imgElement, {
              left: 50,
              top: 50,
              selectable: true,
              evented: true,
              hasControls: true,
              hasBorders: true,
              lockUniScaling: false
            });
            
            // حساب المقياس المناسب
            const maxWidth = canvas.width * 0.7;
            const maxHeight = canvas.height * 0.7;
            const scaleX = Math.min(maxWidth / fabricImage.width, 1);
            const scaleY = Math.min(maxHeight / fabricImage.height, 1);
            const scale = Math.min(scaleX, scaleY);
            
            // تطبيق المقياس والوضعية
            fabricImage.scale(scale);
            fabricImage.center();
            
            // إضافة الصورة للوحة
            canvas.add(fabricImage);
            canvas.setActiveObject(fabricImage);
            canvas.renderAll();
            
            console.log('تم تحميل الصورة بنجاح');
          };
          
          imgElement.onerror = () => {
            alert('فشل في تحميل الصورة. يرجى التأكد من صحة ملف الصورة.');
          };
          
          // تحميل البيانات
          imgElement.src = imgData;
        };
        
        reader.onerror = () => {
          alert('خطأ في قراءة الملف');
        };
        
        reader.readAsDataURL(file);
      };
      input.click();
    };

  // أحداث أدوات الرسم
  $('drawing-color').onchange = updateBrush;
  $('drawing-shadow-color').onchange = updateBrush;
  
  $('drawing-line-width').oninput = () => {
    $('line-width-info').textContent = $('drawing-line-width').value;
    updateBrush();
  };
  
  $('drawing-shadow-width').oninput = () => {
    $('shadow-width-info').textContent = $('drawing-shadow-width').value;
    updateBrush();
  };
  
  $('drawing-shadow-offset').oninput = () => {
    $('shadow-offset-info').textContent = $('drawing-shadow-offset').value;
    updateBrush();
  };

  $('drawing-mode-selector').onchange = () => {
    const val = $('drawing-mode-selector').value;
    
    if (val === 'hline') {
      const brush = new fabric.PatternBrush(canvas);
      brush.getPatternSrc = () => {
        const patternCanvas = document.createElement('canvas');
        patternCanvas.width = patternCanvas.height = 10;
        const ctx = patternCanvas.getContext('2d');
        ctx.strokeStyle = brush.color;
        ctx.lineWidth = 5;
        ctx.beginPath();
        ctx.moveTo(0, 5);
        ctx.lineTo(10, 5);
        ctx.stroke();
        return patternCanvas;
      };
      canvas.freeDrawingBrush = brush;
    } else if (val === 'vline') {
      const brush = new fabric.PatternBrush(canvas);
      brush.getPatternSrc = () => {
        const patternCanvas = document.createElement('canvas');
        patternCanvas.width = patternCanvas.height = 10;
        const ctx = patternCanvas.getContext('2d');
        ctx.strokeStyle = brush.color;
        ctx.lineWidth = 5;
        ctx.beginPath();
        ctx.moveTo(5, 0);
        ctx.lineTo(5, 10);
        ctx.stroke();
        return patternCanvas;
      };
      canvas.freeDrawingBrush = brush;
    } else {
      const brushClass = fabric[val + 'Brush'] || fabric.PencilBrush;
      canvas.freeDrawingBrush = new brushClass(canvas);
    }
    updateBrush();
  };

  // أدوات القص
  document.querySelectorAll('.preset').forEach(btn => {
    btn.onclick = () => {
      if (!cropRect) return;
      
      document.querySelectorAll('.preset').forEach(b => b.classList.remove('active'));
      btn.classList.add('active');
      
      const [w, h] = btn.dataset.ratio.split(':').map(Number);
      const currentWidth = cropRect.width;
      const newHeight = currentWidth * (h / w);
      
      cropRect.set({ height: newHeight });
      canvas.requestRenderAll();
    };
  });

  $('free-crop').onclick = () => {
    document.querySelectorAll('.preset').forEach(b => b.classList.remove('active'));
    $('free-crop').classList.add('active');
  };

  $('rotate-box').onclick = () => {
    if (!cropRect) return;
    cropRect.set({ angle: (cropRect.angle || 0) + 15 });
    canvas.requestRenderAll();
  };

  $('apply-crop').onclick = () => {
    if (!cropRect || !activeImage) return;
    
    // حساب منطقة القص
    const imgElement = activeImage.getElement();
    const cropBounds = cropRect.getBoundingRect();
    const imgBounds = activeImage.getBoundingRect();
    
    // إنشاء canvas مؤقت للقص
    const tempCanvas = document.createElement('canvas');
    const tempCtx = tempCanvas.getContext('2d');
    
    const cropWidth = cropBounds.width;
    const cropHeight = cropBounds.height;
    
    tempCanvas.width = cropWidth;
    tempCanvas.height = cropHeight;
    
    // حساب النسب والإزاحات
    const scaleX = activeImage.scaleX || 1;
    const scaleY = activeImage.scaleY || 1;
    
    const sourceX = (cropBounds.left - imgBounds.left) / scaleX;
    const sourceY = (cropBounds.top - imgBounds.top) / scaleY;
    const sourceWidth = cropWidth / scaleX;
    const sourceHeight = cropHeight / scaleY;
    
    // رسم الجزء المقصوص
    tempCtx.drawImage(
      imgElement,
      sourceX, sourceY, sourceWidth, sourceHeight,
      0, 0, cropWidth, cropHeight
    );
    
    // إنشاء صورة جديدة من النتيجة
    fabric.Image.fromURL(tempCanvas.toDataURL(), (croppedImg) => {
      croppedImg.set({
        left: cropBounds.left,
        top: cropBounds.top,
        selectable: true
      });
      
      canvas.remove(activeImage);
      canvas.remove(cropRect);
      canvas.add(croppedImg);
      canvas.setActiveObject(croppedImg);
      
      hideAllPanels();
      cropRect = null;
      activeImage = null;
    });
  };

  $('cancel-crop').onclick = () => {
    if (cropRect) {
      canvas.remove(cropRect);
      cropRect = null;
    }
    hideAllPanels();
    activeImage = null;
  };

    // تحديث الفرشاة عند التحميل
    updateBrush();
    
    // إعداد تفاعل لوحة المفاتيح
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Delete' && canvas.getActiveObject()) {
        canvas.remove(canvas.getActiveObject());
        canvas.renderAll();
      }
      
      if (e.key === 'Escape') {
        hideAllPanels();
        canvas.discardActiveObject();
        canvas.renderAll();
      }
    });
  }
</script>
</body>
</html>