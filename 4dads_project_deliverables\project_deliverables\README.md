# ملف README للمشروع

## نظام اشتراكات 4dads.pro

هذا المشروع هو تطبيق ويب متكامل لإدارة الاشتراكات المدفوعة مع دعم PayPal ومصادقة Google، مصمم خصيصًا لموقع 4dads.pro.

### الميزات الرئيسية

- نظام تسجيل وتسجيل دخول متكامل
- مصادقة باستخدام Google
- نظام اشتراكات مدفوعة مع PayPal
- لوحة تحكم للمستخدم لإدارة الاشتراكات
- واجهة مستخدم متجاوبة وسهلة الاستخدام

### التقنيات المستخدمة

- **الواجهة الأمامية**: Next.js, React, Tailwind CSS
- **الواجهة الخلفية**: Next.js API Routes
- **قاعدة البيانات**: MySQL
- **المصادقة**: Google OAuth 2.0
- **المدفوعات**: PayPal Subscriptions API

### هيكل المشروع

```
4dads_subscription_app/
├── database/
│   └── migrations/
│       └── 001_create_tables.sql  # سكريبت إنشاء قاعدة البيانات
├── src/
│   ├── app/
│   │   ├── api/                   # واجهات برمجة التطبيقات
│   │   │   ├── auth/              # واجهات المصادقة
│   │   │   └── subscription/      # واجهات الاشتراكات
│   │   ├── dashboard/             # صفحات لوحة التحكم
│   │   ├── register/              # صفحة التسجيل
│   │   └── subscription/          # صفحة الاشتراكات
│   ├── components/
│   │   ├── auth/                  # مكونات المصادقة
│   │   ├── dashboard/             # مكونات لوحة التحكم
│   │   └── subscription/          # مكونات الاشتراكات
│   ├── hooks/                     # خطافات React المخصصة
│   └── lib/                       # وظائف مساعدة
├── INSTALLATION.md                # دليل التثبيت والإعداد
└── README.md                      # هذا الملف
```

### التثبيت والإعداد

يرجى الاطلاع على [دليل التثبيت](./INSTALLATION.md) للحصول على تعليمات مفصلة حول كيفية إعداد وتشغيل المشروع.

### المتغيرات التي تحتاج إلى تعديل

جميع المتغيرات التي تحتاج إلى تعديل يدوي تم تمييزها بتعليق `[!]` في الكود، وتشمل:

- بيانات الاتصال بقاعدة البيانات
- معرفات وأسرار Google OAuth
- معرفات وأسرار PayPal
- معرفات خطط الاشتراك في PayPal

### المساهمة

إذا كنت ترغب في المساهمة في تطوير هذا المشروع، يرجى اتباع الخطوات التالية:

1. قم بعمل fork للمشروع
2. قم بإنشاء فرع جديد للميزة التي ترغب في إضافتها
3. قم بإجراء التغييرات اللازمة
4. قم بإرسال طلب سحب (pull request)

### الترخيص

هذا المشروع مرخص بموجب [رخصة MIT](LICENSE).

### الدعم

للحصول على الدعم، يرجى التواصل مع فريق الدعم الفني على البريد الإلكتروني: <EMAIL>
