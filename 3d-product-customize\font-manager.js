/**
 * <PERSON>ont<PERSON>ana<PERSON> <PERSON>les font loading and previewing
 */
class FontManager {
  constructor(editorCore) {
    this.editor = editorCore
    this.fonts = []
    this.fontSelector = null
    this.previewContainer = null
    this.isInitialized = false

    // Initialize
    this.init()
  }

  /**
   * Initialize the font manager
   */
  init() {
    // Load default fonts
    this.loadDefaultFonts()

    // Create font selector UI
    this.createFontSelector()

    this.isInitialized = true
  }

  /**
   * Load default fonts
   */
  loadDefaultFonts() {
    this.fonts = [
      { name: "Arial", family: "Arial, sans-serif", category: "sans-serif" },
      { name: "Times New Roman", family: "Times New Roman, serif", category: "serif" },
      { name: "Courier New", family: "Courier New, monospace", category: "monospace" },
      { name: "Georgia", family: "Georgia, serif", category: "serif" },
      { name: "<PERSON>erd<PERSON>", family: "Verdana, sans-serif", category: "sans-serif" },
      { name: "Helvetica", family: "Helvetica, Arial, sans-serif", category: "sans-serif" },
      { name: "<PERSON><PERSON><PERSON>", family: "Tahoma, sans-serif", category: "sans-serif" },
      { name: "Trebuchet MS", family: "Trebuchet MS, sans-serif", category: "sans-serif" },
      { name: "Impact", family: "Impact, sans-serif", category: "display" },
      { name: "Comic Sans MS", family: "Comic Sans MS, cursive", category: "handwriting" },
    ]

    // Load Google Fonts (in a real implementation)
    this.loadGoogleFonts()
  }

  /**
   * Load Google Fonts
   */
  loadGoogleFonts() {
    // This is a placeholder - in a real implementation,
    // you would load fonts from Google Fonts API

    // Example Google Fonts to add
    const googleFonts = [
      { name: "Roboto", family: "Roboto, sans-serif", category: "sans-serif" },
      { name: "Open Sans", family: "Open Sans, sans-serif", category: "sans-serif" },
      { name: "Lato", family: "Lato, sans-serif", category: "sans-serif" },
      { name: "Montserrat", family: "Montserrat, sans-serif", category: "sans-serif" },
      { name: "Roboto Condensed", family: "Roboto Condensed, sans-serif", category: "sans-serif" },
      { name: "Source Sans Pro", family: "Source Sans Pro, sans-serif", category: "sans-serif" },
      { name: "Oswald", family: "Oswald, sans-serif", category: "sans-serif" },
      { name: "Raleway", family: "Raleway, sans-serif", category: "sans-serif" },
      { name: "Merriweather", family: "Merriweather, serif", category: "serif" },
      { name: "Ubuntu", family: "Ubuntu, sans-serif", category: "sans-serif" },
    ]

    // Add Google Fonts to the fonts array
    this.fonts = [...this.fonts, ...googleFonts]

    // In a real implementation, you would load the font files
    // For example:
    const link = document.createElement("link")
    link.rel = "stylesheet"
    link.href =
      "https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&family=Open+Sans:wght@400;700&family=Lato:wght@400;700&family=Montserrat:wght@400;700&family=Roboto+Condensed:wght@400;700&family=Source+Sans+Pro:wght@400;700&family=Oswald:wght@400;700&family=Raleway:wght@400;700&family=Merriweather:wght@400;700&family=Ubuntu:wght@400;700&display=swap"
    document.head.appendChild(link)
  }

  /**
   * Create font selector UI
   */
  createFontSelector() {
    // Create container
    const container = document.createElement("div")
    container.className = "font-selector-container"
    container.style.cssText = `
      position: absolute;
      top: 20px;
      right: 20px;
      z-index: 100;
    `

    // Create dropdown
    this.fontSelector = document.createElement("select")
    this.fontSelector.className = "font-selector"
    this.fontSelector.style.cssText = `
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      background: white;
      cursor: pointer;
      min-width: 150px;
    `

    // Add options to dropdown
    this.fonts.forEach((font) => {
      const option = document.createElement("option")
      option.value = font.family
      option.textContent = font.name
      option.style.fontFamily = font.family
      this.fontSelector.appendChild(option)
    })

    // Create preview container
    this.previewContainer = document.createElement("div")
    this.previewContainer.className = "font-preview-container"
    this.previewContainer.style.cssText = `
      position: absolute;
      top: 100%;
      right: 0;
      width: 250px;
      background: white;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      padding: 10px;
      margin-top: 5px;
      display: none;
    `

    // Add event listeners
    this.fontSelector.addEventListener("change", () => this.handleFontChange())
    this.fontSelector.addEventListener("mouseover", () => this.showFontPreview())
    this.fontSelector.addEventListener("mouseout", () => this.hideFontPreview())

    // Add elements to container
    container.appendChild(this.fontSelector)
    container.appendChild(this.previewContainer)

    // Add container to document
    document.body.appendChild(container)
  }

  /**
   * Handle font change
   */
  handleFontChange() {
    const fontFamily = this.fontSelector.value

    // Update selected object's font
    this.editor.updateTextFont(fontFamily)
  }

  /**
   * Show font preview
   */
  showFontPreview() {
    // Get selected font
    const fontFamily = this.fontSelector.value
    const fontName = this.fontSelector.options[this.fontSelector.selectedIndex].textContent

    // Create preview content
    this.previewContainer.innerHTML = `
      <h3 style="margin: 0 0 8px; font-size: 14px; font-weight: bold;">Font Preview: ${fontName}</h3>
      <p style="margin: 0 0 5px; font-family: ${fontFamily}; font-size: 24px;">Aa Bb Cc</p>
      <p style="margin: 0; font-family: ${fontFamily}; font-size: 14px;">
        The quick brown fox jumps over the lazy dog.
      </p>
    `

    // Show preview
    this.previewContainer.style.display = "block"
  }

  /**
   * Hide font preview
   */
  hideFontPreview() {
    this.previewContainer.style.display = "none"
  }

  /**
   * Get font by name
   */
  getFontByName(name) {
    return this.fonts.find((font) => font.name === name)
  }

  /**
   * Add custom font
   */
  addCustomFont(name, family, category = "custom") {
    // Add to fonts array
    this.fonts.push({ name, family, category })

    // Add to dropdown
    const option = document.createElement("option")
    option.value = family
    option.textContent = name
    option.style.fontFamily = family
    this.fontSelector.appendChild(option)

    // In a real implementation, you would also load the font file
  }
}
