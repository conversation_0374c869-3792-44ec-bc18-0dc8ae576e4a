// src/app/dashboard/page.jsx
// [!] هذه الصفحة تعرض لوحة تحكم المستخدم مع معلومات الحساب والاشتراك

import React from 'react';
import UserDashboard from '../../components/dashboard/UserDashboard';

export const metadata = {
  title: 'لوحة التحكم - 4dads.pro',
  description: 'إدارة حسابك واشتراكك في موقع 4dads.pro',
};

export default function DashboardPage() {
  // [!] في بيئة الإنتاج، يجب جلب بيانات المستخدم من الجلسة
  // للاختبار، نستخدم بيانات وهمية
  const mockUser = {
    id: 1,
    name: 'محمد أحمد',
    email: '<EMAIL>',
    profile_picture: null, // يمكن استبدالها برابط صورة حقيقية
    phone: '+1234567890'
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* رأس الصفحة */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <h1 className="text-3xl font-bold text-gray-900">لوحة التحكم</h1>
        </div>
      </div>
      
      {/* محتوى الصفحة */}
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
          {/* القائمة الجانبية */}
          <div className="lg:col-span-1">
            <div className="bg-white shadow rounded-lg overflow-hidden">
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">القائمة</h2>
              </div>
              <nav className="p-4">
                <ul className="space-y-2">
                  <li>
                    <a 
                      href="/dashboard" 
                      className="block px-4 py-2 rounded-md bg-blue-50 text-blue-700 font-medium"
                    >
                      لوحة التحكم
                    </a>
                  </li>
                  <li>
                    <a 
                      href="/dashboard/profile" 
                      className="block px-4 py-2 rounded-md text-gray-700 hover:bg-gray-50 font-medium"
                    >
                      الملف الشخصي
                    </a>
                  </li>
                  <li>
                    <a 
                      href="/dashboard/billing" 
                      className="block px-4 py-2 rounded-md text-gray-700 hover:bg-gray-50 font-medium"
                    >
                      الفواتير والمدفوعات
                    </a>
                  </li>
                  <li>
                    <a 
                      href="/dashboard/settings" 
                      className="block px-4 py-2 rounded-md text-gray-700 hover:bg-gray-50 font-medium"
                    >
                      الإعدادات
                    </a>
                  </li>
                  <li className="pt-4 mt-4 border-t border-gray-200">
                    <a 
                      href="/logout" 
                      className="block px-4 py-2 rounded-md text-red-700 hover:bg-red-50 font-medium"
                    >
                      تسجيل الخروج
                    </a>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
          
          {/* المحتوى الرئيسي */}
          <div className="lg:col-span-2">
            <UserDashboard user={mockUser} />
          </div>
        </div>
      </div>
    </div>
  );
}
