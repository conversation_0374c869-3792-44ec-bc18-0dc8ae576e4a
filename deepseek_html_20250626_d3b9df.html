<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محرر Canvas متقدم</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.1/fabric.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/hammer.js/2.0.8/hammer.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        :root {
            --primary: #3498db;
            --secondary: #2c3e50;
            --accent: #e74c3c;
            --light: #ecf0f1;
            --dark: #34495e;
            --success: #2ecc71;
            --warning: #f39c12;
            --toolbar-height: 60px;
            --subtoolbar-height: 50px;
        }
        
        body {
            background: linear-gradient(135deg, #1a2a6c, #b21f1f, #2c3e50);
            color: var(--light);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .app-header {
            text-align: center;
            padding: 15px;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .app-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(to right, #3498db, #2ecc71, #f39c12);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .app-header p {
            font-size: 1.1rem;
            opacity: 0.8;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .container {
            display: flex;
            flex: 1;
            padding: 15px;
            gap: 15px;
            overflow: hidden;
        }
        
        .toolbar {
            width: 80px;
            background: rgba(25, 35, 60, 0.8);
            border-radius: 12px;
            padding: 15px 5px;
            display: flex;
            flex-direction: column;
            gap: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            overflow-y: auto;
        }
        
        .toolbar-btn {
            background: rgba(52, 73, 94, 0.7);
            border: none;
            color: white;
            border-radius: 8px;
            padding: 12px 0;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
            font-size: 0.8rem;
            position: relative;
        }
        
        .toolbar-btn:hover, .toolbar-btn.active {
            background: var(--primary);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }
        
        .toolbar-btn i {
            font-size: 1.4rem;
        }
        
        .canvas-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 15px;
            background: rgba(25, 35, 60, 0.8);
            border-radius: 12px;
            padding: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            overflow: hidden;
        }
        
        .canvas-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background: rgba(30, 40, 65, 0.7);
            border-radius: 8px;
        }
        
        .canvas-size {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .canvas-size select, .canvas-size button {
            padding: 8px 15px;
            border-radius: 6px;
            border: none;
            background: rgba(52, 73, 94, 0.7);
            color: white;
            cursor: pointer;
        }
        
        .canvas-wrapper {
            flex: 1;
            background: #1e2a3a;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        #main-canvas {
            background: white;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
        }
        
        .sub-toolbar {
            height: var(--subtoolbar-height);
            background: rgba(30, 40, 65, 0.7);
            border-radius: 8px;
            display: flex;
            align-items: center;
            padding: 0 15px;
            gap: 15px;
            overflow-x: auto;
        }
        
        .sub-tool {
            background: rgba(52, 73, 94, 0.7);
            border: none;
            color: white;
            border-radius: 6px;
            padding: 8px 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
            white-space: nowrap;
        }
        
        .sub-tool:hover, .sub-tool.active {
            background: var(--primary);
        }
        
        .properties-panel {
            width: 300px;
            background: rgba(25, 35, 60, 0.8);
            border-radius: 12px;
            padding: 15px;
            display: flex;
            flex-direction: column;
            gap: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            overflow-y: auto;
        }
        
        .panel-section {
            background: rgba(30, 40, 65, 0.7);
            border-radius: 8px;
            padding: 15px;
        }
        
        .panel-title {
            font-size: 1.1rem;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            color: var(--primary);
        }
        
        .property-group {
            margin-bottom: 15px;
        }
        
        .property-label {
            display: block;
            margin-bottom: 5px;
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .property-input {
            width: 100%;
            padding: 8px;
            border-radius: 6px;
            border: none;
            background: rgba(52, 73, 94, 0.7);
            color: white;
        }
        
        .color-picker {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .color-input {
            flex: 1;
            padding: 5px;
            border-radius: 6px;
            border: none;
            background: rgba(52, 73, 94, 0.7);
            color: white;
        }
        
        .color-preview {
            width: 30px;
            height: 30px;
            border-radius: 6px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            backdrop-filter: blur(5px);
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
        }
        
        .modal.active {
            opacity: 1;
            pointer-events: all;
        }
        
        .modal-content {
            background: rgba(25, 35, 60, 0.95);
            border-radius: 12px;
            padding: 30px;
            max-width: 90%;
            max-height: 90%;
            overflow: auto;
            width: 800px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .modal-title {
            font-size: 1.5rem;
            color: var(--primary);
        }
        
        .close-modal {
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.3s ease;
        }
        
        .close-modal:hover {
            background: rgba(231, 76, 60, 0.3);
        }
        
        .library-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .library-item {
            background: rgba(52, 73, 94, 0.7);
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .library-item:hover {
            background: var(--primary);
            transform: translateY(-5px);
        }
        
        .library-item i {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .library-item span {
            font-size: 0.8rem;
        }
        
        .image-upload-btn {
            position: relative;
            overflow: hidden;
            display: inline-block;
        }
        
        .image-upload-btn input {
            position: absolute;
            left: 0;
            top: 0;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }
        
        .status-bar {
            padding: 10px;
            background: rgba(30, 40, 65, 0.7);
            border-radius: 8px;
            font-size: 0.9rem;
            text-align: center;
        }
        
        @media (max-width: 1200px) {
            .container {
                flex-direction: column;
            }
            
            .toolbar {
                width: 100%;
                flex-direction: row;
                height: 70px;
                padding: 5px 15px;
                overflow-x: auto;
            }
            
            .properties-panel {
                width: 100%;
            }
        }
        
        @media (max-width: 768px) {
            .app-header h1 {
                font-size: 2rem;
            }
            
            .canvas-controls {
                flex-direction: column;
                gap: 10px;
                align-items: flex-start;
            }
            
            .toolbar-btn {
                padding: 8px;
                font-size: 0.7rem;
            }
            
            .toolbar-btn i {
                font-size: 1.2rem;
            }
            
            .sub-tool {
                padding: 6px 10px;
                font-size: 0.8rem;
            }
        }
        
        /* Custom icons */
        .icon {
            display: inline-block;
            width: 24px;
            height: 24px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        
        .icon-draw { background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/></svg>'); }
        .icon-shapes { background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M11 13h2v2h-2zm0 4h2v2h-2zm0-8h2v2h-2zm-4 4h2v2H7zm8 0h2v2h-2zm-4-4h2v2h-2zm8-6H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V5h14v14z"/></svg>'); }
        .icon-text { background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M5 4v3h5.5v12h3V7H19V4z"/></svg>'); }
        .icon-image { background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/></svg>'); }
        .icon-crop { background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M17 15h2V7c0-1.1-.9-2-2-2H9v2h8v8zM7 17V1H5v4H1v2h4v10c0 1.1.9 2 2 2h10v4h2v-4h4v-2H7z"/></svg>'); }
        .icon-effects { background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M12 6c3.79 0 7.17 2.13 8.82 5.5-.59 1.22-1.42 2.27-2.41 3.12l1.41 1.41c1.39-1.23 2.49-2.77 3.18-4.53C21.27 7.11 17 4 12 4c-1.27 0-2.49.2-3.64.57l1.65 1.65C10.66 6.09 11.32 6 12 6zm-1.07 1.14L13 9.21c.57.25 1.03.71 1.28 1.28l2.07 2.07c.08-.34.14-.7.14-1.07C16.5 9.01 14.48 7 12 7c-.37 0-.72.05-1.07.14zM2.01 3.87l2.68 2.68C3.06 7.83 1.77 9.53 1 11.5 2.73 15.89 7 19 12 19c1.52 0 2.98-.29 4.32-.82l3.42 3.42 1.41-1.41L3.42 2.45 2.01 3.87zm7.5 7.5l2.61 2.61c-.04.01-.08.02-.12.02-1.38 0-2.5-1.12-2.5-2.5 0-.05.01-.08.01-.13zm-3.4-3.4l1.75 1.75c-.23.55-.36 1.15-.36 1.78 0 2.48 2.02 4.5 4.5 4.5.63 0 1.23-.13 1.77-.36l.98.98c-.88.24-1.8.38-2.75.38-3.79 0-7.17-2.13-8.82-5.5.7-1.43 1.72-2.61 2.93-3.53z"/></svg>'); }
        .icon-library { background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M4 6H2v14c0 1.1.9 2 2 2h14v-2H4V6zm16-4H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-8 12.5v-9l6 4.5-6 4.5z"/></svg>'); }
        .icon-view { background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/></svg>'); }
        .icon-export { background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/></svg>'); }
        .icon-upload { background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96zM14 13v4h-4v-4H7l5-5 5 5h-3z"/></svg>'); }
        .icon-select { background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M13 5.41V17a1 1 0 0 1-2 0V5.41l-3.3 3.3a1 1 0 0 1-1.4-1.42l5-5a1 1 0 0 1 1.4 0l5 5a1 1 0 1 1-1.4 1.42L13 5.4z"/></svg>'); }
    </style>
</head>
<body>
    <div class="app-header">
        <h1>محرر Canvas متقدم</h1>
        <p>محرر كامل مع دعم لجميع أدوات Fabric.js</p>
    </div>
    
    <div class="container">
        <div class="toolbar">
            <button class="toolbar-btn" id="select-mode" data-category="select">
                <i class="icon icon-select"></i>
                <span>تحديد</span>
            </button>
            <button class="toolbar-btn" data-category="draw">
                <i class="icon icon-draw"></i>
                <span>رسم</span>
            </button>
            <button class="toolbar-btn" data-category="shapes">
                <i class="icon icon-shapes"></i>
                <span>أشكال</span>
            </button>
            <button class="toolbar-btn" data-category="text">
                <i class="icon icon-text"></i>
                <span>نص</span>
            </button>
            <button class="toolbar-btn" data-category="images">
                <i class="icon icon-image"></i>
                <span>صور</span>
            </button>
            <button class="toolbar-btn" data-category="crop">
                <i class="icon icon-crop"></i>
                <span>قص</span>
            </button>
            <button class="toolbar-btn" data-category="effects">
                <i class="icon icon-effects"></i>
                <span>تأثيرات</span>
            </button>
            <button class="toolbar-btn" data-category="library">
                <i class="icon icon-library"></i>
                <span>مكتبة</span>
            </button>
            <button class="toolbar-btn image-upload-btn" id="upload-images">
                <i class="icon icon-upload"></i>
                <span>رفع صور</span>
                <input type="file" id="image-upload" accept="image/*" multiple>
            </button>
        </div>
        
        <div class="canvas-container">
            <div class="canvas-controls">
                <div class="canvas-size">
                    <span>حجم اللوحة:</span>
                    <select id="canvas-size">
                        <option value="600x400">600x400</option>
                        <option value="800x600" selected>800x600</option>
                        <option value="1024x768">1024x768</option>
                        <option value="1920x1080">1920x1080</option>
                        <option value="custom">مخصص</option>
                    </select>
                    <button id="clear-canvas">مسح اللوحة</button>
                </div>
                <div>
                    <button id="undo-btn">تراجع</button>
                    <button id="redo-btn">إعادة</button>
                    <button id="toggle-grid">شبكة: غير مفعلة</button>
                </div>
            </div>
            
            <div class="canvas-wrapper">
                <canvas id="main-canvas" width="800" height="600"></canvas>
            </div>
            
            <div class="sub-toolbar">
                <button class="sub-tool" data-tool="pencil">قلم رصاص</button>
                <button class="sub-tool" data-tool="brush">فرشاة</button>
                <button class="sub-tool" data-tool="marker">ماركر</button>
                <button class="sub-tool" data-tool="spray">رش</button>
                <button class="sub-tool" data-tool="dotted">خط منقط</button>
                <button class="sub-tool" data-tool="eraser">ممحاة</button>
                <button class="sub-tool" data-tool="rectangle">مستطيل</button>
                <button class="sub-tool" data-tool="circle">دائرة</button>
                <button class="sub-tool" data-tool="triangle">مثلث</button>
                <button class="sub-tool" data-tool="line">خط</button>
            </div>
            
            <div class="status-bar" id="status-bar">
                الوضع: تحديد (اضغط على كائن لتحريره)
            </div>
        </div>
        
        <div class="properties-panel">
            <div class="panel-section">
                <h3 class="panel-title">خصائص الكائن</h3>
                <div class="property-group">
                    <label class="property-label">الموقع</label>
                    <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                        <input type="number" class="property-input" id="pos-x" placeholder="X">
                        <input type="number" class="property-input" id="pos-y" placeholder="Y">
                    </div>
                </div>
                <div class="property-group">
                    <label class="property-label">الحجم</label>
                    <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                        <input type="number" class="property-input" id="width" placeholder="العرض">
                        <input type="number" class="property-input" id="height" placeholder="الارتفاع">
                    </div>
                </div>
                <div class="property-group">
                    <label class="property-label">التدوير</label>
                    <input type="range" class="property-input" id="rotation" min="0" max="360" step="1">
                </div>
                <div class="property-group">
                    <label class="property-label">الشفافية</label>
                    <input type="range" class="property-input" id="opacity" min="0" max="1" step="0.1" value="1">
                </div>
            </div>
            
            <div class="panel-section">
                <h3 class="panel-title">الملء والحدود</h3>
                <div class="property-group">
                    <label class="property-label">لون الملء</label>
                    <div class="color-picker">
                        <input type="color" class="color-input" id="fill-color" value="#3498db">
                        <div class="color-preview" id="fill-preview" style="background: #3498db;"></div>
                    </div>
                </div>
                <div class="property-group">
                    <label class="property-label">لون الحدود</label>
                    <div class="color-picker">
                        <input type="color" class="color-input" id="stroke-color" value="#e74c3c">
                        <div class="color-preview" id="stroke-preview" style="background: #e74c3c;"></div>
                    </div>
                </div>
                <div class="property-group">
                    <label class="property-label">عرض الحدود</label>
                    <input type="range" class="property-input" id="stroke-width" min="1" max="20" value="2">
                </div>
            </div>
            
            <div class="panel-section">
                <h3 class="panel-title">الظل</h3>
                <div class="property-group">
                    <label class="property-label">لون الظل</label>
                    <div class="color-picker">
                        <input type="color" class="color-input" id="shadow-color" value="#000000">
                        <div class="color-preview" id="shadow-preview" style="background: #000000;"></div>
                    </div>
                </div>
                <div class="property-group">
                    <label class="property-label">الضبابية</label>
                    <input type="range" class="property-input" id="shadow-blur" min="0" max="20" value="5">
                </div>
                <div class="property-group">
                    <label class="property-label">الإزاحة</label>
                    <div style="display: flex; gap: 10px;">
                        <input type="number" class="property-input" id="shadow-offset-x" placeholder="X" value="5">
                        <input type="number" class="property-input" id="shadow-offset-y" placeholder="Y" value="5">
                    </div>
                </div>
            </div>
            
            <div class="panel-section">
                <h3 class="panel-title">مرشحات الصور</h3>
                <div class="property-group">
                    <button id="apply-brightness" class="property-input">سطوع</button>
                    <button id="apply-contrast" class="property-input">تباين</button>
                    <button id="apply-blur" class="property-input">ضبابية</button>
                    <button id="apply-grayscale" class="property-input">تدرج رمادي</button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="modal" id="library-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">مكتبة الصور</h2>
                <button class="close-modal" id="close-library">&times;</button>
            </div>
            <p>اختر صورة لإضافتها إلى اللوحة:</p>
            <div class="library-grid">
                <div class="library-item">
                    <i class="icon icon-shapes"></i>
                    <span>أيقونة 1</span>
                </div>
                <div class="library-item">
                    <i class="icon icon-shapes"></i>
                    <span>أيقونة 2</span>
                </div>
                <div class="library-item">
                    <i class="icon icon-shapes"></i>
                    <span>أيقونة 3</span>
                </div>
                <div class="library-item">
                    <i class="icon icon-shapes"></i>
                    <span>أيقونة 4</span>
                </div>
                <div class="library-item">
                    <i class="icon icon-shapes"></i>
                    <span>أيقونة 5</span>
                </div>
                <div class="library-item">
                    <i class="icon icon-shapes"></i>
                    <span>أيقونة 6</span>
                </div>
                <div class="library-item">
                    <i class="icon icon-shapes"></i>
                    <span>أيقونة 7</span>
                </div>
                <div class="library-item">
                    <i class="icon icon-shapes"></i>
                    <span>أيقونة 8</span>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Initialize Fabric.js canvas
            const canvas = new fabric.Canvas('main-canvas', {
                selection: true,
                backgroundColor: '#ffffff'
            });
            
            // State management
            const state = {
                currentCategory: 'select',
                currentTool: 'select',
                history: [],
                historyIndex: -1,
                gridEnabled: false
            };
            
            // Initialize Hammer.js for touch gestures
            const canvasElement = document.getElementById('main-canvas');
            const hammer = new Hammer(canvasElement);
            
            // Set up Hammer.js gestures
            hammer.get('pinch').set({ enable: true });
            hammer.get('rotate').set({ enable: true });
            
            // Pinch to zoom
            let lastScale = 1;
            hammer.on('pinch', (e) => {
                const zoom = canvas.getZoom();
                const newZoom = zoom * e.scale / lastScale;
                canvas.zoomToPoint({ x: e.center.x, y: e.center.y }, newZoom);
                lastScale = e.scale;
            });
            
            hammer.on('pinchend', () => {
                lastScale = 1;
            });
            
            // Toolbar event listeners
            document.querySelectorAll('.toolbar-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    document.querySelectorAll('.toolbar-btn').forEach(b => b.classList.remove('active'));
                    btn.classList.add('active');
                    state.currentCategory = btn.dataset.category;
                    
                    // Update UI and status
                    document.getElementById('status-bar').textContent = 
                        `الوضع: ${getModeName(state.currentCategory)}`;
                    
                    // Handle mode changes
                    if (state.currentCategory === 'draw') {
                        canvas.isDrawingMode = true;
                        setDrawingTool(state.currentTool);
                    } else {
                        canvas.isDrawingMode = false;
                    }
                    
                    // For select mode, ensure we can select objects
                    if (state.currentCategory === 'select') {
                        canvas.selection = true;
                    }
                });
            });
            
            // Set select mode as active initially
            document.getElementById('select-mode').classList.add('active');
            
            // Sub-toolbar event listeners
            document.querySelectorAll('.sub-tool').forEach(tool => {
                tool.addEventListener('click', () => {
                    document.querySelectorAll('.sub-tool').forEach(t => t.classList.remove('active'));
                    tool.classList.add('active');
                    state.currentTool = tool.dataset.tool;
                    
                    // Update drawing tool if in draw mode
                    if (state.currentCategory === 'draw') {
                        setDrawingTool(state.currentTool);
                    }
                    
                    // For shape tools, add the shape
                    if (state.currentCategory === 'shapes') {
                        addShape(state.currentTool);
                    }
                });
            });
            
            // Canvas controls
            document.getElementById('canvas-size').addEventListener('change', function() {
                if (this.value === 'custom') {
                    const width = prompt('أدخل عرض اللوحة:', canvas.width);
                    const height = prompt('أدخل ارتفاع اللوحة:', canvas.height);
                    if (width && height) {
                        canvas.setWidth(width);
                        canvas.setHeight(height);
                    }
                } else {
                    const [width, height] = this.value.split('x');
                    canvas.setWidth(width);
                    canvas.setHeight(height);
                }
                canvas.renderAll();
            });
            
            document.getElementById('clear-canvas').addEventListener('click', () => {
                if (confirm('هل أنت متأكد أنك تريد مسح اللوحة؟')) {
                    canvas.clear();
                    canvas.backgroundColor = '#ffffff';
                    saveState();
                }
            });
            
            document.getElementById('toggle-grid').addEventListener('click', () => {
                state.gridEnabled = !state.gridEnabled;
                document.getElementById('toggle-grid').textContent = 
                    `شبكة: ${state.gridEnabled ? 'مفعّلة' : 'غير مفعّلة'}`;
                canvas.renderAll();
            });
            
            // Object property controls
            document.getElementById('fill-color').addEventListener('input', function() {
                const activeObject = canvas.getActiveObject();
                if (activeObject) {
                    activeObject.set('fill', this.value);
                    document.getElementById('fill-preview').style.background = this.value;
                    canvas.renderAll();
                }
            });
            
            document.getElementById('stroke-color').addEventListener('input', function() {
                const activeObject = canvas.getActiveObject();
                if (activeObject) {
                    activeObject.set('stroke', this.value);
                    document.getElementById('stroke-preview').style.background = this.value;
                    canvas.renderAll();
                }
            });
            
            document.getElementById('stroke-width').addEventListener('input', function() {
                const activeObject = canvas.getActiveObject();
                if (activeObject) {
                    activeObject.set('strokeWidth', parseInt(this.value));
                    canvas.renderAll();
                }
            });
            
            document.getElementById('rotation').addEventListener('input', function() {
                const activeObject = canvas.getActiveObject();
                if (activeObject) {
                    activeObject.set('angle', parseInt(this.value));
                    canvas.renderAll();
                }
            });
            
            document.getElementById('opacity').addEventListener('input', function() {
                const activeObject = canvas.getActiveObject();
                if (activeObject) {
                    activeObject.set('opacity', parseFloat(this.value));
                    canvas.renderAll();
                }
            });
            
            // Shadow controls
            document.getElementById('shadow-color').addEventListener('input', function() {
                const activeObject = canvas.getActiveObject();
                if (activeObject) {
                    if (!activeObject.shadow) {
                        activeObject.shadow = new fabric.Shadow({
                            color: this.value,
                            blur: 5,
                            offsetX: 5,
                            offsetY: 5
                        });
                    } else {
                        activeObject.shadow.color = this.value;
                    }
                    document.getElementById('shadow-preview').style.background = this.value;
                    canvas.renderAll();
                }
            });
            
            document.getElementById('shadow-blur').addEventListener('input', function() {
                const activeObject = canvas.getActiveObject();
                if (activeObject && activeObject.shadow) {
                    activeObject.shadow.blur = parseInt(this.value);
                    canvas.renderAll();
                }
            });
            
            document.getElementById('shadow-offset-x').addEventListener('input', function() {
                const activeObject = canvas.getActiveObject();
                if (activeObject && activeObject.shadow) {
                    activeObject.shadow.offsetX = parseInt(this.value);
                    canvas.renderAll();
                }
            });
            
            document.getElementById('shadow-offset-y').addEventListener('input', function() {
                const activeObject = canvas.getActiveObject();
                if (activeObject && activeObject.shadow) {
                    activeObject.shadow.offsetY = parseInt(this.value);
                    canvas.renderAll();
                }
            });
            
            // Image filter buttons
            document.getElementById('apply-brightness').addEventListener('click', () => {
                const activeObject = canvas.getActiveObject();
                if (activeObject && activeObject.type === 'image') {
                    activeObject.filters.push(new fabric.Image.filters.Brightness({ brightness: 0.2 }));
                    activeObject.applyFilters();
                    canvas.renderAll();
                }
            });
            
            document.getElementById('apply-contrast').addEventListener('click', () => {
                const activeObject = canvas.getActiveObject();
                if (activeObject && activeObject.type === 'image') {
                    activeObject.filters.push(new fabric.Image.filters.Contrast({ contrast: 0.2 }));
                    activeObject.applyFilters();
                    canvas.renderAll();
                }
            });
            
            document.getElementById('apply-blur').addEventListener('click', () => {
                const activeObject = canvas.getActiveObject();
                if (activeObject && activeObject.type === 'image') {
                    activeObject.filters.push(new fabric.Image.filters.Blur({ blur: 0.1 }));
                    activeObject.applyFilters();
                    canvas.renderAll();
                }
            });
            
            document.getElementById('apply-grayscale').addEventListener('click', () => {
                const activeObject = canvas.getActiveObject();
                if (activeObject && activeObject.type === 'image') {
                    activeObject.filters.push(new fabric.Image.filters.Grayscale());
                    activeObject.applyFilters();
                    canvas.renderAll();
                }
            });
            
            // Library modal
            document.querySelector('[data-category="library"]').addEventListener('click', () => {
                document.getElementById('library-modal').classList.add('active');
            });
            
            document.getElementById('close-library').addEventListener('click', () => {
                document.getElementById('library-modal').classList.remove('active');
            });
            
            // Image upload
            document.getElementById('image-upload').addEventListener('change', function(e) {
                const files = e.target.files;
                for (let i = 0; i < files.length; i++) {
                    const file = files[i];
                    if (!file.type.match('image.*')) continue;
                    
                    const reader = new FileReader();
                    reader.onload = function(f) {
                        fabric.Image.fromURL(f.target.result, function(img) {
                            img.set({
                                left: 100 + (i * 20),
                                top: 100 + (i * 20),
                                scaleX: 0.5,
                                scaleY: 0.5,
                                cornerStyle: 'circle',
                                cornerColor: '#3498db',
                                transparentCorners: false
                            });
                            canvas.add(img);
                            canvas.setActiveObject(img);
                            saveState();
                        });
                    };
                    reader.readAsDataURL(file);
                }
            });
            
            // Save state on object modification
            canvas.on('object:modified', saveState);
            canvas.on('object:added', saveState);
            canvas.on('object:removed', saveState);
            
            // Update properties when object is selected
            canvas.on('selection:created', updatePropertiesPanel);
            canvas.on('selection:updated', updatePropertiesPanel);
            canvas.on('selection:cleared', clearPropertiesPanel);
            
            // Draw grid if enabled
            canvas.on('before:render', function() {
                if (state.gridEnabled) {
                    canvas.clearContext(canvas.contextTop);
                    drawGrid();
                }
            });
            
            canvas.on('after:render', function() {
                if (state.gridEnabled) {
                    drawGrid();
                }
            });
            
            // Undo/redo functionality
            document.getElementById('undo-btn').addEventListener('click', undo);
            document.getElementById('redo-btn').addEventListener('click', redo);
            
            // Functions
            function setDrawingTool(tool) {
                if (state.currentCategory !== 'draw') return;
                
                switch(tool) {
                    case 'pencil':
                        canvas.freeDrawingBrush = new fabric.PencilBrush(canvas);
                        canvas.freeDrawingBrush.width = 2;
                        canvas.freeDrawingBrush.color = '#000000';
                        break;
                    case 'brush':
                        canvas.freeDrawingBrush = new fabric.PencilBrush(canvas);
                        canvas.freeDrawingBrush.width = 8;
                        canvas.freeDrawingBrush.color = '#3498db';
                        break;
                    case 'marker':
                        canvas.freeDrawingBrush = new fabric.PencilBrush(canvas);
                        canvas.freeDrawingBrush.width = 15;
                        canvas.freeDrawingBrush.color = '#e74c3c';
                        canvas.freeDrawingBrush.opacity = 0.6;
                        break;
                    case 'spray':
                        canvas.freeDrawingBrush = new fabric.SprayBrush(canvas);
                        canvas.freeDrawingBrush.width = 20;
                        canvas.freeDrawingBrush.color = '#2ecc71';
                        break;
                    case 'dotted':
                        canvas.freeDrawingBrush = new fabric.PatternBrush(canvas);
                        canvas.freeDrawingBrush.width = 3;
                        canvas.freeDrawingBrush.color = '#9b59b6';
                        break;
                    case 'eraser':
                        canvas.freeDrawingBrush = new fabric.EraserBrush(canvas);
                        canvas.freeDrawingBrush.width = 20;
                        break;
                }
            }
            
            function addShape(shapeType) {
                let shape;
                switch(shapeType) {
                    case 'rectangle':
                        shape = new fabric.Rect({
                            width: 100,
                            height: 100,
                            fill: '#3498db',
                            stroke: '#2980b9',
                            strokeWidth: 2
                        });
                        break;
                    case 'circle':
                        shape = new fabric.Circle({
                            radius: 50,
                            fill: '#e74c3c',
                            stroke: '#c0392b',
                            strokeWidth: 2
                        });
                        break;
                    case 'triangle':
                        shape = new fabric.Triangle({
                            width: 100,
                            height: 100,
                            fill: '#2ecc71',
                            stroke: '#27ae60',
                            strokeWidth: 2
                        });
                        break;
                    case 'line':
                        shape = new fabric.Line([50, 50, 150, 150], {
                            stroke: '#9b59b6',
                            strokeWidth: 3
                        });
                        break;
                }
                
                if (shape) {
                    shape.set({
                        left: 200,
                        top: 200,
                        cornerStyle: 'circle',
                        cornerColor: '#3498db',
                        transparentCorners: false
                    });
                    canvas.add(shape);
                    canvas.setActiveObject(shape);
                    saveState();
                }
            }
            
            function saveState() {
                // Save canvas state to history for undo/redo
                state.history = state.history.slice(0, state.historyIndex + 1);
                state.history.push(JSON.stringify(canvas));
                state.historyIndex++;
                
                // Limit history to 50 states
                if (state.history.length > 50) {
                    state.history.shift();
                    state.historyIndex--;
                }
            }
            
            function undo() {
                if (state.historyIndex <= 0) return;
                state.historyIndex--;
                loadState();
            }
            
            function redo() {
                if (state.historyIndex >= state.history.length - 1) return;
                state.historyIndex++;
                loadState();
            }
            
            function loadState() {
                canvas.loadFromJSON(state.history[state.historyIndex], () => {
                    canvas.renderAll();
                });
            }
            
            function updatePropertiesPanel() {
                const activeObject = canvas.getActiveObject();
                if (!activeObject) return;
                
                document.getElementById('pos-x').value = Math.round(activeObject.left);
                document.getElementById('pos-y').value = Math.round(activeObject.top);
                
                if (activeObject.width) {
                    document.getElementById('width').value = Math.round(activeObject.width * activeObject.scaleX);
                }
                
                if (activeObject.height) {
                    document.getElementById('height').value = Math.round(activeObject.height * activeObject.scaleY);
                }
                
                if (activeObject.angle) {
                    document.getElementById('rotation').value = Math.round(activeObject.angle);
                }
                
                if (activeObject.opacity) {
                    document.getElementById('opacity').value = activeObject.opacity;
                }
                
                if (activeObject.fill) {
                    document.getElementById('fill-color').value = activeObject.fill;
                    document.getElementById('fill-preview').style.background = activeObject.fill;
                }
                
                if (activeObject.stroke) {
                    document.getElementById('stroke-color').value = activeObject.stroke;
                    document.getElementById('stroke-preview').style.background = activeObject.stroke;
                }
                
                if (activeObject.strokeWidth) {
                    document.getElementById('stroke-width').value = activeObject.strokeWidth;
                }
                
                if (activeObject.shadow) {
                    document.getElementById('shadow-color').value = activeObject.shadow.color;
                    document.getElementById('shadow-preview').style.background = activeObject.shadow.color;
                    document.getElementById('shadow-blur').value = activeObject.shadow.blur;
                    document.getElementById('shadow-offset-x').value = activeObject.shadow.offsetX;
                    document.getElementById('shadow-offset-y').value = activeObject.shadow.offsetY;
                }
            }
            
            function clearPropertiesPanel() {
                // Reset property inputs when no object is selected
                document.getElementById('pos-x').value = '';
                document.getElementById('pos-y').value = '';
                document.getElementById('width').value = '';
                document.getElementById('height').value = '';
            }
            
            function drawGrid() {
                const gridSize = 20;
                const ctx = canvas.contextTop;
                const width = canvas.width;
                const height = canvas.height;
                
                ctx.save();
                ctx.strokeStyle = 'rgba(200, 200, 200, 0.3)';
                ctx.lineWidth = 1;
                
                // Vertical lines
                for (let x = 0; x <= width; x += gridSize) {
                    ctx.beginPath();
                    ctx.moveTo(x, 0);
                    ctx.lineTo(x, height);
                    ctx.stroke();
                }
                
                // Horizontal lines
                for (let y = 0; y <= height; y += gridSize) {
                    ctx.beginPath();
                    ctx.moveTo(0, y);
                    ctx.lineTo(width, y);
                    ctx.stroke();
                }
                
                ctx.restore();
            }
            
            function getModeName(mode) {
                const modeNames = {
                    'select': 'تحديد',
                    'draw': 'رسم',
                    'shapes': 'أشكال',
                    'text': 'نص',
                    'images': 'صور',
                    'crop': 'قص',
                    'effects': 'تأثيرات',
                    'library': 'مكتبة'
                };
                return modeNames[mode] || mode;
            }
            
            // Add some sample objects to the canvas
            const text = new fabric.IText('اضغط هنا لتعديل النص', {
                left: 100,
                top: 100,
                fontFamily: 'Arial',
                fontSize: 30,
                fill: '#2c3e50',
                shadow: 'rgba(0,0,0,0.3) 5px 5px 5px'
            });
            
            const rect = new fabric.Rect({
                left: 250,
                top: 150,
                width: 100,
                height: 100,
                fill: '#3498db',
                stroke: '#2980b9',
                strokeWidth: 2,
                shadow: 'rgba(0,0,0,0.3) 5px 5px 5px'
            });
            
            const circle = new fabric.Circle({
                left: 400,
                top: 150,
                radius: 50,
                fill: '#e74c3c',
                stroke: '#c0392b',
                strokeWidth: 2
            });
            
            // Add sample image
            fabric.Image.fromURL('https://via.placeholder.com/150', function(img) {
                img.set({
                    left: 500,
                    top: 150,
                    scaleX: 0.7,
                    scaleY: 0.7
                });
                canvas.add(img);
                canvas.renderAll();
            });
            
            canvas.add(text, rect, circle);
            
            // Save initial state
            saveState();
        });
    </script>
</body>
</html>