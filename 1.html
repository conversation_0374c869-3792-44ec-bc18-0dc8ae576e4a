<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Fabric.js Advanced Editor</title>
    <!-- Fabric.js Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.1/fabric.min.js"></script>
    <!-- Hammer.js Library (for touch gestures) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/hammer.js/2.0.8/hammer.min.js"></script>
    <!-- Cropper.js CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/cropperjs@1.5.12/dist/cropper.min.css"
      rel="stylesheet"
    />
    <style>
      /* Base styles and variables for a dark theme */
      :root {
        --primary-bg: #282c34; /* Dark background */
        --secondary-bg: #3a4049; /* Slightly lighter dark for toolbars */
        --accent-color: #61dafb; /* Cyan/blue accent */
        --text-color: #ffffff; /* White text */
        --border-color: #555; /* Gray border */
        --button-hover: #4a5059; /* Darker gray on hover */
        --input-bg: #444; /* Dark gray for input backgrounds */
        --input-border: #666; /* Medium gray for input borders */
        --modal-bg: rgba(
          0,
          0,
          0,
          0.7
        ); /* Semi-transparent black for modal overlay */
        --touch-target-min-size: 44px; /* Minimum size for touch targets */
      }

      body {
        margin: 0;
        font-family: "Inter", Arial, sans-serif; /* Use Inter font */
        background-color: var(--primary-bg);
        color: var(--text-color);
        overflow: hidden; /* Prevent body scroll to ensure full canvas display */
        display: flex;
        flex-direction: column;
        height: 100vh; /* Full viewport height */
      }

      /* Styling for toolbars (top and bottom) */
      .toolbar {
        display: flex;
        justify-content: center; /* Center buttons horizontally */
        align-items: center;
        padding: 8px;
        background-color: var(--secondary-bg);
        border-bottom: 1px solid var(--border-color);
        flex-wrap: wrap; /* Allow buttons to wrap on smaller screens */
        gap: 8px; /* Space between buttons */
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        min-height: 50px; /* Ensure visibility */
      }

      /* Styling for all interactive elements within toolbars */
      .toolbar button,
      .toolbar select,
      .toolbar input[type="color"],
      .toolbar input[type="number"],
      .toolbar input[type="range"],
      .toolbar input[type="file"] {
        background-color: var(--input-bg);
        color: var(--text-color);
        border: 1px solid var(--input-border);
        padding: 8px 12px;
        border-radius: 8px; /* Rounded corners */
        cursor: pointer;
        transition: background-color 0.2s ease, border-color 0.2s ease,
          transform 0.1s ease;
        font-size: 14px;
        white-space: nowrap; /* Prevent text wrapping on buttons */
        min-width: var(--touch-target-min-size); /* Ensure touch-friendliness */
        min-height: var(--touch-target-min-size);
        display: flex; /* For icon+text alignment */
        align-items: center;
        justify-content: center;
      }

      /* Hover states for interactive elements */
      .toolbar button:hover,
      .toolbar select:hover,
      .toolbar input[type="range"]:hover {
        background-color: var(--button-hover);
        border-color: var(--accent-color);
      }

      .toolbar button:active {
        transform: scale(0.98); /* Subtle click feedback */
      }

      /* Specific styling for color input */
      .toolbar input[type="color"] {
        padding: 4px; /* Adjust padding for color swatch */
        width: var(--touch-target-min-size);
        height: var(--touch-target-min-size);
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        border: none; /* Remove default browser border */
        cursor: pointer;
        border: 1px solid var(--input-border); /* Re-add custom border */
        border-radius: 8px;
      }

      .toolbar input[type="color"]::-webkit-color-swatch-wrapper {
        padding: 0;
      }

      .toolbar input[type="color"]::-webkit-color-swatch {
        border: none;
        border-radius: 5px; /* Inner swatch rounded */
      }

      /* Range slider specific styles */
      .toolbar input[type="range"] {
        width: 80px; /* Compact width for sliders */
        margin: 0 5px;
        padding: 0; /* Remove padding */
        height: var(--touch-target-min-size); /* Make it touch friendly */
      }

      /* Labels for inputs */
      .toolbar label {
        font-size: 13px;
        margin-right: 5px;
        white-space: nowrap;
      }

      /* Main toolbar positioning */
      .main-toolbar {
        position: sticky;
        top: 0;
        z-index: 1000;
      }

      /* Sub-toolbar positioning and initial state */
      .sub-toolbar {
        display: none; /* Hidden by default */
        /* Removed sticky positioning as it will be at the bottom */
        position: relative; /* Changed to relative for flow */
        z-index: 999;
        justify-content: flex-start; /* Align tools to the left */
        padding: 8px;
        background-color: var(--secondary-bg);
        border-top: 1px solid var(--border-color); /* Now top border */
        border-bottom: none; /* Removed bottom border as it's at the bottom */
        box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1); /* Shadow from top */
        overflow-x: auto; /* Enable horizontal scrolling for many tools */
        -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
        white-space: nowrap; /* Prevent tools from wrapping by default */
        gap: 5px;
        margin-top: auto; /* Push to bottom if flex container */
      }

      .sub-toolbar.active {
        display: flex; /* Show when active */
      }

      /* Canvas container wrapper */
      .canvas-container-wrapper {
        flex-grow: 1; /* Takes up remaining vertical space */
        display: flex;
        justify-content: center;
        align-items: center;
        overflow: hidden; /* Prevent canvas overflow */
        position: relative;
        background-color: var(--primary-bg);
        padding: 10px; /* Padding around the canvas */
      }

      /* Canvas container */
      #canvas-container {
        border: 2px dashed var(--accent-color); /* Dashed border for canvas area */
        background-color: #333; /* Dark background for canvas */
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.5);
        touch-action: none; /* Prevent default browser gestures on canvas */
        max-width: 100%; /* Ensure it doesn't overflow */
        max-height: 100%;
      }

      /* Canvas element itself */
      #c {
        display: block; /* Remove extra space below canvas */
      }

      /* General Modal Styling (for PNG Library etc.) */
      .modal {
        display: none; /* Hidden by default */
        position: fixed;
        z-index: 2000; /* High z-index to overlay everything */
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        background-color: var(--modal-bg);
        justify-content: center;
        align-items: center;
      }

      .modal-content {
        background-color: var(--secondary-bg);
        margin: auto;
        padding: 20px;
        border: 1px solid var(--border-color);
        width: 90%;
        max-width: 800px; /* Max width for readability */
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        display: flex;
        flex-direction: column;
        max-height: 90vh; /* Limit height to prevent overflow on small screens */
        overflow-y: auto; /* Scrollable content inside modal */
      }

      .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 10px;
        border-bottom: 1px solid var(--border-color);
        margin-bottom: 15px;
      }

      .modal-header h2 {
        margin: 0;
        color: var(--accent-color);
      }

      .close-button {
        color: var(--text-color);
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
        background: none;
        border: none;
        padding: 0;
        min-width: var(--touch-target-min-size); /* Touch friendly */
        min-height: var(--touch-target-min-size);
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px; /* Rounded corners */
      }

      .close-button:hover,
      .close-button:focus {
        color: #ccc;
        background-color: var(--button-hover);
      }

      /* PNG Library specific styles */
      .png-grid {
        display: grid;
        grid-template-columns: repeat(
          auto-fill,
          minmax(100px, 1fr)
        ); /* Responsive grid */
        gap: 15px;
        padding: 10px;
        overflow-y: auto;
        flex-grow: 1;
      }

      .png-item {
        background-color: var(--input-bg);
        border: 1px solid var(--input-border);
        border-radius: 8px; /* Rounded corners */
        padding: 5px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: grab; /* Indicate draggable */
        transition: transform 0.2s ease, box-shadow 0.2s ease;
        min-height: 100px; /* Ensure minimum size */
      }

      .png-item:hover {
        transform: translateY(-3px); /* Lift effect on hover */
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
      }

      .png-item img {
        max-width: 90%;
        max-height: 90%;
        object-fit: contain;
        display: block;
      }

      /* Cropper.js Display Area as an Overlay */
      #cropperDisplayArea {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        /* Removed background color here to make it transparent, showing canvas underneath */
        background-color: rgba(
          0,
          0,
          0,
          0.3
        ); /* Semi-transparent overlay to dim background slightly */
        display: none; /* Hidden by default */
        z-index: 1001; /* Above canvas */
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden; /* Important for cropper to behave */
      }

      #cropperImageElement {
        display: block;
        /* No max-width/height here as it will be positioned directly */
        margin: 0; /* Remove auto margin */
        object-fit: fill; /* Fill the cropper area */
        /* Crucial: Set position absolute and use JS to match Fabric.js image position/size */
        position: absolute;
        top: 0;
        left: 0;
        transform-origin: 0 0; /* Important for correct scaling/rotation */
      }

      /* Styles for Cropper controls within sub-toolbar */
      .sub-toolbar .aspect-ratio-container {
        margin-top: 0; /* Remove top margin from modal styling */
        padding-top: 0;
        border-top: none;
        display: flex;
        flex-direction: column; /* Stack portrait/landscape ratio rows */
        gap: 5px; /* Space between ratio rows */
      }
      .sub-toolbar .ratio-label {
        font-weight: bold;
        margin-bottom: 0;
        display: inline-block; /* Keep label inline with buttons */
        margin-right: 5px;
      }
      .sub-toolbar .ratio-row {
        display: flex;
        flex-wrap: wrap; /* Allow buttons to wrap */
        gap: 5px;
        margin-bottom: 5px; /* Space between rows */
      }
      .sub-toolbar .ratio-button {
        padding: 6px 10px;
        font-size: 13px;
        min-width: unset; /* Allow buttons to size naturally */
        min-height: 38px; /* Smaller touch target for sub-toolbar */
      }
      .sub-toolbar .ratio-button.active {
        background-color: var(--accent-color);
        border-color: var(--accent-color);
        color: white;
        box-shadow: 0 0 5px rgba(97, 218, 251, 0.5);
      }
      .sub-toolbar button#apply-cropper-button,
      .sub-toolbar button#cancel-cropper-button {
        padding: 5px;
        font-size: 12px;
        margin-top: 5px; /* Space above these action buttons */
      }

      /* Responsive adjustments */
      @media (max-width: 768px) {
        .toolbar {
          justify-content: flex-start; /* Align left for more space */
          padding: 5px;
          gap: 5px;
        }

        .toolbar button,
        .toolbar select,
        .toolbar input {
          font-size: 12px;
          padding: 6px 10px;
          min-width: unset; /* Allow natural width */
          min-height: 40px; /* Slightly smaller for mobile */
        }

        .sub-toolbar {
          top: auto; /* Let it flow if main toolbar wraps */
          position: static; /* No sticky on small screens if it wraps */
          border-top: none; /* No top border as it might be directly below canvas */
          flex-wrap: wrap; /* Allow sub-toolbar buttons to wrap */
          justify-content: flex-start; /* Align to start for more tools */
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); /* Shadow for bottom toolbar on mobile */
        }
        .canvas-container-wrapper {
          flex-grow: 1;
          height: auto;
          min-height: 200px;
          padding: 5px;
        }
        .modal-content {
          width: 75%;
          padding: 5px;
        }
        .png-grid {
          grid-template-columns: repeat(
            auto-fill,
            minmax(80px, 1fr)
          ); /* Adjust grid for smaller screens */
          gap: 10px;
        }
        .sub-toolbar .ratio-button {
          flex: 1 1 45%; /* Make ratio buttons take half width */
          text-align: center;
          min-width: unset; /* Remove min-width to allow flexible sizing */
          min-height: 30px;
        }
        .sub-toolbar button#apply-cropper-button,
        .sub-toolbar button#cancel-cropper-button {
          padding: 5px;
          font-size: 12px;
        }
        .sub-toolbar .aspect-ratio-container {
          flex-direction: row; /* Keep ratios in a row on smaller screens too */
          flex-wrap: wrap;
        }
        .sub-toolbar .ratio-row {
          flex-basis: 100%; /* Each row takes full width */
          margin-bottom: 5px;
        }
      }

      .drawing-controls {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
        align-items: center;
      }

      .drawing-controls label {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 12px;
      }

      .drawing-controls .info {
        font-size: 11px;
      }

      .drawing-controls input[type="range"] {
        width: 80px;
      }

      .drawing-controls select {
        padding: 5px;
        border-radius: 4px;
        border: 1px solid #ced4da;
        min-width: 100px;
      }

      @media (max-width: 480px) {
        .toolbar {
          min-height: unset;
        }
        .toolbar button,
        .toolbar select,
        .toolbar input {
          min-height: 38px; /* Even smaller for very small screens */
          font-size: 11px;
          padding: 5px 8px;
        }
        .toolbar input[type="color"],
        .toolbar input[type="range"] {
          min-width: 38px;
          min-height: 38px;
          width: 38px; /* Fixed size */
        }
        .toolbar label {
          font-size: 11px;
        }
      }

      /* Basic SVG icons for buttons */
      .icon {
        display: inline-block;
        width: 18px; /* Slightly larger icons */
        height: 18px;
        vertical-align: middle;
        fill: var(--text-color);
        margin-right: 5px; /* Space between icon and text */
      }
      /* No margin-right if button contains only an icon */
      button:not([title]) .icon {
        margin-right: 0;
      }

      /* Visually hide file input */
      #uploadImageInput {
        display: none;
      }
    </style>
  </head>
  <body>
    <div class="toolbar main-toolbar" id="main-toolbar">
      <button id="startDrawing">
        <i class="fas fa-paint-brush"></i> Draw
      </button>
      <button data-category="Draw" title="Drawing Tools">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          height="24px"
          viewBox="0 -960 960 960"
          width="24px"
          fill="#d3a77b"
        >
          <path
            d="M480-80q-82 0-155-31.5t-127.5-86Q143-252 111.5-325T80-480q0-83 32.5-156t88-127Q256-817 330-848.5T488-880q80 0 151 27.5t124.5 76q53.5 48.5 85 115T880-518q0 115-70 176.5T640-280h-74q-9 0-12.5 5t-3.5 11q0 12 15 34.5t15 51.5q0 50-27.5 74T480-80Zm0-400Zm-220 40q26 0 43-17t17-43q0-26-17-43t-43-17q-26 0-43 17t-17 43q0 26 17 43t43 17Zm120-160q26 0 43-17t17-43q0-26-17-43t-43-17q-26 0-43 17t-17 43q0 26 17 43t43 17Zm200 0q26 0 43-17t17-43q0-26-17-43t-43-17q-26 0-43 17t-17 43q0 26 17 43t43 17Zm120 160q26 0 43-17t17-43q0-26-17-43t-43-17q-26 0-43 17t-17 43q0 26 17 43t43 17ZM480-160q9 0 14.5-5t5.5-13q0-14-15-33t-15-57q0-42 29-67t71-25h70q66 0 113-38.5T800-518q0-121-92.5-201.5T488-800q-136 0-232 93t-96 227q0 133 93.5 226.5T480-160Z"
          />
        </svg>
        <!--Draw-->
      </button>
      <button data-category="Shapes" title="Add Shapes">
        <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#d3a77b"><path d="M600-360ZM320-242q10 1 19.5 1.5t20.5.5q11 0 20.5-.5T400-242v82h400v-400h-82q1-10 1.5-19.5t.5-20.5q0-11-.5-20.5T718-640h82q33 0 56.5 23.5T880-560v400q0 33-23.5 56.5T800-80H400q-33 0-56.5-23.5T320-160v-82Zm40-78q-117 0-198.5-81.5T80-600q0-117 81.5-198.5T360-880q117 0 198.5 81.5T640-600q0 117-81.5 198.5T360-320Zm0-80q83 0 141.5-58.5T560-600q0-83-58.5-141.5T360-800q-83 0-141.5 58.5T160-600q0 83 58.5 141.5T360-400Zm0-200Z"/></svg>
        <!-- Shapes -->
      </button>
      <button data-category="Text" title="Add Text">
        <svg class="icon" viewBox="0 0 24 24">
          <path d="M5 4h14v2H5zm5 5h4v11h-4zm-4 0h2v11H6zm8 0h2v11h-2z" />
        </svg>
        Text
      </button>
      <button data-category="Images" title="Image Tools">
        <svg class="icon" viewBox="0 0 24 24">
          <path
            d="M22 16V4a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2Zm-11-4 2.03 2.71L16 11l4 5H8l3-4ZM2 8v12a2 2 0 0 0 2 2h12v-2H4V8H2Z"
          />
        </svg>
        Images
      </button>
      <button data-category="Crop" title="Crop Image">
        <svg class="icon" viewBox="0 0 24 24">
          <path
            d="M17 15h2V7a2 2 0 0 0-2-2H9v2h7a1 1 0 0 1 1 1v7Zm-9 2H5a2 2 0 0 1-2-2V9a2 2 0 0 1 2-2h2v2H5v6h6v2Zm2-4v2h2v-2h-2Zm4 0v2h2v-2h-2Zm-4-4v2h2v-2h-2Zm4 0v2h2v-2h-2Z"
          />
        </svg>
        Crop
      </button>
      <button data-category="Effects" title="Image Effects & Filters">
        <svg class="icon" viewBox="0 0 24 24">
          <path
            d="M21 16v-2h-2v2h-2v2h2v2h2v-2h2v-2h-2ZM7 3v2H4v2h3v2H4v2h3v2H4v2h3v2H4v2h3a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2Z"
          />
        </svg>
        Effects
      </button>
      <button data-category="Library" title="PNG Library">
        <svg class="icon" viewBox="0 0 24 24">
          <path
            d="M20 2H4c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2ZM4 4h16v16H4V4Zm9 3h-2v4H7v2h4v4h2v-4h4v-2h-4V7Z"
          />
        </svg>
        Library
      </button>
      <button data-category="View" title="View Options">
        <svg class="icon" viewBox="0 0 24 24">
          <path
            d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5ZM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5Zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3Z"
          />
        </svg>
        View
      </button>
      <button data-category="Export" title="Export Image">
        <svg class="icon" viewBox="0 0 24 24">
          <path
            d="M19 12v7H5v-7H3v7a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7h-2Zm-6.47 2.53-2-2-1.41 1.41 3.91 3.91 3.91-3.91-1.41-1.41-2 2V3h-2v11.53Z"
          />
        </svg>
        Export
      </button>
    </div>

    <div class="canvas-container-wrapper">
      <div id="canvas-container">
        <canvas id="c"></canvas>
        <div
          id="emptyCanvasMessage"
          style="
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #ccc;
            font-size: 1.2em;
            text-align: center;
            pointer-events: none;
            user-select: none;
          "
        >
          اللوحة فارغة. <br />
          قم بتحميل صورة للبدء!
        </div>
      </div>
      <!-- Cropper.js Display Area as an Overlay -->
      <div
        id="cropperDisplayArea"
        style="
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          display: none;
          background-color: rgba(0, 0, 0, 0.3);
          z-index: 1001;
          display: flex;
          align-items: center;
          justify-content: center;
        "
      >
        <img
          id="cropperImageElement"
          style="
            position: absolute;
            top: 0;
            left: 0;
            transform-origin: 0 0;
            object-fit: fill;
          "
        />
      </div>
    </div>

    <!-- Sub-toolbar is now at the bottom -->
    <div class="toolbar sub-toolbar" id="sub-toolbar">
      <!-- Sub-toolbar content will be dynamically loaded here -->
    </div>

    <!-- PNG Library Modal -->
    <div id="pngLibraryModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h2>PNG Library</h2>
          <button
            class="close-button"
            id="closePngLibraryModal"
            title="Close Library"
          >
            &times;
          </button>
        </div>
        <div class="png-grid" id="pngGrid">
          <!-- PNGs will be loaded here -->
        </div>
      </div>
    </div>

    <!-- Cropper.js Library -->
    <script src="https://cdn.jsdelivr.net/npm/cropperjs@1.5.12/dist/cropper.min.js"></script>

    <script>
      // --- Main Variables ---
      const canvas = new fabric.Canvas("c", {
        width: 800,
        height: 600,
        backgroundColor: "#ffffff",
        selection: true,
        preserveObjectStacking: true,
      });
      
      // Drawing state variables
      let isDrawing = false;
      let isCropping = false;
      let currentImage = null;
      let originalImageData = null;
      let cropRect = null;

      // --- Global State & Utilities ---
      let undoHistory = []; // Stack for undo actions
      let redoHistory = []; // Stack for redo actions
      let currentCategory = "Draw"; // To keep track of active sub-toolbar

      // Cropper.js related variables
      let cropper = null;
      let currentImageForCropper = null; // The Fabric.js object currently being cropped

      // Define initial canvas dimensions
      const INITIAL_CANVAS_WIDTH = 800;
      const INITIAL_CANVAS_HEIGHT = 600;

      // Get accent color from CSS variables once
      const ACCENT_COLOR_CSS = getComputedStyle(document.documentElement)
        .getPropertyValue("--accent-color")
        .trim();

      // Set initial canvas dimensions
      canvas.setWidth(INITIAL_CANVAS_WIDTH);
      canvas.setHeight(INITIAL_CANVAS_HEIGHT);

      // Get empty canvas message element
      const emptyCanvasMessage = document.getElementById("emptyCanvasMessage");
      // Get cropper display area and image element
      const cropperDisplayArea = document.getElementById("cropperDisplayArea");
      const cropperImageElement = document.getElementById(
        "cropperImageElement"
      );

      /**
       * Hides the empty canvas message when an object is added.
       */
      function hideEmptyCanvasMessage() {
        if (emptyCanvasMessage) {
          emptyCanvasMessage.style.display = "none";
        }
      }

      /**
       * Shows the empty canvas message when all objects are removed.
       */
      function showEmptyCanvasMessage() {
        if (emptyCanvasMessage && canvas.getObjects().length === 0) {
          emptyCanvasMessage.style.display = "block";
        }
      }

      /**
       * Saves the current state of the canvas to the undo history.
       * Clears the redo history on a new action.
       */
      function saveCanvasState() {
        const json = JSON.stringify(canvas.toJSON());
        undoHistory.push(json);
        redoHistory = []; // Clear redo history on new action
        console.log("Canvas state saved. History length:", undoHistory.length);
        // Hide message if canvas is no longer empty
        if (canvas.getObjects().length > 0) {
          hideEmptyCanvasMessage();
        }
      }

      /**
       * Undoes the last action by loading the previous canvas state from history.
       */
      function undo() {
        if (undoHistory.length > 1) {
          // Need at least two states to undo (current + previous)
          const lastState = undoHistory.pop(); // Remove current state from undo
          redoHistory.push(lastState); // Add current state to redo
          canvas.clear();
          canvas.loadFromJSON(undoHistory[undoHistory.length - 1], () => {
            canvas.renderAll();
            console.log("Undo performed. History length:", undoHistory.length);
            showEmptyCanvasMessage(); // Check if canvas became empty after undo
          });
        } else if (undoHistory.length === 1) {
          // If only the initial state is left
          const initialState = undoHistory.pop();
          redoHistory.push(initialState);
          canvas.clear(); // Clear canvas completely
          canvas.renderAll();
          console.log("Canvas cleared (initial state).");
          showEmptyCanvasMessage(); // Show message because canvas is empty
        }
      }

      /**
       * Redoes the last undone action by loading the state from redo history.
       */
      function redo() {
        if (redoHistory.length > 0) {
          const nextState = redoHistory.pop();
          undoHistory.push(nextState);
          canvas.clear();
          canvas.loadFromJSON(nextState, () => {
            canvas.renderAll();
            console.log("Redo performed. History length:", undoHistory.length);
            hideEmptyCanvasMessage(); // Hide message if objects are back
          });
        }
      }

      // Event listeners for saving canvas state on modifications
      canvas.on("object:added", saveCanvasState);
      canvas.on("object:modified", saveCanvasState);
      canvas.on("object:removed", () => {
        saveCanvasState(); // Save state first
        showEmptyCanvasMessage(); // Then check if message needs to be shown
      });
      canvas.on("text:changed", saveCanvasState); // For text changes
      canvas.on("selection:cleared", saveCanvasState); // For ungroup, delete

      // Initial save of the empty canvas state
      saveCanvasState();
      showEmptyCanvasMessage(); // Show on initial load since canvas is empty

      // --- UI Elements & Event Listeners Setup ---
      const mainToolbar = document.getElementById("main-toolbar");
      const subToolbar = document.getElementById("sub-toolbar");
      const canvasContainer = document.getElementById("canvas-container");
      const pngLibraryModal = document.getElementById("pngLibraryModal");
      const closePngLibraryModalBtn = document.getElementById(
        "closePngLibraryModal"
      );
      const pngGrid = document.getElementById("pngGrid");

      // Main toolbar category selection
      mainToolbar.addEventListener("click", (e) => {
        const button = e.target.closest("button");
        if (button && button.dataset.category) {
          const category = button.dataset.category;
          displaySubToolbar(category);

          // Deactivate drawing mode if active, unless it's the Draw category
          if (canvas.isDrawingMode && category !== "Draw") {
            canvas.isDrawingMode = false;
            // Reset default brush properties for next time drawing mode is entered
            canvas.freeDrawingBrush.color = "#000000";
            canvas.freeDrawingBrush.width = 5;
            canvas.freeDrawingBrush.opacity = 1;
          }

          // Deactivate cropper if switching away from Crop category
          if (
            category !== "Crop" &&
            cropperDisplayArea.style.display === "flex"
          ) {
            deactivateCropperOnCanvas();
          }

          canvas.discardActiveObject().renderAll(); // Deselect active objects
        }
      });

      // Close PNG Library Modal
      closePngLibraryModalBtn.addEventListener("click", () => {
        pngLibraryModal.style.display = "none";
      });

      // --- Hammer.js Integration (Step 9) ---
      /**
       * Sets up Hammer.js for multi-touch gestures on the canvas.
       */
      function setupHammerJs() {
        // Prevent default browser touch actions like scrolling/zooming
        canvasContainer.style.touchAction = "none";

        hammerCanvas = new Hammer(canvas.getElement(), {
          // Configure tap recognizer to work alongside others
          recognizers: [
            [Hammer.Pinch, { enable: true }],
            [Hammer.Rotate, { enable: true }],
            [Hammer.Pan, { direction: Hammer.DIRECTION_ALL }],
            [Hammer.Tap, { event: "tap" }],
            [Hammer.Tap, { event: "doubletap", taps: 2 }],
            [Hammer.Press, { event: "press", time: 500 }],
          ],
        });

        // Allow pinch and rotate to be recognized simultaneously
        hammerCanvas.get("pinch").recognizeWith(hammerCanvas.get("rotate"));

        let lastPanX = 0;
        let lastPanY = 0;
        let lastScale = 1;
        let lastRotation = 0;

        // Pan: Move the canvas viewport
        hammerCanvas.on("panstart", (e) => {
          lastPanX = e.center.x;
          lastPanY = e.center.y;
          canvas.isDragging = true; // Custom property to prevent object dragging
        });

        hammerCanvas.on("panmove", (e) => {
          if (!canvas.isDragging) return; // Only pan if started with canvas dragging

          const deltaX = e.center.x - lastPanX;
          const deltaY = e.center.y - lastPanY;
          const viewport = canvas.viewportTransform;

          // Update viewport position
          viewport[4] += deltaX;
          viewport[5] += deltaY;
          canvas.requestRenderAll();

          lastPanX = e.center.x;
          lastPanY = e.center.y;
        });

        hammerCanvas.on("panend", () => {
          canvas.isDragging = false;
        });

        // Pinch: Zoom in/out
        hammerCanvas.on("pinchstart", () => {
          lastScale = canvas.getZoom();
        });

        hammerCanvas.on("pinchmove", (e) => {
          const newZoom = lastScale * e.scale;
          // Limit zoom to reasonable bounds
          const minZoom = 0.1;
          const maxZoom = 5;
          canvas.zoomToPoint(
            new fabric.Point(e.center.x, e.center.y),
            Math.max(minZoom, Math.min(newZoom, maxZoom))
          );
        });

        // Rotate: Rotate selected objects
        hammerCanvas.on("rotatestart", (e) => {
          lastRotation = e.rotation;
        });

        hammerCanvas.on("rotatemove", (e) => {
          const deltaRotation = e.rotation - lastRotation;
          const activeObject = canvas.getActiveObject();

          if (activeObject) {
            activeObject.rotate(activeObject.angle + deltaRotation);
            canvas.requestRenderAll();
          }
          lastRotation = e.rotation;
        });

        // Tap: Select object or deselect if tapping empty space
        hammerCanvas.on("tap", (e) => {
          // Fabric.js handles object selection/deselection on tap automatically
          // unless custom logic is needed. We'll ensure it clears active object
          // if background is tapped.
          const pointer = canvas.getPointer(e.srcEvent);
          const target = canvas.findTarget(e.srcEvent); // findTarget without second arg returns first object hit
          if (target) {
            canvas.setActiveObject(target);
          } else {
            canvas.discardActiveObject();
          }
          canvas.renderAll();
        });

        // Double Tap: Zoom to fit or trigger crop if in cropping mode
        hammerCanvas.on("doubletap", (e) => {
          // Double tap on canvas will reset zoom, unless object is selected
          const activeObject = canvas.getActiveObject();
          if (activeObject) {
            // Double tap on an object could potentially be used for something else,
            // but for now, we'll let Fabric.js handle it (e.g., editing text).
          } else {
            const currentZoom = canvas.getZoom();
            if (currentZoom < 1.5) {
              // If zoomed out, zoom in slightly
              canvas.zoomToPoint(new fabric.Point(e.center.x, e.center.y), 2);
            } else {
              // If zoomed in, reset zoom
              canvas.setViewportTransform([1, 0, 0, 1, 0, 0]); // Reset pan
              canvas.setZoom(1); // Reset zoom
            }
          }
          canvas.renderAll();
        });

        // Press: Long press to select (useful if tap is too sensitive)
        hammerCanvas.on("press", (e) => {
          const target = canvas.findTarget(e.srcEvent);
          if (target) {
            canvas.setActiveObject(target);
            canvas.renderAll();
          }
        });

        // Prevent Fabric.js's default drag behavior for the canvas itself when Hammer.js is panning
        canvas.on("mouse:down", function (options) {
          if (options.e.touches && options.e.touches.length > 1) {
            // Check for multi-touch
            this.isDragging = false; // Prevent Fabric from starting drag for objects
          } else if (options.target) {
            this.isDragging = false; // Allow object dragging
          } else {
            this.isDragging = true; // Allow canvas dragging for single touch on empty space
            this.selection = false; // Disable selection box drawing for canvas pan
          }
        });

        canvas.on("mouse:up", function () {
          this.isDragging = false;
          this.selection = true; // Re-enable selection box
        });

        canvas.on("mouse:move", function (options) {
          if (this.isDragging && !options.target) {
            // If canvas dragging and no object targeted
            const mEvent = options.e;
            const deltaX =
              mEvent.movementX ||
              mEvent.mozMovementX ||
              mEvent.webkitMovementX ||
              0;
            const deltaY =
              mEvent.movementY ||
              mEvent.mozMovementY ||
              mEvent.webkitMovementY ||
              0;
            const viewport = this.viewportTransform;
            viewport[4] += deltaX;
            viewport[5] += deltaY;
            this.requestRenderAll();
          }
        });
      }
      setupHammerJs(); // Call Hammer.js setup on load

      // --- Sub-Toolbar Dynamic Content ---
      // Templates for sub-toolbar content based on main category
      const subToolbarTemplates = {
        Draw: `
            <div class="drawing-controls">
                <label for="drawing-mode-selector">
                    الوضع:
                    <select id="drawing-mode-selector">
                        <option value="PencilBrush">Pencil</option>
                        <option value="CircleBrush">Circle</option>
                        <option value="SprayBrush">Spray</option>
                        <option value="PatternBrush">Pattern</option>
                        <option value="hline">Horizontal Line</option>
                        <option value="vline">Vertical Line</option>
                        <option value="square">Square</option>
                        <option value="diamond">Diamond</option>
                        <option value="texture">Texture</option>
                    </select>
                </label>
                <label for="drawing-line-width">
                    سمك الخط:
                    <span class="info" id="line-width-info">5</span>
                    <input type="range" min="1" max="50" id="drawing-line-width" value="5">
                </label>
                <label for="drawing-color">
                    لون الخط:
                    <input type="color" id="drawing-color" value="#000000">
                </label>
                <label for="drawing-shadow-color">
                    لون الظل:
                    <input type="color" id="drawing-shadow-color" value="#000000">
                </label>
                <label for="drawing-shadow-width">
                    عرض الظل:
                    <span class="info" id="shadow-width-info">0</span>
                    <input type="range" min="0" max="20" id="drawing-shadow-width" value="0">
                </label>
                <label for="drawing-shadow-offset">
                    إزاحة الظل:
                    <span class="info" id="shadow-offset-info">0</span>
                    <input type="range" min="0" max="20" id="drawing-shadow-offset" value="0">
                </label>
                <button id="clear-drawings">مسح الرسومات</button>
            </div>
            `,
        Shapes: `
                <button id="add-rect" title="Add Rectangle"><svg class="icon" viewBox="0 0 24 24"><path d="M20 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2Zm0 14H4V6h16v12Z"/></svg>Rect</button>
                <button id="add-circle" title="Add Circle"><svg class="icon" viewBox="0 0 24 24"><path d="M12 2c5.52 0 10 4.48 10 10s-4.48 10-10 10S2 17.52 2 12 6.48 2 12 2Zm0 2a8 8 0 1 0 0 16 8 8 0 0 0 0-16Z"/><path d="M7 10h10v4H7z"/></svg>Circle</button>
                <button id="add-triangle" title="Add Triangle"><svg class="icon" viewBox="0 0 24 24"><path d="M12 2 1 21h22L12 2Zm0 3.79L18.39 19H5.61L12 3.79Z"/></svg>Triangle</button>
                <button id="add-line" title="Add Line"><svg class="icon" viewBox="0 0 24 24"><path d="M20 11H4v2h16Z"/></svg>Line</button>
                <button id="add-polygon" title="Add Polygon"><svg class="icon" viewBox="0 0 24 24"><path d="M19 12.91a7 7 0 0 1-1.07 3.5c-1.19 2.37-3.72 3.59-6.4 3.59-2.6 0-5.1-.98-6.28-3.32a7 7 0 0 1-1.05-3.68c.03-3.08 1.4-5.91 3.71-7.85C8.88 3.54 11.08 2.5 13.2 2.5c2.4 0 4.6 1.13 6.3 3.39 2.05 2.68 2.7 5.75 1.5 8.02l-.09-.01Zm-7-8.91a5 5 0 1 0 0 10 5 5 0 0 0 0-10Z"/></svg>Polygon</button>
                <button id="add-polyline" title="Add Polyline"><svg class="icon" viewBox="0 0 24 24"><path d="M2 19V5h20v14ZM4 7v10h16V7H4Z"/></svg>Polyline</button>
                <button id="add-path" title="Add Custom Path"><svg class="icon" viewBox="0 0 24 24"><path d="M20.65 6.05c-.65-.66-1.57-1.05-2.58-1.05-.88 0-1.7.35-2.3.9L10 11.5l1.41 1.41 6.34-6.35c.19-.19.45-.3.72-.3.27 0 .52.11.71.3.39.39.39 1.02 0 1.41L9.01 19 2 22l3-7 11.59-11.59c.78-.78 2.05-.78 2.83 0l2.12 2.12c.79.78.79 2.05.01 2.83L8.85 20.31a.996.996 0 0 1-.707.293c-.27 0-.52-.11-.71-.3L2 14.5l5.5-5.5L18.59 1.76c.78-.79 2.05-.79 2.83 0l-2.77-2.77Z"/></svg>Path</button>
            `,
        Text: `
                <label for="default-text-input">Text:</label><input type="text" id="default-text-input" value="Double click to edit" title="Default text for new objects">
                <button id="add-text" title="Add Textbox"><svg class="icon" viewBox="0 0 24 24"><path d="M5 4h14v2H5zm5 5h4v11h-4zm-4 0h2v11H6zm8 0h2v11h-2z"/></svg>Add Text</button>
                <label for="font-family">Font:</label>
                <select id="font-family" title="Font Family">
                    <option value="Arial">Arial</option>
                    <option value="Verdana">Verdana</option>
                    <option value="Times New Roman">Times New Roman</option>
                    <option value="Courier New">Courier New</option>
                    <option value="Georgia">Georgia</option>
                    <option value="Impact">Impact</option>
                    <option value="Tahoma">Tahoma</option>
                    <!-- Arabic Fonts. Ensure these are loaded if not system fonts -->
                    <option value="'Amiri', serif" style="font-family:'Amiri', serif;">Amiri (Arabic)</option>
                    <option value=" 'Cairo', sans-serif" style="font-family: 'Cairo', sans-serif;">Cairo (Arabic)</option>
                    <option value=" 'Lateef', cursive" style="font-family: 'Lateef', cursive;">Lateef (Arabic)</option>
                </select>
                <label for="font-size">Size:</label><input type="range" id="font-size" min="10" max="200" value="40" title="Font Size">
                <label for="text-color">Color:</label><input type="color" id="text-color" value="#000000" title="Text Color">
                <label for="text-bg-color">BG:</label><input type="color" id="text-bg-color" value="#ffffff" title="Text Background Color">
                <label for="text-stroke-color">Stroke Color:</label><input type="color" id="text-stroke-color" value="#000000" title="Text Stroke Color">
                <label for="text-stroke-width">Stroke Width:</label><input type="range" id="text-stroke-width" min="0" max="10" step="0.1" value="0" title="Text Stroke Width">
                <label for="text-shadow-color">Shadow Color:</label><input type="color" id="text-shadow-color" value="#000000" title="Text Shadow Color">
                <label for="text-shadow-blur">Blur:</label><input type="range" id="text-shadow-blur" min="0" max="20" value="0" title="Text Shadow Blur">
                <label for="text-shadow-offsetX">OffsetX:</label><input type="range" id="text-shadow-offsetX" min="-20" max="20" value="0" title="Text Shadow Offset X">
                <label for="text-shadow-offsetY">OffsetY:</label><input type="range" id="text-shadow-offsetY" min="-20" max="20" value="0" title="Text Shadow Offset Y">
                <button id="text-align-left" title="Align Left"><svg class="icon" viewBox="0 0 24 24"><path d="M3 21h18v-2H3v2Zm0-4h10v-2H3v2Zm0-4h18v-2H3v2Zm0-4h10V7H3v2Zm0-4h18V3H3v2Z"/></svg></button>
                <button id="text-align-center" title="Align Center"><svg class="icon" viewBox="0 0 24 24"><path d="M7 21h10v-2H7v2Zm-4-4h18v-2H3v2Zm4-4h10v-2H7v2Zm-4-4h18V7H3v2Zm4-4h18V3H7v2Z"/></svg></button>
                <button id="text-align-right" title="Align Right"><svg class="icon" viewBox="0 0 24 24"><path d="M3 21h18v-2H3v2Zm8-4h10v-2H11v2Zm-8-4h18v-2H3v2Zm8-4h10V7H11v2Zm-8-4h18V3H3v2Z"/></svg></button>
                <button id="text-align-justify" title="Justify"><svg class="icon" viewBox="0 0 24 24"><path d="M3 21h18v-2H3v2Zm0-4h18v-2H3v2Zm0-4h18v-2H3v2Zm0-4h18V7H3v2Zm0-4h18V3H3v2Z"/></svg></button>
            `,
        Images: `
                <input type="file" id="uploadImageInput" multiple accept="image/*">
                <button id="upload-image-button" title="Upload Image(s)"><svg class="icon" viewBox="0 0 24 24"><path d="M11 16h2v-3h3v-2h-3V8h-2v3h-3v2h3Zm-7-7v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2Zm2 0h12v11H6V9Zm8.5-6H9.5L8 4.5V6h8V4.5l-1.5-1.5Z"/></svg>Upload</button>
                <label for="image-border-color">Border Color:</label><input type="color" id="image-border-color" value="#000000" title="Image Border Color">
                <label for="image-border-width">Border Width:</label><input type="range" id="image-border-width" min="0" max="20" value="0" title="Image Border Width">
                <label for="image-shadow-color">Shadow Color:</label><input type="color" id="image-shadow-color" value="#000000" title="Image Shadow Color">
                <label for="image-shadow-blur">Blur:</label><input type="range" id="image-shadow-blur" min="0" max="50" value="0" title="Image Shadow Blur">
                <label for="image-shadow-offsetX">OffsetX:</label><input type="range" id="image-shadow-offsetX" min="-50" max="50" value="0" title="Image Shadow Offset X">
                <label for="image-shadow-offsetY">OffsetY:</label><input type="range" id="image-shadow-offsetY" min="-50" max="50" value="0" title="Image Shadow Offset Y">
                <label for="image-shadow-opacity">Opacity:</label><input type="range" id="image-shadow-opacity" min="0" max="1" step="0.05" value="1" title="Image Shadow Opacity">
            `,
        Crop: `
                <button id="activate-cropper-button" title="Activate Cropper Tool"><svg class="icon" viewBox="0 0 24 24"><path d="M17 15h2V7a2 2 0 0 0-2-2H9v2h7a1 1 0 0 1 1 1v7Zm-9 2H5a2 2 0 0 1-2-2V9a2 2 0 0 1 2-2h2v2H5v6h6v2Zm2-4v2h2v-2h-2Zm4 0v2h2v-2h-2Zm-4-4v2h2v-2h-2Zm4 0v2h2v-2h-2Z"/></svg>تفعيل القص</button>
                <div class="aspect-ratio-container">
                    <span class="ratio-label">نسب أبعاد طولية:</span>
                    <div class="ratio-row" id="portraitRatiosSub"></div>
                    <span class="ratio-label">نسب أبعاد عرضية:</span>
                    <div class="ratio-row" id="landscapeRatiosSub"></div>
                </div>
                <button id="apply-cropper-button" title="Apply Crop">تطبيق</button>
                <button id="cancel-cropper-button" title="Cancel Crop">إلغاء</button>
            `,
        Effects: `
                <label for="filter-brightness-val">Brightness:</label><input type="range" id="filter-brightness-val" min="-1" max="1" step="0.05" value="0" title="Brightness Filter">
                <label for="filter-contrast-val">Contrast:</label><input type="range" id="filter-contrast-val" min="-1" max="1" step="0.05" value="0" title="Contrast Filter">
                <label for="filter-blur-val">Blur:</label><input type="range" id="filter-blur-val" min="0" max="1" step="0.01" value="0" title="Blur Filter">
                <label for="filter-saturation-val">Saturation:</label><input type="range" id="filter-saturation-val" min="-1" max="1" step="0.05" value="0" title="Saturation Filter">
                <button id="filter-grayscale" title="Toggle Grayscale">Grayscale</button>
                <button id="filter-blackwhite" title="Toggle Black & White">B&W</button>
                <button id="filter-sharpen" title="Toggle Sharpen">Sharpen</button>
                <button id="filter-invert" title="Toggle Invert">Invert</button>
                <button id="filter-reset" title="Reset All Filters">Reset Filters</button>
            `,
        Library: `
                <button id="open-png-library" title="Open PNG Library">Open Library</button>
                <span style="margin-left: 10px; font-size: 0.9em; opacity: 0.8;">Drag & Drop or Click to Add</span>
            `,
        View: `
                <button id="zoom-in" title="Zoom In"><svg class="icon" viewBox="0 0 24 24"><path d="M12 4a8 8 0 1 0 0 16 8 8 0 0 0 0-16Zm0 2a6 6 0 1 1 0 12 6 6 0 0 1 0-12Zm-3 5h6v2H9v-2Z"/></svg>Zoom In</button>
                <button id="zoom-out" title="Zoom Out"><svg class="icon" viewBox="0 0 24 24"><path d="M12 4a8 8 0 1 0 0 16 8 8 0 0 0 0-16Zm0 2a6 6 0 1 1 0 12 6 6 0 0 1 0-12Zm-3 5h6v2H9v-2Z"/></svg>Zoom Out</button>
                <label for="canvas-width">W:</label><input type="number" id="canvas-width" value="${INITIAL_CANVAS_WIDTH}" min="100" title="Canvas Width">
                <label for="canvas-height">H:</label><input type="number" id="canvas-height" value="${INITIAL_CANVAS_HEIGHT}" min="100" title="Canvas Height">
                <button id="set-canvas-size" title="Set Canvas Size">Apply Size</button>
                <button id="toggle-grid-snap" title="Toggle Grid Snapping">Grid Snap</button>
                <button id="bring-to-front" title="Bring to Front">To Front</button>
                <button id="send-to-back" title="Send to Back">To Back</button>
                <button id="bring-forward" title="Bring Forward">Forward</button>
                <button id="send-backward" title="Send Backward">Backward</button>
                <button id="align-left" title="Align Left">Align L</button>
                <button id="align-center" title="Align Center">Align C</button>
                <button id="align-right" title="Align Right">Align R</button>
                <button id="align-top" title="Align Top">Align T</button>
                <button id="align-middle" title="Align Middle">Align M</button>
                <button id="align-bottom" title="Align Bottom">Align B</button>
                <button id="duplicate-object" title="Duplicate Object">Duplicate</button>
                <button id="delete-object" title="Delete Object">Delete</button>
                <button id="group-objects" title="Group Selected Objects">Group</button>
                <button id="ungroup-objects" title="Ungroup Selected Objects">Ungroup</button>
                <button id="lock-object" title="Lock Object"><svg class="icon" viewBox="0 0 24 24"><path d="M12 17c1.1 0 2-.9 2-2V9h-4v6c0 1.1.9 2 2 2Zm6-9h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2Zm0 12H6V10h12v10Z"/></svg>Lock</button>
                <button id="unlock-object" title="Unlock Object"><svg class="icon" viewBox="0 0 24 24"><path d="M18 10h-1V7c0-2.76-2.24-5-5-5S7 4.24 7 7h2c0-1.66 1.34-3 3-3s3 1.34 3 3v3H6c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2v-8c0-1.1-.9-2-2-2Zm0 10H6V12h12v8Z"/></svg>Unlock</button>
                <button id="flip-horizontal" title="Flip Horizontal">Flip H</button>
                <button id="flip-vertical" title="Flip Vertical">Flip V</button>
                <button id="undo-action" title="Undo"><svg class="icon" viewBox="0 0 24 24"><path d="M12.5 8c-2.65 0-5.05.99-6.9 2.6L2 7v9h9l-3.61-3.61c1.39-1.16 3.16-1.89 5.11-1.89 3.03 0 5.68 1.74 6.95 4.3l2.39-1.39C20.6 10.15 16.85 8 12.5 8Z"/></svg>Undo</button>
                <button id="redo-action" title="Redo"><svg class="icon" viewBox="0 0 24 24"><path d="M18.4 10.15a8 8 0 0 0-14.88 4.3l-2.39-1.39A10 10 0 0 1 12.5 8c2.65 0 5.05.99 6.9 2.6L22 7v9h-9l3.61-3.61c-1.39-1.16-3.16-1.89-5.11-1.89-3.03 0-5.68 1.74-6.95 4.3Z"/></svg>Redo</button>
            `,
        Export: `
                <button id="export-png" title="Export as PNG">Export PNG</button>
                <button id="export-jpeg" title="Export as JPEG">Export JPEG</button>
                <button id="export-svg" title="Export as SVG">Export SVG</button>
                <button id="export-json" title="Export as JSON">Export JSON</button>
            `,
      };

      /**
       * Dynamically displays the sub-toolbar content based on the selected category.
       * @param {string} category - The category name (e.g., 'Draw', 'Shapes').
       */
      function displaySubToolbar(category) {
        subToolbar.innerHTML = subToolbarTemplates[category] || "";
        subToolbar.classList.add("active");
        currentCategory = category;
        attachSubToolbarListeners(category);
      }

      // --- Core Fabric.js Functions ---
      /**
       * Adds a new shape to the canvas.
       * @param {string} type - The type of shape to add (e.g., 'rect', 'circle', 'triangle', 'line', 'polygon', 'polyline', 'path').
       */
      function addShape(type) {
        let shape;
        const commonProps = {
          left: 50,
          top: 50,
          fill: "rgba(97, 218, 251, 0.7)" /* Semi-transparent accent color */,
          stroke: ACCENT_COLOR_CSS.replace(
            ")",
            ", 1)"
          ) /* Solid accent color */,
          strokeWidth: 2,
          cornerColor: "white",
          borderColor: "white",
          transparentCorners: false,
        };

        switch (type) {
          case "rect":
            shape = new fabric.Rect({
              width: 100,
              height: 100,
              ...commonProps,
            });
            break;
          case "circle":
            shape = new fabric.Circle({
              radius: 50,
              ...commonProps,
            });
            break;
          case "triangle":
            shape = new fabric.Triangle({
              width: 100,
              height: 100,
              ...commonProps,
            });
            break;
          case "line":
            shape = new fabric.Line([50, 50, 200, 50], {
              stroke: ACCENT_COLOR_CSS.replace(")", ", 1)"),
              strokeWidth: 5,
              selectable: true,
              evented: true,
              hasControls: true,
              hasBorders: true,
            });
            break;
          case "polygon":
            // Example: simple pentagon
            shape = new fabric.Polygon(
              [
                { x: 100, y: 0 },
                { x: 200, y: 70 },
                { x: 150, y: 180 },
                { x: 50, y: 180 },
                { x: 0, y: 70 },
              ],
              { ...commonProps, left: 50, top: 50 }
            );
            break;
          case "polyline":
            shape = new fabric.Polyline(
              [
                { x: 0, y: 0 },
                { x: 50, y: 100 },
                { x: 100, y: 50 },
                { x: 150, y: 150 },
              ],
              {
                left: 50,
                top: 50,
                stroke: ACCENT_COLOR_CSS.replace(")", ", 1)"),
                strokeWidth: 3,
                fill: "",
                strokeLineCap: "round",
                strokeLineJoin: "round",
                selectable: true,
                evented: true,
                hasControls: true,
                hasBorders: true,
              }
            );
            break;
          case "path":
            // A simple SVG path example (a heart shape)
            shape = new fabric.Path(
              "M213.1,2.1c-2.1-2.1-5.4-2.1-7.5,0L128,79.6L18.4,2.1c-2.1-2.1-5.4-2.1-7.5,0c-2.1,2.1-2.1,5.4,0,7.5l112,112c2.1,2.1,5.4,2.1,7.5,0l112-112C215.2,7.5,215.2,4.2,213.1,2.1z",
              {
                left: 50,
                top: 50,
                fill: "red",
                stroke: "darkred",
                strokeWidth: 2,
                scaleX: 0.2,
                scaleY: 0.2,
                selectable: true,
                evented: true,
                hasControls: true,
                hasBorders: true,
              }
            );
            break;
        }
        if (shape) {
          canvas.add(shape);
          canvas.setActiveObject(shape);
          canvas.renderAll();
        }
      }

      /**
       * Adds a new Textbox object to the canvas.
       */
      function addText() {
        const defaultTextInput = document.getElementById("default-text-input");
        const initialText = defaultTextInput
          ? defaultTextInput.value
          : "Double click to edit";
        const text = new fabric.Textbox(initialText, {
          left: 50,
          top: 50,
          width: 250, // Default width for textbox
          fontSize: 40,
          fill: "#000000",
          fontFamily: "Arial",
          hasControls: true,
          editable: true, // Enable in-place editing on double click
          splitByGrapheme: true, // Essential for correct cursor movement and selection with complex scripts (e.g., Arabic)
        });
        canvas.add(text);
        canvas.setActiveObject(text);
        canvas.renderAll();
      }

      // --- Object Manipulation ---
      /** Deletes the currently selected object(s). */
      function deleteObject() {
        const activeObject = canvas.getActiveObject();
        if (activeObject) {
          if (activeObject.type === "activeSelection") {
            // If multiple objects are selected
            activeObject.forEachObject((obj) => canvas.remove(obj));
          } else {
            canvas.remove(activeObject);
          }
          canvas.discardActiveObject(); // Clear selection
          canvas.renderAll();
        }
      }

      /** Duplicates the currently selected object. */
      function duplicateObject() {
        const activeObject = canvas.getActiveObject();
        if (activeObject) {
          activeObject.clone((clonedObj) => {
            canvas.discardActiveObject();
            clonedObj.set({
              left: clonedObj.left + 10,
              top: clonedObj.top + 10,
              evented: true,
              selectable: true,
            });
            if (clonedObj.type === "activeSelection") {
              // Ungroup the cloned selection and add individual objects
              clonedObj.canvas = canvas; // Required for ungroupOnCanvas
              clonedObj.ungroupOnCanvas(); // Add objects individually
            } else {
              canvas.add(clonedObj);
            }
            canvas.setActiveObject(clonedObj); // Select the new object
            canvas.renderAll();
          });
        }
      }

      /** Groups selected objects into a single Fabric.Group. */
      function groupObjects() {
        const activeObjects = canvas.getActiveObjects();
        if (activeObjects.length > 1) {
          const group = new fabric.Group(activeObjects, {
            // Calculate overall position for the group
            left: Math.min(...activeObjects.map((o) => o.left)),
            top: Math.min(...activeObjects.map((o) => o.top)),
          });
          activeObjects.forEach((obj) => canvas.remove(obj)); // Remove individual objects
          canvas.add(group);
          canvas.setActiveObject(group);
          canvas.renderAll();
        }
      }

      /** Ungroups a selected Fabric.Group into individual objects. */
      function ungroupObjects() {
        const activeObject = canvas.getActiveObject();
        if (activeObject && activeObject.type === "group") {
          activeObject.ungroupOnCanvas(); // Fabric.js utility to ungroup
          canvas.renderAll();
        }
      }

      /**
       * Locks or unlocks the selected object(s) from manipulation.
       * @param {boolean} lock - True to lock, false to unlock.
       */
      function lockObject(lock) {
        const activeObject = canvas.getActiveObject();
        if (activeObject) {
          const objectsToLock =
            activeObject.type === "activeSelection"
              ? activeObject.getObjects()
              : [activeObject];
          objectsToLock.forEach((obj) => {
            obj.set({
              lockMovementX: lock,
              lockMovementY: lock,
              lockRotation: lock,
              lockScalingX: lock,
              lockScalingY: lock,
              hasControls: !lock, // Hide controls when locked
              hasBorders: !lock, // Hide borders when locked
              selectable: !lock, // Make non-selectable when locked
              evented: !lock, // Disable events when locked
            });
          });
          canvas.renderAll();
          canvas.discardActiveObject(); // Clear selection to reflect new state
        }
      }

      /**
       * Flips the selected object horizontally or vertically.
       * @param {string} axis - 'h' for horizontal, 'v' for vertical.
       */
      function flipObject(axis) {
        const activeObject = canvas.getActiveObject();
        if (activeObject) {
          if (axis === "h") {
            activeObject.set("flipX", !activeObject.flipX);
          } else if (axis === "v") {
            activeObject.set("flipY", !activeObject.flipY);
          }
          canvas.renderAll();
          saveCanvasState();
        }
      }

      /**
       * Changes the Z-index (layer) of the selected object.
       * @param {string} action - 'front', 'back', 'forward', or 'backward'.
       */
      function changeZIndex(action) {
        const activeObject = canvas.getActiveObject();
        if (!activeObject) return;

        if (action === "front") activeObject.bringToFront();
        else if (action === "back") activeObject.sendToBack();
        else if (action === "forward") activeObject.bringForward();
        else if (action === "backward") activeObject.sendBackward();
        canvas.renderAll();
        saveCanvasState();
      }

      /**
       * Aligns multiple selected objects relative to each other.
       * @param {string} alignment - 'left', 'center', 'right', 'top', 'middle', 'bottom'.
       */
      function alignObjects(alignment) {
        const activeObjects = canvas.getActiveObjects();
        if (activeObjects.length < 2) return; // Need at least two objects to align

        // Get bounding box of all selected objects
        const groupBoundingRect = canvas.getActiveObject().getBoundingRect();

        activeObjects.forEach((obj) => {
          if (obj === canvas.getActiveObject()) return; // Skip the active object itself for relative alignment

          if (alignment === "left") {
            obj.set({ left: groupBoundingRect.left });
          } else if (alignment === "center") {
            obj.set({
              left:
                groupBoundingRect.left +
                (groupBoundingRect.width - obj.getScaledWidth()) / 2,
            });
          } else if (alignment === "right") {
            obj.set({
              left:
                groupBoundingRect.left +
                groupBoundingRect.width -
                obj.getScaledWidth(),
            });
          } else if (alignment === "top") {
            obj.set({ top: groupBoundingRect.top });
          } else if (alignment === "middle") {
            obj.set({
              top:
                groupBoundingRect.top +
                (groupBoundingRect.height - obj.getScaledHeight()) / 2,
            });
          } else if (alignment === "bottom") {
            obj.set({
              top:
                groupBoundingRect.top +
                groupBoundingRect.height -
                obj.getScaledHeight(),
            });
          }
          obj.setCoords(); // Update object coordinates after setting new position
        });
        canvas.renderAll();
        saveCanvasState();
      }

      // --- Snapping to Grid ---
      let gridSnapEnabled = false;
      const GRID_SIZE = 20; // Pixels for grid snapping

      /** Toggles grid snapping on/off. */
      function toggleGridSnap() {
        gridSnapEnabled = !gridSnapEnabled;
        if (gridSnapEnabled) {
          canvas.on("object:moving", snapToGrid);
          console.log("Grid snapping ON");
        } else {
          canvas.off("object:moving", snapToGrid);
          console.log("Grid snapping OFF");
        }
      }

      /** Event handler to snap object to grid during movement. */
      function snapToGrid(options) {
        options.target.set({
          left: Math.round(options.target.left / GRID_SIZE) * GRID_SIZE,
          top: Math.round(options.target.top / GRID_SIZE) * GRID_SIZE,
        });
      }

      // --- Drawing Tools ---
      /**
       * Sets up event handlers for drawing controls
       */
      function setupDrawingEventHandlers() {
        const drawingColorEl = document.getElementById('drawing-color');
        const drawingShadowColorEl = document.getElementById('drawing-shadow-color');
        const drawingLineWidthEl = document.getElementById('drawing-line-width');
        const drawingShadowWidth = document.getElementById('drawing-shadow-width');
        const drawingShadowOffset = document.getElementById('drawing-shadow-offset');
        const selectorEl = document.getElementById('drawing-mode-selector');

        drawingColorEl.addEventListener('change', updateBrush);
        drawingShadowColorEl.addEventListener('change', updateBrush);

        drawingLineWidthEl.addEventListener('input', () => {
          document.getElementById('line-width-info').textContent = drawingLineWidthEl.value;
          updateBrush();
        });

        drawingShadowWidth.addEventListener('input', () => {
          document.getElementById('shadow-width-info').textContent = drawingShadowWidth.value;
          updateBrush();
        });

        drawingShadowOffset.addEventListener('input', () => {
          document.getElementById('shadow-offset-info').textContent = drawingShadowOffset.value;
          updateBrush();
        });

        selectorEl.addEventListener('change', handleDrawingModeChange);
      }


      /**
       * Updates the brush with current settings from the UI
       */
      function updateBrush() {
        const brush = canvas.freeDrawingBrush;
        if (!brush) return;

        const drawingColorEl = document.getElementById('drawing-color');
        const drawingShadowColorEl = document.getElementById('drawing-shadow-color');
        const drawingLineWidthEl = document.getElementById('drawing-line-width');
        const drawingShadowWidth = document.getElementById('drawing-shadow-width');
        const drawingShadowOffset = document.getElementById('drawing-shadow-offset');

        brush.color = drawingColorEl.value;
        brush.width = parseInt(drawingLineWidthEl.value, 10) || 1;
        brush.shadow = new fabric.Shadow({
          blur: parseInt(drawingShadowWidth.value, 10) || 0,
          offsetX: parseInt(drawingShadowOffset.value, 10) || 0,
          offsetY: parseInt(drawingShadowOffset.value, 10) || 0,
          color: drawingShadowColorEl.value,
        });
      }

      /**
       * Handles changes to the drawing mode selector
       */
      function handleDrawingModeChange() {
        const selectorEl = document.getElementById('drawing-mode-selector');
        const val = selectorEl.value;

        // Enable drawing mode when a brush is selected
        if (!canvas.isDrawingMode) {
          canvas.isDrawingMode = true;
          isDrawing = true;
          document.getElementById('startDrawing').innerHTML = '<i class="fas fa-paint-brush"></i> Hide Drawing Tools';
        }

        if (val === 'hline') {
          const brush = new fabric.PatternBrush(canvas);
          brush.getPatternSrc = () => {
            const patternCanvas = document.createElement('canvas');
            patternCanvas.width = patternCanvas.height = 10;
            const ctx = patternCanvas.getContext('2d');
            ctx.strokeStyle = brush.color;
            ctx.lineWidth = 5;
            ctx.beginPath();
            ctx.moveTo(0, 5);
            ctx.lineTo(10, 5);
            ctx.stroke();
            return patternCanvas;
          };
          canvas.freeDrawingBrush = brush;
        }
        else if (val === 'vline') {
          const brush = new fabric.PatternBrush(canvas);
          brush.getPatternSrc = () => {
            const patternCanvas = document.createElement('canvas');
            patternCanvas.width = patternCanvas.height = 10;
            const ctx = patternCanvas.getContext('2d');
            ctx.strokeStyle = brush.color;
            ctx.lineWidth = 5;
            ctx.beginPath();
            ctx.moveTo(5, 0);
            ctx.lineTo(5, 10);
            ctx.stroke();
            return patternCanvas;
          };
          canvas.freeDrawingBrush = brush;
        }
        else if (val === 'square') {
          const brush = new fabric.PatternBrush(canvas);
          brush.getPatternSrc = () => {
            const patternCanvas = document.createElement('canvas');
            patternCanvas.width = patternCanvas.height = 10;
            const ctx = patternCanvas.getContext('2d');
            ctx.fillStyle = brush.color;
            ctx.fillRect(0, 0, 10, 10);
            return patternCanvas;
          };
          canvas.freeDrawingBrush = brush;
        }
        else if (val === 'diamond') {
          const brush = new fabric.PatternBrush(canvas);
          brush.getPatternSrc = () => {
            const patternCanvas = document.createElement('canvas');
            patternCanvas.width = patternCanvas.height = 10;
            const ctx = patternCanvas.getContext('2d');
            ctx.fillStyle = brush.color;
            ctx.beginPath();
            ctx.moveTo(5, 0);
            ctx.lineTo(10, 5);
            ctx.lineTo(5, 10);
            ctx.lineTo(0, 5);
            ctx.closePath();
            ctx.fill();
            return patternCanvas;
          };
          canvas.freeDrawingBrush = brush;
        }
        else if (val === 'texture') {
          const brush = new fabric.PatternBrush(canvas);
          brush.getPatternSrc = () => {
            const patternCanvas = document.createElement('canvas');
            patternCanvas.width = patternCanvas.height = 20;
            const ctx = patternCanvas.getContext('2d');
            
            // Create a simple texture pattern
            ctx.fillStyle = brush.color;
            for (let i = 0; i < 10; i++) {
              for (let j = 0; j < 10; j++) {
                if ((i + j) % 2 === 0) {
                  ctx.fillRect(i * 2, j * 2, 2, 2);
                }
              }
            }
            return patternCanvas;
          };
          canvas.freeDrawingBrush = brush;
        }
        else {
          // Use the built-in brush types
          const brushClass = fabric[val] || fabric.PencilBrush;
          canvas.freeDrawingBrush = new brushClass(canvas);
        }

        updateBrush();
      }

      /**
       * Clears all drawings from the canvas
       */
      function clearDrawings() {
        // Remove all non-background objects
        const objects = canvas.getObjects();
        objects.forEach(obj => {
          if (obj.type !== 'image' && obj.type !== 'text') {
            canvas.remove(obj);
          }
        });
        canvas.renderAll();
      }

      /**
       * Toggles drawing mode on/off
       */
      function toggleDrawingMode() {
        if (!currentImage) {
          alert('Please upload an image first.');
          return;
        }

        isDrawing = !isDrawing;
        const submenu = document.getElementById('drawingSubmenu');

        if (isDrawing) {
          // If crop mode is active, turn it off
          if (isCropping) {
            document.getElementById('cropSubmenu').style.display = 'none';
            document.querySelector('[data-category="Crop"]').textContent = 'Start Crop';
            isCropping = false;
            if (cropRect) {
              canvas.remove(cropRect);
              cropRect = null;
            }
          }

          submenu.style.display = 'block';
          document.getElementById('startDrawing').innerHTML = '<i class="fas fa-paint-brush"></i> Hide Drawing Tools';
          canvas.isDrawingMode = true;
          updateBrush();
        } else {
          submenu.style.display = 'none';
          document.getElementById('startDrawing').innerHTML = '<i class="fas fa-paint-brush"></i> Start Drawing';
          canvas.isDrawingMode = false;
        }
      }

      // --- Image Features ---
      // Create a hidden file input element for image uploads
      const uploadImageInput = document.createElement("input");
      uploadImageInput.type = "file";
      uploadImageInput.accept = "image/*";
      uploadImageInput.style.display = "none";
      document.body.appendChild(uploadImageInput);

      // Handle file selection
      uploadImageInput.addEventListener("change", handleFileSelect);

      // Add click event to the upload button
      document.getElementById("upload-image-button").addEventListener("click", function() {
        uploadImageInput.click();
      });

      // Handle file selection
      function handleFileSelect(event) {
        const file = event.target.files[0];
        if (file && file.type.startsWith('image/')) {
          const reader = new FileReader();
          reader.onload = function(e) {
            loadImageToCanvas(e.target.result);
            // Show object toolbar after upload
            document.getElementById("objectToolbar").style.display = "block";
          };
          reader.readAsDataURL(file);
        } else {
          alert('Please select a valid image file.');
        }
        // Reset the input value to allow selecting the same file again
        event.target.value = "";
      }

      // Load image to canvas
      function loadImageToCanvas(imageSrc) {
        canvas.clear();
        canvas.isDrawingMode = false;

        fabric.Image.fromURL(imageSrc, function(img) {
          const canvasWidth = canvas.getWidth();
          const canvasHeight = canvas.getHeight();
          const imgWidth = img.width;
          const imgHeight = img.height;

          const scaleX = (canvasWidth - 40) / imgWidth;
          const scaleY = (canvasHeight - 40) / imgHeight;
          const scale = Math.min(scaleX, scaleY, 1);

          img.set({
            scaleX: scale,
            scaleY: scale,
            selectable: true,
            left: (canvasWidth - imgWidth * scale) / 2,
            top: (canvasHeight - imgHeight * scale) / 2
          });

          canvas.add(img);
          canvas.renderAll();

          currentImage = img;
          originalImageData = {
            src: imageSrc,
            left: img.left,
            top: img.top,
            scaleX: img.scaleX,
            scaleY: img.scaleY
          };
        });
      }
      uploadImageInput.multiple = true; // Allow multiple files
      uploadImageInput.accept = "image/*"; // Accept only image files
      uploadImageInput.style.display = "none"; // Keep hidden, triggered by a button

      // Event listener for image file selection
      uploadImageInput.addEventListener("change", (e) => {
        const files = e.target.files;
        if (!files.length) {
          console.log("No files selected.");
          return;
        }

        Array.from(files).forEach((file) => {
          console.log(
            "Processing file:",
            file.name,
            "Type:",
            file.type,
            "Size:",
            file.size
          );
          const reader = new FileReader();
          reader.onload = (f) => {
            const dataURL = f.target.result;
            console.log(
              "FileReader finished for",
              file.name,
              "DataURL length:",
              dataURL.length
            );

            // Use fabric.Image.fromURL directly for more robust loading
            fabric.Image.fromURL(
              dataURL,
              (image) => {
                if (!image) {
                  console.error(
                    "Fabric.js failed to create image from DataURL for",
                    file.name
                  );
                  // Modified from alert to a more user-friendly message
                  const message = document.createElement("div");
                  message.textContent = `فشل إنشاء الصورة من الملف: ${file.name}.`;
                  message.style.cssText = `
                                position: fixed;
                                top: 50%;
                                left: 50%;
                                transform: translate(-50%, -50%);
                                background-color: #f44336; /* Red background */
                                color: white;
                                padding: 15px 20px;
                                border-radius: 8px;
                                z-index: 9999;
                                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                                animation: fadeOut 3s forwards; /* Fade out animation */
                            `;
                  document.body.appendChild(message);

                  const style = document.createElement("style");
                  style.innerHTML = `
                                @keyframes fadeOut {
                                    from { opacity: 1; }
                                    to { opacity: 0; display: none; }
                                }
                            `;
                  document.head.appendChild(style);

                  setTimeout(() => {
                    message.remove();
                    style.remove();
                  }, 3000); // Remove message after 3 seconds
                  return;
                }

                console.log(
                  "Fabric.js image created. Original dimensions:",
                  image.width,
                  image.height,
                  "for",
                  file.name
                );

                // Basic check for valid dimensions
                if (image.width === 0 || image.height === 0) {
                  console.warn(
                    "Image dimensions are zero for",
                    file.name,
                    "Skipping this image."
                  );
                  // Modified from alert to a more user-friendly message
                  const message = document.createElement("div");
                  message.textContent = `الصورة "${file.name}" لها أبعاد غير صالحة ولا يمكن تحميلها.`;
                  message.style.cssText = `
                                position: fixed;
                                top: 50%;
                                left: 50%;
                                transform: translate(-50%, -50%);
                                background-color: #f44336; /* Red background */
                                color: white;
                                padding: 15px 20px;
                                border-radius: 8px;
                                z-index: 9999;
                                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                                animation: fadeOut 3s forwards; /* Fade out animation */
                            `;
                  document.body.appendChild(message);

                  const style = document.createElement("style");
                  style.innerHTML = `
                                @keyframes fadeOut {
                                    from { opacity: 1; }
                                    to { opacity: 0; display: none; }
                                }
                            `;
                  document.head.appendChild(style);

                  setTimeout(() => {
                    message.remove();
                    style.remove();
                  }, 3000); // Remove message after 3 seconds
                  return;
                }

                // Calculate scale to fit within 75% of canvas while maintaining aspect ratio
                const maxAllowedWidth = canvas.getWidth() * 0.75;
                const maxAllowedHeight = canvas.getHeight() * 0.75;

                // Calculate ratio to fit both width and height constraints
                const widthRatio = maxAllowedWidth / image.width;
                const heightRatio = maxAllowedHeight / image.height;

                // Use the smaller ratio to ensure the image fits entirely
                const finalScale = Math.min(widthRatio, heightRatio, 1); // Also clamp to 1 if image is smaller than target size

                image.set({
                  left: (canvas.getWidth() - image.width * finalScale) / 2, // Center new image
                  top: (canvas.getHeight() - image.height * finalScale) / 2,
                  scaleX: finalScale,
                  scaleY: finalScale,
                  originX: "left",
                  originY: "top",
                  filters: [], // Initialize filters array
                });

                console.log(
                  "Fabric.js image properties set:",
                  "Left:",
                  image.left,
                  "Top:",
                  image.top,
                  "ScaleX:",
                  image.scaleX,
                  "ScaleY:",
                  image.scaleY,
                  "Rendered Width:",
                  image.getScaledWidth(),
                  "Rendered Height:",
                  image.getScaledHeight()
                );

                canvas.add(image);
                canvas.setActiveObject(image);
                canvas.renderAll();
                saveCanvasState();
                console.log(
                  "Image added to canvas and rendered successfully:",
                  file.name
                );
              },
              { crossOrigin: "anonymous" }
            ); // 'anonymous' might not strictly be needed for local files, but it doesn't hurt.
          };
          reader.onerror = (err) => {
            console.error("FileReader error for file:", file.name, err);
            // Modified from alert to a more user-friendly message
            const message = document.createElement("div");
            message.textContent = `خطأ في قراءة الملف: ${file.name}`;
            message.style.cssText = `
                        position: fixed;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        background-color: #f44336; /* Red background */
                        color: white;
                        padding: 15px 20px;
                        border-radius: 8px;
                        z-index: 9999;
                        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                        animation: fadeOut 3s forwards; /* Fade out animation */
                    `;
            document.body.appendChild(message);

            const style = document.createElement("style");
            style.innerHTML = `
                        @keyframes fadeOut {
                            from { opacity: 1; }
                            to { opacity: 0; display: none; }
                        }
                    `;
            document.head.appendChild(style);

            setTimeout(() => {
              message.remove();
              style.remove();
            }, 3000); // Remove message after 3 seconds
          };
          reader.readAsDataURL(file);
        });
        e.target.value = ""; // Clear the input value so the same files can be selected again
      });

      /**
       * Applies an image filter to the active object (if it's an image).
       * Uses range slider values for dynamic updates.
       * @param {string} filterType - The type of filter (e.g., 'brightness', 'grayscale').
       * @param {number} [value=0] - The value for the filter (if applicable).
       */
      function applyImageFilter(filterType, value = 0) {
        const obj = canvas.getActiveObject();
        if (!obj || obj.type !== "image") return;

        // Remove existing filter of the same type (for sliders) or toggle (for on/off filters)
        let filterFound = false;
        obj.filters = obj.filters.filter((f) => {
          if (f.type === "Brightness" && filterType === "brightness")
            return false;
          if (f.type === "Contrast" && filterType === "contrast") return false;
          if (f.type === "Blur" && filterType === "blur") return false;
          if (f.type === "Saturation" && filterType === "saturation")
            return false;
          if (
            f.type === "Grayscale" &&
            filterType === "grayscale" &&
            (filterFound = true)
          )
            return false;
          if (
            f.type === "BlackWhite" &&
            filterType === "blackwhite" &&
            (filterFound = true)
          )
            return false;
          if (
            f.type === "Convolute" &&
            filterType === "sharpen" &&
            (filterFound = true)
          )
            return false; // Sharpen is a convolute filter
          if (
            f.type === "Invert" &&
            filterType === "invert" &&
            (filterFound = true)
          )
            return false;
          return true;
        });

        let newFilter;
        switch (filterType) {
          case "brightness":
            newFilter = new fabric.Image.filters.Brightness({
              brightness: value,
            });
            break;
          case "contrast":
            newFilter = new fabric.Image.filters.Contrast({ contrast: value });
            break;
          case "blur":
            newFilter = new fabric.Image.filters.Blur({ blur: value });
            break;
          case "saturation":
            newFilter = new fabric.Image.filters.Saturation({
              saturation: value,
            });
            break;
          case "grayscale":
            if (!filterFound) newFilter = new fabric.Image.filters.Grayscale();
            break;
          case "blackwhite":
            if (!filterFound) newFilter = new fabric.Image.filters.BlackWhite();
            break;
          case "sharpen":
            if (!filterFound)
              newFilter = new fabric.Image.filters.Convolute({
                matrix: [0, -1, 0, -1, 5, -1, 0, -1, 0], // Sharpening matrix
              });
            break;
          case "invert":
            if (!filterFound) newFilter = new fabric.Image.filters.Invert();
            break;
          case "reset":
            obj.filters = [];
            break;
          default:
            return;
        }

        if (newFilter) {
          obj.filters.push(newFilter);
        }

        obj.applyFilters(); // Apply all filters in the array
        canvas.renderAll();
        saveCanvasState();
      }

      /** Applies border (stroke) to the active image object. */
      function setImageBorder() {
        const activeObject = canvas.getActiveObject();
        if (activeObject && activeObject.type === "image") {
          const color = document.getElementById("image-border-color").value;
          const width = parseInt(
            document.getElementById("image-border-width").value,
            10
          );
          activeObject.set({
            stroke: color,
            strokeWidth: width,
          });
          canvas.renderAll();
          saveCanvasState();
        }
      }

      /** Applies shadow to the active image object. */
      function setImageShadow() {
        const activeObject = canvas.getActiveObject();
        if (activeObject && activeObject.type === "image") {
          const color = document.getElementById("image-shadow-color").value;
          const blur = parseInt(
            document.getElementById("image-shadow-blur").value,
            10
          );
          const offsetX = parseInt(
            document.getElementById("image-shadow-offsetX").value,
            10
          );
          const offsetY = parseInt(
            document.getElementById("image-shadow-offsetY").value,
            10
          );
          const opacity = parseFloat(
            document.getElementById("image-shadow-opacity").value
          );

          activeObject.set({
            shadow: new fabric.Shadow({
              color: color,
              blur: blur,
              offsetX: offsetX,
              offsetY: offsetY,
              opacity: opacity,
            }),
          });
          canvas.renderAll();
          saveCanvasState();
        }
      }

      // --- Cropper.js Integration (New Crop System - Integrated into main UI) ---
      // Define the aspect ratios with corrected vertical ratios
      const aspectRatios = {
        portrait: [
          { label: "1:1", value: "1:1" },
          { label: "2:3", value: "2:3" },
          { label: "3:4", value: "3:4" },
          { label: "4:5", value: "4:5" },
          { label: "9:16", value: "9:16" },
          { label: "9:21", value: "9:21" },
          { label: "5:4", value: "5:4" }, // This is 5:4 in portrait implies 4:5 landscape.
          { label: "1:2.63", value: "1:2.63" },
          { label: "1:3", value: "1:3" },
          { label: "1:2", value: "1:2" },
        ],
        landscape: [
          { label: "Free", value: "free" },
          { label: "3:2", value: "3:2" },
          { label: "4:3", value: "4:3" },
          { label: "5:4", value: "5:4" },
          { label: "16:9", value: "16:9" },
          { label: "21:9", value: "21:9" },
          { label: "2.63:1", value: "2.63:1" },
          { label: "3:1", value: "3:1" },
          { label: "2:1", value: "2:1" },
        ],
      };

      /**
       * Parses a ratio string (e.g., "16:9", "1:1", "free") into a number for Cropper.js.
       * Returns `null` for "free".
       */
      function parseAspectRatio(ratio) {
        if (ratio === "free") return null;
        const [width, height] = ratio.split(":").map(Number);
        return width / height;
      }

      /**
       * Creates and appends aspect ratio buttons to the sub-toolbar.
       */
      function createRatioButtonsSubToolbar() {
        const portraitRatiosSubContainer =
          document.getElementById("portraitRatiosSub");
        const landscapeRatiosSubContainer =
          document.getElementById("landscapeRatiosSub");

        if (!portraitRatiosSubContainer || !landscapeRatiosSubContainer) {
          console.warn("Ratio containers not found in sub-toolbar.");
          return;
        }

        portraitRatiosSubContainer.innerHTML = "";
        landscapeRatiosSubContainer.innerHTML = "";

        // Create portrait ratio buttons
        aspectRatios.portrait.forEach((ratio) => {
          const button = document.createElement("button");
          button.className = "ratio-button";
          button.textContent = ratio.label;
          button.dataset.ratio = ratio.value;
          button.addEventListener("click", () => {
            setCropperAspectRatio(button.dataset.ratio);
          });
          portraitRatiosSubContainer.appendChild(button);
        });

        // Create landscape ratio buttons
        aspectRatios.landscape.forEach((ratio) => {
          const button = document.createElement("button");
          button.className = "ratio-button";
          button.textContent = ratio.label;
          button.dataset.ratio = ratio.value;
          button.addEventListener("click", () => {
            setCropperAspectRatio(button.dataset.ratio);
          });
          landscapeRatiosSubContainer.appendChild(button);
        });
        console.log("Ratio buttons created in sub-toolbar.");
      }

      /**
       * Activates the Cropper.js instance on the currently selected image, displaying it as an overlay.
       */
      function activateCropperOnCanvas() {
        const activeObject = canvas.getActiveObject();
        if (!activeObject || activeObject.type !== "image") {
          console.log("No image selected for cropping.");
          const message = document.createElement("div");
          message.textContent = "الرجاء تحديد صورة للقص أولاً.";
          message.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background-color: #f44336; /* Red background */
                    color: white;
                    padding: 15px 20px;
                    border-radius: 8px;
                    z-index: 9999;
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                    animation: fadeOut 3s forwards; /* Fade out animation */
                `;
          document.body.appendChild(message);

          const style = document.createElement("style");
          style.innerHTML = `
                    @keyframes fadeOut {
                        from { opacity: 1; }
                        to { opacity: 0; display: none; }
                    }
                `;
          document.head.appendChild(style);

          setTimeout(() => {
            message.remove();
            style.remove();
          }, 3000);
          return;
        }

        currentImageForCropper = activeObject;

        // Get the bounding rect of the Fabric.js image relative to the canvas container
        const imgRect = activeObject.getBoundingRect(true); // true for including controls

        // Position and size the cropperImageElement to exactly overlay the Fabric.js image
        cropperImageElement.style.width = `${imgRect.width}px`;
        cropperImageElement.style.height = `${imgRect.height}px`;
        cropperImageElement.style.left = `${imgRect.left}px`;
        cropperImageElement.style.top = `${imgRect.top}px`;
        cropperImageElement.style.transform = `rotate(${activeObject.angle}deg)`;

        // Set the source for the Cropper.js image element
        // Use toDataURL without multiplier to get the original resolution of the Fabric.js image's content
        cropperImageElement.src = currentImageForCropper.toDataURL({
          format: "png",
          multiplier: 1, // Crucial for getting the exact pixel data for Cropper.js
        });

        // Show the cropper overlay
        cropperDisplayArea.style.display = "flex";

        // Disable Fabric.js selection/events for all objects while cropping to avoid conflicts
        canvas.selection = false;
        canvas.forEachObject((obj) => {
          obj.selectable = false;
          obj.evented = false;
        });
        canvas.renderAll(); // Re-render to reflect disabled selection

        // Destroy existing cropper instance if it exists
        if (cropper) {
          cropper.destroy();
        }

        // Initialize Cropper.js on the overlay image
        cropper = new Cropper(cropperImageElement, {
          aspectRatio: 1 / 1, // Default to 1:1, can be overridden by ratio buttons
          viewMode: 1, // Restrict the crop box to not exceed the canvas
          dragMode: "move",
          autoCropArea: 1, // Automatically show crop area when initialized
          background: false, // Hide the grid background - important for seeing canvas underneath
          zoomable: true,
          rotatable: false, // Fabric.js object might be rotated, but Cropper.js applies crop on axis-aligned source image
          scalable: true,
          toggleDragModeOnDblclick: true,
        });

        console.log("Cropper.js activated on canvas overlay.");
        // Highlight the '1:1' ratio button as default active
        document
          .querySelectorAll("#portraitRatiosSub .ratio-button")
          .forEach((btn) => btn.classList.remove("active"));
        document
          .querySelector('#portraitRatiosSub [data-ratio="1:1"]')
          ?.classList.add("active");
      }

      /**
       * Sets the aspect ratio for the Cropper.js instance.
       * @param {string} ratio - The aspect ratio string (e.g., "16:9", "free").
       */
      function setCropperAspectRatio(ratio) {
        if (cropper) {
          cropper.setAspectRatio(parseAspectRatio(ratio));
          // Update active button styling
          document.querySelectorAll(".ratio-button").forEach((btn) => {
            btn.classList.remove("active");
            if (btn.dataset.ratio === ratio) {
              btn.classList.add("active");
            }
          });
        }
      }

      /**
       * Applies the crop using Cropper.js and updates the Fabric.js canvas.
       */
      function applyCropperCrop() {
        if (!cropper || !currentImageForCropper) {
          console.log("No cropper active or image selected to apply crop.");
          const message = document.createElement("div");
          message.textContent =
            "لا توجد أداة قص نشطة أو صورة محددة لتطبيق القص.";
          message.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background-color: #f44336;
                    color: white;
                    padding: 15px 20px;
                    border-radius: 8px;
                    z-index: 9999;
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                    animation: fadeOut 3s forwards;
                `;
          document.body.appendChild(message);

          const style = document.createElement("style");
          style.innerHTML = `
                    @keyframes fadeOut {
                        from { opacity: 1; }
                        to { opacity: 0; display: none; }
                    }
                `;
          document.head.appendChild(style);

          setTimeout(() => {
            message.remove();
            style.remove();
          }, 3000);
          deactivateCropperOnCanvas();
          return;
        }

        try {
          // Get cropped canvas as a data URL
          const croppedDataURL = cropper
            .getCroppedCanvas()
            .toDataURL("image/png");

          if (!croppedDataURL || croppedDataURL.length < 100) {
            console.error(
              "Generated croppedDataURL is too small or invalid after Cropper.js operation."
            );
            const message = document.createElement("div");
            message.textContent =
              "فشل القص: الصورة الناتجة فارغة أو غير صالحة.";
            message.style.cssText = `
                        position: fixed;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        background-color: #f44336;
                        color: white;
                        padding: 15px 20px;
                        border-radius: 8px;
                        z-index: 9999;
                        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                        animation: fadeOut 3s forwards;
                    `;
            document.body.appendChild(message);

            const style = document.createElement("style");
            style.innerHTML = `
                        @keyframes fadeOut {
                            from { opacity: 1; }
                            to { opacity: 0; display: none; }
                        }
                    `;
            document.head.appendChild(style);

            setTimeout(() => {
              message.remove();
              style.remove();
            }, 3000);
            deactivateCropperOnCanvas();
            return;
          }

          fabric.Image.fromURL(croppedDataURL, (newImg) => {
            if (!newImg.width || !newImg.height) {
              console.error(
                "New Fabric.Image has invalid dimensions after loading cropped dataURL.",
                { width: newImg.width, height: newImg.height }
              );
              const message = document.createElement("div");
              message.textContent = "فشل تحميل الصورة المقصوصة. قد تكون فارغة.";
              message.style.cssText = `
                            position: fixed;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                            background-color: #f44336;
                            color: white;
                            padding: 15px 20px;
                            border-radius: 8px;
                            z-index: 9999;
                            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                            animation: fadeOut 3s forwards;
                        `;
              document.body.appendChild(message);

              const style = document.createElement("style");
              style.innerHTML = `
                            @keyframes fadeOut {
                                from { opacity: 1; }
                                to { opacity: 0; display: none; }
                            }
                        `;
              document.head.appendChild(style);

              setTimeout(() => {
                message.remove();
                style.remove();
              }, 3000);
              deactivateCropperOnCanvas();
              return;
            }

            // Preserve original image's position, scale, rotation, and filters
            // IMPORTANT: The new image's dimensions after cropping will be different,
            // so apply the scaling based on the original image's dimensions, not the cropped ones.
            const originalWidth = currentImageForCropper.width;
            const originalHeight = currentImageForCropper.height;
            const croppedRatioX = newImg.width / cropper.getImageData().width; // Ratio of cropped width to original Cropper image width
            const croppedRatioY = newImg.height / cropper.getImageData().height; // Ratio of cropped height to original Cropper image height

            // Adjust currentImageForCropper's original dimensions by the crop box area before applying to newImg
            const cropBoxData = cropper.getCropBoxData();
            const newImageSourceWidth = cropBoxData.width;
            const newImageSourceHeight = cropBoxData.height;

            // Calculate the new scale relative to the *original Fabric.js object's* scale
            // This is more robust as it accounts for the actual size of the image on canvas
            // relative to its initial full size, then applies the cropped dimensions.
            const newScaleX =
              currentImageForCropper.scaleX *
              (newImg.width / newImageSourceWidth);
            const newScaleY =
              currentImageForCropper.scaleY *
              (newImg.height / newImageSourceHeight);

            newImg.set({
              left:
                currentImageForCropper.left +
                cropBoxData.left * currentImageForCropper.scaleX, // Adjust position based on crop box offset
              top:
                currentImageForCropper.top +
                cropBoxData.top * currentImageForCropper.scaleY, // Adjust position based on crop box offset
              angle: currentImageForCropper.angle,
              scaleX: newImg.scaleX * currentImageForCropper.scaleX, // Apply existing scale and then the cropper scale
              scaleY: newImg.scaleY * currentImageForCropper.scaleY, // Apply existing scale and then the cropper scale
              flipX: currentImageForCropper.flipX,
              flipY: currentImageForCropper.flipY,
              filters: currentImageForCropper.filters.slice(), // Clone filters array
              originX: currentImageForCropper.originX,
              originY: currentImageForCropper.originY,
              stroke: currentImageForCropper.stroke,
              strokeWidth: currentImageForCropper.strokeWidth,
              shadow: currentImageForCropper.shadow,
            });

            // Ensure filters are applied to the new image
            newImg.applyFilters();

            // Remove the old image and add the new cropped one
            canvas.remove(currentImageForCropper);
            canvas.add(newImg);
            canvas.setActiveObject(newImg); // Select the new image
            canvas.renderAll();
            saveCanvasState(); // Save state after crop

            deactivateCropperOnCanvas(); // Deactivate cropper UI
          });
        } catch (error) {
          console.error("Error applying crop with Cropper.js:", error);
          const message = document.createElement("div");
          message.textContent = "حدث خطأ أثناء تطبيق القص: " + error.message;
          message.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background-color: #f44336;
                    color: white;
                    padding: 15px 20px;
                    border-radius: 8px;
                    z-index: 9999;
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                    animation: fadeOut 3s forwards;
                `;
          document.body.appendChild(message);

          const style = document.createElement("style");
          style.innerHTML = `
                    @keyframes fadeOut {
                        from { opacity: 1; }
                        to { opacity: 0; display: none; }
                    }
                `;
          document.head.appendChild(style);

          setTimeout(() => {
            message.remove();
            style.remove();
          }, 3000);
          deactivateCropperOnCanvas();
        }
      }

      /**
       * Deactivates the Cropper.js instance and hides its overlay.
       */
      function deactivateCropperOnCanvas() {
        if (cropper) {
          cropper.destroy();
          cropper = null;
        }
        cropperDisplayArea.style.display = "none"; // Hide the cropper overlay
        cropperImageElement.src = ""; // Clear image source
        cropperImageElement.style.width = ""; // Reset inline styles
        cropperImageElement.style.height = "";
        cropperImageElement.style.left = "";
        cropperImageElement.style.top = "";
        cropperImageElement.style.transform = "";

        // Restore selectable and evented properties for all objects on the canvas
        canvas.selection = true; // Re-enable general canvas selection
        canvas.forEachObject((obj) => {
          obj.selectable = true;
          obj.evented = true;
        });
        canvas.renderAll(); // Re-render to reflect restored selection

        currentImageForCropper = null; // Clear reference
        canvas.discardActiveObject().renderAll(); // Deselect any active Fabric.js objects
        console.log("Cropper.js deactivated.");
      }

      // --- PNG Library ---
      // Placeholder PNGs (replace with actual URLs or base64 if hosted)
      const pngs = [
        {
          id: "icon-star",
          src: "https://placehold.co/100x100/A3BE8C/000000/png?text=%E2%98%85",
        }, // Star emoji
        {
          id: "icon-heart",
          src: "https://placehold.co/100x100/EBCB8B/000000/png?text=%E2%99%A5",
        }, // Heart emoji
        {
          id: "shape-square",
          src: "https://placehold.co/100x100/BF616A/FFFFFF/png?text=%E2%96%A0",
        }, // Square emoji
        {
          id: "ui-button",
          src: "https://placehold.co/100x100/B48EAD/000000/png?text=Button",
        },
        {
          id: "icon-arrow",
          src: "https://placehold.co/100x100/8FBCBB/000000/png?text=%E2%9E%A1",
        }, // Right arrow emoji
        {
          id: "shape-circle",
          src: "https://placehold.co/100x100/D08770/FFFFFF/png?text=%E2%97%8F",
        }, // Circle emoji
        {
          id: "ui-slider",
          src: "https://placehold.co/100x100/5E81AC/000000/png?text=Slider",
        },
      ];

      /** Opens the PNG library modal and populates the grid. */
      function openPngLibrary() {
        pngGrid.innerHTML = ""; // Clear previous content
        pngs.forEach((png) => {
          const div = document.createElement("div");
          div.classList.add("png-item");
          div.dataset.src = png.src;
          div.title = `Add ${png.id}`;

          const img = document.createElement("img");
          img.src = png.src;
          img.alt = png.id;
          img.draggable = true; // Make image draggable
          img.dataset.src = png.src; // For drag and drop data

          div.appendChild(img);
          pngGrid.appendChild(div);

          // Click to insert functionality
          div.addEventListener("click", (e) => {
            addImageFromUrl(png.src);
            pngLibraryModal.style.display = "none"; // Close modal after selection
          });
        });

        // Drag & Drop event listeners for PNG library images onto the canvas
        pngGrid.addEventListener("dragstart", (e) => {
          e.dataTransfer.setData("text/plain", e.target.dataset.src); // Store image URL
        });

        canvasContainer.addEventListener("dragover", (e) => {
          e.preventDefault(); // Allow drop operation
        });

        canvasContainer.addEventListener("drop", (e) => {
          e.preventDefault();
          const data = e.dataTransfer.getData("text/plain");
          if (data && data.startsWith("https://placehold.co")) {
            // Check if it's our PNG data
            const pointer = canvas.getPointer(e); // Get drop coordinates on canvas
            addImageFromUrl(data, pointer.x, pointer.y);
          }
        });

        pngLibraryModal.style.display = "flex"; // Show the modal
      }

      /**
       * Adds an image to the canvas from a given URL.
       * @param {string} url - The URL of the image.
       * @param {number} [left=50] - Initial X position on canvas.
       * @param {number} [top=50] - Initial Y position on canvas.
       */
      function addImageFromUrl(url, left = 50, top = 50) {
        fabric.Image.fromURL(
          url,
          (img) => {
            img.set({
              left: left,
              top: top,
              scaleX: 0.5, // Default scale for library images
              scaleY: 0.5,
            });
            canvas.add(img);
            canvas.setActiveObject(img);
            canvas.renderAll();
            saveCanvasState();
          },
          { crossOrigin: "anonymous" }
        ); // 'anonymous' is crucial for CORS if images are from other domains
      }

      // --- Canvas & UI Behavior ---
      /** Sets the canvas dimensions based on user input. */
      function setCanvasSize() {
        const width = parseInt(
          document.getElementById("canvas-width").value,
          10
        );
        const height = parseInt(
          document.getElementById("canvas-height").value,
          10
        );
        if (!isNaN(width) && !isNaN(height) && width > 0 && height > 0) {
          canvas.setWidth(width);
          canvas.setHeight(height);
          canvas.renderAll();
          saveCanvasState();
        } else {
          // Modified from alert to a more user-friendly message
          const message = document.createElement("div");
          message.textContent =
            "الرجاء إدخال عرض وارتفاع صالحين للوحة (أرقام موجبة).";
          message.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background-color: #f44336; /* Red background */
                    color: white;
                    padding: 15px 20px;
                    border-radius: 8px;
                    z-index: 9999;
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                    animation: fadeOut 3s forwards; /* Fade out animation */
                `;
          document.body.appendChild(message);

          const style = document.createElement("style");
          style.innerHTML = `
                    @keyframes fadeOut {
                        from { opacity: 1; }
                        to { opacity: 0; display: none; }
                    }
                `;
          document.head.appendChild(style);

          setTimeout(() => {
            message.remove();
            style.remove();
          }, 3000);
        }
      }

      /** Adjusts canvas size to fill its container while maintaining fixed internal Fabric.js dimensions. */
      function resizeCanvas() {
        const container = document.getElementById("canvas-container");
        // This ensures the canvas CSS scales, but Fabric.js internal dimensions
        // remain fixed as set by `canvas.setWidth/Height`. This is often desired
        // to maintain design scale irrespective of viewport size.
        // If the Fabric.js canvas itself needs to dynamically change resolution:
        // canvas.setWidth(container.offsetWidth);
        // canvas.setHeight(container.offsetHeight);
        canvas.renderAll();
      }

      // --- Export ---
      /**
       * Exports the canvas content to a specified format.
       * @param {string} format - 'png', 'jpeg', 'svg', or 'json'.
       */
      function exportCanvas(format) {
        let dataURL;
        let filename = `canvas-design-${Date.now()}`;

        if (format === "png") {
          dataURL = canvas.toDataURL({
            format: "png",
            quality: 1.0,
            multiplier: 2, // Export at 2x resolution for better quality
          });
          filename += ".png";
        } else if (format === "jpeg") {
          dataURL = canvas.toDataURL({
            format: "jpeg",
            quality: 0.8,
            multiplier: 2,
          });
          filename += ".jpeg";
        } else if (format === "svg") {
          dataURL =
            "data:image/svg+xml;utf8," + encodeURIComponent(canvas.toSVG());
          filename += ".svg";
        } else if (format === "json") {
          const json = JSON.stringify(canvas.toJSON());
          dataURL =
            "data:application/json;charset=utf-8," + encodeURIComponent(json);
          filename += ".json";
        } else {
          return;
        }

        const a = document.createElement("a");
        a.href = dataURL;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a); // Clean up the temporary anchor element
      }

      // --- Attach Sub-Toolbar Listeners ---
      /**
       * Attaches event listeners to the dynamically loaded sub-toolbar elements.
       * This function must be called every time the sub-toolbar content changes.
       * @param {string} category - The active main category.
       */
      function attachSubToolbarListeners(category) {
        // First, clear all existing listeners by replacing elements with clones.
        // This is crucial for dynamic content to prevent listener accumulation.
        subToolbar.querySelectorAll("*").forEach((el) => {
          const newEl = el.cloneNode(true);
          el.parentNode.replaceChild(newEl, el);
        });

        // Re-select elements after cloning to ensure new references
        const elements = {};
        Array.from(subToolbar.children).forEach((el) => {
          if (el.id) elements[el.id] = el;
          // Also get inputs/selects that might not have direct IDs but are important
          if (el.tagName === "INPUT" || el.tagName === "SELECT") {
            if (
              el.type === "color" ||
              el.type === "range" ||
              el.type === "number" ||
              el.tagName === "SELECT" ||
              el.type === "text"
            ) {
              elements[el.id] = el;
            }
          }
        });

        // Disable free drawing mode by default when switching categories,
        // it will be enabled specifically for 'Draw' category.
        canvas.isDrawingMode = false;

        switch (category) {
          case "Draw":
            elements["draw-pencil"].addEventListener("click", () =>
              activateDrawingMode("pencil")
            );
            elements["draw-brush"].addEventListener("click", () =>
              activateDrawingMode("brush")
            );
            elements["draw-marker"].addEventListener("click", () =>
              activateDrawingMode("marker")
            );
            elements["draw-spray"].addEventListener("click", () =>
              activateDrawingMode("spray")
            );
            elements["draw-dotted"].addEventListener("click", () =>
              activateDrawingMode("dotted")
            );
            elements["draw-eraser"].addEventListener("click", () =>
              activateDrawingMode("eraser")
            );

            const brushColorInput = elements["brush-color"];
            const brushSizeInput = elements["brush-size"];
            const brushOpacityInput = elements["brush-opacity"];

            const updateBrush = () => {
              if (canvas.freeDrawingBrush) {
                canvas.freeDrawingBrush.color = brushColorInput.value;
                canvas.freeDrawingBrush.width = parseInt(
                  brushSizeInput.value,
                  10
                );
                canvas.freeDrawingBrush.opacity = parseFloat(
                  brushOpacityInput.value
                );
                canvas.renderAll();
              }
            };

            brushColorInput.addEventListener("input", updateBrush);
            brushSizeInput.addEventListener("input", updateBrush);
            brushOpacityInput.addEventListener("input", updateBrush);

            // Initialize drawing mode to pencil when 'Draw' category is first displayed
            activateDrawingMode("pencil");
            updateBrush(); // Apply initial values to the brush
            break;

          case "Shapes":
            elements["add-rect"].addEventListener("click", () =>
              addShape("rect")
            );
            elements["add-circle"].addEventListener("click", () =>
              addShape("circle")
            );
            elements["add-triangle"].addEventListener("click", () =>
              addShape("triangle")
            );
            elements["add-line"].addEventListener("click", () =>
              addShape("line")
            );
            elements["add-polygon"].addEventListener("click", () =>
              addShape("polygon")
            );
            elements["add-polyline"].addEventListener("click", () =>
              addShape("polyline")
            );
            elements["add-path"].addEventListener("click", () =>
              addShape("path")
            );
            break;

          case "Text":
            elements["add-text"].addEventListener("click", addText);

            const fontFamilySelect = elements["font-family"];
            const fontSizeInput = elements["font-size"];
            const textColorInput = elements["text-color"];
            const textBgColorInput = elements["text-bg-color"];
            const textStrokeColorInput = elements["text-stroke-color"];
            const textStrokeWidthInput = elements["text-stroke-width"];
            const textShadowColorInput = elements["text-shadow-color"];
            const textShadowBlurInput = elements["text-shadow-blur"];
            const textShadowOffsetXInput = elements["text-shadow-offsetX"];
            const textShadowOffsetYInput = elements["text-shadow-offsetY"];

            const applyTextProperties = () => {
              const activeObject = canvas.getActiveObject();
              if (
                activeObject &&
                (activeObject.type === "i-text" ||
                  activeObject.type === "textbox")
              ) {
                activeObject.set({
                  fontFamily: fontFamilySelect.value,
                  fontSize: parseInt(fontSizeInput.value, 10),
                  fill: textColorInput.value,
                  // Only set background if not default white to prevent unnecessary rendering
                  backgroundColor:
                    textBgColorInput.value === "#ffffff"
                      ? ""
                      : textBgColorInput.value,
                  stroke: textStrokeColorInput.value,
                  strokeWidth: parseInt(textStrokeWidthInput.value, 10),
                  shadow: new fabric.Shadow({
                    color: textShadowColorInput.value,
                    blur: parseInt(textShadowBlurInput.value, 10),
                    offsetX: parseInt(textShadowOffsetXInput.value, 10),
                    offsetY: parseInt(textShadowOffsetYInput.value, 10),
                    affectStroke: true, // Apply shadow to stroke as well
                  }),
                });
                canvas.renderAll();
                saveCanvasState();
              }
            };

            // Add event listeners for text property changes
            fontFamilySelect.addEventListener("change", applyTextProperties);
            fontSizeInput.addEventListener("input", applyTextProperties);
            textColorInput.addEventListener("input", applyTextProperties);
            textBgColorInput.addEventListener("input", applyTextProperties);
            textStrokeColorInput.addEventListener("input", applyTextProperties);
            textStrokeWidthInput.addEventListener("input", applyTextProperties);
            textShadowColorInput.addEventListener("input", applyTextProperties);
            textShadowBlurInput.addEventListener("input", applyTextProperties);
            textShadowOffsetXInput.addEventListener(
              "input",
              applyTextProperties
            );
            textShadowOffsetYInput.addEventListener(
              "input",
              applyTextProperties
            );

            // Text Alignment buttons
            elements["text-align-left"].addEventListener("click", () => {
              const activeObject = canvas.getActiveObject();
              if (
                activeObject &&
                (activeObject.type === "i-text" ||
                  activeObject.type === "textbox")
              ) {
                activeObject.set("textAlign", "left");
                canvas.renderAll();
                saveCanvasState();
              }
            });
            elements["text-align-center"].addEventListener("click", () => {
              const activeObject = canvas.getActiveObject();
              if (
                activeObject &&
                (activeObject.type === "i-text" ||
                  activeObject.type === "textbox")
              ) {
                activeObject.set("textAlign", "center");
                canvas.renderAll();
                saveCanvasState();
              }
            });
            elements["text-align-right"].addEventListener("click", () => {
              const activeObject = canvas.getActiveObject();
              if (
                activeObject &&
                (activeObject.type === "i-text" ||
                  activeObject.type === "textbox")
              ) {
                activeObject.set("textAlign", "right");
                canvas.renderAll();
                saveCanvasState();
              }
            });
            elements["text-align-justify"].addEventListener("click", () => {
              const activeObject = canvas.getActiveObject();
              if (
                activeObject &&
                (activeObject.type === "i-text" ||
                  activeObject.type === "textbox")
              ) {
                activeObject.set("textAlign", "justify");
                canvas.renderAll();
                saveCanvasState();
              }
            });

            // Update controls when a text object is selected
            canvas.on("selection:created", updateTextControls);
            canvas.on("selection:updated", updateTextControls);

            function updateTextControls(e) {
              if (
                e.selected &&
                e.selected[0] &&
                (e.selected[0].type === "i-text" ||
                  e.selected[0].type === "textbox")
              ) {
                const obj = e.selected[0];
                fontFamilySelect.value = obj.fontFamily;
                fontSizeInput.value = obj.fontSize;
                textColorInput.value = obj.fill;
                textBgColorInput.value = obj.backgroundColor || "#ffffff"; // Default to white if no background
                textStrokeColorInput.value = obj.stroke || "#000000";
                textStrokeWidthInput.value = obj.strokeWidth || 0;
                if (obj.shadow) {
                  textShadowColorInput.value = obj.shadow.color;
                  textShadowBlurInput.value = obj.shadow.blur;
                  textShadowOffsetXInput.value = obj.shadow.offsetX;
                  textShadowOffsetYInput.value = obj.shadow.offsetY;
                } else {
                  // Reset shadow controls if no shadow
                  textShadowColorInput.value = "#000000";
                  textShadowBlurInput.value = 0;
                  textShadowOffsetXInput.value = 0;
                  textShadowOffsetYInput.value = 0;
                }
              }
            }
            break;

          case "Images":
            // Add the hidden input to the DOM (only once, if not already present)
            if (!document.body.contains(uploadImageInput)) {
              document.body.appendChild(uploadImageInput);
            }
            // Attach event listener to the "Upload" button
            elements["upload-image-button"].addEventListener("click", () => {
              console.log(
                "Upload Image button clicked, triggering hidden input."
              );
              uploadImageInput.click(); // Programmatically click the hidden input
            });

            elements["image-border-color"].addEventListener(
              "input",
              setImageBorder
            );
            elements["image-border-width"].addEventListener(
              "input",
              setImageBorder
            );

            elements["image-shadow-color"].addEventListener(
              "input",
              setImageShadow
            );
            elements["image-shadow-blur"].addEventListener(
              "input",
              setImageShadow
            );
            elements["image-shadow-offsetX"].addEventListener(
              "input",
              setImageShadow
            );
            elements["image-shadow-offsetY"].addEventListener(
              "input",
              setImageShadow
            );
            elements["image-shadow-opacity"].addEventListener(
              "input",
              setImageShadow
            );

            // Update image border/shadow controls when image is selected
            canvas.on("selection:created", updateImageControls);
            canvas.on("selection:updated", updateImageControls);

            function updateImageControls(e) {
              if (
                e.selected &&
                e.selected[0] &&
                e.selected[0].type === "image"
              ) {
                const obj = e.selected[0];
                elements["image-border-color"].value = obj.stroke || "#000000";
                elements["image-border-width"].value = obj.strokeWidth || 0;

                if (obj.shadow) {
                  elements["image-shadow-color"].value = obj.shadow.color;
                  elements["image-shadow-blur"].value = obj.shadow.blur;
                  elements["image-shadow-offsetX"].value = obj.shadow.offsetX;
                  elements["image-shadow-offsetY"].value = obj.shadow.offsetY;
                  elements["image-shadow-opacity"].value = obj.shadow.opacity;
                } else {
                  // Reset shadow controls if no shadow
                  elements["image-shadow-color"].value = "#000000";
                  elements["image-shadow-blur"].value = 0;
                  elements["image-shadow-offsetX"].value = 0;
                  elements["image-shadow-offsetY"].value = 0;
                  elements["image-shadow-opacity"].value = 1;
                }
              }
            }
            break;

          case "Crop":
            elements["activate-cropper-button"].addEventListener(
              "click",
              activateCropperOnCanvas
            );
            elements["apply-cropper-button"].addEventListener(
              "click",
              applyCropperCrop
            );
            elements["cancel-cropper-button"].addEventListener(
              "click",
              deactivateCropperOnCanvas
            );

            // Dynamically generate ratio buttons for the sub-toolbar
            createRatioButtonsSubToolbar();
            break;

          case "Effects":
            const filterBrightnessVal = elements["filter-brightness-val"];
            const filterContrastVal = elements["filter-contrast-val"];
            const filterBlurVal = elements["filter-blur-val"];
            const filterSaturationVal = elements["filter-saturation-val"];

            filterBrightnessVal.addEventListener("input", (e) =>
              applyImageFilter("brightness", parseFloat(e.target.value))
            );
            filterContrastVal.addEventListener("input", (e) =>
              applyImageFilter("contrast", parseFloat(e.target.value))
            );
            filterBlurVal.addEventListener("input", (e) =>
              applyImageFilter("blur", parseFloat(e.target.value))
            );
            filterSaturationVal.addEventListener("input", (e) =>
              applyImageFilter("saturation", parseFloat(e.target.value))
            );

            elements["filter-grayscale"].addEventListener("click", () =>
              applyImageFilter("grayscale")
            );
            elements["filter-blackwhite"].addEventListener("click", () =>
              applyImageFilter("blackwhite")
            );
            elements["filter-sharpen"].addEventListener("click", () =>
              applyImageFilter("sharpen")
            );
            elements["filter-invert"].addEventListener("click", () =>
              applyImageFilter("invert")
            );
            elements["filter-reset"].addEventListener("click", () =>
              applyImageFilter("reset")
            );

            // Update filter sliders when image is selected
            canvas.on("selection:created", updateFilterControls);
            canvas.on("selection:updated", updateFilterControls);

            function updateFilterControls(e) {
              if (
                e.selected &&
                e.selected[0] &&
                e.selected[0].type === "image"
              ) {
                const obj = e.selected[0];
                // Reset all sliders first to a default/neutral state
                filterBrightnessVal.value = 0;
                filterContrastVal.value = 0;
                filterBlurVal.value = 0;
                filterSaturationVal.value = 0;

                obj.filters.forEach((filter) => {
                  if (filter.type === "Brightness")
                    filterBrightnessVal.value = filter.brightness;
                  if (filter.type === "Contrast")
                    filterContrastVal.value = filter.contrast;
                  if (filter.type === "Blur") filterBlurVal.value = filter.blur;
                  if (filter.type === "Saturation")
                    filterSaturationVal.value = filter.saturation;
                });
              }
            }
            break;

          case "Library":
            elements["open-png-library"].addEventListener(
              "click",
              openPngLibrary
            );
            break;

          case "View":
            elements["zoom-in"].addEventListener("click", () =>
              canvas.setZoom(canvas.getZoom() * 1.1)
            );
            elements["zoom-out"].addEventListener("click", () =>
              canvas.setZoom(canvas.getZoom() / 1.1)
            );
            elements["set-canvas-size"].addEventListener(
              "click",
              setCanvasSize
            );
            elements["toggle-grid-snap"].addEventListener(
              "click",
              toggleGridSnap
            );
            elements["bring-to-front"].addEventListener("click", () =>
              changeZIndex("front")
            );
            elements["send-to-back"].addEventListener("click", () =>
              changeZIndex("back")
            );
            elements["bring-forward"].addEventListener("click", () =>
              changeZIndex("forward")
            );
            elements["send-backward"].addEventListener("click", () =>
              changeZIndex("backward")
            );
            elements["align-left"].addEventListener("click", () =>
              alignObjects("left")
            );
            elements["align-center"].addEventListener("click", () =>
              alignObjects("center")
            );
            elements["align-right"].addEventListener("click", () =>
              alignObjects("right")
            );
            elements["align-top"].addEventListener("click", () =>
              alignObjects("top")
            );
            elements["align-middle"].addEventListener("click", () =>
              alignObjects("middle")
            );
            elements["align-bottom"].addEventListener("click", () =>
              alignObjects("bottom")
            );
            elements["duplicate-object"].addEventListener(
              "click",
              duplicateObject
            );
            elements["delete-object"].addEventListener("click", deleteObject);
            elements["group-objects"].addEventListener("click", groupObjects);
            elements["ungroup-objects"].addEventListener(
              "click",
              ungroupObjects
            );
            elements["lock-object"].addEventListener("click", () =>
              lockObject(true)
            );
            elements["unlock-object"].addEventListener("click", () =>
              lockObject(false)
            );
            elements["flip-horizontal"].addEventListener("click", () =>
              flipObject("h")
            );
            elements["flip-vertical"].addEventListener("click", () =>
              flipObject("v")
            );
            elements["undo-action"].addEventListener("click", undo);
            elements["redo-action"].addEventListener("click", redo);
            break;

          case "Export":
            elements["export-png"].addEventListener("click", () =>
              exportCanvas("png")
            );
            elements["export-jpeg"].addEventListener("click", () =>
              exportCanvas("jpeg")
            );
            elements["export-svg"].addEventListener("click", () =>
              exportCanvas("svg")
            );
            elements["export-json"].addEventListener("click", () =>
              exportCanvas("json")
            );
            break;
        }
      }

      // --- Initial Setup and Responsive Handling ---
      // Initialize with a default category, e.g., 'Draw', on document load.
      document.addEventListener("DOMContentLoaded", () => {
        displaySubToolbar("Draw"); // Show Draw tools by default
        
        // Add event listener for the drawing toggle button
        document.getElementById('startDrawing').addEventListener('click', toggleDrawingMode);
        setupDrawingEventHandlers(); // Initialize drawing event handlers
        
        // Set up clear drawings button
        document.getElementById('clear-drawings').addEventListener('click', clearDrawings);
        
        // Set up drawing mode toggle
        document.querySelector('[data-category="Draw"]').addEventListener('click', () => {
          toggleDrawingMode();
        });
        resizeCanvas(); // Perform initial canvas resize check
      });

      // Event listener for window resize to handle canvas container responsiveness
      window.addEventListener("resize", resizeCanvas);

      // --- وظائف أدوات الرسم الجديدة ---
      function setupDrawingEventHandlers1Draw() {
        const drawingColorEl = document.getElementById("drawing-color");
        const drawingShadowColorEl = document.getElementById(
          "drawing-shadow-color"
        );
        const drawingLineWidthEl =
          document.getElementById("drawing-line-width");
        const drawingShadowWidth = document.getElementById(
          "drawing-shadow-width"
        );
        const drawingShadowOffset = document.getElementById(
          "drawing-shadow-offset"
        );
        const selectorEl = document.getElementById("drawing-mode-selector");

        if (!drawingColorEl) return; // لم يتم تفعيل أدوات الرسم بعد

        function updateBrush() {
          const brush = canvas.freeDrawingBrush;
          if (!brush) return;
          brush.color = drawingColorEl.value;
          brush.width = parseInt(drawingLineWidthEl.value, 10) || 1;
          brush.shadow = new fabric.Shadow({
            blur: parseInt(drawingShadowWidth.value, 10) || 0,
            offsetX: parseInt(drawingShadowOffset.value, 10) || 0,
            offsetY: parseInt(drawingShadowOffset.value, 10) || 0,
            color: drawingShadowColorEl.value,
          });
        }

        function handleDrawingModeChange() {
          const val = selectorEl.value;
          if (val === "hline") {
            const brush = new fabric.PatternBrush(canvas);
            brush.getPatternSrc = () => {
              const patternCanvas = document.createElement("canvas");
              patternCanvas.width = patternCanvas.height = 10;
              const ctx = patternCanvas.getContext("2d");
              ctx.strokeStyle = brush.color;
              ctx.lineWidth = 5;
              ctx.beginPath();
              ctx.moveTo(0, 5);
              ctx.lineTo(10, 5);
              ctx.stroke();
              return patternCanvas;
            };
            canvas.freeDrawingBrush = brush;
          } else if (val === "vline") {
            const brush = new fabric.PatternBrush(canvas);
            brush.getPatternSrc = () => {
              const patternCanvas = document.createElement("canvas");
              patternCanvas.width = patternCanvas.height = 10;
              const ctx = patternCanvas.getContext("2d");
              ctx.strokeStyle = brush.color;
              ctx.lineWidth = 5;
              ctx.beginPath();
              ctx.moveTo(5, 0);
              ctx.lineTo(5, 10);
              ctx.stroke();
              return patternCanvas;
            };
            canvas.freeDrawingBrush = brush;
          } else {
            const brushClass = fabric[val + "Brush"] || fabric.PencilBrush;
            canvas.freeDrawingBrush = new brushClass(canvas);
          }
          updateBrush();
        }

        drawingColorEl.addEventListener("change", updateBrush);
        drawingShadowColorEl.addEventListener("change", updateBrush);
        drawingLineWidthEl.addEventListener("input", () => {
          document.getElementById("line-width-info").textContent =
            drawingLineWidthEl.value;
          updateBrush();
        });
        drawingShadowWidth.addEventListener("input", () => {
          document.getElementById("shadow-width-info").textContent =
            drawingShadowWidth.value;
          updateBrush();
        });
        drawingShadowOffset.addEventListener("input", () => {
          document.getElementById("shadow-offset-info").textContent =
            drawingShadowOffset.value;
          updateBrush();
        });
        selectorEl.addEventListener("change", handleDrawingModeChange);

        // تفعيل وضع الرسم عند اختيار أدوات الرسم
        canvas.isDrawingMode = true;
        handleDrawingModeChange();
      }

      // استبدال زر مسح الرسومات:
      function clearDrawings1Draw() {
        // إزالة كل العناصر ما عدا الصور والخلفية
        const objects = canvas.getObjects();
        objects.forEach((obj) => {
          if (
            obj.type === "path" ||
            obj.type === "line" ||
            obj.type === "circle" ||
            obj.type === "polygon" ||
            obj.type === "polyline"
          ) {
            canvas.remove(obj);
          }
        });
        saveCanvasState();
        canvas.renderAll();
      }

      // أضف في attachSubToolbarListeners بعد case 'Draw':
      if (category === "Draw") {
        setupDrawingEventHandlers1Draw();
        const clearBtn = document.getElementById("clear-drawings");
        if (clearBtn) clearBtn.addEventListener("click", clearDrawings1Draw);
      }
    </script>
  </body>
</html>
