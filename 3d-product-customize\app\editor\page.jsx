'use client';

import { useEffect, useRef } from 'react';

export default function EditorPage() {
  const canvasRef = useRef(null);
  const threePreviewRef = useRef(null);
  const editorInitialized = useRef(false);

  useEffect(() => {
    // Load Fabric.js and Three.js
    const loadLibraries = async () => {
      try {
        // Create script elements for external libraries
        const fabricScript = document.createElement('script');
        fabricScript.src = "https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.1/fabric.min.js";
        
        const threeScript = document.createElement('script');
        threeScript.src = "https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js";
        
        // Append to document
        document.body.appendChild(fabricScript);
        document.body.appendChild(threeScript);
        
        // Wait for libraries to load
        await new Promise((resolve) => {
          fabricScript.onload = () => {
            threeScript.onload = resolve;
          };
        });
        
        // Load our scripts in order
        const scripts = [
          '/editor-core.js',
          '/control-panel.js',
          '/canvas-size-manager.js',
          '/history.js', 
          '/three-d-sync.js',
          '/font-manager.js',
          '/alignment-guides.js',
          '/enhanced-main.js', // Using the enhanced version
        ];
        
        // Load each script in sequence
        for (const src of scripts) {
          await new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.onload = resolve;
            script.onerror = reject;
            document.body.appendChild(script);
          });
        }
        
        console.log("All scripts loaded - editor initialized");
        editorInitialized.current = true;
      } catch (error) {
        console.error('Error loading editor dependencies:', error);
      }
    };
    
    if (!editorInitialized.current && canvasRef.current) {
      loadLibraries();
    }
    
    return () => {
      // Clean up if needed when component unmounts
    };
  }, []);

  return (
    <div className="editor-container" style={{ 
      display: 'flex',
      flexDirection: 'column',
      height: '100vh',
      margin: 0,
      fontFamily: 'Arial, sans-serif',
      backgroundColor: '#f5f5f5'
    }}>
      <div className="editor-header" style={{ 
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: '10px 20px',
        backgroundColor: '#2c3e50',
        color: 'white'
      }}>
        <div className="editor-title" style={{ fontSize: '20px', fontWeight: 'bold' }}>3D Product Customizer</div>
        <div className="user-controls" style={{ display: 'flex', alignItems: 'center' }}>
          <span className="user-name" style={{ marginRight: '15px' }}>Guest User</span>
          <a href="#" style={{ color: 'white', textDecoration: 'none', marginLeft: '15px' }}>
            Dashboard
          </a>
        </div>
      </div>
      
      <div className="toolbar" style={{
        display: 'flex',
        padding: '10px',
        backgroundColor: '#34495e',
        color: 'white'
      }}>
        <div className="toolbar-section" style={{ display: 'flex', marginRight: '20px' }}>
          <h3 style={{ margin: '0 10px 0 0', fontSize: '14px', lineHeight: '32px' }}>Add</h3>
          <button id="add-text-btn" style={{
            marginRight: '5px',
            padding: '5px 10px',
            backgroundColor: '#555',
            color: 'white',
            border: 'none',
            borderRadius: '3px',
            cursor: 'pointer'
          }}>Text</button>
          <button id="add-image-btn" style={{
            marginRight: '5px',
            padding: '5px 10px',
            backgroundColor: '#555',
            color: 'white',
            border: 'none',
            borderRadius: '3px',
            cursor: 'pointer'
          }}>Image</button>
        </div>
        
        <div className="toolbar-section" style={{ display: 'flex', marginRight: '20px' }}>
          <h3 style={{ margin: '0 10px 0 0', fontSize: '14px', lineHeight: '32px' }}>Edit</h3>
          <button id="delete-btn" style={{
            marginRight: '5px',
            padding: '5px 10px',
            backgroundColor: '#555',
            color: 'white',
            border: 'none',
            borderRadius: '3px',
            cursor: 'pointer'
          }}>Delete</button>
          <button id="undo-btn" style={{
            marginRight: '5px',
            padding: '5px 10px',
            backgroundColor: '#555',
            color: 'white',
            border: 'none',
            borderRadius: '3px',
            cursor: 'pointer'
          }}>Undo</button>
          <button id="redo-btn" style={{
            marginRight: '5px',
            padding: '5px 10px',
            backgroundColor: '#555',
            color: 'white',
            border: 'none',
            borderRadius: '3px',
            cursor: 'pointer'
          }}>Redo</button>
        </div>
        
        <div className="toolbar-section" style={{ display: 'flex', marginRight: '20px' }}>
          <h3 style={{ margin: '0 10px 0 0', fontSize: '14px', lineHeight: '32px' }}>Arrange</h3>
          <button id="group-btn" style={{
            marginRight: '5px',
            padding: '5px 10px',
            backgroundColor: '#555',
            color: 'white',
            border: 'none',
            borderRadius: '3px',
            cursor: 'pointer'
          }}>Group</button>
          <button id="ungroup-btn" style={{
            marginRight: '5px',
            padding: '5px 10px',
            backgroundColor: '#555',
            color: 'white',
            border: 'none',
            borderRadius: '3px',
            cursor: 'pointer'
          }}>Ungroup</button>
        </div>
        
        <div className="toolbar-section" style={{ display: 'flex', marginRight: '20px' }}>
          <h3 style={{ margin: '0 10px 0 0', fontSize: '14px', lineHeight: '32px' }}>Export</h3>
          <button id="export-btn" style={{
            marginRight: '5px',
            padding: '5px 10px',
            backgroundColor: '#555',
            color: 'white',
            border: 'none',
            borderRadius: '3px',
            cursor: 'pointer'
          }}>Export Image</button>
          <button id="export-json-btn" style={{
            marginRight: '5px',
            padding: '5px 10px',
            backgroundColor: '#555',
            color: 'white',
            border: 'none',
            borderRadius: '3px',
            cursor: 'pointer'
          }}>Export JSON</button>
          <button id="import-json-btn" style={{
            marginRight: '5px',
            padding: '5px 10px',
            backgroundColor: '#555',
            color: 'white',
            border: 'none',
            borderRadius: '3px',
            cursor: 'pointer'
          }}>Import JSON</button>
        </div>
      </div>
      
      <div className="canvas-container" style={{
        flex: 1,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        padding: '20px',
        overflow: 'auto'
      }}>
        <canvas id="fabric-canvas" ref={canvasRef} style={{
          backgroundColor: 'white',
          boxShadow: '0 0 10px rgba(0, 0, 0, 0.1)'
        }}></canvas>
      </div>
      
      <div className="preview-container" style={{
        position: 'fixed',
        bottom: '20px',
        left: '20px',
        width: '300px',
        height: '200px',
        backgroundColor: '#333',
        borderRadius: '5px',
        overflow: 'hidden'
      }}>
        <h3 style={{
          margin: 0,
          padding: '10px',
          backgroundColor: '#222',
          color: 'white',
          fontSize: '14px'
        }}>3D Preview</h3>
        <div className="preview-content" style={{
          height: 'calc(100% - 35px)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center'
        }}>
          <canvas id="three-preview" ref={threePreviewRef} style={{
            maxWidth: '100%',
            maxHeight: '100%'
          }}></canvas>
        </div>
      </div>
    </div>
  );
}
