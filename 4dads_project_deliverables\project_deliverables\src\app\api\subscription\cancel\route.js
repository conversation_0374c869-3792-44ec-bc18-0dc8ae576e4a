// src/app/api/subscription/cancel/route.js
// [!] هذا الملف يتعامل مع طلبات إلغاء الاشتراكات
// [!] يجب تعديل متغيرات الاتصال بقاعدة البيانات وPayPal حسب إعداداتك

import { NextResponse } from 'next/server';

// [!] متغيرات يجب تعديلها: DB_HOST, DB_USER, DB_PASSWORD, DB_NAME, PAYPAL_CLIENT_ID, PAYPAL_SECRET
const DB_CONFIG = {
  host: 'DB_HOST',      // مثال: 'mysql-4dads.hostinger.com'
  user: 'DB_USER',      // مثال: 'u123456789_4dads'
  password: 'DB_PASSWORD', // كلمة المرور الخاصة بقاعدة البيانات
  database: 'DB_NAME',  // مثال: 'u123456789_4dads'
};

const PAYPAL_CONFIG = {
  clientId: 'PAYPAL_CLIENT_ID',
  secret: 'PAYPAL_SECRET',
  environment: 'sandbox' // استخدم 'production' في بيئة الإنتاج
};

export async function POST(request) {
  try {
    const { subscription_id, paypal_subscription_id } = await request.json();

    // التحقق من البيانات المدخلة
    if (!subscription_id || !paypal_subscription_id) {
      return NextResponse.json(
        { success: false, message: 'بيانات الاشتراك غير مكتملة' },
        { status: 400 }
      );
    }

    // [!] في بيئة الإنتاج، يجب إلغاء الاشتراك في PayPal أولاً
    // هذا مثال لكيفية تنفيذ ذلك
    
    /*
    // الحصول على رمز الوصول من PayPal
    const auth = Buffer.from(`${PAYPAL_CONFIG.clientId}:${PAYPAL_CONFIG.secret}`).toString('base64');
    const tokenResponse = await fetch(`https://api.${PAYPAL_CONFIG.environment === 'sandbox' ? 'sandbox.' : ''}paypal.com/v1/oauth2/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${auth}`
      },
      body: 'grant_type=client_credentials'
    });
    
    const { access_token } = await tokenResponse.json();
    
    // إلغاء الاشتراك في PayPal
    const cancelResponse = await fetch(`https://api.${PAYPAL_CONFIG.environment === 'sandbox' ? 'sandbox.' : ''}paypal.com/v1/billing/subscriptions/${paypal_subscription_id}/cancel`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${access_token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        reason: 'User requested cancellation'
      })
    });
    
    if (!cancelResponse.ok) {
      const errorData = await cancelResponse.json();
      throw new Error(`PayPal cancellation failed: ${errorData.message}`);
    }
    
    // استيراد مكتبة mysql2
    import mysql from 'mysql2/promise';
    
    // إنشاء اتصال بقاعدة البيانات
    const connection = await mysql.createConnection(DB_CONFIG);
    
    // تحديث حالة الاشتراك في قاعدة البيانات
    await connection.execute(
      'UPDATE user_subscriptions SET status = ?, auto_renew = ?, updated_at = NOW() WHERE id = ?',
      ['cancelled', false, subscription_id]
    );
    
    // إغلاق الاتصال بقاعدة البيانات
    await connection.end();
    */
    
    // للاختبار، نعيد استجابة نجاح
    return NextResponse.json(
      { 
        success: true, 
        message: 'تم إلغاء الاشتراك بنجاح',
        subscription: {
          id: subscription_id,
          status: 'cancelled',
          auto_renew: false
        }
      },
      { status: 200 }
    );
    
  } catch (error) {
    console.error('Subscription cancellation error:', error);
    return NextResponse.json(
      { success: false, message: 'حدث خطأ في الخادم أثناء إلغاء الاشتراك' },
      { status: 500 }
    );
  }
}
