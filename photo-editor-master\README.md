# Photo Editor

> A photo editing application based on the [Cropper.js](https://github.com/fengyuanchen/cropperjs).

- [Website](https://fengyuanchen.github.io/photo-editor)

## Keyboard support

- `Esc`: Cancel the cropping.
- `Enter`: Complete the cropping.
- `Delete`: Delete the uploaded or cropped image.
- `←`: Move the image to left by 1 pixel.
- `→`: Move the image to right by 1 pixel.
- `↑`: Move the image to top by 1 pixel.
- `↓`: Move the image to bottom by 1 pixel.
- `M`: Enable to move the image by dragging.
- `C`: Enable to crop on the image by dragging.
- `I`: Zoom in the image.
- `O`: Zoom out the image.
- `L`: Rotate the image to the left.
- `R`: Rotate the image to the right.
- `H`: Flip the image on the horizontal.
- `V`: Flip the image on the vertical.

## Browser support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Opera (latest)
- Edge (latest)
- Internet Explorer 10+

## License

[MIT](https://opensource.org/licenses/MIT) © [<PERSON>](https://chenfengyuan.com/)
