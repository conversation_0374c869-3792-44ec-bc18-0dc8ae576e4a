<!DOCTYPE html>
<html lang="ar">
<head>
  <meta charset="UTF-8" />
  <!-- عرض مناسب للأجهزة المختلفة -->
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no" />
  <title>Interactive 3D Model with Real-Time Pixie Editor</title>
  <!-- خط <PERSON> -->
  <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700&display=swap" rel="stylesheet">
  <style>
    /* ----------------------------------------
       تنسيق أساسي لملء الشاشة:
       - الحاوية الرئيسية flex عمودية
       - المشهد يأخذ المساحة المتبقية
       - المحرر يأخذ ارتفاع محدد (مثلاً 30vh) ويمكن تعديله بالملف الشخصي
       - إضافة زر إخفاء/إظهار
       ---------------------------------------- */
    html, body {
      width: 100%;
      height: 100%;
      margin: 0;
      padding: 0;
      direction: rtl; /* لأن المحتوى عربي */
    }
    #app {
      display: flex;
      flex-direction: column; /* ترتيب عمودي: المشهد فوق، المحرر تحت */
      height: 100vh; /* ملء كامل ارتفاع النافذة */ 
      font-family: "Tajawal", sans-serif;
    }
    #scene-container {
      flex: 1 1 auto; /* يأخذ المساحة المتبقية */
      position: relative;
      overflow: hidden;
      background-color: #10161c;
    }
    #editor-container {
      flex: none; /* لا ينمو أو ينكمش تلقائياً */
      height: 30vh; /* يمكنك ضبط هذه القيمة حسب الرغبة */
      min-height: 150px; /* أقل ارتفاع مسموح حتى لا يصبح مخفيًّا تماماً */
      position: relative;
      overflow: hidden;
      border-top: 1px solid #444;
      transition: height 0.3s ease;
    }
    /* عند إخفاء المحرر نجعل height=0 ونخفي المحتوى */
    #editor-container.hidden {
      height: 0 !important;
      min-height: 0 !important;
      border-top: none;
    }
    /* زر الإخفاء/الإظهار */
    #toggle-editor-btn {
      position: absolute;
      top: 8px;
      left: 8px; /* يظهر على المشهد */
      z-index: 1000;
      padding: 6px 12px;
      background: rgba(0,0,0,0.5);
      color: #fff;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
    }
    #toggle-editor-btn:hover {
      background: rgba(0,0,0,0.7);
    }
    /* عند إخفاء المحرر، زر الإظهار يجب أن يظل مرئيًا */
    #show-editor-btn {
      position: absolute;
      bottom: 8px;
      right: 8px;
      z-index: 1000;
      padding: 6px 12px;
      background: rgba(0,0,0,0.5);
      color: #fff;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      display: none; /* يظهر فقط عند الإخفاء */
    }
    #show-editor-btn.visible {
      display: block;
    }
    /* تأكد من أن Pixie Editor يملأ حاويته */
    #editor-container > * {
      width: 100%;
      height: 100%;
    }
  </style>
  <!-- مكتبات Three.js و Pixie -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r134/three.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/three/examples/js/controls/OrbitControls.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/three/examples/js/loaders/GLTFLoader.js"></script>
  <!-- تأكد من المسار الصحيح لمكتبة Pixie -->
  <script src="js/pixie.umd.js?29"></script>
</head>
<body>
  <div id="app">
    <div id="scene-container">
      <!-- زر إخفاء المحرر -->
      <button id="toggle-editor-btn">إخفاء المحرر</button>
      <!-- زر إظهار المحرر عند اختفائه -->
      <button id="show-editor-btn">إظهار المحرر</button>
      <!-- المشهد ثلاثي الأبعاد سيُضاف هنا -->
    </div>
    <div id="editor-container">
      <!-- محرر Pixie سيُضاف هنا -->
    </div>
  </div>

  <script>
    // =====================================
    // التهيئة الأساسية لـ Three.js
    // =====================================
    let scene, camera, renderer, controls;
    let model, ground;
    let pixieTexture = null;
    const gltfLoader = new THREE.GLTFLoader();
    const sceneContainer = document.getElementById("scene-container");
    const editorContainer = document.getElementById("editor-container");

    function initThree() {
      scene = new THREE.Scene();
      scene.background = new THREE.Color(0x10161c);

      // أبعاد الكانفاس اعتماداً على الحاوية
      const updateSizes = () => {
        const containerWidth = sceneContainer.clientWidth;
        const containerHeight = sceneContainer.clientHeight;
        if (camera) {
          camera.aspect = containerWidth / containerHeight;
          camera.updateProjectionMatrix();
        }
        if (renderer) {
          renderer.setSize(containerWidth, containerHeight);
        }
      };

      camera = new THREE.PerspectiveCamera(60, sceneContainer.clientWidth / sceneContainer.clientHeight, 1.2, 1000);
      camera.position.set(-1, 2, 2);

      renderer = new THREE.WebGLRenderer({ antialias: true });
      renderer.setSize(sceneContainer.clientWidth, sceneContainer.clientHeight);
      renderer.shadowMap.enabled = true;
      sceneContainer.appendChild(renderer.domElement);

      controls = new THREE.OrbitControls(camera, renderer.domElement);
      controls.enableDamping = true;
      controls.dampingFactor = 0.05;

      // تحميل النموذج GLTF
      gltfLoader.load("model.gltf", function (gltf) {
        model = gltf.scene;
        model.scale.set(1, 1, 1);
        model.position.set(0.5, -1, -1);
        model.traverse((child) => {
          if (child.isMesh) {
            child.castShadow = true;
            if (pixieTexture) {
              child.material.map = pixieTexture;
              child.material.needsUpdate = true;
            }
          }
        });
        scene.add(model);
      }, undefined, function(error){
        console.error("خطأ في تحميل النموذج:", error);
      });

      // الأرض
      const groundGeometry = new THREE.PlaneGeometry(10, 10);
      const groundMaterial = new THREE.MeshStandardMaterial({ color: 0x888888 });
      ground = new THREE.Mesh(groundGeometry, groundMaterial);
      ground.rotation.x = -Math.PI / 2;
      ground.position.y = -2.2;
      ground.receiveShadow = true;
      scene.add(ground);

      // الإضاءة
      scene.add(new THREE.AmbientLight(0xedf0f3, 0.5));
      const directionalLight = new THREE.DirectionalLight(0xedf0f3, 0.5);
      directionalLight.position.set(5, 5, 5);
      directionalLight.castShadow = true;
      scene.add(directionalLight);
      const backLight = new THREE.DirectionalLight(0xffffff, 0.5);
      backLight.position.set(-1, 1, -1);
      scene.add(backLight);

      // عند تغيير حجم النافذة
      window.addEventListener("resize", () => {
        updateSizes();
      });
    }

    function animateThree() {
      requestAnimationFrame(animateThree);
      controls.update();
      if (pixieTexture) {
        pixieTexture.needsUpdate = true;
      }
      renderer.render(scene, camera);
    }

    initThree();
    animateThree();

    // =====================================
    // تهيئة Pixie Editor
    // =====================================
    const pixie = new Pixie({
      selector: "#editor-container",
      baseUrl: "../assets",
      image: "",
      activeLanguage: "en",
      onLoad: function() {
        console.log("Pixie Editor Loaded");
      },
      ui: {
        activeTheme: "i2img",
        themes: [{
          name: "i2img",
          colors: {
            "--be-primary": "#7961f2",
            "--be-primary-light": "#d0caff",
            "--be-primary-dark": "#5139ff",
          }
        }],
        nav: {
          position: "top",
          replaceDefault: false,
          items: [{
            name: "Watermark",
            icon: [{
              tag: "path",
              attr: {
                d: "M18 20H4V6h9V4H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-9h-2v9zm-7.79-3.17-1.96-2.36L5.5 18h11l-3.54-4.71zM20 4V1h-2v3h-3c.01.01 0 2 0 2h3v2.99c.01.01 2 0 2 0V6h-3V4h-3z"
              }
            }],
            action: function(editor) {
              editor.tools.import.uploadAndAddImage();
            }
          }]
        },
        menubar: {
          items: [{
            type: "button",
            icon: [{
              tag: "path",
              attr: {
                d: "m11.99 18.54-7.37-5.73L3 14.07l9 7 9-7-1.63-1.27zM12 16l7.36-5.73L21 9l-9-7-9 7 1.63 1.27L12 16zm0-11.47L17.74 9 12 13.47 6.26 9 12 4.53z",
              }
            }],
            align: "right",
            position: 0,
            action: editor => {
              editor.togglePanel("objects");
            }
          }]
        }
      },
      tools: {
        text: {
          defaultText: "فلسطين داري",
          replaceDefault: false,
          items: [
            {
              family: "Tajawal",
              src: "../editors/fonts/Tajawal.ttf"
            },
            {
              family: "AGA Mishmish مشمش",
              src: "../editors/fonts/arabic1.ttf"
            }
          ]
        },
        export: {
          defaultQuality: 1.0,
          defaultFormat: "png"
        }
      }
    });
    window.pixie = pixie;

    // =====================================
    // ربط Pixie Canvas بـ Three.js كنسيج
    // =====================================
    function monitorPixieCanvasUpdates() {
      const canvas = document.querySelector("#editor-container canvas");
      if (canvas) {
        if (!pixieTexture) {
          console.log("إنشاء النسيج لأول مرة...");
          pixieTexture = new THREE.CanvasTexture(canvas);
          pixieTexture.needsUpdate = true;

          if (model) {
            model.traverse(function(child) {
              if (child.isMesh) {
                child.material.map = pixieTexture;
                child.material.needsUpdate = true;
              }
            });
          }
        } else {
          pixieTexture.image = canvas;
          pixieTexture.needsUpdate = true;
        }
      }
      requestAnimationFrame(monitorPixieCanvasUpdates);
    }
    monitorPixieCanvasUpdates();

    // =====================================
    // زر إخفاء/إظهار المحرر
    // =====================================
    const toggleBtn = document.getElementById("toggle-editor-btn");
    const showBtn = document.getElementById("show-editor-btn");

    toggleBtn.addEventListener("click", () => {
      // إخفاء المحرر
      editorContainer.classList.add("hidden");
      // إخفاء زر الإخفاء نفسه، وإظهار زر الإظهار
      toggleBtn.style.display = "none";
      showBtn.classList.add("visible");
    });

    showBtn.addEventListener("click", () => {
      // إظهار المحرر
      editorContainer.classList.remove("hidden");
      // إظهار زر الإخفاء مجدداً
      toggleBtn.style.display = "block";
      showBtn.classList.remove("visible");
      // تحديث حجم المشهد فور إظهار المحرر
      setTimeout(() => {
        window.dispatchEvent(new Event('resize'));
      }, 300); // بعد انتهاء الانتقال
    });

    // عند إخفاء/إظهار المحرر، من الجيد توليد حدث resize لThree.js لإعادة ضبط الكاميرا
    // مثال: عند إعادة تحميل الصفحة تظهر الحاويات بشكل افتراضي (المحرر ظاهر).
  </script>
</body>
</html>