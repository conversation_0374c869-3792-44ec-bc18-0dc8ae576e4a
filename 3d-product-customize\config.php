<?php
// Database configuration
define('DB_SERVER', 'localhost');
define('DB_USERNAME', 'u850009485_aboud79dbdb');
define('DB_PASSWORD', '##Host@1979inger');
define('DB_NAME', 'u850009485_subscriptiondb');

// Attempt to connect to MySQL database
$conn = new mysqli(DB_SERVER, DB_USERNAME, DB_PASSWORD, DB_NAME);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// PayPal configuration
define('PAYPAL_CLIENT_ID', 'AXgPoRNaqSHSwGjkABv89PBIkQxVwz-7ZCX5EoBkkG2UYqTYDhZ9W_3ajWWr17ij30QW7QLLBZIYbYve');
define('PAYPAL_CLIENT_SECRET', 'EMmYmoDVGjvZeewKQK0cC1KdiL359YOD2HRDcx1y23tbfy1eUic6bmTqL5iNOzmPrNM1EkC5AslDmoQ4');

// Subscription Plan IDs
define('PLAN_MONTHLY', 'P-6ML527490D2009848NAFY5WA');  // $5/month
define('PLAN_ANNUAL', 'P-6V5326030C814122HNAFZAFI');   // $50/year
define('PLAN_ARAB', 'P-59T55805XP961023HNAOIPYI');     // $25/year
define('PLAN_FREE', 'P-707238101Y904923HNAOIWGY');     // $1 free plan

// Download limits for each plan
$downloadLimits = [
    'monthly' => [
        'mp4_web_videos' => 20,
        'jpg_png_images' => 50
    ],
    'annual' => [
        'mp4_web_videos' => 50,
        'jpg_png_images' => 100
    ],
    'arab' => [
        'mp4_web_videos' => 30,
        'jpg_png_images' => 70
    ],
    'free' => [
        'mp4_web_videos' => 5,
        'jpg_png_images' => 10
    ]
];

// Session timeout (in seconds)
define('SESSION_TIMEOUT', 1800); // 30 minutes

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
?>
