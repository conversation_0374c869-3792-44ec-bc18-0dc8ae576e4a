/**
 * ThreeDSync - Handles synchronization with Three.js 3D model
 */
class ThreeDSync {
  constructor(editorCore) {
    this.editor = editorCore
    this.threeJsRenderer = null
    this.textureCanvas = null
    this.textureContext = null
    this.textureSize = { width: 2048, height: 2048 } // High resolution for better text quality
    this.isInitialized = false

    // Initialize
    this.init()
  }

  /**
   * Initialize the 3D sync
   */
  init() {
    // Create high-resolution canvas for texture
    this.textureCanvas = document.createElement("canvas")
    this.textureCanvas.width = this.textureSize.width
    this.textureCanvas.height = this.textureSize.height
    this.textureContext = this.textureCanvas.getContext("2d")

    // Set flag
    this.isInitialized = true

    // Look for Three.js renderer in the page
    this.findThreeJsRenderer()
  }

  /**
   * Find Three.js renderer in the page
   */
  findThreeJsRenderer() {
    // This is a placeholder - in a real implementation, you would
    // either have a direct reference to the Three.js renderer
    // or use a custom event system to communicate with it

    // For demonstration, we'll assume the Three.js renderer
    // exposes itself globally or through a custom event

    window.addEventListener("threeJsRendererReady", (event) => {
      this.threeJsRenderer = event.detail.renderer
      console.log("Three.js renderer connected")
    })

    // Check if it's already available
    if (window.threeJsRenderer) {
      this.threeJsRenderer = window.threeJsRenderer
      console.log("Three.js renderer found")
    }
  }

  /**
   * Sync Fabric.js canvas with Three.js texture
   */
  syncWithThreeJs() {
    if (!this.isInitialized) return

    // Clear the texture canvas
    this.textureContext.clearRect(0, 0, this.textureSize.width, this.textureSize.height)

    // Scale factor for high-resolution texture - improved clarity with higher DPI
    const scaleFactor = this.textureSize.width / this.editor.canvas.width
    
    // Before drawing, optimize text for 3D viewing if needed
    this.optimizeTextFor3D()

    // Draw the Fabric.js canvas onto the texture canvas with enhanced quality
    this.textureContext.save()
    this.textureContext.scale(scaleFactor, scaleFactor)
    // Enable high-quality image rendering
    this.textureContext.imageSmoothingEnabled = true
    this.textureContext.imageSmoothingQuality = 'high'

    // Get Fabric.js canvas content as an image
    const dataUrl = this.editor.canvas.toDataURL({
      format: "png",
      multiplier: 1,
    })

    const img = new Image()
    img.onload = () => {
      this.textureContext.drawImage(img, 0, 0)
      this.textureContext.restore()

      // Update Three.js texture
      this.updateThreeJsTexture()
    }
    img.src = dataUrl
  }

  /**
   * Update Three.js texture with the current canvas content
   */
  updateThreeJsTexture() {
    if (!this.threeJsRenderer) {
      console.warn("Three.js renderer not found")
      return
    }

    // Convert texture canvas to data URL
    const textureDataUrl = this.textureCanvas.toDataURL("image/png")

    // Dispatch event for Three.js to update texture
    const event = new CustomEvent("fabricTextureUpdate", {
      detail: {
        textureDataUrl: textureDataUrl,
      },
    })
    window.dispatchEvent(event)

    console.log("Texture updated and sent to Three.js")
  }

  /**
   * Optimize text for 3D rendering
   * This improves text clarity on 3D models
   */
  optimizeTextFor3D() {
    const objects = this.editor.canvas.getObjects()

    objects.forEach((obj) => {
      if (obj.type === "i-text" || obj.type === "text") {
        // Add slight stroke to text for better visibility on textures
        if (!obj.stroke) {
          const textColor = obj.fill

          // Create a contrasting stroke color
          let strokeColor
          if (this.isLightColor(textColor)) {
            strokeColor = "rgba(0, 0, 0, 0.3)"
          } else {
            strokeColor = "rgba(255, 255, 255, 0.3)"
          }

          obj.set({
            stroke: strokeColor,
            strokeWidth: 0.5,
          })
        }
      }
    })

    this.editor.canvas.renderAll()
  }

  /**
   * Check if a color is light or dark
   */
  isLightColor(color) {
    // Convert color to RGB
    let r, g, b

    if (color.startsWith("#")) {
      // Hex color
      r = Number.parseInt(color.slice(1, 3), 16)
      g = Number.parseInt(color.slice(3, 5), 16)
      b = Number.parseInt(color.slice(5, 7), 16)
    } else if (color.startsWith("rgb")) {
      // RGB color
      const match = color.match(/(\d+),\s*(\d+),\s*(\d+)/)
      if (match) {
        r = Number.parseInt(match[1])
        g = Number.parseInt(match[2])
        b = Number.parseInt(match[3])
      } else {
        return true // Default to light if parsing fails
      }
    } else {
      return true // Default to light for unknown formats
    }

    // Calculate perceived brightness
    // Formula: (0.299*R + 0.587*G + 0.114*B)
    const brightness = (0.299 * r + 0.587 * g + 0.114 * b) / 255

    // Return true if light, false if dark
    return brightness > 0.5
  }

  /**
   * Set texture size
   */
  setTextureSize(width, height) {
    this.textureSize = { width, height }
    this.textureCanvas.width = width
    this.textureCanvas.height = height

    // Re-sync after changing size
    this.syncWithThreeJs()
  }
}
