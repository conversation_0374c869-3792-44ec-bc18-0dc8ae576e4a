<!DOCTYPE html>
<html lang="ar">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
  <title>Fabric.js Image & Text Editor</title>

  <!-- Font Awesome for Icons -->
  <link
    rel="stylesheet"
    href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
  />
  <!-- Fabric.js -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/6.7.0/fabric.min.js"></script>

  <style>
    html, body { margin:0; padding:0; height:100%; overflow:hidden; direction:rtl; }
    #canvas { border:1px solid #ccc; display:block; margin:0 auto; background:#fff; }

    /* زر إظهار/إخفاء شريط الأدوات */
    .toolbar-toggle {
      position: absolute; top:10px; right:10px;
      width:40px; height:40px; border:none; border-radius:4px;
      background:#444; color:#fff; cursor:pointer; z-index:1000;
      touch-action: manipulation;
    }
    .toolbar-toggle i { font-size:18px; }

    /* شريط الأدوات الرئيسي */
    #toolbar {
      position: absolute; top:60px; right:10px;
      display:none; flex-direction: column;
      gap:10px; z-index:1000;
    }
    #toolbar .tool-group {
      display:flex; gap:8px;
      background:rgba(255,255,255,0.9);
      padding:6px; border-radius:4px;
    }
    .tool-btn {
      background:#eee; border:none; width:36px; height:36px;
      border-radius:4px; cursor:pointer; position:relative;
    }
    .tool-btn i { font-size:16px; color:#333; }
    .tool-btn:hover { background:#ddd; }

    /* اللوحات الفرعية */
    .sub-panel {
      display:none; flex-wrap: wrap; gap:6px;
      background:rgba(255,255,255,0.95); padding:6px;
      border-radius:4px;
    }
    .sub-panel button {
      background:#fafafa; border:none; width:36px; height:36px;
      border-radius:4px; cursor:pointer; position:relative;
    }
    .sub-panel button i { font-size:15px; color:#333; }
    .sub-panel button:hover { background:#ddd; }

    /* التلميحات */
    .tool-btn[title]:hover:after,
    .sub-panel button[title]:hover:after {
      content: attr(title);
      position: absolute; top:-28px; right:50%;
      transform: translateX(50%);
      background:rgba(0,0,0,0.75); color:#fff;
      padding:2px 6px; font-size:12px; white-space:nowrap;
      border-radius:3px; pointer-events:none;
      z-index:2000;
    }
  </style>
</head>
<body>

  <!-- زر تبديل ظهور الأدوات -->
  <button id="btn-toggle-toolbar" class="toolbar-toggle" title="إظهار الأدوات">
    <i class="fas fa-bars"></i>
  </button>

  <!-- شريط الأدوات -->
  <div id="toolbar">
    <!-- مجموعة الفئات -->
    <div class="tool-group">
      <button class="tool-btn" data-panel="panel-image" title="أدوات الصورة">
        <i class="fas fa-image"></i>
      </button>
      <button class="tool-btn" data-panel="panel-draw" title="أدوات الرسم الحر">
        <i class="fas fa-paint-brush"></i>
      </button>
      <button class="tool-btn" data-panel="panel-shape" title="أشكال">
        <i class="fas fa-shapes"></i>
      </button>
      <button class="tool-btn" data-panel="panel-text" title="أدوات النص">
        <i class="fas fa-font"></i>
      </button>
      <button class="tool-btn" data-panel="panel-filter" title="فلاتر">
        <i class="fas fa-adjust"></i>
      </button>
      <button class="tool-btn" data-panel="panel-layer" title="طبقات وتعديل">
        <i class="fas fa-layer-group"></i>
      </button>
      <button class="tool-btn" data-panel="panel-edit" title="تراجع/إعادة">
        <i class="fas fa-undo"></i>
      </button>
    </div>

    <!-- لوحات الأدوات الفرعية -->
    <div id="panel-image" class="sub-panel">
      <button data-action="upload-image" title="رفع صورة"><i class="fas fa-upload"></i></button>
      <button data-action="crop" title="قص"><i class="fas fa-crop"></i></button>
      <button data-action="resize" title="تغيير الحجم"><i class="fas fa-expand-arrows-alt"></i></button>
      <button data-action="rotate" title="تدوير 90°"><i class="fas fa-rotate-right"></i></button>
      <button data-action="flipH" title="انعكاس أفقي"><i class="fas fa-arrows-alt-h"></i></button>
      <button data-action="flipV" title="انعكاس عمودي"><i class="fas fa-arrows-alt-v"></i></button>
    </div>

    <div id="panel-draw" class="sub-panel">
      <button data-action="enableDraw" title="تفعيل/تعطيل الرسم"><i class="fas fa-pencil-alt"></i></button>
      <button data-action="pencilBrush" title="فرشاة خط"><i class="fas fa-pen-nib"></i></button>
      <button data-action="circleBrush" title="فرشاة دوائر"><i class="fas fa-circle"></i></button>
      <button data-action="sprayBrush" title="فرشاة رش"><i class="fas fa-spray-can"></i></button>
      <button data-action="patternBrush" title="فرشاة نقش"><i class="fas fa-th"></i></button>
    </div>

    <div id="panel-shape" class="sub-panel">
      <button data-action="addRect" title="مستطيل"><i class="fas fa-square"></i></button>
      <button data-action="addCircle" title="دائرة"><i class="fas fa-circle"></i></button>
      <button data-action="addTriangle" title="مثلث"><i class="fas fa-play"></i></button>
      <button data-action="addLine" title="خط"><i class="fas fa-minus"></i></button>
      <button data-action="addPolygon" title="مضلع"><i class="fas fa-draw-polygon"></i></button>
    </div>

    <div id="panel-text" class="sub-panel">
      <button data-action="addText" title="إضافة نص"><i class="fas fa-font"></i></button>
    </div>

    <div id="panel-filter" class="sub-panel">
      <button data-action="grayscale" title="رمادي"><i class="fas fa-adjust"></i></button>
      <button data-action="sepia" title="سيبيا"><i class="fas fa-leaf"></i></button>
      <button data-action="invert" title="عكس"><i class="fas fa-exchange-alt"></i></button>
      <button data-action="blur" title="تمويه"><i class="fas fa-eye-slash"></i></button>
      <button data-action="brightness" title="سطوع"><i class="fas fa-sun"></i></button>
      <button data-action="contrast" title="تباين"><i class="fas fa-adjust"></i></button>
    </div>

    <div id="panel-layer" class="sub-panel">
      <button data-action="bringForward" title="إلى الأمام"><i class="fas fa-level-up-alt"></i></button>
      <button data-action="sendBackwards" title="إلى الخلف"><i class="fas fa-level-down-alt"></i></button>
      <button data-action="group" title="تجميع"><i class="fas fa-object-group"></i></button>
      <button data-action="ungroup" title="إلغاء تجميع"><i class="fas fa-object-ungroup"></i></button>
      <button data-action="zoomIn" title="تكبير"><i class="fas fa-search-plus"></i></button>
      <button data-action="zoomOut" title="تصغير"><i class="fas fa-search-minus"></i></button>
    </div>

    <div id="panel-edit" class="sub-panel">
      <button data-action="undo" title="تراجع"><i class="fas fa-undo"></i></button>
      <button data-action="redo" title="إعادة"><i class="fas fa-redo"></i></button>
      <button data-action="delete" title="حذف"><i class="fas fa-trash"></i></button>
      <button data-action="save" title="حفظ"><i class="fas fa-save"></i></button>
    </div>
  </div>

  <!-- لوحة الرسم -->
  <canvas id="canvas" width="1000" height="600"></canvas>

  <script>
    // === إعداد الـ Canvas وحفظ التاريخ (تراجع/إعادة) ===
    const canvas = new fabric.Canvas('canvas', { preserveObjectStacking: true });
    let state = [], modIndex = -1;

    function saveState() {
      modIndex++;
      state.length = modIndex;
      state.push(canvas.toJSON());
    }
    canvas.on('object:added',    saveState);
    canvas.on('object:modified', saveState);
    canvas.on('object:removed',  saveState);

    // === تبديل ظهور شريط الأدوات ===
    const btnToggle    = document.getElementById('btn-toggle-toolbar');
    const toolbar      = document.getElementById('toolbar');

    function toggleToolbar(e) {
      e.preventDefault();
      const isFlex = toolbar.style.display === 'flex';
      toolbar.style.display = isFlex ? 'none' : 'flex';
    }
    btnToggle.addEventListener('click',      toggleToolbar);
    btnToggle.addEventListener('pointerdown',toggleToolbar);
    btnToggle.addEventListener('touchstart', toggleToolbar);

    // === عرض اللوحات الفرعية عند اختيار فئة ===
    const panels     = document.querySelectorAll('.sub-panel');
    const toolBtns   = document.querySelectorAll('.tool-btn');

    function hideAllPanels() {
      panels.forEach(p => p.style.display = 'none');
    }
    toolBtns.forEach(btn => {
      function showPanel(e) {
        e.preventDefault();
        hideAllPanels();
        document.getElementById(btn.dataset.panel).style.display = 'flex';
      }
      btn.addEventListener('click',      showPanel);
      btn.addEventListener('pointerdown',showPanel);
    });

    // === معالج أزرار الأدوات الفرعية ===
    const actionButtons = document.querySelectorAll('[data-action]');
    actionButtons.forEach(btn => {
      function handler(e) {
        e.preventDefault();
        const action = btn.dataset.action;
        if (actions[action]) actions[action]();
      }
      btn.addEventListener('click',      handler);
      btn.addEventListener('pointerdown',handler);
      btn.addEventListener('touchstart', handler);
    });

    // === دوال الأدوات ===
    const actions = {
      // أدوات الصور
      'upload-image': () => {
        const inp = document.createElement('input');
        inp.type = 'file'; inp.accept = 'image/*';
        inp.onchange = e => {
          const file = e.target.files[0];
          const reader = new FileReader();
          reader.onload = f => {
            fabric.Image.fromURL(f.target.result, img => {
              img.set({ left:100, top:50, cornerSize:8 });
              canvas.add(img).setActiveObject(img);
            });
          };
          reader.readAsDataURL(file);
        };
        inp.click();
      },
      'crop': () => {
        const obj = canvas.getActiveObject();
        if (!obj || obj.type !== 'image') return alert('اختر صورة أولاً');
        const left = obj.left, top = obj.top;
        const w = obj.width * obj.scaleX, h = obj.height * obj.scaleY;
        const dataURL = canvas.toDataURL({ left, top, width: w, height: h });
        fabric.Image.fromURL(dataURL, img => {
          canvas.remove(obj);
          img.set({ left, top, scaleX:1, scaleY:1 });
          canvas.add(img).setActiveObject(img);
        });
      },
      'resize': () => {
        const obj = canvas.getActiveObject();
        if (!obj) return alert('اختر عنصراً');
        const w = parseInt(prompt('العرض الجديد(px):', obj.width * obj.scaleX));
        const h = parseInt(prompt('الطول الجديد(px):', obj.height * obj.scaleY));
        if (w>0 && h>0) {
          obj.scaleX = w/obj.width;
          obj.scaleY = h/obj.height;
          canvas.requestRenderAll();
        }
      },
      'rotate': () => {
        const obj = canvas.getActiveObject();
        if (obj) {
          obj.rotate((obj.angle + 90) % 360);
          canvas.requestRenderAll();
        }
      },
      'flipH': () => {
        const obj = canvas.getActiveObject();
        if (obj) { obj.toggle('flipX'); canvas.requestRenderAll(); }
      },
      'flipV': () => {
        const obj = canvas.getActiveObject();
        if (obj) { obj.toggle('flipY'); canvas.requestRenderAll(); }
      },

      // رسم حر
      'enableDraw': () => {
        canvas.isDrawingMode = !canvas.isDrawingMode;
        alert('وضع الرسم الحر ' + (canvas.isDrawingMode ? 'مفعل': 'معطل'));
      },
      'pencilBrush': () => { canvas.freeDrawingBrush = new fabric.PencilBrush(canvas); },
      'circleBrush': () => { canvas.freeDrawingBrush = new fabric.CircleBrush(canvas); },
      'sprayBrush': () => { canvas.freeDrawingBrush = new fabric.SprayBrush(canvas); },
      'patternBrush': () => {
        const img = new Image();
        img.src = 'https://fabricjs.com/assets/5.gif';
        img.onload = () => {
          const p = new fabric.PatternBrush(canvas);
          p.source = img;
          canvas.freeDrawingBrush = p;
        };
      },

      // أشكال
      'addRect': () => {
        canvas.add(new fabric.Rect({
          left:100, top:50, fill:'#f55', width:100, height:60, cornerSize:8
        })).setActiveObject(canvas.item(canvas.size()-1));
      },
      'addCircle': () => {
        canvas.add(new fabric.Circle({
          left:100, top:50, radius:50, fill:'#5af', cornerSize:8
        })).setActiveObject(canvas.item(canvas.size()-1));
      },
      'addTriangle': () => {
        canvas.add(new fabric.Triangle({
          left:100, top:50, width:100, height:80, fill:'#5f5', cornerSize:8
        })).setActiveObject(canvas.item(canvas.size()-1));
      },
      'addLine': () => {
        canvas.add(new fabric.Line([50,50,200,200], {
          stroke:'#333', strokeWidth:4, cornerSize:8
        })).setActiveObject(canvas.item(canvas.size()-1));
      },
      'addPolygon': () => {
        canvas.add(new fabric.Polygon([
          { x:0,y:0 }, { x:80,y:0 }, { x:50,y:80 }, { x:-10,y:60 }
        ], {
          left:100, top:50, fill:'#fa5', cornerSize:8
        })).setActiveObject(canvas.item(canvas.size()-1));
      },

      // نصوص
      'addText': () => {
        const text = new fabric.IText('نص قابل للتحرير', {
          left:100, top:50, fontFamily:'Arial', fontSize:24, fill:'#000'
        });
        canvas.add(text).setActiveObject(text);
      },

      // فلاتر
      applyFilter: filter => {
        const obj = canvas.getActiveObject();
        if (!obj || obj.type !== 'image') return alert('اختر الصورة أولاً');
        obj.filters.push(filter);
        obj.applyFilters();
        canvas.requestRenderAll();
      },
      'grayscale':   () => actions.applyFilter(new fabric.Image.filters.Grayscale()),
      'sepia':       () => actions.applyFilter(new fabric.Image.filters.Sepia()),
      'invert':      () => actions.applyFilter(new fabric.Image.filters.Invert()),
      'blur':        () => actions.applyFilter(new fabric.Image.filters.Blur({ blur:0.5 })),
      'brightness':  () => actions.applyFilter(new fabric.Image.filters.Brightness({ brightness:0.05 })),
      'contrast':    () => actions.applyFilter(new fabric.Image.filters.Contrast({ contrast:0.05 })),

      // طبقات وتجميع
      'bringForward':   () => { const obj=canvas.getActiveObject(); if(obj) canvas.bringForward(obj); },
      'sendBackwards':  () => { const obj=canvas.getActiveObject(); if(obj) canvas.sendBackwards(obj); },
      'group':          () => {
        const sel = canvas.getActiveObjects();
        if (sel.length>1) {
          const grp = new fabric.Group(sel);
          canvas.discardActiveObject().remove(...sel).add(grp);
          canvas.setActiveObject(grp);
        }
      },
      'ungroup':        () => {
        const obj = canvas.getActiveObject();
        if (obj?.type==='group') {
          const items = obj._objects;
          obj._restoreObjectsState();
          canvas.remove(obj).add(...items).requestRenderAll();
        }
      },
      'zoomIn':         () => { let z=canvas.getZoom(); canvas.setZoom(z+0.1); },
      'zoomOut':        () => { let z=canvas.getZoom(); if(z>0.2) canvas.setZoom(z-0.1); },

      // تراجع/إعادة، حذف، حفظ
      'undo': () => {
        if (modIndex<=0) return;
        modIndex--;
        canvas.loadFromJSON(state[modIndex], () => canvas.renderAll());
      },
      'redo': () => {
        if (modIndex+1 >= state.length) return;
        modIndex++;
        canvas.loadFromJSON(state[modIndex], () => canvas.renderAll());
      },
      'delete': () => {
        const obj = canvas.getActiveObject();
        if (obj) canvas.remove(obj);
      },
      'save': () => {
        const data = canvas.toDataURL({ format:'png', quality:0.9 });
        const a = document.createElement('a');
        a.href = data; a.download = 'canvas.png'; a.click();
      }
    };
  </script>
</body>
</html>
