<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
    <?mso-application progid="Word.Document"?>
    <w:wordDocument xmlns:w="http://schemas.microsoft.com/office/word/2003/wordml"
                    xmlns:v="urn:schemas-microsoft-com:vml"
                    xmlns:w10="urn:schemas-microsoft-com:office:word"
                    xmlns:sl="http://schemas.microsoft.com/schemaLibrary/2003/core"
                    xmlns:aml="http://schemas.microsoft.com/aml/2001/core"
                    xmlns:wx="http://schemas.microsoft.com/office/word/2003/auxHint"
                    xmlns:o="urn:schemas-microsoft-com:office:office"
                    xmlns:dt="uuid:C2F41010-65B3-11d1-A29F-00AA00C14882">
    <w:body>
      
        <w:p>
          <w:r>
            <w:t>ملخص: إنشاء تطبيق ويب كامل بصفحة واحدة باستخدام HTML و CSS و JavaScript فقط، يتضمن محرر Fabric.js كامل الميزات مع جميع الأدوات والتحسينات الممكنة، بما في ذلك تحميل الصور المتعددة وتحريرها.</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>html</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>&lt;!DOCTYPE html&gt;</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>&lt;html lang=&quot;ar&quot; dir=&quot;rtl&quot;&gt;</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>&lt;head&gt;</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>    &lt;meta charset=&quot;UTF-8&quot;&gt;</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>    &lt;meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1.0&quot;&gt;</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>    &lt;title&gt;محرر Fabric.js&lt;/title&gt;</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>    &lt;script src=&quot;https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js&quot;&gt;&lt;/script&gt;</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>    &lt;script src=&quot;https://cdnjs.cloudflare.com/ajax/libs/hammer.js/2.0.8/hammer.min.js&quot;&gt;&lt;/script&gt;</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>    &lt;style&gt;</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>        body {</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>            font-family: sans-serif;</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>            margin: 0;</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>            overflow: hidden; / منع تمرير الصفحة /</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>        }</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>        .toolbar {</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>            display: flex;</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>            justify-content: space-around;</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>            align-items: center;</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>            background-color: f0f0f0;</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>            padding: 10px;</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>            overflow-x: auto; / للسماح بالتمرير الأفقي إذا كان هناك الكثير من الأدوات /</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>            white-space: nowrap; / لمنع التفاف الأزرار /</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>        }</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>        .toolbar button {</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>            background-color: ddd;</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>            border: none;</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>            padding: 8px 12px;</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>            margin: 5px;</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>            cursor: pointer;</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>            border-radius: 5px;</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>            transition: background-color 0.3s;</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>        }</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>        .toolbar button:hover {</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>            background-color: ccc;</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>        }</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>        canvas-container {</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>            display: flex;</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>            justify-content: center;</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>            align-items: center;</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>            height: calc(100vh - 100px); / ارتفاع الشاشة ناقص ارتفاع الأشرطة /</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>        }</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>        canvas {</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>            border: 1px solid 000;</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>        }</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>        .sub-toolbar {</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>            display: flex;</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>            justify-content: space-around;</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>            align-items: center;</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>            background-color: eee;</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>            padding: 10px;</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>            overflow-x: auto;</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>            white-space: nowrap;</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>        }</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>        .sub-toolbar button {</w:t>
          </w:r>
        </w:p>
      
        <w:p>
          <w:r>
            <w:t>            background-color: ddd;</w:t>
          </w:r>
        </w:p>
      
    </w:body>
    </w:wordDocument>