<?php
require_once 'config.php';

// Check if user is logged in
session_start();
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit;
}

// Get user information
$userId = $_SESSION['user_id'];
$userName = $_SESSION['full_name'];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Choose Your Subscription - 3D Product Customizer</title>
    <link rel="stylesheet" href="styles/subscription.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
    <div class="subscription-container">
        <div class="subscription-header">
            <h1>Choose Your Subscription Plan</h1>
            <p>Select the plan that works best for you</p>
        </div>
        
        <div class="subscription-plans">
            <!-- Free Plan -->
            <div class="plan-card">
                <div class="plan-name">Free Plan</div>
                <div class="plan-price">$1 <small>one-time payment</small></div>
                <ul class="plan-features">
                    <li><strong><?php echo $downloadLimits['free']['mp4_web_videos']; ?></strong> video downloads per month</li>
                    <li><strong><?php echo $downloadLimits['free']['jpg_png_images']; ?></strong> image downloads per month</li>
                    <li>Basic customization options</li>
                    <li>Standard resolution exports</li>
                </ul>
                <div id="paypal-button-container-free"></div>
            </div>
            
            <!-- Monthly Plan -->
            <div class="plan-card">
                <div class="plan-name">Monthly Plan</div>
                <div class="plan-price">$5 <small>per month</small></div>
                <ul class="plan-features">
                    <li><strong><?php echo $downloadLimits['monthly']['mp4_web_videos']; ?></strong> video downloads per month</li>
                    <li><strong><?php echo $downloadLimits['monthly']['jpg_png_images']; ?></strong> image downloads per month</li>
                    <li>Advanced customization options</li>
                    <li>High-resolution exports</li>
                    <li>Email support</li>
                </ul>
                <div id="paypal-button-container-monthly"></div>
            </div>
            
            <!-- Annual Plan -->
            <div class="plan-card popular">
                <div class="popular-tag">Best Value</div>
                <div class="plan-name">Annual Plan</div>
                <div class="plan-price">$50 <small>per year</small></div>
                <ul class="plan-features">
                    <li><strong><?php echo $downloadLimits['annual']['mp4_web_videos']; ?></strong> video downloads per month</li>
                    <li><strong><?php echo $downloadLimits['annual']['jpg_png_images']; ?></strong> image downloads per month</li>
                    <li>All customization options</li>
                    <li>Ultra HD exports</li>
                    <li>Priority email support</li>
                    <li><strong>Save 16%</strong> compared to monthly</li>
                </ul>
                <div id="paypal-button-container-annual"></div>
            </div>
            
            <!-- Arab Plan -->
            <div class="plan-card">
                <div class="plan-name">Arab Plan</div>
                <div class="plan-price">$25 <small>per year</small></div>
                <ul class="plan-features">
                    <li><strong><?php echo $downloadLimits['arab']['mp4_web_videos']; ?></strong> video downloads per month</li>
                    <li><strong><?php echo $downloadLimits['arab']['jpg_png_images']; ?></strong> image downloads per month</li>
                    <li>Advanced customization options</li>
                    <li>High-resolution exports</li>
                    <li>Arabic language support</li>
                    <li>Regional payment methods</li>
                </ul>
                <div id="paypal-button-container-arab"></div>
            </div>
        </div>
    </div>

    <!-- PayPal JavaScript SDK -->
    <script src="https://www.paypal.com/sdk/js?client-id=<?php echo PAYPAL_CLIENT_ID; ?>&vault=true&intent=subscription"></script>
    
    <script>
        // Free Plan
        paypal.Buttons({
            style: {
                shape: 'rect',
                color: 'blue',
                layout: 'vertical',
                label: 'subscribe'
            },
            createSubscription: function(data, actions) {
                return actions.subscription.create({
                    plan_id: '<?php echo PLAN_FREE; ?>'
                });
            },
            onApprove: function(data, actions) {
                // Redirect to payment success page with subscription data
                window.location.href = 'payment_success.php?subscription_id=' + data.subscriptionID + '&plan_type=free';
            }
        }).render('#paypal-button-container-free');

        // Monthly Plan
        paypal.Buttons({
            style: {
                shape: 'rect',
                color: 'blue',
                layout: 'vertical',
                label: 'subscribe'
            },
            createSubscription: function(data, actions) {
                return actions.subscription.create({
                    plan_id: '<?php echo PLAN_MONTHLY; ?>'
                });
            },
            onApprove: function(data, actions) {
                // Redirect to payment success page with subscription data
                window.location.href = 'payment_success.php?subscription_id=' + data.subscriptionID + '&plan_type=monthly';
            }
        }).render('#paypal-button-container-monthly');

        // Annual Plan
        paypal.Buttons({
            style: {
                shape: 'rect',
                color: 'gold',
                layout: 'vertical',
                label: 'subscribe'
            },
            createSubscription: function(data, actions) {
                return actions.subscription.create({
                    plan_id: '<?php echo PLAN_ANNUAL; ?>'
                });
            },
            onApprove: function(data, actions) {
                // Redirect to payment success page with subscription data
                window.location.href = 'payment_success.php?subscription_id=' + data.subscriptionID + '&plan_type=annual';
            }
        }).render('#paypal-button-container-annual');

        // Arab Plan
        paypal.Buttons({
            style: {
                shape: 'rect',
                color: 'silver',
                layout: 'vertical',
                label: 'subscribe'
            },
            createSubscription: function(data, actions) {
                return actions.subscription.create({
                    plan_id: '<?php echo PLAN_ARAB; ?>'
                });
            },
            onApprove: function(data, actions) {
                // Redirect to payment success page with subscription data
                window.location.href = 'payment_success.php?subscription_id=' + data.subscriptionID + '&plan_type=arab';
            }
        }).render('#paypal-button-container-arab');
    </script>
</body>
</html>
