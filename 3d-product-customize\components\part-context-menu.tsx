"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Upload, Palette, X, Trash } from "lucide-react"

interface PartContextMenuProps {
  position: { x: number; y: number }
  onClose: () => void
  onUploadImage: () => void
  onSelectColor: (color: string) => void
  onRemoveTexture?: () => void
  hasTexture?: boolean
  partName: string
}

export function PartContextMenu({
  position,
  onClose,
  onUploadImage,
  onSelectColor,
  onRemoveTexture,
  hasTexture,
  partName,
}: PartContextMenuProps) {
  const [showColorPicker, setShowColorPicker] = useState(false)
  const [selectedColor, setSelectedColor] = useState("#ffffff")
  const menuRef = useRef<HTMLDivElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // <PERSON><PERSON> click outside to close the menu
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [onClose])

  // Handle file upload
  const handleFileUpload = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click()
    }
  }

  // Handle file change
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // In a real app, you would process the file here
      // For now, we'll just close the menu
      onUploadImage()
      onClose()
    }
  }

  // Handle color selection
  const handleColorChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const color = event.target.value
    setSelectedColor(color)
  }

  // Apply selected color
  const applyColor = () => {
    onSelectColor(selectedColor)
    setShowColorPicker(false)
  }

  // Get part name in Arabic
  const getPartNameInArabic = (name: string) => {
    switch (name) {
      case "body":
        return "جسم القميص"
      case "leftSleeve":
        return "الكم الأيسر"
      case "rightSleeve":
        return "الكم الأيمن"
      case "collar":
        return "الياقة"
      case "frontPrint":
        return "الطباعة الأمامية"
      default:
        return "الجزء المحدد"
    }
  }

  return (
    <div
      ref={menuRef}
      className="absolute z-50 rounded-lg border border-[#D6A25E] bg-[#1F2A45] p-3 shadow-lg"
      style={{
        top: position.y,
        left: position.x,
      }}
    >
      <div className="mb-2 flex items-center justify-between">
        <h3 className="text-sm font-medium text-[#D6A25E]">تخصيص {getPartNameInArabic(partName)}</h3>
        <Button variant="ghost" size="icon" className="h-6 w-6 text-[#D6A25E]" onClick={onClose}>
          <X className="h-4 w-4" />
        </Button>
      </div>

      <div className="flex flex-col gap-2">
        <Button
          variant="outline"
          className="flex w-full items-center justify-center gap-2 border-[#D6A25E] bg-[#344464] text-white hover:bg-[#D6A25E] hover:text-[#1F2A45]"
          onClick={handleFileUpload}
        >
          <Upload className="h-4 w-4" />
          <span>رفع صورة</span>
        </Button>

        <input type="file" ref={fileInputRef} className="hidden" accept="image/*" onChange={handleFileChange} />

        <Button
          variant="outline"
          className="flex w-full items-center justify-center gap-2 border-[#D6A25E] bg-[#344464] text-white hover:bg-[#D6A25E] hover:text-[#1F2A45]"
          onClick={() => setShowColorPicker(!showColorPicker)}
        >
          <Palette className="h-4 w-4" />
          <span>اختيار لون</span>
        </Button>

        {hasTexture && onRemoveTexture && (
          <Button
            variant="outline"
            className="flex w-full items-center justify-center gap-2 border-red-500 bg-[#344464] text-white hover:bg-red-500 hover:text-white"
            onClick={onRemoveTexture}
          >
            <Trash className="h-4 w-4" />
            <span>إزالة النسيج</span>
          </Button>
        )}

        {showColorPicker && (
          <div className="mt-2 rounded-md border border-[#D6A25E] bg-[#344464] p-2">
            <div className="mb-2 flex items-center justify-between">
              <input
                type="color"
                value={selectedColor}
                onChange={handleColorChange}
                className="h-8 w-full cursor-pointer rounded border-none bg-transparent"
              />
            </div>
            <Button
              variant="outline"
              className="w-full border-[#D6A25E] bg-[#D6A25E] text-[#1F2A45] hover:bg-[#D6A25E]/90"
              onClick={applyColor}
            >
              تطبيق اللون
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
