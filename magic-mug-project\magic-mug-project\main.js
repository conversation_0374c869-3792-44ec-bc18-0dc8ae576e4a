// إنشاء ملفات الأصول الوهمية للاختبار
// texture.jpg - صورة النسيج الأساسية
// effect.png - قناع التأثير الحراري

// إعداد متغيرات عامة
let scene, camera, renderer, mug;
let fabricCanvas;
let animationState = 'stopped'; // 'playing', 'paused', 'stopped'
let effectType = 'shader'; // 'shader', 'overlay', 'gradient'
let heatLevel = 0; // مستوى الحرارة (0-1)
let animationSpeed = 0.005; // سرعة التأثير الحراري
let isModelLoaded = false; // مؤشر لحالة تحميل النموذج

// تهيئة Three.js
function initThreeJS() {
    // إنشاء المشهد
    scene = new THREE.Scene();
    scene.background = new THREE.Color(0xf0f0f0);

    // إعداد الكاميرا
    camera = new THREE.PerspectiveCamera(
        45, 
        document.getElementById('three-canvas').clientWidth / document.getElementById('three-canvas').clientHeight, 
        0.1, 
        1000
    );
    camera.position.set(0, 0, 5);
    
    // إعداد المُصيِّر
    renderer = new THREE.WebGLRenderer({ 
        canvas: document.getElementById('three-canvas'),
        antialias: true 
    });
    renderer.setSize(
        document.getElementById('three-canvas').clientWidth,
        document.getElementById('three-canvas').clientHeight
    );
    renderer.setPixelRatio(window.devicePixelRatio);
    
    // إضافة إضاءة
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
    scene.add(ambientLight);
    
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(1, 1, 1);
    scene.add(directionalLight);
    
    // تحميل نموذج الكوب
    loadMugModel();
    
    // إضافة التحكم بالكاميرا (دوران)
    addCameraControls();
    
    // بدء حلقة الرسم
    animate();
}

// تحميل نموذج الكوب
function loadMugModel() {
    const loader = new THREE.GLTFLoader();
    
    // إظهار رسالة تحميل
    const loadingMessage = document.createElement('div');
    loadingMessage.style.position = 'absolute';
    loadingMessage.style.top = '50%';
    loadingMessage.style.left = '50%';
    loadingMessage.style.transform = 'translate(-50%, -50%)';
    loadingMessage.style.background = 'rgba(0,0,0,0.7)';
    loadingMessage.style.color = 'white';
    loadingMessage.style.padding = '20px';
    loadingMessage.style.borderRadius = '10px';
    loadingMessage.style.zIndex = '1000';
    loadingMessage.textContent = 'جاري تحميل النموذج...';
    document.querySelector('.three-container').appendChild(loadingMessage);
    
    loader.load(
        'models/model.gltf', // مسار النموذج
        function(gltf) {
            mug = gltf.scene;
            
            // ضبط حجم وموضع النموذج
            mug.scale.set(1, 1, 1);
            mug.position.set(0, 0, 0);
            mug.rotation.set(0, Math.PI, 0);
            
            scene.add(mug);
            
            // تطبيق المواد والقوام على النموذج
            applyMaterialsToMug();
            
            // تعيين مؤشر تحميل النموذج
            isModelLoaded = true;
            
            // إزالة رسالة التحميل
            document.querySelector('.three-container').removeChild(loadingMessage);
            
            console.log('تم تحميل نموذج الكوب بنجاح');
            
            // تمكين أزرار التحكم بعد تحميل النموذج
            document.getElementById('play-btn').disabled = false;
            document.getElementById('pause-btn').disabled = true;
            document.getElementById('stop-btn').disabled = true;
        },
        function(xhr) {
            const progress = Math.floor(xhr.loaded / xhr.total * 100);
            loadingMessage.textContent = `جاري التحميل: ${progress}%`;
            console.log('جاري التحميل: ' + progress + '%');
        },
        function(error) {
            console.error('حدث خطأ أثناء تحميل النموذج:', error);
            loadingMessage.textContent = 'حدث خطأ أثناء تحميل النموذج';
            loadingMessage.style.background = 'rgba(255,0,0,0.7)';
            
            // إظهار زر إعادة المحاولة
            const retryButton = document.createElement('button');
            retryButton.textContent = 'إعادة المحاولة';
            retryButton.style.marginTop = '10px';
            retryButton.style.padding = '5px 10px';
            retryButton.addEventListener('click', function() {
                document.querySelector('.three-container').removeChild(loadingMessage);
                loadMugModel();
            });
            loadingMessage.appendChild(document.createElement('br'));
            loadingMessage.appendChild(retryButton);
        }
    );
}

// تطبيق المواد والقوام على النموذج
function applyMaterialsToMug() {
    if (!mug) return;
    
    // إنشاء قوام افتراضي للاختبار
    const textureLoader = new THREE.TextureLoader();
    
    // تحميل قوام التصميم (سيتم استبداله لاحقًا بقوام من Fabric.js)
    const designTexture = new THREE.CanvasTexture(document.createElement('canvas'));
    designTexture.wrapS = THREE.RepeatWrapping;
    designTexture.wrapT = THREE.RepeatWrapping;
    
    // تحميل قوام تأثير الحرارة
    const heatEffectTexture = new THREE.CanvasTexture(document.createElement('canvas'));
    
    // تطبيق المواد على النموذج
    mug.traverse(function(child) {
        if (child.isMesh) {
            // تخزين المادة الأصلية
            child.originalMaterial = child.material.clone();
            
            // إنشاء مادة جديدة للكوب
            const newMaterial = createMugMaterial(designTexture, heatEffectTexture);
            child.material = newMaterial;
        }
    });
}

// إنشاء مادة الكوب مع تأثير الحرارة
function createMugMaterial(designTexture, heatEffectTexture) {
    // إنشاء مادة أساسية
    const material = new THREE.MeshStandardMaterial({
        map: designTexture,
        roughness: 0.5,
        metalness: 0.2
    });
    
    // إضافة خصائص إضافية للمادة
    material.userData = {
        designTexture: designTexture,
        heatEffectTexture: heatEffectTexture
    };
    
    return material;
}

// تحديث قوام التصميم من Fabric.js
function updateDesignTexture() {
    if (!mug || !fabricCanvas) return;
    
    // الحصول على صورة من قماش Fabric.js
    const designImage = fabricCanvas.toDataURL('image/png');
    
    // تحميل الصورة كقوام
    const textureLoader = new THREE.TextureLoader();
    textureLoader.load(designImage, function(texture) {
        // تطبيق القوام على جميع أجزاء النموذج
        mug.traverse(function(child) {
            if (child.isMesh && child.material.userData && child.material.userData.designTexture) {
                child.material.map = texture;
                child.material.needsUpdate = true;
            }
        });
    });
}

// تحديث تأثير الحرارة
function updateHeatEffect() {
    if (!mug) return;
    
    // تطبيق تأثير الحرارة على جميع أجزاء النموذج
    mug.traverse(function(child) {
        if (child.isMesh) {
            // تحديث تأثير الحرارة حسب النوع المحدد
            switch (effectType) {
                case 'shader':
                    applyShaderEffect(child, heatLevel);
                    break;
                case 'overlay':
                    applyOverlayEffect(child, heatLevel);
                    break;
                case 'gradient':
                    applyGradientEffect(child, heatLevel);
                    break;
            }
        }
    });
}

// تطبيق تأثير قناع الشيدر
function applyShaderEffect(mesh, level) {
    // التأكد من وجود المادة
    if (!mesh.material) return;
    
    // إذا لم تكن المادة تحتوي على شيدر مخصص، قم بإنشائه
    if (!mesh.material.isShaderMaterial) {
        // حفظ المادة الأصلية
        if (!mesh._originalMaterial) {
            mesh._originalMaterial = mesh.material.clone();
        }
        
        // تحميل قناع التأثير
        const textureLoader = new THREE.TextureLoader();
        const maskTexture = textureLoader.load('textures/effect.png');
        
        // تعريف شيدر الرأس (Vertex Shader)
        const vertexShader = `
            varying vec2 vUv;
            
            void main() {
                vUv = uv;
                gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
            }
        `;
        
        // تعريف شيدر الشظايا (Fragment Shader)
        const fragmentShader = `
            uniform sampler2D baseTexture;
            uniform sampler2D maskTexture;
            uniform float heatLevel;
            
            varying vec2 vUv;
            
            void main() {
                // الحصول على لون القوام الأساسي
                vec4 baseColor = texture2D(baseTexture, vUv);
                
                // الحصول على قيمة القناع
                vec4 maskColor = texture2D(maskTexture, vUv);
                
                // حساب عتبة الكشف بناءً على مستوى الحرارة
                // يتم الكشف من الأسفل إلى الأعلى
                float threshold = step(1.0 - heatLevel, maskColor.r);
                
                // مزج اللون الأساسي مع اللون الأسود بناءً على العتبة
                vec4 finalColor = mix(vec4(0.0, 0.0, 0.0, 1.0), baseColor, threshold);
                
                gl_FragColor = finalColor;
            }
        `;
        
        // إنشاء مادة الشيدر
        const shaderMaterial = new THREE.ShaderMaterial({
            uniforms: {
                baseTexture: { value: mesh._originalMaterial.map },
                maskTexture: { value: maskTexture },
                heatLevel: { value: level }
            },
            vertexShader: vertexShader,
            fragmentShader: fragmentShader,
            transparent: true
        });
        
        // تطبيق المادة الجديدة
        mesh.material = shaderMaterial;
    } else {
        // تحديث قيمة مستوى الحرارة في الشيدر
        mesh.material.uniforms.heatLevel.value = level;
    }
}

// تطبيق تأثير التراكب الأسود المتلاشي
function applyOverlayEffect(mesh, level) {
    // التأكد من وجود المادة
    if (!mesh.material) return;
    
    // حفظ المادة الأصلية إذا لم تكن محفوظة
    if (!mesh._originalMaterial) {
        mesh._originalMaterial = mesh.material.clone();
    }
    
    // إذا كانت المادة الحالية هي شيدر، استعادة المادة الأصلية
    if (mesh.material.isShaderMaterial) {
        mesh.material = mesh._originalMaterial.clone();
    }
    
    // إنشاء قماش لرسم التراكب
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    canvas.width = 512;
    canvas.height = 512;
    
    // رسم التراكب الأسود المتلاشي
    // يتم رسم مستطيل أسود بارتفاع يتناسب عكسيًا مع مستوى الحرارة
    ctx.fillStyle = '#000000';
    ctx.fillRect(0, 0, canvas.width, canvas.height * (1 - level));
    
    // إنشاء قوام من القماش
    const overlayTexture = new THREE.CanvasTexture(canvas);
    overlayTexture.needsUpdate = true;
    
    // إنشاء مادة للتراكب
    const overlayMaterial = new THREE.MeshBasicMaterial({
        map: overlayTexture,
        transparent: true,
        opacity: 1.0
    });
    
    // إنشاء نسخة من الشبكة الأصلية للتراكب
    if (!mesh._overlayMesh) {
        const overlayGeometry = mesh.geometry.clone();
        mesh._overlayMesh = new THREE.Mesh(overlayGeometry, overlayMaterial);
        mesh._overlayMesh.position.copy(mesh.position);
        mesh._overlayMesh.rotation.copy(mesh.rotation);
        mesh._overlayMesh.scale.copy(mesh.scale);
        mesh._overlayMesh.renderOrder = 1; // رسم فوق الشبكة الأصلية
        
        // إضافة التراكب إلى المشهد
        mesh.parent.add(mesh._overlayMesh);
    } else {
        // تحديث مادة التراكب
        mesh._overlayMesh.material.map = overlayTexture;
        mesh._overlayMesh.material.needsUpdate = true;
    }
    
    // تحديث موضع وتدوير ومقياس التراكب ليتطابق مع الشبكة الأصلية
    mesh._overlayMesh.position.copy(mesh.position);
    mesh._overlayMesh.rotation.copy(mesh.rotation);
    mesh._overlayMesh.scale.copy(mesh.scale);
    
    // تحديث رؤية التراكب
    mesh._overlayMesh.visible = true;
}

// تطبيق تأثير التدرج الشفاف
function applyGradientEffect(mesh, level) {
    // التأكد من وجود المادة
    if (!mesh.material) return;
    
    // حفظ المادة الأصلية إذا لم تكن محفوظة
    if (!mesh._originalMaterial) {
        mesh._originalMaterial = mesh.material.clone();
    }
    
    // إذا كانت المادة الحالية هي شيدر، استعادة المادة الأصلية
    if (mesh.material.isShaderMaterial) {
        mesh.material = mesh._originalMaterial.clone();
    }
    
    // إخفاء التراكب إذا كان موجودًا
    if (mesh._overlayMesh) {
        mesh._overlayMesh.visible = false;
    }
    
    // إنشاء قماش لرسم التدرج
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    canvas.width = 512;
    canvas.height = 512;
    
    // إنشاء تدرج شفاف
    const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
    gradient.addColorStop(0, 'rgba(0, 0, 0, 1)'); // أعلى: أسود غير شفاف
    gradient.addColorStop(level, 'rgba(0, 0, 0, 0)'); // مستوى الحرارة: شفاف تمامًا
    gradient.addColorStop(1, 'rgba(0, 0, 0, 0)'); // أسفل: شفاف تمامًا
    
    // رسم التدرج
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // إنشاء قوام من القماش
    const alphaTexture = new THREE.CanvasTexture(canvas);
    alphaTexture.needsUpdate = true;
    
    // تطبيق القوام كخريطة ألفا على المادة
    mesh.material.alphaMap = alphaTexture;
    mesh.material.transparent = true;
    mesh.material.needsUpdate = true;
}

// إضافة التحكم بالكاميرا
function addCameraControls() {
    const threeContainer = document.querySelector('.three-container');
    
    // متغيرات لتتبع حركة الماوس
    let isDragging = false;
    let previousMousePosition = {
        x: 0,
        y: 0
    };
    
    // استمع لأحداث الماوس
    threeContainer.addEventListener('mousedown', function(e) {
        isDragging = true;
    });
    
    threeContainer.addEventListener('mousemove', function(e) {
        if (isDragging && mug) {
            const deltaMove = {
                x: e.offsetX - previousMousePosition.x,
                y: e.offsetY - previousMousePosition.y
            };
            
            // تدوير النموذج
            mug.rotation.y += deltaMove.x * 0.01;
            mug.rotation.x += deltaMove.y * 0.01;
        }
        
        previousMousePosition = {
            x: e.offsetX,
            y: e.offsetY
        };
    });
    
    threeContainer.addEventListener('mouseup', function() {
        isDragging = false;
    });
    
    // دعم اللمس للأجهزة المحمولة
    threeContainer.addEventListener('touchstart', function(e) {
        isDragging = true;
        previousMousePosition = {
            x: e.touches[0].clientX,
            y: e.touches[0].clientY
        };
        e.preventDefault();
    });
    
    threeContainer.addEventListener('touchmove', function(e) {
        if (isDragging && mug) {
            const deltaMove = {
                x: e.touches[0].clientX - previousMousePosition.x,
                y: e.touches[0].clientY - previousMousePosition.y
            };
            
            // تدوير النموذج
            mug.rotation.y += deltaMove.x * 0.01;
            mug.rotation.x += deltaMove.y * 0.01;
            
            previousMousePosition = {
                x: e.touches[0].clientX,
                y: e.touches[0].clientY
            };
        }
        e.preventDefault();
    });
    
    threeContainer.addEventListener('touchend', function(e) {
        isDragging = false;
        e.preventDefault();
    });
}

// حلقة الرسم
function animate() {
    requestAnimationFrame(animate);
    
    // تحديث مستوى الحرارة إذا كان التأثير قيد التشغيل
    if (animationState === 'playing') {
        heatLevel += animationSpeed;
        if (heatLevel > 1) {
            heatLevel = 1;
            // إيقاف التأثير تلقائيًا عند اكتمال الكشف
            pauseHeatEffect();
        }
        
        // تحديث تأثير الحرارة
        updateHeatEffect();
    }
    
    // تحديث المشهد فقط إذا كان النموذج محملًا أو إذا كان هناك تغيير في الحالة
    if (isModelLoaded || animationState === 'playing') {
        renderer.render(scene, camera);
    }
}

// تهيئة Fabric.js
function initFabricJS() {
    // إنشاء قماش Fabric.js
    fabricCanvas = new fabric.Canvas('fabric-canvas', {
        width: document.querySelector('.canvas-container').clientWidth,
        height: document.querySelector('.canvas-container').clientHeight
    });
    
    // إضافة مستمع لتحديث التصميم عند التغيير
    fabricCanvas.on('object:modified', function() {
        updateDesignTexture();
    });
    
    // إضافة مستمع لتحديث التصميم عند إضافة عنصر جديد
    fabricCanvas.on('object:added', function() {
        updateDesignTexture();
    });
    
    // إضافة مستمع لتحديث التصميم عند إزالة عنصر
    fabricCanvas.on('object:removed', function() {
        updateDesignTexture();
    });
    
    // إضافة خلفية بيضاء للقماش
    fabricCanvas.setBackgroundColor('#ffffff', fabricCanvas.renderAll.bind(fabricCanvas));
}

// إضافة نص إلى قماش Fabric.js
function addTextToCanvas() {
    const text = new fabric.IText('أدخل النص هنا', {
        left: 50,
        top: 50,
        fontFamily: 'Arial',
        fill: '#000000',
        fontSize: 20
    });
    
    fabricCanvas.add(text);
    fabricCanvas.setActiveObject(text);
}

// إضافة صورة إلى قماش Fabric.js
function addImageToCanvas() {
    // إنشاء عنصر إدخال ملف مؤقت
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = 'image/*';
    
    fileInput.onchange = function(e) {
        const file = e.target.files[0];
        if (!file) return;
        
        const reader = new FileReader();
        reader.onload = function(event) {
            const imgObj = new Image();
            imgObj.src = event.target.result;
            
            imgObj.onload = function() {
                const image = new fabric.Image(imgObj);
                
                // تغيير حجم الصورة إذا كانت كبيرة جدًا
                if (image.width > fabricCanvas.width) {
                    image.scaleToWidth(fabricCanvas.width / 2);
                }
                
                fabricCanvas.add(image);
                fabricCanvas.setActiveObject(image);
                fabricCanvas.renderAll();
            };
        };
        
        reader.readAsDataURL(file);
    };
    
    fileInput.click();
}

// مسح قماش Fabric.js
function clearCanvas() {
    fabricCanvas.clear();
    fabricCanvas.setBackgroundColor('#ffffff', fabricCanvas.renderAll.bind(fabricCanvas));
}

// تشغيل تأثير الحرارة
function playHeatEffect() {
    animationState = 'playing';
    console.log('تم تشغيل تأثير الحرارة');
    
    // تحديث حالة الأزرار
    document.getElementById('play-btn').disabled = true;
    document.getElementById('pause-btn').disabled = false;
    document.getElementById('stop-btn').disabled = false;
}

// إيقاف تأثير الحرارة مؤقتًا
function pauseHeatEffect() {
    animationState = 'paused';
    console.log('تم إيقاف تأثير الحرارة مؤقتًا');
    
    // تحديث حالة الأزرار
    document.getElementById('play-btn').disabled = false;
    document.getElementById('pause-btn').disabled = true;
    document.getElementById('stop-btn').disabled = false;
}

// إيقاف تأثير الحرارة وإعادة تعيينه
function stopHeatEffect() {
    animationState = 'stopped';
    heatLevel = 0;
    updateHeatEffect();
    console.log('تم إيقاف تأثير الحرارة وإعادة تعيينه');
    
    // تحديث حالة الأزرار
    document.getElementById('play-btn').disabled = false;
    document.getElementById('pause-btn').disabled = true;
    document.getElementById('stop-btn').disabled = true;
}

// تغيير نوع تأثير الحرارة
function changeEffectType(type) {
    effectType = type;
    
    // إعادة تعيين مستوى الحرارة عند تغيير نوع التأثير
    heatLevel = 0;
    
    // تحديث التأثير
    updateHeatEffect();
    
    console.log('تم تغيير نوع التأثير إلى: ' + type);
    
    // إعادة تعيين حالة الأزرار
    document.getElementById('play-btn').disabled = false;
    document.getElementById('pause-btn').disabled = true;
    document.getElementById('stop-btn').disabled = true;
    
    // إيقاف التأثير
    animationState = 'stopped';
}

// استجابة لتغيير حجم النافذة
function handleResize() {
    if (camera && renderer) {
        // تحديث نسبة العرض إلى الارتفاع للكاميرا
        camera.aspect = document.getElementById('three-canvas').clientWidth / document.getElementById('three-canvas').clientHeight;
        camera.updateProjectionMatrix();
        
        // تحديث حجم المُصيِّر
        renderer.setSize(
            document.getElementById('three-canvas').clientWidth,
            document.getElementById('three-canvas').clientHeight
        );
    }
    
    if (fabricCanvas) {
        // تحديث حجم قماش Fabric.js
        fabricCanvas.setDimensions({
            width: document.querySelector('.canvas-container').clientWidth,
            height: document.querySelector('.canvas-container').clientHeight
        });
    }
}

// إضافة مستمعي الأحداث
function addEventListeners() {
    // أزرار التحكم بالتأثير
    document.getElementById('play-btn').addEventListener('click', playHeatEffect);
    document.getElementById('pause-btn').addEventListener('click', pauseHeatEffect);
    document.getElementById('stop-btn').addEventListener('click', stopHeatEffect);
    
    // أزرار التحكم بالتصميم
    document.getElementById('add-text').addEventListener('click', addTextToCanvas);
    document.getElementById('add-image').addEventListener('click', addImageToCanvas);
    document.getElementById('clear-canvas').addEventListener('click', clearCanvas);
    
    // تغيير نوع التأثير
    document.getElementById('effect-type').addEventListener('change', function(e) {
        changeEffectType(e.target.value);
    });
    
    // استجابة لتغيير حجم النافذة
    window.addEventListener('resize', handleResize);
}

// تهيئة التطبيق
function init() {
    initThreeJS();
    initFabricJS();
    addEventListeners();
}

// بدء التطبيق عند تحميل الصفحة
window.addEventListener('load', init);
