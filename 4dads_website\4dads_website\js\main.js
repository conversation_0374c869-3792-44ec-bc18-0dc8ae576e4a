// ملف JavaScript رئيسي للتطبيق
let scene, camera, renderer, controls;
let model, ground, background;
let selectedPart = null;
let isAnimating = false;
let animationId = null;

// إعداد المشهد الأساسي
function initThreeJS() {
    // إنشاء المشهد
    scene = new THREE.Scene();
    scene.background = new THREE.Color(0x182949);

    // إعداد الكاميرا
    camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.position.set(0, 5, 10);

    // إعداد الرندرر
    const container = document.getElementById('threejs-container');
    renderer = new THREE.WebGLRenderer({ antialias: true, preserveDrawingBuffer: true });
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    container.appendChild(renderer.domElement);

    // إعداد التحكم في الكاميرا
    controls = new THREE.OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.05;

    // إضافة الإضاءة
    setupLighting();

    // إنشاء الأرضية
    createGround();

    // بدء حلقة الرندر
    animate();
}

// إعداد الإضاءة
function setupLighting() {
    // إضاءة محيطة
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    scene.add(ambientLight);

    // إضاءة اتجاهية
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    scene.add(directionalLight);

    // إضاءة نقطية
    const pointLight = new THREE.PointLight(0xffffff, 0.5);
    pointLight.position.set(-10, 10, -10);
    scene.add(pointLight);
}

// إنشاء الأرضية
function createGround() {
    const groundGeometry = new THREE.PlaneGeometry(20, 20);
    const groundMaterial = new THREE.MeshLambertMaterial({ color: 0xd3a77b });
    ground = new THREE.Mesh(groundGeometry, groundMaterial);
    ground.rotation.x = -Math.PI / 2;
    ground.receiveShadow = true;
    scene.add(ground);
}

// تحميل نموذج ثلاثي الأبعاد
function loadModel(modelPath) {
    const loader = new THREE.GLTFLoader();
    
    // إزالة النموذج السابق إن وجد
    if (model) {
        scene.remove(model);
    }

    loader.load(
        `models/${modelPath}/model.gltf`,
        function (gltf) {
            model = gltf.scene;
            model.scale.set(2, 2, 2);
            model.position.set(0, 0, 0);
            
            // تمكين الظلال
            model.traverse(function (child) {
                if (child.isMesh) {
                    child.castShadow = true;
                    child.receiveShadow = true;
                }
            });

            scene.add(model);
            console.log('تم تحميل النموذج بنجاح');
        },
        function (progress) {
            console.log('جاري التحميل...', (progress.loaded / progress.total * 100) + '%');
        },
        function (error) {
            console.error('خطأ في تحميل النموذج:', error);
            // تحميل نموذج افتراضي بسيط
            loadDefaultModel();
        }
    );
}

// تحميل نموذج افتراضي
function loadDefaultModel() {
    const geometry = new THREE.BoxGeometry(2, 2, 2);
    const material = new THREE.MeshLambertMaterial({ color: 0xd3a77b });
    model = new THREE.Mesh(geometry, material);
    model.position.set(0, 1, 0);
    model.castShadow = true;
    model.receiveShadow = true;
    scene.add(model);
}

// حلقة الرندر
function animate() {
    animationId = requestAnimationFrame(animate);
    
    controls.update();
    
    // تطبيق الحركات المختلفة
    if (isAnimating && model) {
        applyAnimations();
    }
    
    renderer.render(scene, camera);
}

// تطبيق الحركات
function applyAnimations() {
    const time = Date.now() * 0.001;
    
    if (model) {
        switch (currentAnimation) {
            case 'rotate':
                model.rotation.y = time;
                break;
            case 'reverse':
                model.rotation.y = -time;
                break;
            case 'swing':
                model.rotation.z = Math.sin(time * 2) * 0.2;
                break;
            case 'hover':
                model.position.y = 1 + Math.sin(time * 3) * 0.5;
                break;
            case 'jump':
                model.position.y = 1 + Math.abs(Math.sin(time * 4)) * 2;
                break;
        }
    }
}

// متغيرات الحركة
let currentAnimation = null;

// وظائف التحكم في الحركة
function startModelRotation() {
    currentAnimation = 'rotate';
    isAnimating = true;
}

function startModelReverse() {
    currentAnimation = 'reverse';
    isAnimating = true;
}

function startSceneRotation() {
    currentAnimation = 'scene-rotate';
    isAnimating = true;
}

function startModelSwing() {
    currentAnimation = 'swing';
    isAnimating = true;
}

function startModelHover() {
    currentAnimation = 'hover';
    isAnimating = true;
}

function startModelJump() {
    currentAnimation = 'jump';
    isAnimating = true;
}

function stopAnimation() {
    isAnimating = false;
    currentAnimation = null;
    if (model) {
        model.rotation.set(0, 0, 0);
        model.position.set(0, 0, 0);
    }
}

// تصدير الوسائط
function exportMedia() {
    const format = document.getElementById('export-format').value;
    const dimensions = document.getElementById('export-dimensions').value;
    const [width, height] = dimensions.split('x').map(Number);
    
    // تغيير حجم الرندرر مؤقتاً
    const originalWidth = renderer.domElement.width;
    const originalHeight = renderer.domElement.height;
    
    renderer.setSize(width, height);
    renderer.render(scene, camera);
    
    if (format === 'png' || format === 'jpg') {
        const dataURL = renderer.domElement.toDataURL(`image/${format}`);
        downloadImage(dataURL, `export.${format}`);
    }
    
    // إعادة الحجم الأصلي
    renderer.setSize(originalWidth, originalHeight);
}

// تنزيل الصورة
function downloadImage(dataURL, filename) {
    const link = document.createElement('a');
    link.download = filename;
    link.href = dataURL;
    link.click();
}

// تغيير حجم النافذة
function onWindowResize() {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    window.addEventListener('resize', onWindowResize);
    
    // أزرار الحركة
    document.getElementById('btn-model-rotate').addEventListener('click', startModelRotation);
    document.getElementById('btn-model-reverse').addEventListener('click', startModelReverse);
    document.getElementById('btn-scene-rotate').addEventListener('click', startSceneRotation);
    document.getElementById('btn-model-swing').addEventListener('click', startModelSwing);
    document.getElementById('btn-model-hover').addEventListener('click', startModelHover);
    document.getElementById('btn-model-jump').addEventListener('click', startModelJump);
    
    // أزرار التحكم في الألوان
    document.getElementById('model-color-picker').addEventListener('change', function(event) {
        if (model && model.material) {
            model.material.color.set(event.target.value);
        }
    });
    
    document.getElementById('ground-color-picker').addEventListener('change', function(event) {
        if (ground && ground.material) {
            ground.material.color.set(event.target.value);
        }
    });
    
    document.getElementById('background-color-picker').addEventListener('change', function(event) {
        scene.background = new THREE.Color(event.target.value);
    });
}

// قائمة النماذج المتاحة
const models = [
    { name: 'نموذج افتراضي', folder: 'default' },
    { name: 'مكعب', folder: 'cube' },
    { name: 'كرة', folder: 'sphere' },
    { name: 'أسطوانة', folder: 'cylinder' }
];

// تبديل القوائم
function toggleExportMenus() {
    const mediaMenu = document.getElementById('mediaMenu');
    if (mediaMenu.style.display === 'none' || mediaMenu.style.display === '') {
        mediaMenu.style.display = 'flex';
    } else {
        mediaMenu.style.display = 'none';
    }
}

// بدء التطبيق
document.addEventListener('DOMContentLoaded', function() {
    initThreeJS();
    setupEventListeners();
    loadDefaultModel();
});

// تصدير الوظائف للاستخدام العام
window.loadModel = loadModel;
window.exportMedia = exportMedia;
window.toggleExportMenus = toggleExportMenus;

