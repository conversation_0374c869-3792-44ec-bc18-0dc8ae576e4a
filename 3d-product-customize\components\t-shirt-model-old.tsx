"use client"

import { useRef, useCallback } from "react"
import { useFrame } from "@react-three/fiber"
import * as THREE from "three"

interface TShirtModelProps {
  position: [number, number, number]
  color: string
  metalness: number
  roughness: number
  envMapIntensity: number
  onPartClick: (event: any, partName: string) => void
  partTextures: Record<string, string>
  partColors: Record<string, string>
}

export function TShirtModel({
  position,
  color,
  metalness,
  roughness,
  envMapIntensity,
  onPartClick,
  partTextures,
  partColors,
}: TShirtModelProps) {
  // In a real application, you would use a real GLTF model path
  // const { nodes, materials } = useGLTF("/t-shirt.glb")

  const groupRef = useRef<THREE.Group>(null)
  const bodyRef = useRef<THREE.Mesh>(null)
  const leftSleeveRef = useRef<THREE.Mesh>(null)
  const rightSleeveRef = useRef<THREE.Mesh>(null)
  const collarRef = useRef<THREE.Mesh>(null)
  const frontPrintRef = useRef<THREE.Mesh>(null)

  // Simple animation
  useFrame((state) => {
    if (groupRef.current) {
      groupRef.current.rotation.y = Math.sin(state.clock.getElapsedTime() * 0.2) * 0.1
    }
  })

  // Create textures for each part
  const createTexture = useCallback(
    (partName: string) => {
      if (partTextures[partName]) {
        // Create a new texture loader
        const texture = new THREE.TextureLoader().load(partTextures[partName])

        // Set proper texture properties
        texture.wrapS = THREE.RepeatWrapping
        texture.wrapT = THREE.RepeatWrapping
        texture.needsUpdate = true

        return texture
      }
      return null
    },
    [partTextures],
  )

  // Create canvas texture for the front print
  const frontPrintTexture = new THREE.CanvasTexture(
    (() => {
      const canvas = document.createElement("canvas")
      canvas.width = 256
      canvas.height = 256
      const ctx = canvas.getContext("2d")
      if (ctx) {
        ctx.fillStyle = "rgba(255, 255, 255, 0)"
        ctx.fillRect(0, 0, 256, 256)

        // Draw a circle
        ctx.fillStyle = "#ff6b6b"
        ctx.beginPath()
        ctx.arc(128, 80, 40, 0, Math.PI * 2)
        ctx.fill()

        // Add text
        ctx.fillStyle = "#5f27cd"
        ctx.font = "bold 24px Arial"
        ctx.textAlign = "center"
        ctx.fillText("نص العرض", 128, 160)

        // Add Arabic text
        ctx.font = "bold 18px Arial"
        ctx.fillText("نص العرض", 128, 190)
      }
      return canvas
    })(),
  )

  // Handle double click on parts
  const handlePartClick = (event: any, partName: string) => {
    event.stopPropagation()
    onPartClick(event, partName)
  }

  return (
    <group ref={groupRef} position={position}>
      {/* T-shirt body */}
      <mesh
        ref={bodyRef}
        castShadow
        receiveShadow
        position={[0, 0, 0]}
        onDoubleClick={(e) => handlePartClick(e, "body")}
      >
        <cylinderGeometry args={[0.6, 0.8, 1.2, 32]} />
        <meshStandardMaterial
          color={partColors.body || color}
          map={createTexture("body")}
          metalness={metalness}
          roughness={roughness}
          envMapIntensity={envMapIntensity}
        />
      </mesh>

      {/* Left sleeve */}
      <mesh
        ref={leftSleeveRef}
        castShadow
        receiveShadow
        position={[-0.7, 0, 0]}
        rotation={[0, 0, Math.PI * 0.15]}
        onDoubleClick={(e) => handlePartClick(e, "leftSleeve")}
      >
        <cylinderGeometry args={[0.2, 0.25, 0.6, 16]} />
        <meshStandardMaterial
          color={partColors.leftSleeve || color}
          map={createTexture("leftSleeve")}
          metalness={metalness}
          roughness={roughness}
          envMapIntensity={envMapIntensity}
        />
      </mesh>

      {/* Right sleeve */}
      <mesh
        ref={rightSleeveRef}
        castShadow
        receiveShadow
        position={[0.7, 0, 0]}
        rotation={[0, 0, -Math.PI * 0.15]}
        onDoubleClick={(e) => handlePartClick(e, "rightSleeve")}
      >
        <cylinderGeometry args={[0.2, 0.25, 0.6, 16]} />
        <meshStandardMaterial
          color={partColors.rightSleeve || color}
          map={createTexture("rightSleeve")}
          metalness={metalness}
          roughness={roughness}
          envMapIntensity={envMapIntensity}
        />
      </mesh>

      {/* Collar */}
      <mesh
        ref={collarRef}
        castShadow
        receiveShadow
        position={[0, 0.6, 0]}
        onDoubleClick={(e) => handlePartClick(e, "collar")}
      >
        <torusGeometry args={[0.3, 0.1, 16, 32, Math.PI * 2]} />
        <meshStandardMaterial
          color={partColors.collar || color}
          map={createTexture("collar")}
          metalness={metalness}
          roughness={roughness}
          envMapIntensity={envMapIntensity}
        />
      </mesh>

      {/* Front print */}
      <mesh
        ref={frontPrintRef}
        position={[0, 0.1, 0.41]}
        rotation={[0, 0, 0]}
        onDoubleClick={(e) => handlePartClick(e, "frontPrint")}
      >
        <planeGeometry args={[0.8, 0.8]} />
        <meshStandardMaterial
          transparent
          opacity={0.9}
          metalness={0}
          roughness={0.5}
          map={partTextures.frontPrint ? createTexture("frontPrint") : frontPrintTexture}
        />
      </mesh>
    </group>
  )
}
