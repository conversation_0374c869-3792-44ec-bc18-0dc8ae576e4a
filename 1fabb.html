<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أداة الرسم التفاعلية</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.1/fabric.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Tajawal', sans-serif;
        }
        
        body {
            background-color: #1a2a4a;
            color: #d3a77b;
            direction: rtl;
            overflow: hidden;
            height: 100vh;
        }
        
        #container {
            display: flex;
            flex-direction: column;
            height: 100%;
        }
        
        #canvas-container {
            flex-grow: 1;
            position: relative;
            background: #182949;
        }
        
        #canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        
        #image-editor-container {
            height: 30%;
            position: relative;
            z-index: 10;
            display: flex;
            flex-direction: column;
            background: #344464;
            border-top: 2px solid #d3a77b;
        }
        
        #floating-menu-image {
            display: flex;
            flex-wrap: wrap;
            padding: 10px;
            gap: 8px;
            background: rgba(24, 41, 73, 0.9);
            border-bottom: 1px solid #d3a77b;
        }
        
        #floating-menu-image button {
            width: 44px;
            height: 44px;
            background: #182949;
            border: 2px solid #d3a77b;
            border-radius: 5px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
        }
        
        #floating-menu-image button:hover {
            background: #d3a77b;
        }
        
        #floating-menu-image button svg {
            width: 24px;
            height: 24px;
            fill: #d3a77b;
        }
        
        #floating-menu-image button:hover svg {
            fill: #182949;
        }
        
        #font-buttons-container {
            display: flex;
            gap: 10px;
            overflow-x: auto;
            padding: 10px;
            background: #182949;
        }
        
        .font-btn {
            flex: 0 0 auto;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            color: #efc776;
            border: 1px solid #efc776;
            background-color: #254667;
            border-radius: 4px;
            transition: background-color 0.3s;
            white-space: nowrap;
        }
        
        .font-btn:hover {
            background-color: #3a5a7e;
        }
        
        #filterSlider {
            display: none;
            overflow-x: auto;
            white-space: nowrap;
            padding: 10px;
            background: #344464;
            border-top: 1px solid #d3a77b;
        }
        
        .filter-btn {
            display: inline-block;
            padding: 8px 16px;
            margin: 0 5px;
            background: #344464;
            border: 1px solid #d3a77b;
            border-radius: 5px;
            cursor: pointer;
            white-space: nowrap;
            color: #d3a77b;
        }
        
        .filter-btn:hover {
            background: #d3a77b;
            color: #182949;
        }
        
        #bottom-bar {
            display: flex;
            justify-content: space-around;
            padding: 10px;
            background: #344464;
            border-top: 2px solid #d3a77b;
            z-index: 100;
        }
        
        #bottom-bar button {
            background: transparent;
            border: none;
            color: #d3a77b;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 5px 10px;
            border-radius: 5px;
            transition: all 0.3s;
        }
        
        #bottom-bar button:hover {
            background: rgba(211, 167, 123, 0.2);
        }
        
        #bottom-bar button svg {
            width: 24px;
            height: 24px;
            margin-bottom: 5px;
            fill: #d3a77b;
        }
        
        .tooltip {
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            padding: 5px 10px;
            background-color: #182949;
            color: #d3a77b;
            border: 1px solid #d3a77b;
            border-radius: 5px;
            white-space: nowrap;
            font-size: 12px;
            opacity: 0;
            transition: opacity 0.3s;
            pointer-events: none;
        }
        
        button:hover .tooltip {
            opacity: 1;
        }
        
        #stickers-panel {
            display: none;
            position: absolute;
            bottom: 60px;
            left: 0;
            right: 0;
            background: #344464;
            border: 1px solid #d3a77b;
            padding: 10px;
            max-height: 180px;
            overflow-y: auto;
            z-index: 1000;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .sticker-item {
            width: 40px;
            height: 40px;
            margin: 5px;
            cursor: pointer;
            border: 1px solid #d3a77b;
            border-radius: 4px;
        }
        
        .sticker-item:hover {
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="canvas-container">
            <canvas id="canvas"></canvas>
            
            <!-- القائمة العائمة للصور -->
            <div id="floating-menu-image">
                <button id="btn-img-bring-front" data-tooltip="إحضار للأمام">
                    <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px">
                        <path d="M360-280q-33 0-56.5-23.5T280-360v-400q0-33 23.5-56.5T360-840h400q33 0 56.5 23.5T840-760v400q0 33-23.5 56.5T760-280H360Zm0-80h400v-400H360v400ZM200-200v80q-33 0-56.5-23.5T120-200h80Zm-80-80v-80h80v80h-80Zm0-160v-80h80v80h-80Zm0-160v-80h80v80h-80Zm160 480v-80h80v80h-80Zm160 0v-80h80v80h-80Zm160 0v-80h80v80h-80Z"/>
                    </svg>
                    <span class="tooltip">إحضار للأمام</span>
                </button>
                
                <button id="btn-img-send-back" data-tooltip="إرسال للخلف">
                    <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px">
                        <path d="M200-120q-33 0-56.5-23.5T120-200v-480h80v480h480v80H200Zm160-240v80q-33 0-56.5-23.5T280-360h80Zm-80-80v-80h80v80h-80Zm0-160v-80h80v80h-80Zm80-160h-80q0-33 23.5-56.5T360-840v80Zm80 480v-80h80v80h-80Zm0-480v-80h80v80h-80Zm160 0v-80h80v80h-80Zm0 480v-80h80v80h-80Zm160-480v-80q33 0 56.5 23.5T840-760h-80Zm0 400h80q0 33-23.5 56.5T760-280v-80Zm0-80v-80h80v80h-80Zm0-160v-80h80v80h-80Z"/>
                    </svg>
                    <span class="tooltip">إرسال للخلف</span>
                </button>
                
                <button id="btn-img-copy" data-tooltip="نسخ">
                    <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px">
                        <path d="M360-240q-33 0-56.5-23.5T280-320v-480q0-33 23.5-56.5T360-880h360q33 0 56.5 23.5T800-800v480q0 33-23.5 56.5T720-240H360Zm0-80h360v-480H360v480ZM200-80q-33 0-56.5-23.5T120-160v-560h80v560h440v80H200Zm160-240v-480 480Z"/>
                    </svg>
                    <span class="tooltip">نسخ</span>
                </button>
                
                <button id="btn-delete-image" data-tooltip="حذف">
                    <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px">
                        <path d="M280-120q-33 0-56.5-23.5T200-200v-520h-40v-80h200v-40h240v40h200v80h-40v520q0 33-23.5 56.5T680-120H280Zm400-600H280v520h400v-520ZM360-280h80v-360h-80v360Zm160 0h80v-360h-80v360ZM280-720v520-520Z"/>
                    </svg>
                    <span class="tooltip">حذف</span>
                </button>
                
                <button id="btn-font" data-tooltip="الخط">
                    <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px">
                        <path d="M280-160v-520H80v-120h520v120H400v520H280Zm360 0v-320H520v-120h360v120H760v320H640Z"/>
                    </svg>
                    <span class="tooltip">الخط</span>
                </button>
                
                <button id="filterToggle" data-tooltip="الفلاتر">
                    <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px">
                        <path d="M200-120q-33 0-56.5-23.5T120-200v-560q0-33 23.5-56.5T200-840h560q33 0 56.5 23.5T840-760v560q0 33-23.5 56.5T760-120H200Zm0-80h560v-560L200-200Zm481-120h-83q-26-88-99-144t-169-56q-117 0-198.5 81.5T200-480q0 72 32.5 132t87.5 98v-110h80v240H160v-80h94q-62-50-98-122.5T120-480q0-75 28.5-140.5t77-114q48.5-48.5 114-77T480-840q129 0 226.5 79.5T831-560Z"/>
                    </svg>
                    <span class="tooltip">الفلاتر</span>
                </button>
                
                <button id="cropBtn" data-tooltip="قص">
                    <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px">
                        <path d="M680-40v-160H280q-33 0-56.5-23.5T200-280v-400H40v-80h160v-160h80v640h640v80H760v160h-80Zm0-320v-320H360v-80h320q33 0 56.5 23.5T760-680v320h-80Z"/>
                    </svg>
                    <span class="tooltip">قص</span>
                </button>
                
                <button id="undoBtn" data-tooltip="تراجع">
                    <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px">
                        <path d="M280-200v-80h284q63 0 109.5-40T720-420q0-60-46.5-100T564-560H312l104 104-56 56-200-200 200-200 56 56-104 104h252q97 0 166.5 63T800-420q0 94-69.5 157T564-200H280Z"/>
                    </svg>
                    <span class="tooltip">تراجع</span>
                </button>
                
                <button id="redoBtn" data-tooltip="إعادة">
                    <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px">
                        <path d="M396-200q-97 0-166.5-63T160-420q0-94 69.5-157T396-640h252L544-744l56-56 200 200-200 200-56-56 104-104H396q-63 0-109.5 40T240-420q0 60 46.5 100T396-280h284v80H396Z"/>
                    </svg>
                    <span class="tooltip">إعادة</span>
                </button>
            </div>
            
            <!-- لوحة الخطوط -->
            <div id="font-buttons-container">
                <button class="font-btn tajawal-regular" data-font="Tajawal">تجوال</button>
                <button class="font-btn almarai-regular" data-font="Almarai">مرايا</button>
                <button class="font-btn amiri-regular" data-font="Amiri">أميري</button>
                <button class="font-btn noto-kufi-arabic-regular" data-font="Noto Kufi Arabic">الكوفة</button>
                <button class="font-btn changa-regular" data-font="Changa">تغيير</button>
                <button class="font-btn lalezar-regular" data-font="Lalezar">ليزر</button>
                <button class="font-btn mada-regular" data-font="Mada">مدى</button>
                <button class="font-btn rakkas-regular" data-font="Rakkas">رقاص</button>
                <button class="font-btn reem-kufi-regular" data-font="Reem Kufi">ريم</button>
                <button class="font-btn lateef-regular" data-font="Lateef">لطيف</button>
                <button class="font-btn ruwudu-regular" data-font="Ruwudu">روضة</button>
                <button class="font-btn badeen-display-regular" data-font="Badeen Display">بدين</button>
                <button class="font-btn cairo-regular" data-font="Cairo">القاهرة</button>
                <button class="font-btn kufam-regular" data-font="Kufam">كوفام</button>
                <button class="font-btn aref-ruqaa-regular" data-font="Aref Ruqaa">رقعة</button>
                <button class="font-btn fustat-regular" data-font="Fustat-Regular">فتات</button>
                <button class="font-btn zain-regular" data-font="Zain">زين</button>
            </div>
            
            <!-- شريط الفلاتر -->
            <div id="filterSlider">
                <button class="filter-btn" data-filter="brightness">سطوع</button>
                <button class="filter-btn" data-filter="contrast">تباين</button>
                <button class="filter-btn" data-filter="saturate">تشبع</button>
                <button class="filter-btn" data-filter="grayscale">رمادي</button>
                <button class="filter-btn" data-filter="sepia">سيبيا</button>
                <button class="filter-btn" data-filter="invert">عكسي</button>
                <button class="filter-btn" data-filter="hue-rotate">تدوير اللون</button>
                <button class="filter-btn" data-filter="blur">ضبابية</button>
            </div>
            
            <!-- لوحة الملصقات -->
            <div id="stickers-panel"></div>
        </div>
        
        <!-- القائمة السفلية -->
        <div id="bottom-bar">
            <button id="add-images-btn" data-tooltip="إضافة صور">
                <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px">
                    <path d="M480-260q75 0 127.5-52.5T660-440q0-75-52.5-127.5T480-620q-75 0-127.5 52.5T300-440q0 75 52.5 127.5T480-260Zm0-80q-42 0-71-29t-29-71q0-42 29-71t71-29q42 0 71 29t29 71q0 42-29 71t-71 29ZM160-120q-33 0-56.5-23.5T80-200v-480q0-33 23.5-56.5T160-760h126l74-80h240l74 80h126q33 0 56.5 23.5T880-680v480q0 33-23.5 56.5T800-120H160Zm0-80h640v-480H638l-73-80H395l-73 80H160v480Zm320-240Z"/>
                </svg>
                <span class="tooltip">إضافة صور</span>
            </button>
            
            <button id="add-text-btn" data-tooltip="إضافة نص">
                <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px">
                    <path d="M420-160v-520H200v-120h560v120H540v520H420Z"/>
                </svg>
                <span class="tooltip">إضافة نص</span>
            </button>
            
            <button id="add-stickers-btn" data-tooltip="إضافة ملصقات">
                <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px">
                    <path d="M40-40v-240h80v-400H40v-240h240v80h400v-80h240v240h-80v400h80v240H680v-80H280v80H40Zm160-240v80q-33 0-56.5-23.5T120-280v-400h80v400h400v80H200Zm80-80v-400H280v400h80Zm0-480v-80h80v80h-80Zm480 480v-80h80v80h-80Zm0-480v-80h80v80h-80Zm-240 0v-80h80v80h-80Zm0 480v-80h80v80h-80Z"/>
                </svg>
                <span class="tooltip">إضافة ملصقات</span>
            </button>
            
            <button id="toggle-canvas-btn" data-tooltip="تبديل وضعية الرسم">
                <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px">
                    <path d="M200-120q-33 0-56.5-23.5T120-200v-560q0-33 23.5-56.5T200-840h560q33 0 56.5 23.5T840-760v560q0 33-23.5 56.5T760-120H200Zm0-80h560v-560H200v560Zm280-80q82 0 146-44.5T680-440q-29-71-93.5-115.5T480-600q-82 0-146.5 44.5T240-440q29 71 93.5 115.5T480-280Zm0-60q-56 0-102-26.5T306-440q26-47 72-73.5T480-540q56 0 102 26.5T680-440q-26 47-72 73.5T480-340Zm0-100Zm0 60q25 0 42.5-17.5T540-440q0-25-17.5-42.5T480-500q-25 0-42.5 17.5T420-440q0 25 17.5 42.5T480-380Z"/>
                </svg>
                <span class="tooltip">تبديل وضعية الرسم</span>
            </button>
        </div>
    </div>
    
    <input type="file" id="upload-btn" multiple accept="image/*" style="display: none;">
    
    <script>
        // تهيئة متغيرات الرسم
        let canvas = null;
        let isDrawingMode = false;
        let currentText = null;
        let history = [];
        let historyIndex = -1;
        
        // تهيئة لوحة الرسم عند تحميل الصفحة
        window.onload = function() {
            initCanvas();
            initStickers();
            initEventListeners();
        };
        
        // تهيئة لوحة الرسم
        function initCanvas() {
            const container = document.getElementById('canvas-container');
            canvas = new fabric.Canvas('canvas', {
                width: container.clientWidth,
                height: container.clientHeight,
                backgroundColor: '#182949',
                selection: false,
                isDrawingMode: false
            });
            
            // حفظ حالة البداية في التاريخ
            saveState();
        }
        
        // تهيئة الملصقات
        function initStickers() {
            const stickersPanel = document.getElementById("stickers-panel");
            for (let i = 1; i <= 36; i++) {
                const img = document.createElement("img");
                img.src = `https://via.placeholder.com/40?text=Sticker${i}`;
                img.className = "sticker-item";
                img.alt = `ملصق ${i}`;
                img.onclick = () => addSticker(`https://via.placeholder.com/150?text=Sticker${i}`);
                stickersPanel.appendChild(img);
            }
        }
        
        // تهيئة المستمعين للأحداث
        function initEventListeners() {
            // إضافة صورة
            document.getElementById('add-images-btn').addEventListener('click', () => {
                document.getElementById('upload-btn').click();
            });
            
            // اختيار ملفات الصور
            document.getElementById('upload-btn').addEventListener('change', function(e) {
                Array.from(e.target.files).forEach(file => {
                    if (file.type.match('image.*')) {
                        const reader = new FileReader();
                        reader.onload = function(f) {
                            fabric.Image.fromURL(f.target.result, function(img) {
                                img.scaleToWidth(200);
                                canvas.add(img);
                                saveState();
                            });
                        };
                        reader.readAsDataURL(file);
                    }
                });
            });
            
            // إضافة نص
            document.getElementById('add-text-btn').addEventListener('click', addText);
            
            // إظهار/إخفاء الملصقات
            document.getElementById('add-stickers-btn').addEventListener('click', function() {
                const panel = document.getElementById('stickers-panel');
                panel.style.display = panel.style.display === 'flex' ? 'none' : 'flex';
            });
            
            // ترتيب العناصر
            document.getElementById('btn-img-bring-front').addEventListener('click', () => bringSelectedToFront());
            document.getElementById('btn-img-send-back').addEventListener('click', () => sendSelectedToBack());
            
            // نسخ العنصر المحدد
            document.getElementById('btn-img-copy').addEventListener('click', copySelected);
            
            // حذف العنصر المحدد
            document.getElementById('btn-delete-image').addEventListener('click', deleteSelected);
            
            // تغيير الخط
            document.querySelectorAll('.font-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    if (canvas.getActiveObject() && canvas.getActiveObject().type === 'textbox') {
                        canvas.getActiveObject().set('fontFamily', this.getAttribute('data-font'));
                        canvas.renderAll();
                        saveState();
                    }
                });
            });
            
            // إظهار/إخفاء الفلاتر
            document.getElementById('filterToggle').addEventListener('click', function() {
                const slider = document.getElementById('filterSlider');
                slider.style.display = slider.style.display === 'flex' ? 'none' : 'flex';
            });
            
            // تطبيق الفلاتر
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    if (canvas.getActiveObject()) {
                        const filterType = this.getAttribute('data-filter');
                        applyFilter(filterType);
                    }
                });
            });
            
            // التراجع
            document.getElementById('undoBtn').addEventListener('click', undo);
            
            // الإعادة
            document.getElementById('redoBtn').addEventListener('click', redo);
            
            // تغيير وضعية الرسم
            document.getElementById('toggle-canvas-btn').addEventListener('click', function() {
                isDrawingMode = !isDrawingMode;
                canvas.isDrawingMode = isDrawingMode;
                if (isDrawingMode) {
                    this.style.backgroundColor = '#d3a77b';
                } else {
                    this.style.backgroundColor = '';
                }
            });
            
            // تغيير حجم لوحة الرسم مع تغيير حجم النافذة
            window.addEventListener('resize', function() {
                const container = document.getElementById('canvas-container');
                canvas.setWidth(container.clientWidth);
                canvas.setHeight(container.clientHeight);
                canvas.renderAll();
            });
            
            // حفظ الحالة عند التغيير
            canvas.on('object:modified', () => saveState());
            canvas.on('object:added', () => saveState());
            canvas.on('object:removed', () => saveState());
        }
        
        // إضافة نص جديد
        function addText() {
            const text = new fabric.Textbox('اضغط لتعديل النص', {
                left: 100,
                top: 100,
                width: 200,
                fontSize: 24,
                fontFamily: 'Tajawal',
                fill: '#d3a77b',
                textAlign: 'right',
                borderColor: '#d3a77b',
                cornerColor: '#d3a77b',
                cornerSize: 10,
                transparentCorners: false
            });
            
            canvas.add(text);
            canvas.setActiveObject(text);
            canvas.renderAll();
            saveState();
        }
        
        // إضافة ملصق
        function addSticker(src) {
            fabric.Image.fromURL(src, function(img) {
                img.scaleToWidth(150);
                canvas.add(img);
                saveState();
            });
            document.getElementById('stickers-panel').style.display = 'none';
        }
        
        // إحضار العنصر المحدد للأمام
        function bringSelectedToFront() {
            const activeObject = canvas.getActiveObject();
            if (activeObject) {
                activeObject.bringToFront();
                canvas.renderAll();
                saveState();
            }
        }
        
        // إرسال العنصر المحدد للخلف
        function sendSelectedToBack() {
            const activeObject = canvas.getActiveObject();
            if (activeObject) {
                activeObject.sendToBack();
                canvas.renderAll();
                saveState();
            }
        }
        
        // نسخ العنصر المحدد
        function copySelected() {
            const activeObject = canvas.getActiveObject();
            if (activeObject) {
                activeObject.clone(function(clone) {
                    clone.set({
                        left: activeObject.left + 20,
                        top: activeObject.top + 20
                    });
                    canvas.add(clone);
                    saveState();
                });
            }
        }
        
        // حذف العنصر المحدد
        function deleteSelected() {
            const activeObject = canvas.getActiveObject();
            if (activeObject) {
                canvas.remove(activeObject);
                saveState();
            }
        }
        
        // تطبيق فلتر على العنصر المحدد
        function applyFilter(filterType) {
            const activeObject = canvas.getActiveObject();
            if (activeObject) {
                let filterValue;
                
                switch(filterType) {
                    case 'brightness':
                        filterValue = 1.5;
                        activeObject.filters.push(new fabric.Image.filters.Brightness({ brightness: filterValue }));
                        break;
                    case 'contrast':
                        filterValue = 1.5;
                        activeObject.filters.push(new fabric.Image.filters.Contrast({ contrast: filterValue }));
                        break;
                    case 'saturate':
                        filterValue = 2;
                        activeObject.filters.push(new fabric.Image.filters.Saturation({ saturation: filterValue }));
                        break;
                    case 'grayscale':
                        activeObject.filters.push(new fabric.Image.filters.Grayscale());
                        break;
                    case 'sepia':
                        activeObject.filters.push(new fabric.Image.filters.Sepia());
                        break;
                    case 'invert':
                        activeObject.filters.push(new fabric.Image.filters.Invert());
                        break;
                    case 'hue-rotate':
                        filterValue = 90;
                        activeObject.filters.push(new fabric.Image.filters.HueRotation({ rotation: filterValue }));
                        break;
                    case 'blur':
                        filterValue = 0.5;
                        activeObject.filters.push(new fabric.Image.filters.Blur({ blur: filterValue }));
                        break;
                }
                
                activeObject.applyFilters();
                canvas.renderAll();
                saveState();
            }
        }
        
        // حفظ حالة اللوحة
        function saveState() {
            // إزالة الحالات المستقبلية عند إجراء تغيير جديد
            history = history.slice(0, historyIndex + 1);
            
            // حفظ الحالة الحالية
            history.push(JSON.stringify(canvas));
            historyIndex++;
            
            // تحديث حالة أزرار التراجع والإعادة
            updateUndoRedoButtons();
        }
        
        // التراجع
        function undo() {
            if (historyIndex > 0) {
                historyIndex--;
                canvas.loadFromJSON(history[historyIndex], function() {
                    canvas.renderAll();
                });
            }
            updateUndoRedoButtons();
        }
        
        // الإعادة
        function redo() {
            if (historyIndex < history.length - 1) {
                historyIndex++;
                canvas.loadFromJSON(history[historyIndex], function() {
                    canvas.renderAll();
                });
            }
            updateUndoRedoButtons();
        }
        
        // تحديث حالة أزرار التراجع والإعادة
        function updateUndoRedoButtons() {
            document.getElementById('undoBtn').disabled = historyIndex <= 0;
            document.getElementById('redoBtn').disabled = historyIndex >= history.length - 1;
        }
    </script>
</body>
</html>