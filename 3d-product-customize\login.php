<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - 3D Product Customizer</title>
    <link rel="stylesheet" href="styles/subscription.css">
</head>
<body>
    <div class="auth-container">
        <div class="auth-box">
            <h1>Login to Your Account</h1>
            
            <div id="message-container"></div>
            
            <form id="login-form" method="post" action="process_login.php">
                <div class="form-group">
                    <label for="email">Email Address</label>
                    <input type="email" id="email" name="email" required>
                </div>
                
                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" name="password" required>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn-primary">Login</button>
                </div>
                
                <div class="auth-links">
                    <p>Don't have an account? <a href="register.php">Register</a></p>
                    <p><a href="forgot_password.php">Forgot Password?</a></p>
                </div>
            </form>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('login-form');
            const messageContainer = document.getElementById('message-container');
            
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                // Get form values
                const email = document.getElementById('email').value.trim();
                const password = document.getElementById('password').value;
                
                // Basic validation
                if (email === '' || !isValidEmail(email)) {
                    showMessage('Please enter a valid email address', 'error');
                    return;
                }
                
                if (password === '') {
                    showMessage('Please enter your password', 'error');
                    return;
                }
                
                // If validation passes, submit the form
                const formData = new FormData(loginForm);
                
                fetch('process_login.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showMessage(data.message, 'success');
                        setTimeout(() => {
                            window.location.href = data.redirect || 'dashboard.php';
                        }, 1000);
                    } else {
                        showMessage(data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showMessage('An error occurred. Please try again later.', 'error');
                });
            });
            
            function showMessage(message, type) {
                messageContainer.innerHTML = `<div class="message ${type}">${message}</div>`;
                setTimeout(() => {
                    messageContainer.innerHTML = '';
                }, 5000);
            }
            
            function isValidEmail(email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email);
            }
        });
    </script>
</body>
</html>
