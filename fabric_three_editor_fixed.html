
<!DOCTYPE html>
<html lang="ar">
<head>
  <meta charset="UTF-8" />
  <title>محرر Fabric + مشهد 3D</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <style>
    html, body {
      margin: 0; padding: 0;
      height: 100%; overflow: hidden;
      font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    }
    #top-bar {
      background: #222;
      color: white;
      padding: 10px;
      text-align: center;
    }
    #toggle-editor-btn {
      padding: 8px 16px;
      font-size: 16px;
    }
    #container {
      display: flex;
      flex-direction: column;
      height: calc(100% - 50px);
    }
    #scene-container {
      flex: 1;
      background: #10161c;
    }
    #editor-container {
      height: 30%;
      background: #fafafa;
      display: flex;
      flex-direction: column;
      border-top: 2px solid #ccc;
    }
    #editor-toolbar {
      background: #ddd;
      padding: 8px;
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      align-items: center;
    }
    #fabric-canvas {
      flex: 1;
      background: white;
      width: 100%;
      display: block;
    }
    @media (min-width: 768px) and (orientation: landscape) {
      #container { flex-direction: row; }
      #scene-container { flex: 2; }
      #editor-container {
        flex: 1;
        height: 100%;
        border-top: none;
        border-right: 2px solid #ccc;
      }
    }
  </style>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r134/three.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/three/examples/js/controls/OrbitControls.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/three/examples/js/loaders/GLTFLoader.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.2.4/fabric.min.js"></script>
</head>
<body>



<div id="container">
  <div id="scene-container"></div>
  <div id="editor-container">
    <div id="editor-toolbar">
      <button id="upload-image-btn">📷 رفع صورة</button>
      <button id="add-text-btn">🅰️ إضافة نص</button>
      <button id="export-btn">💾 حفظ</button>
      <label>لون النص: <input type="color" id="text-color" value="#000000"></label>
      <label>الحجم: <input type="number" id="font-size" value="30"></label>
      <label>الخط:
        <select id="font-family">
          <option value="Arial">Arial</option>
          <option value="Tajawal">Tajawal</option>
          <option value="Times New Roman">Times New Roman</option>
        </select>
      </label>
    </div>
    <canvas id="fabric-canvas"></canvas>
  </div>
</div>

<script>
  let scene, camera, renderer, model, controls;
  let fabricCanvas, fabricTexture;
  const dpr = window.devicePixelRatio || 1;

  function initThree() {
    scene = new THREE.Scene();
    camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.position.set(0, 2, 5);

    renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setPixelRatio(dpr);
    document.getElementById("scene-container").appendChild(renderer.domElement);

    scene.add(new THREE.AmbientLight(0xffffff, 0.6));
    let light = new THREE.DirectionalLight(0xffffff, 0.8);
    light.position.set(5, 10, 7);
    scene.add(light);

    controls = new THREE.OrbitControls(camera, renderer.domElement);

    const loader = new THREE.GLTFLoader();
    loader.load("model.gltf", gltf => {
      model = gltf.scene;
      scene.add(model);
    });

    window.addEventListener("resize", () => {
      camera.aspect = window.innerWidth / window.innerHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(window.innerWidth, window.innerHeight);
    });
  }

  function initFabric() {
    const canvasEl = document.getElementById("fabric-canvas");
    fabricCanvas = new fabric.Canvas("fabric-canvas", {
      preserveObjectStacking: true
    });
    fabricCanvas.setWidth(canvasEl.clientWidth * dpr);
    fabricCanvas.setHeight(canvasEl.clientHeight * dpr);
    fabricCanvas.setZoom(dpr);
  }

  function ensureTexture() {
    if (!fabricTexture && fabricCanvas) {
      fabricTexture = new THREE.CanvasTexture(fabricCanvas.lowerCanvasEl);
      fabricTexture.flipY = false;
      fabricTexture.minFilter = THREE.LinearFilter;
      fabricTexture.magFilter = THREE.LinearFilter;
      fabricTexture.anisotropy = renderer.capabilities.getMaxAnisotropy();
    }
  }

  function applyTextureToPrint() {
    if (!model || !fabricTexture) return;
    model.traverse(child => {
      if (child.isMesh && child.name.toLowerCase() === "print") {
        child.material.map = fabricTexture;
        child.material.needsUpdate = true;
      }
    });
  }

  function animate() {
    requestAnimationFrame(animate);
    controls.update();
    if (fabricCanvas && fabricTexture) {
      fabricCanvas.requestRenderAll();
      fabricTexture.needsUpdate = true;
      applyTextureToPrint();
    }
    renderer.render(scene, camera);
  }

  

  document.getElementById("upload-image-btn").onclick = () => {
    const input = document.createElement("input");
    input.type = "file";
    input.accept = "image/*";
    input.onchange = e => {
      const reader = new FileReader();
      reader.onload = evt => {
        fabric.Image.fromURL(evt.target.result, img => {
          img.set({ left: 50, top: 50 });
          fabricCanvas.add(img).setActiveObject(img);
        });
      };
      reader.readAsDataURL(e.target.files[0]);
    };
    input.click();
  };

  document.getElementById("add-text-btn").onclick = () => {
    const text = new fabric.IText("نص تجريبي", {
      left: 100, top: 100,
      fontSize: parseInt(document.getElementById("font-size").value),
      fill: document.getElementById("text-color").value,
      fontFamily: document.getElementById("font-family").value
    });
    fabricCanvas.add(text).setActiveObject(text);
  };

  document.getElementById("text-color").oninput = e => {
    const obj = fabricCanvas.getActiveObject();
    if (obj && obj.isType("text")) obj.set("fill", e.target.value);
  };

  document.getElementById("font-size").oninput = e => {
    const obj = fabricCanvas.getActiveObject();
    if (obj && obj.isType("text")) obj.set("fontSize", parseInt(e.target.value));
  };

  document.getElementById("font-family").onchange = e => {
    const obj = fabricCanvas.getActiveObject();
    if (obj && obj.isType("text")) obj.set("fontFamily", e.target.value);
  };

  document.getElementById("export-btn").onclick = () => {
    const dataURL = fabricCanvas.toDataURL({ format: "png" });
    const a = document.createElement("a");
    a.href = dataURL;
    a.download = "design.png";
    a.click();
  };

  document.addEventListener("DOMContentLoaded", () => {
    initThree();
    initFabric();
    ensureTexture();
    animate();
  });
</script>
</body>
</html>
