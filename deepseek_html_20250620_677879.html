<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محرر الصور المتقدم مع خاصية القص</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/4.5.0/fabric.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #1a2a6c, #b21f1f, #1a2a6c);
            color: #fff;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        header {
            text-align: center;
            padding: 20px 0;
            margin-bottom: 30px;
            border-bottom: 2px solid rgba(255, 255, 255, 0.2);
        }
        
        header h1 {
            font-size: 2.8rem;
            margin-bottom: 10px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }
        
        header p {
            font-size: 1.2rem;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
            opacity: 0.9;
        }
        
        .app-wrapper {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }
        
        .canvas-container {
            position: relative;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            min-height: 600px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        #canvas {
            border: 2px dashed rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            background: rgba(10, 15, 25, 0.5);
            cursor: default;
            width: 100%;
            height: 500px;
            margin-top: 100px;
        }
        
        .floating-menu {
            position: absolute;
            top: 30px;
            background: rgba(20, 30, 48, 0.95);
            border-radius: 15px;
            padding: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            z-index: 10;
            display: flex;
            gap: 15px;
        }
        
        .menu-btn {
            background: rgba(74, 159, 227, 0.2);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .menu-btn:hover {
            background: linear-gradient(to right, #4a9fe3, #2a5a8c);
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        
        .menu-btn.active {
            background: linear-gradient(to right, #4a9fe3, #2a5a8c);
        }
        
        .crop-bar {
            position: absolute;
            bottom: 30px;
            width: 95%;
            background: rgba(20, 30, 48, 0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            z-index: 10;
            display: none;
        }
        
        .crop-bar.active {
            display: block;
            animation: slideUp 0.5s ease;
        }
        
        @keyframes slideUp {
            from { bottom: -100px; opacity: 0; }
            to { bottom: 30px; opacity: 1; }
        }
        
        .crop-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #4a9fe3;
        }
        
        .crop-header h2 {
            font-size: 1.4rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .crop-header i {
            color: #4a9fe3;
        }
        
        .close-crop {
            background: #e74c3c;
            color: white;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }
        
        .close-crop:hover {
            transform: rotate(90deg);
            background: #c0392b;
        }
        
        .crop-scroll {
            overflow-x: auto;
            padding: 10px 0;
            display: flex;
            gap: 15px;
        }
        
        .crop-scroll::-webkit-scrollbar {
            height: 8px;
        }
        
        .crop-scroll::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }
        
        .crop-scroll::-webkit-scrollbar-thumb {
            background: #4a9fe3;
            border-radius: 4px;
        }
        
        .crop-btn {
            background: rgba(74, 159, 227, 0.2);
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 12px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            min-width: 100px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }
        
        .crop-btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            background: linear-gradient(to right, #4a9fe3, #2a5a8c);
        }
        
        .crop-btn i {
            font-size: 1.8rem;
        }
        
        .crop-btn.active {
            background: linear-gradient(to right, #4a9fe3, #2a5a8c);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        
        .tools-panel {
            background: rgba(20, 30, 48, 0.85);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }
        
        .panel-section {
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .panel-title {
            font-size: 1.4rem;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #4a9fe3;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .panel-title i {
            color: #4a9fe3;
        }
        
        .btn-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        button {
            background: linear-gradient(to right, #4a9fe3, #2a5a8c);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            flex: 1;
            min-width: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        button:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            background: linear-gradient(to right, #5aafe3, #3a6a9c);
        }
        
        button:active {
            transform: translateY(1px);
        }
        
        .input-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        input[type="color"],
        input[type="range"],
        select {
            width: 100%;
            padding: 10px;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
        }
        
        input[type="range"] {
            -webkit-appearance: none;
            height: 8px;
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.1);
            outline: none;
        }
        
        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #4a9fe3;
            cursor: pointer;
        }
        
        .footer-buttons {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }
        
        .btn-save {
            background: linear-gradient(to right, #27ae60, #219653);
        }
        
        .btn-reset {
            background: linear-gradient(to right, #e74c3c, #c0392b);
        }
        
        .preview-text {
            position: absolute;
            top: 150px;
            font-size: 2.5rem;
            font-weight: bold;
            color: white;
            text-shadow: 3px 3px 10px rgba(0, 0, 0, 0.8);
            text-align: center;
            width: 100%;
            z-index: 2;
        }
        
        .crop-overlay {
            position: absolute;
            background: rgba(0, 0, 0, 0.5);
            z-index: 5;
            pointer-events: none;
            display: none;
        }
        
        .crop-rect {
            position: absolute;
            border: 2px dashed #4a9fe3;
            box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5);
            cursor: move;
            z-index: 6;
        }
        
        .crop-handle {
            position: absolute;
            width: 12px;
            height: 12px;
            background: #4a9fe3;
            border-radius: 50%;
            z-index: 7;
        }
        
        .crop-handle.nw { top: -6px; left: -6px; cursor: nw-resize; }
        .crop-handle.ne { top: -6px; right: -6px; cursor: ne-resize; }
        .crop-handle.sw { bottom: -6px; left: -6px; cursor: sw-resize; }
        .crop-handle.se { bottom: -6px; right: -6px; cursor: se-resize; }
        
        @media (max-width: 768px) {
            .canvas-container {
                min-height: 500px;
            }
            
            #canvas {
                height: 400px;
                margin-top: 80px;
            }
            
            .floating-menu {
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .crop-bar {
                width: 90%;
                padding: 15px;
            }
            
            .crop-btn {
                min-width: 90px;
                padding: 12px 15px;
            }
            
            .preview-text {
                font-size: 1.8rem;
            }
            
            header h1 {
                font-size: 2rem;
            }
        }
        
        @media (max-width: 480px) {
            #canvas {
                height: 300px;
            }
            
            .crop-btn {
                min-width: 80px;
                font-size: 0.9rem;
            }
            
            .crop-btn i {
                font-size: 1.5rem;
            }
            
            .preview-text {
                font-size: 1.5rem;
                top: 120px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-crop-alt"></i> محرر الصور المتقدم مع خاصية القص</h1>
            <p>قم بتحميل الصور، قصها حسب النسب المطلوبة، تطبيق الفلاتر وإضافة النصوص</p>
        </header>
        
        <div class="app-wrapper">
            <div class="canvas-container">
                <div class="floating-menu" id="floatingMenu">
                    <button class="menu-btn" id="showFiltersBtn">
                        <i class="fas fa-sliders-h"></i> الفلاتر
                    </button>
                    <button class="menu-btn" id="showCropBtn">
                        <i class="fas fa-crop-alt"></i> قص الصورة
                    </button>
                    <button class="menu-btn" id="addTextBtn">
                        <i class="fas fa-font"></i> إضافة نص
                    </button>
                </div>
                
                <div class="crop-bar" id="cropBar">
                    <div class="crop-header">
                        <h2><i class="fas fa-crop-alt"></i> نسب القص</h2>
                        <button class="close-crop" id="closeCrop">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <div class="crop-scroll">
                        <button class="crop-btn" data-ratio="custom">
                            <i class="fas fa-ruler-combined"></i>
                            مخصص
                        </button>
                        <button class="crop-btn" data-ratio="1:1">
                            <i class="fas fa-square"></i>
                            1:1 (مربع)
                        </button>
                        <button class="crop-btn" data-ratio="4:3">
                            <i class="fas fa-tablet-alt"></i>
                            4:3
                        </button>
                        <button class="crop-btn" data-ratio="16:9">
                            <i class="fas fa-tv"></i>
                            16:9
                        </button>
                        <button class="crop-btn" data-ratio="5:3">
                            <i class="fas fa-camera"></i>
                            5:3
                        </button>
                        <button class="crop-btn" data-ratio="5:4">
                            <i class="fas fa-image"></i>
                            5:4
                        </button>
                        <button class="crop-btn" data-ratio="6:4">
                            <i class="fas fa-rectangle-portrait"></i>
                            6:4
                        </button>
                        <button class="crop-btn" data-ratio="7:5">
                            <i class="fas fa-mobile-alt"></i>
                            7:5
                        </button>
                        <button class="crop-btn" data-ratio="10:8">
                            <i class="fas fa-book-open"></i>
                            10:8
                        </button>
                        <button class="crop-btn" id="applyCrop">
                            <i class="fas fa-check"></i>
                            تطبيق القص
                        </button>
                    </div>
                </div>
                
                <div class="crop-overlay" id="cropOverlay"></div>
                <div class="crop-rect" id="cropRect">
                    <div class="crop-handle nw"></div>
                    <div class="crop-handle ne"></div>
                    <div class="crop-handle sw"></div>
                    <div class="crop-handle se"></div>
                </div>
                
                <div class="preview-text">اضغط على زر "تحميل صورة" للبدء</div>
                <canvas id="canvas" width="800" height="500"></canvas>
            </div>
            
            <div class="tools-panel">
                <div class="panel-section">
                    <h2 class="panel-title"><i class="fas fa-cloud-upload-alt"></i> تحميل الصور</h2>
                    <div class="btn-group">
                        <button id="loadImage"><i class="fas fa-image"></i> تحميل صورة</button>
                    </div>
                    <input type="file" id="imageLoader" accept="image/*" style="display: none;">
                </div>
                
                <div class="panel-section">
                    <h2 class="panel-title"><i class="fas fa-sliders-h"></i> تحسينات الصورة</h2>
                    <div class="input-group">
                        <label>السطوع: <span id="brightnessValue">100</span>%</label>
                        <input type="range" id="brightness" min="0" max="200" value="100">
                    </div>
                    <div class="input-group">
                        <label>التباين: <span id="contrastValue">100</span>%</label>
                        <input type="range" id="contrast" min="0" max="200" value="100">
                    </div>
                </div>
                
                <div class="panel-section">
                    <h2 class="panel-title"><i class="fas fa-text-height"></i> خصائص النص</h2>
                    <div class="input-group">
                        <label>النص:</label>
                        <input type="text" id="textInput" placeholder="أدخل النص هنا" value="مثال لنص">
                    </div>
                    <div class="input-group">
                        <label>حجم الخط:</label>
                        <input type="range" id="fontSize" min="10" max="120" value="40">
                    </div>
                    <div class="input-group">
                        <label>لون الخط:</label>
                        <input type="color" id="textColor" value="#ffffff">
                    </div>
                </div>
                
                <div class="footer-buttons">
                    <button id="saveBtn" class="btn-save"><i class="fas fa-save"></i> حفظ التصميم</button>
                    <button id="resetBtn" class="btn-reset"><i class="fas fa-redo"></i> إعادة تعيين</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // تهيئة لوحة الرسم
        const canvas = new fabric.Canvas('canvas', {
            backgroundColor: 'rgba(10, 20, 30, 0.7)',
            preserveObjectStacking: true
        });
        
        // متغيرات التطبيق
        let activeObject = null;
        let currentImage = null;
        let isCropping = false;
        let cropRatio = null;
        let cropRect = {
            x: 0,
            y: 0,
            width: 0,
            height: 0
        };
        
        // عناصر واجهة المستخدم
        const cropBar = document.getElementById('cropBar');
        const cropOverlay = document.getElementById('cropOverlay');
        const cropRectEl = document.getElementById('cropRect');
        const showCropBtn = document.getElementById('showCropBtn');
        const closeCropBtn = document.getElementById('closeCrop');
        const applyCropBtn = document.getElementById('applyCrop');
        const cropBtns = document.querySelectorAll('.crop-btn');
        
        // إظهار/إخفاء شريط القص
        showCropBtn.addEventListener('click', function() {
            if (currentImage) {
                cropBar.classList.toggle('active');
                initCropMode();
            } else {
                alert('الرجاء تحميل صورة أولاً');
            }
        });
        
        closeCropBtn.addEventListener('click', function() {
            cropBar.classList.remove('active');
            resetCropMode();
        });
        
        // تحميل الصورة
        document.getElementById('loadImage').addEventListener('click', function() {
            document.getElementById('imageLoader').click();
        });
        
        document.getElementById('imageLoader').addEventListener('change', function(e) {
            const reader = new FileReader();
            
            reader.onload = function(event) {
                fabric.Image.fromURL(event.target.result, function(img) {
                    // إزالة النص التوضيحي
                    document.querySelector('.preview-text').style.display = 'none';
                    
                    img.scaleToWidth(600);
                    img.set({
                        left: canvas.width / 2,
                        top: canvas.height / 2,
                        originX: 'center',
                        originY: 'center',
                        angle: 0,
                        padding: 10,
                        cornerSize: 20,
                        hasRotatingPoint: true
                    });
                    
                    canvas.clear();
                    canvas.add(img);
                    canvas.setActiveObject(img);
                    activeObject = img;
                    currentImage = img;
                    
                    // إخفاء شريط القص
                    cropBar.classList.remove('active');
                    resetCropMode();
                });
            };
            
            reader.readAsDataURL(e.target.files[0]);
        });
        
        // إضافة نص
        document.getElementById('addTextBtn').addEventListener('click', function() {
            const text = new fabric.Textbox('نص جديد', {
                left: 250,
                top: 200,
                width: 300,
                fontSize: 40,
                fontFamily: 'Arial',
                fill: '#ffffff',
                textAlign: 'right',
                backgroundColor: '#4a9fe3',
                shadow: new fabric.Shadow({
                    color: '#000000',
                    blur: 5,
                    offsetX: 3,
                    offsetY: 3
                })
            });
            
            canvas.add(text);
            canvas.setActiveObject(text);
            activeObject = text;
        });
        
        // تحديث النص
        document.getElementById('textInput').addEventListener('input', function() {
            if (activeObject && activeObject.type === 'textbox') {
                activeObject.set('text', this.value);
                canvas.renderAll();
            }
        });
        
        // تحديث حجم الخط
        document.getElementById('fontSize').addEventListener('input', function() {
            if (activeObject && activeObject.type === 'textbox') {
                activeObject.set('fontSize', parseInt(this.value));
                canvas.renderAll();
            }
        });
        
        // تحديث لون النص
        document.getElementById('textColor').addEventListener('input', function() {
            if (activeObject && activeObject.type === 'textbox') {
                activeObject.set('fill', this.value);
                canvas.renderAll();
            }
        });
        
        // تحديث السطوع
        document.getElementById('brightness').addEventListener('input', function() {
            document.getElementById('brightnessValue').textContent = this.value;
            if (activeObject && activeObject.type === 'image') {
                activeObject.filters = activeObject.filters || [];
                const filter = new fabric.Image.filters.Brightness({
                    brightness: (parseInt(this.value) - 100) / 100
                });
                
                applyFilter(filter, 'brightness');
            }
        });
        
        // تحديث التباين
        document.getElementById('contrast').addEventListener('input', function() {
            document.getElementById('contrastValue').textContent = this.value;
            if (activeObject && activeObject.type === 'image') {
                const filter = new fabric.Image.filters.Contrast({
                    contrast: (parseInt(this.value) - 100) / 100
                });
                
                applyFilter(filter, 'contrast');
            }
        });
        
        // تطبيق الفلاتر
        function applyFilter(filter, filterName) {
            if (!activeObject || activeObject.type !== 'image') return;
            
            // إزالة الفلاتر القديمة من نفس النوع
            activeObject.filters = activeObject.filters.filter(f => 
                f.type !== filterName && f.type !== 'brightness' && f.type !== 'contrast'
            );
            
            // إضافة الفلتر الجديد إذا كان موجوداً
            if (filter) {
                activeObject.filters.push(filter);
            }
            
            // تطبيق الفلاتر المتبقية مع السطوع والتباين
            const brightness = parseFloat(document.getElementById('brightness').value);
            if (brightness !== 100) {
                activeObject.filters.push(new fabric.Image.filters.Brightness({
                    brightness: (brightness - 100) / 100
                }));
            }
            
            const contrast = parseFloat(document.getElementById('contrast').value);
            if (contrast !== 100) {
                activeObject.filters.push(new fabric.Image.filters.Contrast({
                    contrast: (contrast - 100) / 100
                }));
            }
            
            // تطبيق الفلاتر
            activeObject.applyFilters();
            canvas.renderAll();
        }
        
        // تهيئة وضع القص
        function initCropMode() {
            if (!currentImage) return;
            
            isCropping = true;
            cropOverlay.style.display = 'block';
            cropRectEl.style.display = 'block';
            
            // إخفاء عناصر التحكم في الصورة
            currentImage.selectable = false;
            currentImage.hoverCursor = 'default';
            
            // تعيين حجم مستطيل القص الافتراضي
            const imgWidth = currentImage.getScaledWidth();
            const imgHeight = currentImage.getScaledHeight();
            const imgLeft = currentImage.left - imgWidth/2;
            const imgTop = currentImage.top - imgHeight/2;
            
            const cropSize = Math.min(imgWidth, imgHeight) * 0.8;
            
            cropRect = {
                x: imgLeft + (imgWidth - cropSize)/2,
                y: imgTop + (imgHeight - cropSize)/2,
                width: cropSize,
                height: cropSize
            };
            
            updateCropRect();
            initCropHandlers();
        }
        
        // تحديث مستطيل القص على الشاشة
        function updateCropRect() {
            cropRectEl.style.left = cropRect.x + 'px';
            cropRectEl.style.top = cropRect.y + 'px';
            cropRectEl.style.width = cropRect.width + 'px';
            cropRectEl.style.height = cropRect.height + 'px';
            
            cropOverlay.style.left = '0';
            cropOverlay.style.top = '0';
            cropOverlay.style.width = canvas.width + 'px';
            cropOverlay.style.height = canvas.height + 'px';
        }
        
        // إعادة تعيين وضع القص
        function resetCropMode() {
            isCropping = false;
            cropOverlay.style.display = 'none';
            cropRectEl.style.display = 'none';
            
            if (currentImage) {
                currentImage.selectable = true;
                currentImage.hoverCursor = 'move';
            }
        }
        
        // تهيئة معالجات السحب والتعديل
        function initCropHandlers() {
            let isDragging = false;
            let dragStartX, dragStartY;
            let cropStartX, cropStartY, cropStartWidth, cropStartHeight;
            let activeHandle = null;
            
            // معالج بدء السحب
            function startDrag(e) {
                if (!isCropping) return;
                
                isDragging = true;
                dragStartX = e.clientX;
                dragStartY = e.clientY;
                cropStartX = cropRect.x;
                cropStartY = cropRect.y;
                
                // تحديد المقبض النشط
                if (e.target.classList.contains('crop-handle')) {
                    activeHandle = e.target.classList[1];
                    cropStartWidth = cropRect.width;
                    cropStartHeight = cropRect.height;
                } else {
                    activeHandle = null;
                }
            }
            
            // معالج السحب
            function doDrag(e) {
                if (!isDragging || !isCropping) return;
                
                const dx = e.clientX - dragStartX;
                const dy = e.clientY - dragStartY;
                
                if (activeHandle) {
                    // تعديل الحجم باستخدام المقابض
                    switch(activeHandle) {
                        case 'nw':
                            cropRect.x = cropStartX + dx;
                            cropRect.y = cropStartY + dy;
                            cropRect.width = cropStartWidth - dx;
                            cropRect.height = cropStartHeight - dy;
                            break;
                        case 'ne':
                            cropRect.y = cropStartY + dy;
                            cropRect.width = cropStartWidth + dx;
                            cropRect.height = cropStartHeight - dy;
                            break;
                        case 'sw':
                            cropRect.x = cropStartX + dx;
                            cropRect.width = cropStartWidth - dx;
                            cropRect.height = cropStartHeight + dy;
                            break;
                        case 'se':
                            cropRect.width = cropStartWidth + dx;
                            cropRect.height = cropStartHeight + dy;
                            break;
                    }
                    
                    // تطبيق نسبة القص
                    if (cropRatio && cropRatio !== 'custom') {
                        const [w, h] = cropRatio.split(':').map(Number);
                        const aspectRatio = w / h;
                        
                        if (activeHandle === 'ne' || activeHandle === 'se') {
                            cropRect.height = cropRect.width / aspectRatio;
                        } else {
                            cropRect.height = cropRect.width * aspectRatio;
                        }
                    }
                } else {
                    // نقل مستطيل القص
                    cropRect.x = cropStartX + dx;
                    cropRect.y = cropStartY + dy;
                }
                
                // التأكد من بقاء المستطيل داخل حدود الصورة
                constrainCropRect();
                updateCropRect();
            }
            
            // معالج إنهاء السحب
            function stopDrag() {
                isDragging = false;
                activeHandle = null;
            }
            
            // إضافة معالجي الأحداث
            cropRectEl.addEventListener('mousedown', startDrag);
            document.addEventListener('mousemove', doDrag);
            document.addEventListener('mouseup', stopDrag);
            
            // إضافة معالجي الأحداث للمقابض
            const handles = document.querySelectorAll('.crop-handle');
            handles.forEach(handle => {
                handle.addEventListener('mousedown', startDrag);
            });
        }
        
        // التأكد من بقاء مستطيل القص داخل حدود الصورة
        function constrainCropRect() {
            if (!currentImage) return;
            
            const imgWidth = currentImage.getScaledWidth();
            const imgHeight = currentImage.getScaledHeight();
            const imgLeft = currentImage.left - imgWidth/2;
            const imgTop = currentImage.top - imgHeight/2;
            
            // التأكد من أن المستطيل داخل حدود الصورة
            cropRect.x = Math.max(imgLeft, Math.min(imgLeft + imgWidth - cropRect.width, cropRect.x));
            cropRect.y = Math.max(imgTop, Math.min(imgTop + imgHeight - cropRect.height, cropRect.y));
            
            // التأكد من أن الأبعاد لا تقل عن 50 بكسل
            cropRect.width = Math.max(50, cropRect.width);
            cropRect.height = Math.max(50, cropRect.height);
        }
        
        // معالجات أزرار القص
        cropBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const ratio = this.getAttribute('data-ratio');
                
                if (ratio === 'custom') {
                    cropRatio = null;
                } else {
                    cropRatio = ratio;
                    
                    // تطبيق نسبة القص على مستطيل القص الحالي
                    if (cropRatio) {
                        const [w, h] = cropRatio.split(':').map(Number);
                        const aspectRatio = w / h;
                        
                        // الحفاظ على المركز مع تغيير الحجم
                        const centerX = cropRect.x + cropRect.width/2;
                        const centerY = cropRect.y + cropRect.height/2;
                        
                        if (cropRect.width / cropRect.height > aspectRatio) {
                            cropRect.width = cropRect.height * aspectRatio;
                        } else {
                            cropRect.height = cropRect.width / aspectRatio;
                        }
                        
                        cropRect.x = centerX - cropRect.width/2;
                        cropRect.y = centerY - cropRect.height/2;
                        
                        constrainCropRect();
                        updateCropRect();
                    }
                }
                
                // إزالة التحديد من جميع الأزرار
                cropBtns.forEach(b => b.classList.remove('active'));
                
                // تحديد الزر الحالي
                this.classList.add('active');
            });
        });
        
        // تطبيق القص
        applyCropBtn.addEventListener('click', function() {
            if (!currentImage || !isCropping) return;
            
            // الحصول على إحداثيات الصورة
            const imgWidth = currentImage.getScaledWidth();
            const imgHeight = currentImage.getScaledHeight();
            const imgLeft = currentImage.left - imgWidth/2;
            const imgTop = currentImage.top - imgHeight/2;
            
            // حساب الإحداثيات النسبية داخل الصورة
            const relX = (cropRect.x - imgLeft) / imgWidth;
            const relY = (cropRect.y - imgTop) / imgHeight;
            const relWidth = cropRect.width / imgWidth;
            const relHeight = cropRect.height / imgHeight;
            
            // إنشاء عنصر canvas مؤقت للقص
            const tempCanvas = document.createElement('canvas');
            const tempCtx = tempCanvas.getContext('2d');
            
            // تعيين أبعاد canvas المؤقت
            tempCanvas.width = cropRect.width;
            tempCanvas.height = cropRect.height;
            
            // رسم الجزء المطلوب قصه
            tempCtx.drawImage(
                currentImage.getElement(),
                relX * currentImage.width, 
                relY * currentImage.height,
                relWidth * currentImage.width, 
                relHeight * currentImage.height,
                0, 0, 
                cropRect.width, 
                cropRect.height
            );
            
            // إنشاء صورة جديدة من الجزء المقصوص
            const croppedImg = new Image();
            croppedImg.onload = function() {
                fabric.Image.fromURL(this.src, function(img) {
                    img.set({
                        left: canvas.width / 2,
                        top: canvas.height / 2,
                        originX: 'center',
                        originY: 'center',
                        angle: 0,
                        padding: 10,
                        cornerSize: 20,
                        hasRotatingPoint: true
                    });
                    
                    canvas.clear();
                    canvas.add(img);
                    canvas.setActiveObject(img);
                    activeObject = img;
                    currentImage = img;
                    
                    // إعادة تعيين وضع القص
                    resetCropMode();
                    cropBar.classList.remove('active');
                });
            };
            croppedImg.src = tempCanvas.toDataURL('image/png');
        });
        
        // حفظ التصميم
        document.getElementById('saveBtn').addEventListener('click', function() {
            const link = document.createElement('a');
            link.download = 'تصميمي.png';
            link.href = canvas.toDataURL({
                format: 'png',
                quality: 0.95
            });
            link.click();
        });
        
        // إعادة تعيين
        document.getElementById('resetBtn').addEventListener('click', function() {
            canvas.clear();
            activeObject = null;
            currentImage = null;
            
            // إعادة تعيين عناصر التحكم
            document.getElementById('brightness').value = 100;
            document.getElementById('contrast').value = 100;
            document.getElementById('brightnessValue').textContent = '100';
            document.getElementById('contrastValue').textContent = '100';
            
            // إعادة تعيين وضع القص
            resetCropMode();
            cropBar.classList.remove('active');
            
            // إظهار النص التوضيحي
            document.querySelector('.preview-text').style.display = 'block';
        });
        
        // تحديث الكائن النشط عند النقر
        canvas.on('selection:created', function(e) {
            activeObject = e.selected[0];
        });
        
        canvas.on('selection:updated', function(e) {
            activeObject = e.selected[0];
        });
        
        canvas.on('selection:cleared', function() {
            activeObject = null;
        });
    </script>
</body>
</html>