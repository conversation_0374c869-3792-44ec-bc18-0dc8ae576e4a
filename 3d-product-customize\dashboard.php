<?php
require_once 'config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit;
}

// Get user information
$userId = $_SESSION['user_id'];
$userName = $_SESSION['full_name'];
$email = $_SESSION['email'];

// Get subscription information
$subscriptionQuery = "SELECT s.*, 
                         DATE_FORMAT(s.start_date, '%M %d, %Y') as formatted_start_date,
                         DATE_FORMAT(s.expiry_date, '%M %d, %Y') as formatted_expiry_date,
                         DATE_FORMAT(s.next_renewal_date, '%M %d, %Y') as formatted_renewal_date
                       FROM subscriptions s 
                       WHERE s.user_id = ? AND s.status = 'active'
                       ORDER BY s.start_date DESC LIMIT 1";

$subscriptionStmt = $conn->prepare($subscriptionQuery);
$subscriptionStmt->bind_param("i", $userId);
$subscriptionStmt->execute();
$result = $subscriptionStmt->get_result();

// Check if there is an active subscription
$hasActiveSubscription = false;
$subscription = null;
$daysRemaining = 0;

if ($result->num_rows > 0) {
    $hasActiveSubscription = true;
    $subscription = $result->fetch_assoc();
    
    // Calculate days remaining in subscription
    $now = new DateTime();
    $expiryDate = new DateTime($subscription['expiry_date']);
    $interval = $now->diff($expiryDate);
    $daysRemaining = $interval->days;
    
    // Check if subscription has expired
    if ($now > $expiryDate) {
        // Update subscription status to expired
        $updateSql = "UPDATE subscriptions SET status = 'expired' WHERE subscription_id = ?";
        $updateStmt = $conn->prepare($updateSql);
        $updateStmt->bind_param("i", $subscription['subscription_id']);
        $updateStmt->execute();
        $updateStmt->close();
        
        $hasActiveSubscription = false;
    }
}

$subscriptionStmt->close();

// Get download history
$downloadQuery = "SELECT file_type, COUNT(*) as count, MAX(download_date) as last_download
                 FROM download_history
                 WHERE user_id = ?
                 GROUP BY file_type";

$downloadStmt = $conn->prepare($downloadQuery);
$downloadStmt->bind_param("i", $userId);
$downloadStmt->execute();
$downloadResult = $downloadStmt->get_result();

$downloadStats = [
    'mp4' => 0,
    'web' => 0,
    'jpg' => 0,
    'png' => 0
];

while ($row = $downloadResult->fetch_assoc()) {
    $downloadStats[$row['file_type']] = $row['count'];
}

$downloadStmt->close();

// Calculate usage percentages
$videoUsagePercent = 0;
$imageUsagePercent = 0;

if ($hasActiveSubscription) {
    $videoUsed = $subscription['mp4_web_video_used'];
    $videoLimit = $subscription['mp4_web_video_limit'];
    $videoUsagePercent = ($videoLimit > 0) ? min(100, round(($videoUsed / $videoLimit) * 100)) : 0;
    
    $imageUsed = $subscription['jpg_png_image_used'];
    $imageLimit = $subscription['jpg_png_image_limit'];
    $imageUsagePercent = ($imageLimit > 0) ? min(100, round(($imageUsed / $imageLimit) * 100)) : 0;
}

// Process any success or error messages
$successMessage = '';
$errorMessage = '';

if (isset($_SESSION['success'])) {
    $successMessage = $_SESSION['success'];
    unset($_SESSION['success']);
}

if (isset($_SESSION['error'])) {
    $errorMessage = $_SESSION['error'];
    unset($_SESSION['error']);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - 3D Product Customizer</title>
    <link rel="stylesheet" href="styles/subscription.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
    <div class="dashboard-container">
        <div class="dashboard-sidebar">
            <div class="dashboard-logo">
                <h2>3D Customizer</h2>
            </div>
            
            <ul class="dashboard-nav">
                <li><a href="dashboard.php" class="active"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                <li><a href="index.html"><i class="fas fa-cube"></i> 3D Editor</a></li>
                <li><a href="subscription.php"><i class="fas fa-credit-card"></i> Subscription</a></li>
                <li><a href="profile.php"><i class="fas fa-user"></i> Profile</a></li>
                <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
            </ul>
        </div>
        
        <div class="dashboard-content">
            <div class="dashboard-header">
                <h1>Dashboard</h1>
                
                <div class="user-info">
                    <span><?php echo htmlspecialchars($userName); ?></span>
                </div>
            </div>
            
            <?php if (!empty($successMessage)): ?>
                <div class="message success">
                    <?php echo htmlspecialchars($successMessage); ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($errorMessage)): ?>
                <div class="message error">
                    <?php echo htmlspecialchars($errorMessage); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($hasActiveSubscription): ?>
                <div class="dashboard-cards">
                    <div class="dashboard-card">
                        <div class="dashboard-card-header">
                            <div class="dashboard-card-title">Subscription Plan</div>
                            <div class="dashboard-card-icon blue">
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                        <div class="dashboard-card-value">
                            <?php echo ucfirst(htmlspecialchars($subscription['plan_type'])); ?> Plan
                        </div>
                        <div class="dashboard-card-label">
                            Active until <?php echo htmlspecialchars($subscription['formatted_expiry_date']); ?>
                        </div>
                    </div>
                    
                    <div class="dashboard-card">
                        <div class="dashboard-card-header">
                            <div class="dashboard-card-title">Days Remaining</div>
                            <div class="dashboard-card-icon green">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                        </div>
                        <div class="dashboard-card-value">
                            <?php echo $daysRemaining; ?>
                        </div>
                        <div class="dashboard-card-label">
                            Renews on <?php echo htmlspecialchars($subscription['formatted_renewal_date']); ?>
                        </div>
                    </div>
                    
                    <div class="dashboard-card">
                        <div class="dashboard-card-header">
                            <div class="dashboard-card-title">Amount Paid</div>
                            <div class="dashboard-card-icon orange">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                        </div>
                        <div class="dashboard-card-value">
                            $<?php echo number_format($subscription['amount'], 2); ?>
                        </div>
                        <div class="dashboard-card-label">
                            Started on <?php echo htmlspecialchars($subscription['formatted_start_date']); ?>
                        </div>
                    </div>
                </div>
                
                <div class="dashboard-usage">
                    <h2>Download Usage</h2>
                    
                    <div class="usage-card">
                        <div class="usage-title">Video Downloads</div>
                        <div class="usage-details">
                            <div class="usage-count">
                                <?php echo $subscription['mp4_web_video_used']; ?> / <?php echo $subscription['mp4_web_video_limit']; ?>
                            </div>
                            <div class="usage-bar">
                                <div class="usage-progress" style="width: <?php echo $videoUsagePercent; ?>%"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="usage-card">
                        <div class="usage-title">Image Downloads</div>
                        <div class="usage-details">
                            <div class="usage-count">
                                <?php echo $subscription['jpg_png_image_used']; ?> / <?php echo $subscription['jpg_png_image_limit']; ?>
                            </div>
                            <div class="usage-bar">
                                <div class="usage-progress" style="width: <?php echo $imageUsagePercent; ?>%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="dashboard-action">
                    <a href="index.html" class="btn-primary">
                        <i class="fas fa-cube"></i> Go to 3D Editor
                    </a>
                </div>
            <?php else: ?>
                <div class="no-subscription">
                    <div class="no-subscription-icon">
                        <i class="fas fa-exclamation-circle"></i>
                    </div>
                    <h2>No Active Subscription</h2>
                    <p>You don't have an active subscription. Please subscribe to access our 3D Editor and other premium features.</p>
                    <a href="subscription.php" class="btn-primary">View Subscription Plans</a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <style>
        .dashboard-usage {
            margin-top: 30px;
        }
        
        .dashboard-usage h2 {
            color: var(--primary-color);
            margin-bottom: 20px;
        }
        
        .usage-card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .usage-title {
            font-size: 18px;
            color: var(--primary-color);
            margin-bottom: 15px;
        }
        
        .usage-details {
            display: flex;
            flex-direction: column;
        }
        
        .usage-count {
            font-size: 16px;
            margin-bottom: 10px;
        }
        
        .usage-bar {
            height: 10px;
            background-color: #eee;
            border-radius: 5px;
            overflow: hidden;
        }
        
        .usage-progress {
            height: 100%;
            background-color: var(--secondary-color);
        }
        
        .dashboard-action {
            margin-top: 30px;
            text-align: center;
        }
        
        .dashboard-action .btn-primary {
            display: inline-block;
            padding: 12px 30px;
            font-size: 18px;
        }
        
        .no-subscription {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 40px;
            text-align: center;
        }
        
        .no-subscription-icon {
            font-size: 60px;
            color: var(--warning-color);
            margin-bottom: 20px;
        }
        
        .no-subscription h2 {
            color: var(--primary-color);
            margin-bottom: 15px;
        }
        
        .no-subscription p {
            color: #666;
            margin-bottom: 30px;
            font-size: 18px;
        }
    </style>
</body>
</html>
