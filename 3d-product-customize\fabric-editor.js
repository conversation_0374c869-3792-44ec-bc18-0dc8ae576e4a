/**
 * Main entry point for the Fabric.js editor
 * Integrated with all enhancements
 */
document.addEventListener("DOMContentLoaded", () => {
  // Initialize the editor
  const editor = new EditorCore({
    canvasId: "fabric-canvas",
    width: 1024,
    height: 1024,
    backgroundColor: "#ffffff",
  }).init();

  // Initialize font manager
  const fontManager = new FontManager(editor);

  // Initialize alignment guides
  const alignmentGuides = new AlignmentGuides(editor);

  // Set up toolbar listeners
  setupToolbarListeners(editor);

  // Add keyboard shortcut info
  addKeyboardShortcutInfo();

  // Add canvas size manager dropdown to the toolbar
  setupCanvasSizeControl(editor);

  console.log("Enhanced Fabric.js editor initialized");
});

/**
 * Set up toolbar listeners with enhanced functionality
 */
function setupToolbarListeners(editor) {
  // Add text button
  document.getElementById("add-text-btn")?.addEventListener("click", () => {
    editor.addText("Your text here");
  });

  // Add image button
  document.getElementById("add-image-btn")?.addEventListener("click", () => {
    const input = document.createElement("input");
    input.type = "file";
    input.accept = "image/*";

    input.onchange = (e) => {
      const file = e.target.files[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (event) => {
          editor.addImage(event.target.result);
        };
        reader.readAsDataURL(file);
      }
    };

    input.click();
  });

  // Delete button
  document.getElementById("delete-btn")?.addEventListener("click", () => {
    editor.deleteSelected();
  });

  // Undo button
  document.getElementById("undo-btn")?.addEventListener("click", () => {
    editor.history.undo();
  });

  // Redo button
  document.getElementById("redo-btn")?.addEventListener("click", () => {
    editor.history.redo();
  });

  // Group button
  document.getElementById("group-btn")?.addEventListener("click", () => {
    editor.groupSelected();
  });

  // Ungroup button
  document.getElementById("ungroup-btn")?.addEventListener("click", () => {
    editor.ungroupSelected();
  });

  // Enhanced Export button with quality options
  document.getElementById("export-btn")?.addEventListener("click", () => {
    // Create a dropdown dialog for export options
    const exportDialog = document.createElement("div");
    exportDialog.className = "export-dialog";
    exportDialog.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
      z-index: 1000;
      min-width: 300px;
    `;

    const dialogTitle = document.createElement("h3");
    dialogTitle.textContent = "Export Image";
    dialogTitle.style.marginTop = "0";

    // Format selection
    const formatLabel = document.createElement("label");
    formatLabel.textContent = "Format:";
    formatLabel.style.display = "block";
    formatLabel.style.marginTop = "10px";
    
    const formatSelect = document.createElement("select");
    formatSelect.style.width = "100%";
    formatSelect.style.padding = "8px";
    formatSelect.style.marginTop = "5px";
    
    const pngOption = document.createElement("option");
    pngOption.value = "png";
    pngOption.textContent = "PNG (lossless)";
    
    const jpgOption = document.createElement("option");
    jpgOption.value = "jpeg";
    jpgOption.textContent = "JPEG (smaller file size)";
    
    formatSelect.appendChild(pngOption);
    formatSelect.appendChild(jpgOption);

    // Quality slider
    const qualityLabel = document.createElement("label");
    qualityLabel.textContent = "Quality:";
    qualityLabel.style.display = "block";
    qualityLabel.style.marginTop = "15px";
    
    const qualitySlider = document.createElement("input");
    qualitySlider.type = "range";
    qualitySlider.min = "1";
    qualitySlider.max = "10";
    qualitySlider.value = "10";
    qualitySlider.style.width = "100%";
    qualitySlider.style.marginTop = "5px";

    // Scale factor
    const scaleLabel = document.createElement("label");
    scaleLabel.textContent = "Scale factor:";
    scaleLabel.style.display = "block";
    scaleLabel.style.marginTop = "15px";
    
    const scaleSelect = document.createElement("select");
    scaleSelect.style.width = "100%";
    scaleSelect.style.padding = "8px";
    scaleSelect.style.marginTop = "5px";
    
    [1, 2, 3, 4].forEach(scale => {
      const option = document.createElement("option");
      option.value = scale;
      option.textContent = `${scale}x (${scale === 1 ? 'original size' : 'higher resolution'})`;
      if (scale === 2) option.selected = true;
      scaleSelect.appendChild(option);
    });

    // Buttons
    const buttonContainer = document.createElement("div");
    buttonContainer.style.cssText = `
      display: flex;
      justify-content: space-between;
      margin-top: 20px;
    `;
    
    const cancelButton = document.createElement("button");
    cancelButton.textContent = "Cancel";
    cancelButton.style.cssText = `
      padding: 8px 16px;
      background: #f0f0f0;
      border: 1px solid #ddd;
      border-radius: 4px;
      cursor: pointer;
    `;
    
    const exportButton = document.createElement("button");
    exportButton.textContent = "Export";
    exportButton.style.cssText = `
      padding: 8px 16px;
      background: #2196F3;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    `;
    
    buttonContainer.appendChild(cancelButton);
    buttonContainer.appendChild(exportButton);

    // Add elements to dialog
    exportDialog.appendChild(dialogTitle);
    exportDialog.appendChild(formatLabel);
    exportDialog.appendChild(formatSelect);
    exportDialog.appendChild(qualityLabel);
    exportDialog.appendChild(qualitySlider);
    exportDialog.appendChild(scaleLabel);
    exportDialog.appendChild(scaleSelect);
    exportDialog.appendChild(buttonContainer);

    // Add dialog to document
    document.body.appendChild(exportDialog);

    // Cancel button event
    cancelButton.addEventListener("click", () => {
      document.body.removeChild(exportDialog);
    });

    // Export button event
    exportButton.addEventListener("click", () => {
      const format = formatSelect.value;
      const quality = Number(qualitySlider.value) / 10;
      const multiplier = Number(scaleSelect.value);
      
      // Use enhanced export function with download option
      editor.exportAsImage(format, quality, multiplier, true);
      
      document.body.removeChild(exportDialog);
    });
  });

  // Enhanced Export JSON button
  document.getElementById("export-json-btn")?.addEventListener("click", () => {
    editor.exportAsJSON(true);  // This will trigger a download automatically
  });

  // Enhanced Import JSON button
  document.getElementById("import-json-btn")?.addEventListener("click", () => {
    const input = document.createElement("input");
    input.type = "file";
    input.accept = "application/json";

    input.onchange = (e) => {
      const file = e.target.files[0];
      if (file) {
        editor.importFromJSONFile(file);
      }
    };

    input.click();
  });
}

/**
 * Add canvas size dropdown to the toolbar
 */
function setupCanvasSizeControl(editor) {
  // Create container for size controls
  const container = document.createElement("div");
  container.className = "canvas-size-controls";
  container.style.cssText = `
    position: absolute;
    top: 70px;
    right: 20px;
    z-index: 100;
    background: white;
    padding: 10px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
    gap: 8px;
  `;

  // Create header
  const header = document.createElement("div");
  header.style.cssText = `
    font-weight: bold;
    font-size: 14px;
    margin-bottom: 5px;
  `;
  header.textContent = "Canvas Size";

  // Create size preset dropdown
  const dropdown = document.createElement("select");
  dropdown.style.cssText = `
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    width: 100%;
  `;

  // Define presets
  const presets = [
    { name: "1:1 Square", width: 1024, height: 1024 },
    { name: "4:5 Portrait", width: 1024, height: 1280 },
    { name: "5:7 Portrait", width: 1024, height: 1433 },
    { name: "16:9 Widescreen", width: 1280, height: 720 },
    { name: "9:16 Mobile", width: 1080, height: 1920 },
    { name: "4:3 Classic", width: 1024, height: 768 },
    { name: "2:1 Panorama", width: 2048, height: 1024 }
  ];

  // Add options to dropdown
  presets.forEach((preset, index) => {
    const option = document.createElement("option");
    option.value = index.toString();
    option.textContent = `${preset.name} (${preset.width}x${preset.height})`;
    dropdown.appendChild(option);
  });

  // Create orientation toggle button
  const orientationToggle = document.createElement("button");
  orientationToggle.innerHTML = "🔄 Flip Orientation";
  orientationToggle.style.cssText = `
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
    cursor: pointer;
    width: 100%;
  `;

  // Set initial state
  let isLandscape = false;

  // Handle dropdown change
  dropdown.addEventListener("change", () => {
    const selectedIndex = parseInt(dropdown.value);
    const preset = presets[selectedIndex];
    
    let width = preset.width;
    let height = preset.height;
    
    // Apply orientation if needed
    if (isLandscape && width < height) {
      [width, height] = [height, width];
    } else if (!isLandscape && width > height) {
      [width, height] = [height, width];
    }
    
    resizeCanvas(editor, width, height);
  });

  // Handle orientation toggle
  orientationToggle.addEventListener("click", () => {
    isLandscape = !isLandscape;
    orientationToggle.innerHTML = isLandscape ? "🔄 Portrait" : "🔄 Landscape";
    
    // Trigger resize with new orientation
    dropdown.dispatchEvent(new Event("change"));
  });

  // Add elements to container
  container.appendChild(header);
  container.appendChild(dropdown);
  container.appendChild(orientationToggle);

  // Add container to document
  document.body.appendChild(container);
}

/**
 * Resize the canvas with improved handling
 */
function resizeCanvas(editor, width, height) {
  // Ensure minimum width
  width = Math.max(width, 1024);
  
  // Get current canvas content
  const json = editor.exportAsJSON();
  
  // Set new dimensions
  editor.canvas.setWidth(width);
  editor.canvas.setHeight(height);
  
  // Restore content
  editor.importFromJSON(json);
  
  // Update 3D preview
  editor.threeDSync.syncWithThreeJs();
}

/**
 * Add keyboard shortcut info tooltip
 */
function addKeyboardShortcutInfo() {
  const infoContainer = document.createElement("div");
  infoContainer.className = "keyboard-shortcuts-info";
  infoContainer.style.cssText = `
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    z-index: 1000;
  `;
  infoContainer.textContent = "⌨️ Keyboard Shortcuts";
  
  const shortcutsPanel = document.createElement("div");
  shortcutsPanel.style.cssText = `
    position: fixed;
    bottom: 50px;
    right: 20px;
    width: 300px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    padding: 15px;
    display: none;
    z-index: 1000;
  `;
  
  const shortcuts = [
    { key: "Delete", action: "Delete selected" },
    { key: "Ctrl+Z", action: "Undo" },
    { key: "Ctrl+Y / Ctrl+Shift+Z", action: "Redo" },
    { key: "Ctrl+D", action: "Duplicate selected" },
    { key: "Ctrl+G", action: "Group selected objects" },
    { key: "Ctrl+Shift+G", action: "Ungroup selected" },
    { key: "Arrow keys", action: "Move selected object" },
    { key: "Shift+Arrow keys", action: "Move selected object by 10px" },
  ];
  
  const shortcutsList = document.createElement("ul");
  shortcutsList.style.cssText = `
    margin: 0;
    padding: 0;
    list-style: none;
  `;
  
  shortcuts.forEach(shortcut => {
    const item = document.createElement("li");
    item.style.cssText = `
      display: flex;
      justify-content: space-between;
      padding: 5px 0;
      border-bottom: 1px solid #eee;
    `;
    
    const keySpan = document.createElement("span");
    keySpan.style.fontWeight = "bold";
    keySpan.textContent = shortcut.key;
    
    const actionSpan = document.createElement("span");
    actionSpan.style.color = "#666";
    actionSpan.textContent = shortcut.action;
    
    item.appendChild(keySpan);
    item.appendChild(actionSpan);
    shortcutsList.appendChild(item);
  });
  
  const heading = document.createElement("h4");
  heading.textContent = "Keyboard Shortcuts";
  heading.style.margin = "0 0 10px 0";
  
  shortcutsPanel.appendChild(heading);
  shortcutsPanel.appendChild(shortcutsList);
  
  let isShortcutsPanelVisible = false;
  
  infoContainer.addEventListener("click", () => {
    isShortcutsPanelVisible = !isShortcutsPanelVisible;
    shortcutsPanel.style.display = isShortcutsPanelVisible ? "block" : "none";
  });
  
  document.body.appendChild(infoContainer);
  document.body.appendChild(shortcutsPanel);
}
