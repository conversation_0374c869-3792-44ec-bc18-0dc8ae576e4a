"use client"

import { useState, useEffect } from "react"
import * as THREE from "three"

interface TextureLoaderProps {
  url: string
  onLoad: (texture: THREE.Texture) => void
}

export function useTextureLoader(url: string | null): THREE.Texture | null {
  const [texture, setTexture] = useState<THREE.Texture | null>(null)

  useEffect(() => {
    if (!url) return

    const loader = new THREE.TextureLoader()

    // Load the texture
    loader.load(
      url,
      (loadedTexture) => {
        // Configure the texture
        loadedTexture.wrapS = THREE.RepeatWrapping
        loadedTexture.wrapT = THREE.RepeatWrapping
        loadedTexture.needsUpdate = true

        // Set the texture
        setTexture(loadedTexture)
      },
      undefined,
      (error) => {
        console.error("Error loading texture:", error)
      },
    )

    // Cleanup
    return () => {
      if (texture) {
        texture.dispose()
      }
    }
  }, [url])

  return texture
}

export function TextureLoader({ url, onLoad }: TextureLoaderProps) {
  useEffect(() => {
    if (!url) return

    const loader = new THREE.TextureLoader()

    // Load the texture
    loader.load(
      url,
      (texture) => {
        // Configure the texture
        texture.wrapS = THREE.RepeatWrapping
        texture.wrapT = THREE.RepeatWrapping
        texture.needsUpdate = true

        // Call the onLoad callback
        onLoad(texture)
      },
      undefined,
      (error) => {
        console.error("Error loading texture:", error)
      },
    )
  }, [url, onLoad])

  return null
}
