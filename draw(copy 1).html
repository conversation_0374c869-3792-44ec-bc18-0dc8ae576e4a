<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Fabric.js Drawing Mode</title>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
  <style>
    #drawing-mode-options {
      max-width: 350px;
      padding: 10px;
      display: flex;
      flex-direction: column;
    }
    label {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
    }
    canvas {
      border: 1px solid #ccc;
    }
  </style>
</head>
<body>
<div>
  <canvas id="c" width="800" height="500"></canvas>
  <div id="drawing-mode-options">
    <button id="drawing-mode">Cancel drawing mode</button>
    <button id="clear-canvas">Clear</button>

    <label for="drawing-mode-selector">
      Mode:
      <select id="drawing-mode-selector">
        <option>Pencil</option>
        <option>Circle</option>
        <option>Spray</option>
        <option>Pattern</option>
        <option>hline</option>
        <option>vline</option>
        <option>square</option>
        <option>diamond</option>
        <option>texture</option>
      </select>
    </label>

    <label for="drawing-line-width">
      Line width:
      <span class="info" id="line-width-info">30</span>
      <input type="range" min="1" max="150" id="drawing-line-width" value="30">
    </label>

    <label for="drawing-color">
      Line color:
      <input type="color" id="drawing-color" value="#76cef4">
    </label>

    <label for="drawing-shadow-color">
      Shadow color:
      <input type="color" id="drawing-shadow-color" value="#5a7896">
    </label>

    <label for="drawing-shadow-width">
      Shadow width:
      <span class="info" id="shadow-width-info">0</span>
      <input type="range" min="0" max="50" id="drawing-shadow-width" value="0">
    </label>

    <label for="drawing-shadow-offset">
      Shadow offset:
      <span class="info" id="shadow-offset-info">0</span>
      <input type="range" min="0" max="50" id="drawing-shadow-offset" value="0">
    </label>
  </div>
</div>

<script>
  const canvas = new fabric.Canvas('c', {
    isDrawingMode: true,
  });
  fabric.Object.prototype.transparentCorners = false;

  const $ = id => document.getElementById(id);
  const drawingModeEl = $('drawing-mode');
  const drawingColorEl = $('drawing-color');
  const drawingShadowColorEl = $('drawing-shadow-color');
  const drawingLineWidthEl = $('drawing-line-width');
  const drawingShadowWidth = $('drawing-shadow-width');
  const drawingShadowOffset = $('drawing-shadow-offset');
  const clearEl = $('clear-canvas');
  const selectorEl = $('drawing-mode-selector');

  canvas.freeDrawingBrush = new fabric.PencilBrush(canvas);

  function updateBrush() {
    const brush = canvas.freeDrawingBrush;
    brush.color = drawingColorEl.value;
    brush.width = parseInt(drawingLineWidthEl.value, 10) || 1;
    brush.shadow = new fabric.Shadow({
      blur: parseInt(drawingShadowWidth.value, 10) || 0,
      offsetX: parseInt(drawingShadowOffset.value, 10) || 0,
      offsetY: parseInt(drawingShadowOffset.value, 10) || 0,
      color: drawingShadowColorEl.value,
    });
  }

  drawingModeEl.onclick = () => {
    canvas.isDrawingMode = !canvas.isDrawingMode;
    drawingModeEl.textContent = canvas.isDrawingMode ? 'Cancel drawing mode' : 'Enter drawing mode';
  };

  clearEl.onclick = () => canvas.clear();

  drawingColorEl.onchange = updateBrush;
  drawingShadowColorEl.onchange = updateBrush;
  drawingLineWidthEl.oninput = () => {
    $('line-width-info').textContent = drawingLineWidthEl.value;
    updateBrush();
  };
  drawingShadowWidth.oninput = () => {
    $('shadow-width-info').textContent = drawingShadowWidth.value;
    updateBrush();
  };
  drawingShadowOffset.oninput = () => {
    $('shadow-offset-info').textContent = drawingShadowOffset.value;
    updateBrush();
  };

  selectorEl.onchange = () => {
    const val = selectorEl.value;
    if (val === 'hline') {
      const brush = new fabric.PatternBrush(canvas);
      brush.getPatternSrc = () => {
        const patternCanvas = document.createElement('canvas');
        patternCanvas.width = patternCanvas.height = 10;
        const ctx = patternCanvas.getContext('2d');
        ctx.strokeStyle = brush.color;
        ctx.lineWidth = 5;
        ctx.beginPath();
        ctx.moveTo(0, 5);
        ctx.lineTo(10, 5);
        ctx.stroke();
        return patternCanvas;
      };
      canvas.freeDrawingBrush = brush;
    } else if (val === 'vline') {
      const brush = new fabric.PatternBrush(canvas);
      brush.getPatternSrc = () => {
        const patternCanvas = document.createElement('canvas');
        patternCanvas.width = patternCanvas.height = 10;
        const ctx = patternCanvas.getContext('2d');
        ctx.strokeStyle = brush.color;
        ctx.lineWidth = 5;
        ctx.beginPath();
        ctx.moveTo(5, 0);
        ctx.lineTo(5, 10);
        ctx.stroke();
        return patternCanvas;
      };
      canvas.freeDrawingBrush = brush;
    } else {
      const brushClass = fabric[val + 'Brush'] || fabric.PencilBrush;
      canvas.freeDrawingBrush = new brushClass(canvas);
    }
    updateBrush();
  };

  updateBrush();
</script>
</body>
</html>
