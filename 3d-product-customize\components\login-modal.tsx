"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { X } from "lucide-react"

interface LoginModalProps {
  onClose: () => void
  onRegister: () => void
}

export function LoginModal({ onClose, onRegister }: LoginModalProps) {
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="w-full max-w-md rounded-lg border border-[#D6A25E] bg-[#1F2A45] p-6">
        <div className="mb-4 flex items-center justify-between">
          <h2 className="text-xl font-bold text-[#D6A25E]">تسجيل الدخول</h2>
          <Button variant="ghost" size="icon" onClick={onClose} className="text-[#D6A25E]">
            <X className="h-5 w-5" />
          </Button>
        </div>

        <form className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="email" className="block text-sm text-[#D6A25E]">
              البريد الإلكتروني
            </label>
            <Input
              id="email"
              type="email"
              placeholder="أدخل بريدك الإلكتروني"
              className="border-[#D6A25E] bg-[#344464] text-white"
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="password" className="block text-sm text-[#D6A25E]">
              كلمة المرور
            </label>
            <Input
              id="password"
              type="password"
              placeholder="أدخل كلمة المرور"
              className="border-[#D6A25E] bg-[#344464] text-white"
            />
          </div>

          <Button type="submit" className="w-full border-[#D6A25E] bg-[#D6A25E] text-[#1F2A45] hover:bg-[#D6A25E]/90">
            تسجيل الدخول
          </Button>

          <div className="relative my-2">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t border-[#D6A25E]/30"></span>
            </div>
            <div className="relative flex justify-center text-xs">
              <span className="bg-[#1F2A45] px-2 text-[#D6A25E]">أو</span>
            </div>
          </div>

          <Button
            type="button"
            variant="outline"
            className="flex w-full items-center justify-center gap-2 border-[#D6A25E] bg-[#1F2A45] text-[#D6A25E] hover:bg-[#D6A25E] hover:text-[#1F2A45]"
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="24px" height="24px" className="h-5 w-5">
              <path
                fill="#FFC107"
                d="M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12c0-6.627,5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24c0,11.045,8.955,20,20,20c11.045,0,20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z"
              />
              <path
                fill="#FF3D00"
                d="M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z"
              />
              <path
                fill="#4CAF50"
                d="M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z"
              />
              <path
                fill="#1976D2"
                d="M43.611,20.083H42V20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z"
              />
            </svg>
            تسجيل الدخول باستخدام Google
          </Button>

          <div className="text-center text-sm text-[#D6A25E]">
            ليس لديك حساب؟{" "}
            <button type="button" onClick={onRegister} className="font-bold underline hover:text-[#D6A25E]/90">
              إنشاء حساب جديد
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
