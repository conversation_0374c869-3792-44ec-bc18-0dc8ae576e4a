/* Subscription System Styles */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --light-color: #ecf0f1;
    --dark-color: #34495e;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --error-color: #e74c3c;
    --border-radius: 5px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
    margin: 0;
    padding: 0;
}

/* Container for auth forms */
.auth-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.auth-box {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 30px;
    width: 100%;
    max-width: 500px;
}

.auth-box h1 {
    text-align: center;
    color: var(--primary-color);
    margin-bottom: 30px;
}

/* Form styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: var(--dark-color);
}

.form-group input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-size: 16px;
    transition: var(--transition);
}

.form-group input:focus {
    border-color: var(--secondary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.btn-primary {
    background-color: var(--secondary-color);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    width: 100%;
    transition: var(--transition);
}

.btn-primary:hover {
    background-color: #2980b9;
}

.auth-links {
    text-align: center;
    margin-top: 20px;
}

.auth-links a {
    color: var(--secondary-color);
    text-decoration: none;
}

.auth-links a:hover {
    text-decoration: underline;
}

/* Message styles */
#message-container {
    margin-bottom: 20px;
}

.message {
    padding: 10px 15px;
    border-radius: var(--border-radius);
    margin-bottom: 15px;
}

.success {
    background-color: rgba(46, 204, 113, 0.2);
    color: #27ae60;
    border-left: 4px solid #27ae60;
}

.error {
    background-color: rgba(231, 76, 60, 0.2);
    color: #c0392b;
    border-left: 4px solid #c0392b;
}

.warning {
    background-color: rgba(243, 156, 18, 0.2);
    color: #d35400;
    border-left: 4px solid #d35400;
}

/* Subscription page styles */
.subscription-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
}

.subscription-header {
    text-align: center;
    margin-bottom: 50px;
}

.subscription-plans {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 30px;
    margin-bottom: 50px;
}

.plan-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 30px;
    width: 250px;
    text-align: center;
    transition: var(--transition);
    position: relative;
}

.plan-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.plan-card.popular {
    border-top: 4px solid var(--accent-color);
}

.popular-tag {
    position: absolute;
    top: -12px;
    right: 20px;
    background-color: var(--accent-color);
    color: white;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
}

.plan-name {
    color: var(--primary-color);
    font-size: 24px;
    margin-bottom: 15px;
}

.plan-price {
    font-size: 40px;
    color: var(--dark-color);
    margin-bottom: 20px;
}

.plan-price small {
    font-size: 16px;
    color: #777;
}

.plan-features {
    list-style: none;
    padding: 0;
    margin: 0 0 30px 0;
}

.plan-features li {
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.plan-features li:last-child {
    border-bottom: none;
}

.plan-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: bold;
    transition: var(--transition);
    width: 100%;
}

.plan-button:hover {
    background-color: var(--dark-color);
}

.popular .plan-button {
    background-color: var(--accent-color);
}

.popular .plan-button:hover {
    background-color: #c0392b;
}

/* Dashboard styles */
.dashboard-container {
    display: flex;
    min-height: 100vh;
}

.dashboard-sidebar {
    width: 250px;
    background-color: var(--primary-color);
    color: white;
    padding: 20px 0;
}

.dashboard-logo {
    text-align: center;
    padding: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 20px;
}

.dashboard-nav {
    list-style: none;
    padding: 0;
    margin: 0;
}

.dashboard-nav li a {
    display: block;
    padding: 12px 20px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition);
}

.dashboard-nav li a:hover,
.dashboard-nav li a.active {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    border-left: 4px solid var(--secondary-color);
}

.dashboard-content {
    flex: 1;
    padding: 30px;
    background-color: #f9f9f9;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.dashboard-header h1 {
    color: var(--primary-color);
}

.user-info {
    display: flex;
    align-items: center;
}

.user-info img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
}

.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.dashboard-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 20px;
}

.dashboard-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.dashboard-card-title {
    font-size: 18px;
    color: var(--primary-color);
}

.dashboard-card-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.dashboard-card-icon.blue {
    background-color: var(--secondary-color);
}

.dashboard-card-icon.green {
    background-color: var(--success-color);
}

.dashboard-card-icon.orange {
    background-color: var(--warning-color);
}

.dashboard-card-value {
    font-size: 28px;
    font-weight: bold;
    color: var(--dark-color);
}

.dashboard-card-label {
    color: #777;
    font-size: 14px;
}

/* Responsive styles */
@media screen and (max-width: 768px) {
    .subscription-plans {
        flex-direction: column;
        align-items: center;
    }
    
    .plan-card {
        width: 100%;
        max-width: 400px;
    }
    
    .dashboard-container {
        flex-direction: column;
    }
    
    .dashboard-sidebar {
        width: 100%;
        padding: 10px 0;
    }
    
    .dashboard-logo {
        padding: 10px;
    }
    
    .dashboard-nav li a {
        padding: 10px 15px;
    }
    
    .dashboard-content {
        padding: 15px;
    }
    
    .dashboard-cards {
        grid-template-columns: 1fr;
    }
}
